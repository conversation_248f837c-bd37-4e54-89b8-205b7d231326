import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-file-selector-dialog',
  templateUrl: './file-selector-dialog.component.html',
  styleUrls: ['./file-selector-dialog.component.css']
})
export class FileSelectorDialogComponent  {


  selectedFile: File | null = null;

  constructor(public dialogRef: MatDialogRef<FileSelectorDialogComponent>) { }

  onFileSelected(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;
    if (fileList && fileList.length > 0) {
      this.selectedFile = fileList.item(0);
    }
  }

}
