.json-node {
  margin-bottom: 5px;
}

.node-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
}

.expand-icon {
  margin-right: 5px;
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.node-key {
  font-weight: 500;
  color: #3f51b5;
  margin-right: 8px;
}

.node-type-badge {
  font-size: 12px;
  background-color: #e0e0e0;
  padding: 2px 6px;
  border-radius: 4px;
  color: #616161;
}

.node-content {
  padding-left: 20px;
  display: none;
}

.node-content.expanded {
  display: block;
}

.empty-node {
  font-style: italic;
  color: #999;
  padding: 5px 0;
}

.object-property {
  margin: 5px 0;
}

.array-controls {
  margin: 5px 0;
}

.array-item {
  margin: 10px 0;
  border-left: 2px solid #ff4081;
  padding-left: 10px;
  background-color: #fafafa;
  border-radius: 4px;
}

.array-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    background-color: #f0f0f0;
  }
}

.array-item-title {
  display: flex;
  align-items: center;
}

.array-index {
  font-weight: 500;
  color: #ff4081;
  background-color: #fce4ec;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: 8px;
}

.array-item-preview {
  color: #616161;
  font-style: italic;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.array-item-content {
  padding: 0 8px 8px 8px;
  display: none;

  &.expanded {
    display: block;
  }
}

.primitive-value {
  padding: 5px 0;
}

.null-value-container {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .null-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .null-value {
    font-style: italic;
    color: #999;
  }
}

.full-width {
  width: 100%;
}

// Indentation levels
@for $i from 0 through 10 {
  .json-node-level-#{$i} {
    margin-left: #{$i * 5}px;
  }
}
