import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import { EnvironmentType } from 'src/app/shared-services/configuration.service';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { IframeJwtService } from 'src/app/shared-services/iframe-communication/iframe-jwt.service';

@Component({
  selector: 'app-pdf-filler',
  templateUrl: './pdf-filler.component.html',
  styleUrls: ['./pdf-filler.component.scss']
})
export class PdfFillerComponent implements OnInit, AfterViewInit {

  pdfFilleUrl: SafeResourceUrl;
  private iframeId = 'pdfFillerFrame';

  constructor(
    private userContext: UserContext,
    private sanitizer: Dom<PERSON>anitizer,
    private iframeJwtService: IframeJwtService
  ) {
    const tmpUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);
    let pdfRoute = ApiRoutes.PdfFillerViewer.toString();
    pdfRoute = pdfRoute.replace('{{siteId}}', this.userContext.siteId.toString())
                       .replace('{{workFlowProccessId}}', '1')
                       .replace('{{formRuleId}}', '1');

    // Use the URL without token in the src attribute for better security
    this.pdfFilleUrl = this.sanitizer.bypassSecurityTrustResourceUrl(tmpUrl + pdfRoute);
  }

  ngOnInit(): void {
    // Set up a listener for iframe messages
    const pdfFillerUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);

    window.addEventListener('message', (event) => {
      // Security check - only accept messages from the PDF Filler origin
      if (event.origin !== pdfFillerUrl) return;

      // Handle messages from the iframe
      if (event.data && event.data.action === 'IFRAME_READY') {
        console.log('PDF Filler iframe is ready to receive JWT token');
        this.sendJwtToIframe();
      }
    });
  }

  ngAfterViewInit(): void {
    // After the view is initialized, set up the iframe with JWT token
    setTimeout(() => {
      this.sendJwtToIframe();
    }, 500);
  }

  /**
   * Public method called by the iframe load event
   */
  public onIframeLoad(): void {
    console.log('PDF Filler iframe loaded');
    this.sendJwtToIframe();
  }

  /**
   * Send the JWT token to the iframe using the IframeJwtService
   */
  private sendJwtToIframe(): void {
    // Get the iframe by ID
    const iframe = document.getElementById(this.iframeId) as HTMLIFrameElement;
    if (iframe && iframe.contentWindow) {
      // Send the JWT token to the iframe
      this.iframeJwtService.sendJwtToIframe(this.iframeId);

      // Set up token refresh handling
      this.iframeJwtService.setupTokenRefreshHandling([this.iframeId]);
    } else {
      console.error('PDF Filler iframe not found or not ready');
    }
  }
}
