// ObjectUtils.ts
export class ObjectUtils {
  /**
   * Checks if the provided object is empty.
   * An object is considered empty if it has no own enumerable properties.
   * @param obj The object to check.
   * @returns true if the object is empty, false otherwise.
   */
  public static isObjectEmpty(obj: any): boolean {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
  }
}