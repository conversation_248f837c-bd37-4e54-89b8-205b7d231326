<div class="workflows-container">
  <div class="action-controls">
    <div class="spacer"></div>
    <div class="action-buttons">
      <button type="button" mat-raised-button color="primary" (click)="onCreateWorkflow()" class="btn-primary">
        <mat-icon>add</mat-icon> Create Workflow
      </button>
      <button type="button" mat-icon-button (click)="onRefreshWorkflows()" matTooltip="Refresh Workflows">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <div class="workflows-table-container">
    <table mat-table [dataSource]="workflows" class="mat-elevation-z2">
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let workflow">{{workflow.name}}</td>
      </ng-container>

      <!-- Description Column -->
      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef>Description</th>
        <td mat-cell *matCellDef="let workflow">{{workflow.description}}</td>
      </ng-container>

      <!-- Created Date Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef>Created</th>
        <td mat-cell *matCellDef="let workflow">{{workflow.createdDate | date:'short'}}</td>
      </ng-container>

      <!-- Modified Date Column -->
      <ng-container matColumnDef="modifiedDate">
        <th mat-header-cell *matHeaderCellDef>Modified</th>
        <td mat-cell *matCellDef="let workflow">{{workflow.modifiedDate | date:'short'}}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let workflow">
          <button type="button" mat-icon-button (click)="onViewWorkflow(workflow)" matTooltip="View Workflow">
            <mat-icon>visibility</mat-icon>
          </button>
          <button type="button" mat-icon-button (click)="onExecuteWorkflow(workflow)" matTooltip="Execute Workflow">
            <mat-icon>play_arrow</mat-icon>
          </button>
          <button type="button" mat-icon-button (click)="onDownloadSqlScript(workflow)" matTooltip="Export SQL Script">
            <mat-icon>save_alt</mat-icon>
          </button>
          <button type="button" mat-icon-button (click)="onDeleteWorkflow(workflow)" matTooltip="Delete Workflow">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="workflows.length === 0" class="no-data-message">
      <mat-icon>info</mat-icon>
      <p>No workflows found. Create a new workflow to get started.</p>
    </div>
  </div>
</div>
