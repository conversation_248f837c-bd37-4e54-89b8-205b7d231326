import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Subscription, timer } from 'rxjs';
import { DialogSessionTimeoutComponent } from '../dialogs/session-timeout-dialog/dialog-session-timeout.component';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';


@Component({
  selector: 'app-main-layout',
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss']
})


export class MainLayoutComponent implements OnInit {

  userName: string = "";
  constructor() {
  }

  ngOnInit(): void{}

}
