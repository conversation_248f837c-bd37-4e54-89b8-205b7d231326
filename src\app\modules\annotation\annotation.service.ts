import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiRoutes, ApiTypes } from "src/app/shared-services/ep-api-handler/api-option-enums";
import { ApiHandler } from "src/app/shared-services/ep-api-handler/ep-api-handler";
import { UserContext } from "src/app/shared-services/user-context/user-context.service";
import { AnnotationDetails, AnnotationRequest } from "./annotation-models";

@Injectable({
    providedIn: 'root'
})
export class AnnotationService {
    annotationUrl:string='';
    constructor(private apihandler: ApiHandler, private userContext: UserContext) { 
    }

    GetAnnotationDetails(annotationRequest: AnnotationRequest): Observable<AnnotationDetails> {
        return this.apihandler.Post<AnnotationDetails>(
            ApiTypes.V2,
            ApiRoutes.GetAnnotationDetails + this.userContext.GetCurrentSiteValue(),
            JSON.stringify(annotationRequest),
            null,
            true
        );
    }

    SaveMeasureResponse(annotationDetails: AnnotationDetails): Observable<boolean> {
        return this.apihandler.Post<AnnotationDetails>(
            ApiTypes.V2,
            ApiRoutes.SaveMeasureResponse + this.userContext.GetCurrentSiteValue(),
            JSON.stringify(annotationDetails),
            null,
            true
        );
    }
}