﻿<?xml version="1.0" encoding="utf-8"?>
<Report MustUnderstand="df" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:df="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition/defaultfontfamily">
  <df:DefaultFontFamily>Segoe UI</df:DefaultFontFamily>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="dsExtractDate">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT EXTRACT_DT
          FROM CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsPatientDemographic">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>REPORT.GET_PATIENT_DEMOGRAPHICS</CommandText>
      </Query>
      <Fields>
        <Field Name="PATIENT_HEADER">
          <DataField>PATIENT_HEADER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DATE_RANGE">
          <DataField>DATE_RANGE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsPDF">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>REPORT.PDF_FILL_PATIENT_FORMS</CommandText>
      </Query>
      <Fields>
        <Field Name="PDF_ID">
          <DataField>PDF_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DEMOGRAPHICS_ID">
          <DataField>DEMOGRAPHICS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PDF_NAME">
          <DataField>PDF_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PDF_URL">
          <DataField>PDF_URL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsTitle">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT [NAME] FROM [REPORT].[REPORT] WHERE REPORT_ID = 27</CommandText>
      </Query>
      <Fields>
        <Field Name="NAME">
          <DataField>NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Image Name="EpividianLogo2">
            <Source>Embedded</Source>
            <Value>Epividian_Logotransparentbg_400x104</Value>
            <Sizing>FitProportional</Sizing>
            <Top>0.08524in</Top>
            <Left>0.03428in</Left>
            <Height>0.87594in</Height>
            <Width>3.41314in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Image>
          <Textbox Name="Textbox48">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!EXTRACT_DT.Value, "dsExtractDate")</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Format>dd-MMM-yyyy</Format>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> (data)</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Now()</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Format>dd-MMM-yyyy</Format>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> (run)</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Top>0.07354in</Top>
            <Left>8.24817in</Left>
            <Height>0.38267in</Height>
            <Width>1.78611in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!PATIENT_HEADER.Value, "dsPatientDemographic")</Value>
                    <MarkupType>HTML</MarkupType>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <MarkupType>HTML</MarkupType>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox4</rd:DefaultName>
            <Top>1.46508in</Top>
            <Left>0.15928in</Left>
            <Height>0.69485in</Height>
            <Width>2.81489in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Textbox>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>4.26042in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.83333in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.3125in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  PDF Form</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>DimGray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0061aa</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  Action</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>DimGray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0061aa</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PDF_NAME">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value xml:space="preserve">  </Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>10.5pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!PDF_NAME.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>10.5pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PDF_NAME</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>DimGray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="RESOURCE_LOCATION">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  Open</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>10.5pt</FontSize>
                                    <Color>Blue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>RESOURCE_LOCATION</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Hyperlink>="javascript:void(window.open('" &amp; Fields!PDF_URL.Value &amp; "'))"</Hyperlink>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>DimGray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>dsPDF</DataSetName>
            <Top>2.50917in</Top>
            <Left>2.53303in</Left>
            <Height>0.5625in</Height>
            <Width>5.09375in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TextAlign>Center</TextAlign>
              <VerticalAlign>Middle</VerticalAlign>
            </Style>
          </Tablix>
          <Line Name="Line1">
            <Top>2.4516in</Top>
            <Left>0.03429in</Left>
            <Height>0in</Height>
            <Width>10.12499in</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Note: All available PDFs are listed below and Epividian makes no inference on a patient's eligibility or suitability for any specific form.</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>10.5pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>2.16542in</Top>
            <Left>0.15928in</Left>
            <Height>0.25in</Height>
            <Width>9.875in</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox13">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!NAME.Value, "dsTitle")</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>25pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>#0061aa</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox13</rd:DefaultName>
            <Top>2.47667cm</Top>
            <Height>1.10352cm</Height>
            <Width>25.89166cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>3.07167in</Height>
        <Style />
      </Body>
      <Width>10.19357in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="DEMOGRAPHICS_ID">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>152688</Value>
        </Values>
      </DefaultValue>
      <Prompt>DEMOGRAPHICS ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>09429856-5643-40D1-ABB2-349B798445D0</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>DEMOGRAPHICS_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="Epividian_Logotransparentbg_400x104">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQEASABIAAD/4QgbaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLwA8P3hwYWNrZXQgYmVnaW49J++7vycgaWQ9J1c1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCc/Pg0KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyI+DQoJPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4NCgkJPHJkZjpEZXNjcmlwdGlvbiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iPg0KCQkJPGRjOmZvcm1hdD5pbWFnZS9qcGVnPC9kYzpmb3JtYXQ+DQoJCQk8ZGM6dGl0bGU+DQoJCQkJPHJkZjpBbHQ+DQoJCQkJCTxyZGY6bGkgeG1sOmxhbmc9IngtZGVmYXVsdCI+UHJpbnQ8L3JkZjpsaT4NCgkJCQk8L3JkZjpBbHQ+DQoJCQk8L2RjOnRpdGxlPg0KCQk8L3JkZjpEZXNjcmlwdGlvbj4NCgkJPHJkZjpEZXNjcmlwdGlvbiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iPg0KCQkJPHhtcDpNZXRhZGF0YURhdGU+MjAxMi0wNS0wN1QxMDoxNTozNy0wNzowMDwveG1wOk1ldGFkYXRhRGF0ZT4NCgkJCTx4bXA6TW9kaWZ5RGF0ZT4yMDEyLTA1LTA3VDE3OjE1OjM5WjwveG1wOk1vZGlmeURhdGU+DQoJCQk8eG1wOkNyZWF0ZURhdGU+MjAxMi0wNS0wN1QxMDoxNTozNy0wNzowMDwveG1wOkNyZWF0ZURhdGU+DQoJCQk8eG1wOkNyZWF0b3JUb29sPkFkb2JlIElsbHVzdHJhdG9yIENTNS4xPC94bXA6
Q3JlYXRvclRvb2w+DQoJCTwvcmRmOkRlc2NyaXB0aW9uPg0KCQk8cmRmOkRlc2NyaXB0aW9uIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIj4NCgkJCTx4bXBNTTpJbnN0YW5jZUlEPnhtcC5paWQ6RUM1MjAxQjAxMjIwNjgxMTgwODNCQzYyNjkzMDg4OEE8L3htcE1NOkluc3RhbmNlSUQ+DQoJCQk8eG1wTU06RG9jdW1lbnRJRCByZGY6cmVzb3VyY2U9InhtcC5kaWQ6RUM1MjAxQjAxMjIwNjgxMTgwODNCQzYyNjkzMDg4OEEiLz4NCgkJCTx4bXBNTTpPcmlnaW5hbERvY3VtZW50SUQ+dXVpZDo1RDIwODkyNDkzQkZEQjExOTE0QTg1OTBEMzE1MDhDODwveG1wTU06T3JpZ2luYWxEb2N1bWVudElEPg0KCQkJPHhtcE1NOlJlbmRpdGlvbkNsYXNzPnByb29mOnBkZjwveG1wTU06UmVuZGl0aW9uQ2xhc3M+DQoJCQk8eG1wTU06RGVyaXZlZEZyb20gcmRmOnBhcnNlVHlwZT0iUmVzb3VyY2UiIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIj4NCgkJCQk8c3RSZWY6aW5zdGFuY2VJRD54bXAuaWlkOkVBNTIwMUIwMTIyMDY4MTE4MDgzQkM2MjY5MzA4ODhBPC9zdFJlZjppbnN0YW5jZUlEPg0KCQkJCTxzdFJlZjpkb2N1bWVudElEPnhtcC5kaWQ6RUE1MjAxQjAxMjIwNjgxMTgwODNCQzYyNjkzMDg4OEE8L3N0UmVmOmRvY3VtZW50SUQ+DQoJCQkJPHN0UmVmOm9yaWdpbmFsRG9jdW1lbnRJRD51dWlkOjVEMjA4OTI0OTNCRkRCMTE5MTRB
ODU5MEQzMTUwOEM4PC9zdFJlZjpvcmlnaW5hbERvY3VtZW50SUQ+DQoJCQkJPHN0UmVmOnJlbmRpdGlvbkNsYXNzPnByb29mOnBkZjwvc3RSZWY6cmVuZGl0aW9uQ2xhc3M+DQoJCQk8L3htcE1NOkRlcml2ZWRGcm9tPg0KCQkJPHhtcE1NOkhpc3Rvcnk+DQoJCQkJPHJkZjpTZXE+DQoJCQkJCTxyZGY6bGk+PC9yZGY6bGk+DQoJCQkJPC9yZGY6U2VxPg0KCQkJPC94bXBNTTpIaXN0b3J5Pg0KCQk8L3JkZjpEZXNjcmlwdGlvbj4NCgkJPHJkZjpEZXNjcmlwdGlvbiB4bWxuczppbGx1c3RyYXRvcj0iaHR0cDovL25zLmFkb2JlLmNvbS9pbGx1c3RyYXRvci8xLjAvIj4NCgkJCTxpbGx1c3RyYXRvcjpTdGFydHVwUHJvZmlsZT5QcmludDwvaWxsdXN0cmF0b3I6U3RhcnR1cFByb2ZpbGU+DQoJCTwvcmRmOkRlc2NyaXB0aW9uPg0KCQk8cmRmOkRlc2NyaXB0aW9uIHhtbG5zOnBkZj0iaHR0cDovL25zLmFkb2JlLmNvbS9wZGYvMS4zLyI+DQoJCQk8cGRmOlByb2R1Y2VyPkFkb2JlIFBERiBsaWJyYXJ5IDkuOTA8L3BkZjpQcm9kdWNlcj4NCgkJPC9yZGY6RGVzY3JpcHRpb24+DQoJPC9yZGY6UkRGPg0KPC94OnhtcG1ldGE+DQo8P3hwYWNrZXQgZW5kPSd3Jz8+/9sAQwACAQECAQECAgICAgICAgMFAwMDAwMGBAQDBQcGBwcHBgcHCAkLCQgICggHBwoNCgoLDAwMDAcJDg8NDA4LDAwM/9sAQwECAgIDAwMGAwMGDAgHCAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgAaQGQAwEiAAIR
AQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A/fyiivB/j5+1x4w/Z8n1jUtT+EmsX/gvS5VUa5ZazbStIhwPMa2H7yNckjLcDAyRmu7L8tr42r7DDpOXROUY36WXM1d9krvyPLzjOsLllD6zjHJQW7UJzSSV25ckZcsUlrKVku57xRXyH4R/4LFeDfGWpx2Nh4D+JGo30qllttOsbe7lYDrhVmDED6V1Nx/wUit0H7v4M/HZv9/wuF/9qmvarcF51SlyVaDi/NxX6nzWG8SuGsRD2tDFqUe6Un+UT6Tor438cf8ABYLTfAzql78L/HWnySEiNdURLEucZ43Z/TNfTXwE+K4+OXwd8P8Ai5dOm0ldetRdLaSyCRoQSQPmAAYHGQcDgjgdK5My4ZzLL6EcTjKXLCTsneLu9+jb6HfknG+SZvi54HLq
/PUguaS5ZKyulf3opbtdbnXUUUV4R9UFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFZfjjUtW0fwfqV1oOl2+t61b2zyWWnz3n2OO8lAysbTbH8sMeN2xsZzg1+T3/BUr/goz+0x8MfFbeBdW0zS/hLZ6pZ/bLebw/fG+u7+3Ysh23+FKFXUg+XHDKpAJO1lJ+h4d4bxOc4j6vh5Ri/70kn52j8T+St5nzfE3FGFyTDfWcTGUl/di2rva8vhXzd/I/XWiuG/Zh1O51r9mv4eXl5cTXd5d+GdNmnnmcySTSNaxlnZjyzEkkk8kmu5rwq1N06kqb6Nr7j3sPW9rSjVX2kn96uFFFFZmwUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVyvx00qHXfgl4xsbhd1veaHewSr/eVoHUj8jXVVz3xc/wCSU+J/+wTdf+iXrowcmq8Gu6/M48yipYSrGWzjL8mfnd/wRPhV/wBobxPIVUyL4bZQ2OQDcwEj8cD8q/TKvzP/AOCJp/4v/wCKP+xdP/pTDX6YV+heLX/JRVP8MP8A0k/Hvo+/8kbS/wAdT/0o5H4+fDeH4vfBTxV4altLa9bWNMnt4I5wNgnKHynyQdrLJsYN1UqCORUf7OvgO6+FvwC8F+HNQWNdQ0TRLOzuxG+9BOkKLJtPcbw2D3FSfED4y6Z8O/HHg3w/dR3E+o+Nr6ayskh2/uxFA80krZIOxQqqcZOZF4rrq+FqVsTDBxw01anJ8682rxuvua+R+q0cLgquYzxtN3qwj7OXknaaT03s01rs/MKKx/HXxD0H4Y+H5NV8Raxpuh6bDw1xe3Cwx57KCxGWPYDk9hWJ8I/j34a+Nfwzbxhot46+HfNuUF3eR/ZlKQSMjSnfgqh2FgWwQp5CnIHPHB13R+sKD5LqPNZ2u7tK+19HodcsywscR9TlUj7TlcuW65uVWTlbeybWu2p2dFeR
v+3r8G49e/s4/EXwz9o3bPMFzm3z/wBdgPKx77sV6no+s2fiHS7e+0+6tr6xu4xLBcW8olimQ8hlZSQwPqDitMVl+KwyTxFKUE9uaLV/S61M8DnGAxrlHB14VHHfllGVvWzdvmWa+FvB158d7j/gqRfrdR6sPD0NzKsyTGQaMuiHd5LJj5DKQFIK/N5wYNhRIB901wt7+0v4Hs/jFpvgFfENjdeLtUeVI9OtiZ5IDFC8z+cVBWI7EJAchjxgGvW4fzCrho4iNLDqrz05J3TfIt3NW2stW+mjvofPcXZPh8dLBzxGLlh/Z1oSVpKPtJbRpu+/M3ZJXvqrO+ndUVynjT48eB/hv4ih0fxF4z8KaDq1xALqKy1HV7e1uJYSxUSLHI4YoWVhuAxlSOxrOvv2pvhjplqbi5+I3gO3gUZMkuv2iKPxMmK8WODryScYNp7aPU+oljsPFtSqRTW+q09TvKKh1HUrfR9PuLy8uIbW1tY2mmmmcRxwooyzMx4VQASSeABXB2/7W/wpvLVZ4fid8PZYZF3LIniKzZWHqCJMYqaeHq1FenFv0TZpVxVGk7VZqPq0vzO71M3I024+xiA3nlN5AmJEZkwdu7HO3OM45xX5v/8ABIy8/aWvf2yviZJ8T4/Ew0HbIviIa3vFrFqgdfIWwB+THllv9R+58nyiTjyM/of4G+Ifh/4n6Cuq+Gtc0fxFpbSPCLzTL2O7ty6HDLvjJXcp4IzkGuL+H37Yvw1+LXxq1L4f+FvFmm+IvE+j2D6lfQ6duuLe1iSWOJg1woMJkDyqDGHLLg5AxXuZZjK+HwmKwscOp88UpScW3TSe/l87apPpZ+DmmCoYnGYTFyxLhySbjFSSVRtbefyvo2ut16ZRRWb4w8Z6P8PPDd3rXiDVtN0PR7BPMub7ULpLW2t1yBl5HIVRkgZJHWvn4xcnyxV2z6KUlFc0tEjSr8rf+DliFV1H4LSBVEjw68rNjkgHTcD8Mn8zX2sf+Cov
7Pq6xFYt8VvCsbzttinkmdLWT3W4KiIj3D496+G/+Di/xdpPjvSfgTq+h6pp+s6TfW+vyW17Y3KXFvcLnTBuSRCVYZB5BNfofh7gMVh+IsNKvTlBPntdNX/dz7o/NvEfMMJieGcUsPVjNrkvyyTt+8h2Z+kH7J//ACaz8Nf+xV0v/wBJIq5P/gopc/Em0/Y58ayfCdLp/Ga2q+T9j5vhb71+0G1A5NwIfMKBfnz9zMmwHrP2T/8Ak1n4a/8AYq6X/wCkkVdh4r8W6V4E8O3esa5qen6NpOnp5t1e31wlvb2ydNzyOQqjkckgV8f7Z0cy9qoqbjO/K1dO0r2a6p7WPtFQVfK/YubgpU7cydnG8bXT6Nb3Pk7/AIInSfFaT9kHd8TBqv2VtRkbwy2sGQ6k9gVUkyeZ8/leaX8ov8xXp+68rP2DXBfs+ftN+Cf2qPDOqa14D1pde0nR9Uk0ee7S3lhja4jjikcJ5iqXULKmHUFTngkc13tGeYipXx9WtVpeylJtuNrWv0s+vfu9QyHDU8Pl1GhRqurGMUlNu/NbrdaW7dlpcKK8Z+K3/BRD4H/BLxNNovib4neE9P1e1kMVxZR3f2q4tXBwUlSIOY2H91wDXW/Bj9pn4e/tE2k03gfxl4d8UfZVD3EWn3ySz2wJwDJFnfHnHG5RmuepluLp0vbzpSUH9pxaX32sdNPNMHUrfV4VYuf8qkm/uvc7miiuf+J/xZ8M/BXwhca/4u17SfDejWvD3moXKW8W4gkICxG5zg4Vcsx4AJrkp05TkoQV29ktWzrqVI04uc2klu3okdBRXC/Cb9pTwX8a/glB8RtC1uH/AIQu4F1ImqXyNYxLHbzyQSyOJghjQPE/LheBniuDsP8Agp1+z9qPiZtJj+LXgsXSuU8yS9EdqT7XDAQke4fBrshleMnKcIUpNwbUkot8rW6emlvM4qmbYKnGE51oJTScW5JcyezV3rfyPdqKjtLuK/tY54JI5oZkEkck
bBlkUjIII4II5yKkrhPQCvA/+ChH7Rmtfs0fDfwlrGiXMNvJfeK7KyvfMhWQS2eyaWZPmBxuEQUsMMAxwQcEe+V8i/8ABVnTo/Fmk+E9DmVnjOm+JdZKgfxWejTvGfwkkT86+k4Rw1Gvm1GniI80PebT6qMXJ/kfF+IWOxOE4fxFbBzcKnuxi07WcpxitV5s+uqKyfAniJfGHgfRtWXBXVLGC8BHcSRq/wDWtavnZwcJOMt1ofYUqkakFUhs0mvRhRRRUmgUUUUAFFFFABRRRQAUUUUAFFFFABXPfFz/AJJT4n/7BN1/6Jeuhrnvi5/ySnxP/wBgm6/9EvW+F/jQ9V+ZyZh/utT/AAv8mfk9/wAE9vh18RPiN8W9Rt/hv4qt/COqW2ktJd3s/wAyvbmWIeWF2OGJfYeQMBTz2P2FP+yx+1BcR7W+Omm4/wBm02n8xCDXh/8AwRN/5L/4o/7F0/8ApTDX6YV+yeJXEmJweeToU6dNpRjrKnCT1V95Js/m7wT4LweY8LU8VWrVouU56QrVIR0dvhjJL101Pz1+GHwJ+KPwa/4KNfCmb4meIpPFi6oNU/s3UTqEt3EuzT7jfEFlCmJgWjbaFCncCCxDY/QqsXxN8PNH8Ya/oOqalZrcX/hm6e902bzHRraV4nhY/KQGBjkYbWyOhxkAjar824iz+WbSoVakVGUIcj5Uox0nNppLbSSvtrfSx+1cG8JQ4fp4nD0ZynCpVdSLlJykk4U4tSk9X70Xbf3Wru58E/8ABWT9kjwr4R+GC/EHSl1aPxA2rRwXkl3qlxfLcxzCQkATu5j2uFKhCqgZG3pjQ/4J6fsvxftB/st+H5vH15cal4M0+6uxo3hu2ne2tJnF1I0l3eFGDzS+buREJEaJHnazSMR6F/wWA/5M8m/7DNn/ADetz/glT/yY14R/676h/wCl1xX3VTOsauCadb2j541+VSbvKK5G/de6a1Sa1SbS0Pyqnwzlk/E+vh/YxVOW
F55QStCUnUinzRWkk9G01ZySbVyT4qf8EwfhD8R/Dc1rZ+G4/C+oeXtt9Q0qR43gbsTGSY5BxghlJxnBU8j5S/ZB+J3iz9gf9sCT4UeKrp5PDmsahHYSRht1uks2Ps17Bk4VXLIHwR8rHdlowB+mNfnr/wAFk/BrR/G/4X6xp6t/a2rwS6ehX7xMFxE0WPfddNXPwPnVfM6tTIczm6lKtGVuZtuEoxclJN3atZ6bXs+9+zxS4ZwmR0KXFmR0lRxGGnDm5EoqpCUlBxko2Tu2lfe112t90/E/4Y6Z8XfCM2iaxJqiWNwwaQWGoz2Mj4yNpeF1ZlOTlSSp7g4Ffmj+yB8PLX4Sf8FXrDwzYvJJY6FrOsWds0hzIYksbwJuIABbbjJAAJzwK/U+vzR+C/8Aymmuv+xi1n/0hu6z8P8AFVv7PzTD8z5Pq9R26Xs1e3exr4uYHD/2vkWMUF7T63SjzW15eZO1+10fYH7df7DnhP8Abd+DepaLq2nafH4mhtZDoOttEBcaXc4Jj/eAFjAz4EkfRlz0YKy/mT/wQx0LwPbfteeKPBHxF8H6Je+KrqyePRxrNlHcSabeWcj/AGq2RHBCzMhZi2MqLRxkZIP7SV+N/wDwV6+Fuq/sOf8ABQjwz8ZPCMS29v4muk8QW6qfLiGpWzILuBtvOyZWjdieXNxOOgNTwLmFXGYbEcPSqNe1i3T1atOOtl5O12uyfc9/xBy+lgsVhuJYU0/ZTSqaJ3hLS7843snvdrsfsdPAl1A8ciLJHIpV0YZVgeCCO4NfmL+1x/wT68C/H7/grZ4H8E+HPDem6HoUfhtPEvjeLSYVtIJIEuZVVSsZXZNOwjiZlw+2USclc1+hPhn4/eF/FHwBtfibHqC2/g+50MeIXvJhj7NaCHznaQLkqyIG3LyQVI6ivGP+Cafg3UvFfhLxV8b/ABNaSWvij45agutx28mN+m6NGvl6Xa5HynFviQsAC3ncjIr5zIcZicqjiMZB
uDSdNLVe/K62/upSl5SUV1PqeIMDhM3lh8FOKnGTVRvR+5Gz3/vtxj5pyfQ7j4/fsL/D/wDaF+ENr4HvrPVPD/hvToJbeysPDmozaTZ24kA5NtCy28u0gMqyxuqktgDc2fzL/wCDdWNof2yfFyPt3p4MuFbb0yL6yBxX7LV+N3/BvD/yer40/wCxQuv/AEvs69/hnGVqnD2aUqkm4qMGk3ezbldr1srny/F2DoU+Jcpq04JSlKabStdRUbL5Xdj9e/H3jnS/hh4G1nxJrl0tjo2gWU2o31wVLCGCJDJI2BycKpOBye1fIX7B2i3X/BQzUbj9oD4m2Ud9pJ1Oe1+Hnhe5Am07w9a28hja88skpJevKsimZgWUxHYVVlROl/4LZ67daH/wTc8eC1aSNr6bTbSR0OCsbahb7x9GAKH2c1e/4Iz6ta6t/wAE2vht9l2L9mjvreVARlJEv7kNkdiT83PZge9eJhcL9W4fnmVP451VSv1jHlcnZ9HJ2TfZNbN3+jxeM+s8R08rqfw4UnVt0lJzUVddVFXaXdp7pW+k/EXhzT/F+h3Wl6tYWWqabfRmK5tLuBZ4LhD1V0YFWB9CMV+Kf/Bbf9ifwz+yH8WPDGoeCo20rwx47ivrtdEWRmt9MvIDbrcPAD9yOVZYDs52mMgEIERP25r8r/8Ag5a/4/Pgn/1y1/8Anpler4YY2vTz6lh4SahPm5l0doSa07prffp1Z5HitgcPV4erYipFOcOVxfVXnFPXs03pt13SP0Q/ZP8A+TWfhr/2Kul/+kkVfJf/AAXI/ZD8L+LP2XvFXxSmuPER8U+GpLCW1STWrmbTlV7mC1dVtJHaGLMchbMKIWdQxJ3Nu+tP2T/+TWfhr/2Kul/+kkVeP/8ABZv/AJRp/Ez/AK56d/6crSvHyHFVaHEVKVKTjeqk7dU5pNPyZ7HEOFpV+Ga0a0VJKi2r9GoNprzTPJ/+Ddb/AJMq8Vf9jxd/+kGn1u/8F1P2l/Gv
7P37LWn2Pg2PU9O/4S69ex1PXrVXH9l2ypnyhKv+qknZgqsTnakoX5iGXC/4N1v+TKvFX/Y8Xf8A6QafX3Z4h1rT/Dmg3uoatd2dhpdjA893c3cqxW9vCqku8jsQqoFBJJOAAc16WfY2GE4sq4mpSVRRqX5X1006Prrs/Q8zhvA1MZwdRwtKq6TlTtzrW2uvVdNN1vueN/si/s9fC22/ZK8C2+g+DvDE3h3WfDtjeDzdLhkOorNbRv5sxZf3juGyzNkkmvzF/wCCo3wytv8Agmh+3x4X8a/CeP8A4RmG+sk1+CxtpClvb3CTSJcW6KPu2sqBAYslcSSKAE2qv6K6J/wUp8N/FfXb3Q/g14N8ZfF240vMM97o1rFYaHZyLj93JfXjxR52kMoiEm5eVBFfnN/wXk8SeN/FHxY8D3XjjwrofhG8Ph+4FrZ6frzauxj89stK5t4VRs4+VN477u1e/wAC0cd/bjp4y6hVU+aE5LmaabV4N8z9eXbyPnvECtl7yGNTBWc6Mocs4Rdk00naaXL8lI/au3nW5t45FztkUMM+h5r8wv8AguV+wt4H+HXwJtfiZoi6+vihdeitbyfUtfvtV+1QTrKSo+1TSeXtdUKiPaoG4YPG39ONJ/5BVr/1yT+Qr4t/4L9f8mDN/wBjJYfylr5DgfF1qOd4eNKTipTSaT3Tez7n2fH2DoYjIMTKtBScYNq62aV7rseb/wDBLP8AYut/2sP2HfAtx8Ur651r4e6LcagvhvwlZ3UtpYzyf2jcvNfX5jZXuJ/PMkccZYRRxx52s0rkexftF/8ABEz4I/F7wFfWvhnw5D4D8SCFvsGqaZLL5cUgB2iWBmMckZbG7gPjO11PNa3/AARN/wCUZPw3/wCumr/+ne9r6qroz7iLMsNnOI+rVpQUKs7KLajpJ7xWjvvK6d3uc3D/AAzlmJyPDLFUY1HOlC7kk5awW0nqkto2astj8rf+CFn7THir4a/HTxN+zz4xuJjb
6aLxtMtZ5vMOkX9pNsurSI/883HmSbQQqtC5AzIxr9Uq/Hr4BxLZ/wDBxVdpCvlrJ4p18sF4DFtKvWP5tz9a/YWujxEo01mNLE04qLrUoVJJbc0rp/fbXz1OfwzrVXllXC1JOSoVZ04t78sbNfde3poFeAftC+F4/if+1R4V8Psqs1v4G8SSkEZ4uTZWv/sxFe/14nbwPf8A/BRi6m+9Fpfw4ij4/ha41OQnP4Wy183kc3Tq1K8XZwpzf3rl/wDbj3uKqca1CjhZq6qVaafopc//ALabv7FOuL4i/ZD+GlyjbtvhuxgY+rRQJG3/AI8hr0+vFf8Agn3af2J+y/pejM2ZPDuqatpLj+55OpXKKv4IFr2qss+hGGZYiMdlOdvTmdjo4VqSqZLhJz+J0qd/J8quvkworJ8XePNF8A21nNrmrabo8OoXcdhbPeXCwrcXEmdkSbiNztg4Uc4BPQGtavMcJKKk1o9n3PajVhKTgmrq111V9r9r9AoooqTQKKKKACiiszxb410fwDozajr2raZounxsFa6v7pLaFSegLuQMnB71UISnJRirt9ERUqQpxc6jSS3b0S9WadFZfhHxto3j/Rl1HQdW0vW9Pdii3VhdJcwsR1AdCRkZHfvWpROEoScZqzXRip1IVIKpTaaezWqfowoooqTQK5f43alDo/wX8X3lzIsVva6JezSu3REWBySfoAa6ivKf2hv2S9L/AGl7yFde8UeN7PRVt1gm0TTNSS2068KuXEkyeWWkbO3q20bFIAOSe7LY0HiYPEzcYJ3bSu9Oy019WkeVncsWsFUjgqaqVGmknLlV2rXbs9F1sm+yPhH/AII2eM9M8K/tI6xbalfWlg2qaA8NqbiZYxPKs8LeWu4jc5XcwA5wjHtX6fz30Nrb+dJNFHFjO9nAXH1r5qi/4JHfBZI9r6TrUvu+rTf0IFZuq/8ABG74N6gD5CeKNPY/xW+oqx/8iRtX6DxZmnD+e5k8d7epTukm
nSUlppdNVE/wPyLw/wAh4v4WyVZV9Vo1+WUpJqvKD953s06Mk9et0ev/ABO/bK+F3wgs5pNc8b6BHND1tLW5F3dk9v3MW6T8SuB3IrzT9kb9pfxP+2J8c9c8U6fa3mh/C3w7YPpVlbXG3zdUvpJIpDLJjK5jjTG1WOzzV5O5sc/oX/BGD4UaRq0Nxcap441S3iYM1pc39ukU4/usYoEcD/dZT719R+CPBGkfDbwpY6HoOnWuk6RpsflW1rbpsjiXJJ47kkkknJYkkkkk14eOrcP4TCSp5dz1q01bnmlGMF15Y6vme123ZO61PqMrw/F2Y5hCtnPs8Nh6b5lTpyc51JdOebSSgt7JJtqzVj5w/wCCwH/Jnk3/AGGbP+b1S/4JA/FzSPFP7McPhOG4jTXPCt3c/aLVmHmPDNM0yTKO6EyMmezIc9Rm7/wWA/5M8m/7DNn/ADevNP2F/wBhXwX8bP2WfB/i83XiXwr4yEt6p1zw/qj2d4wS8nRRzuQYQBcqoYgYJr6bBwwU+CFDGylFPEPlklzWlyaXV17trp21TadmfD5lUzOn4ouplkIzawi5oyk480PaJNRlaSUk3Fq9k0mrq59318j674cj/bY/b00TVNPJvPh/8F+J9QT/AFF9rG8S+RC4OH8srbs+MgeUQeJEJ72D9hZ9ZhW18U/Fb4reKNI+7Lpk2si1t72Mgho5zCiySKQeRvFez+C/BOkfDnwtZaHoOm2ek6Tp6eXbWlrGI4ohkk4A7kkknqSSTkkmvjMNjMNlfPUwlT2lWUXFNJxjFSVpNX1cuW6WiSve7eh+l43Lcbnvs6OY0VRoQnGcouSlKbg1KMXy+6oKSUnq3LlUbJNs1K/Mv4M38K/8Fn7qTzEMf/CS6zFuB43fY7tMfXdx9a/Rz4geEpPHfg2/0eLWNY8PvfR+X/aGlSpFeWwyCTG7o6qxAIztJAJxg4I+c9O/4JH/AA103Wo9Uj1rx/8A2vDcfa11Aayq3Qn3
b/NEixgiQNzuHOec5r1eDs4y/AYfGRxs2nWpyppRje3MvieqWnbf0PA8SOHc4zbGZdUy2lGUcNWjWk5T5b8r+FaN3fe1vU+pK+cP+CrH7KrftafsaeItJ0+2a58S+HgNf0NUUtJLcwKxaFQOrSwtLEoPG6RSfu17B8HfhK/wg0W6sW8VeL/FSXEwlSXxDfLeTW/yhdiOEVtpxnDbuc88119fLYbFywGNjicLPmdOSadmr212f3NH6DiMKsyy+WGxtPl9pFqUbp2vputPNM/F3/gnJ8WfEP7W/wAD9F/ZVjjvm0G88Rf23repRttS28MxsLm6swykOjS3nloHGR/pZU8V+z1paRWFrHBBHHDDCgjjjjUKsagYAAHAAHGBXiX7KX7APgf9j74h/EDxJ4XW8e88fX/2t0uBH5elQ7nkFrb7VBWISSOcHJKiIHPlgn3Gva4wzvDZjjXPAx5afxW7zkk5yfm2kvlfqeHwVkOKyzAKnj5c1VLlv2hFtQivJJt+rt0Cvxp/4N3r2OX9tjxcFbd5/gy6lQgfeUX9jz/48K/Vj9pL4BRftK/DSTwrdeKvGPhKxupg93P4bvY7O6vYdjq1s8jxyfuX3ZYKFY7QN23cG+cfBH/BDX4S/DHXYNW8M+Jvit4c1q1Vkh1LTPEYtLqLcNrbXSIYyCQR0IJBBHFdnDuaZfhcrxmExU2p11FK0bpct3d6re/Taxw8T5PmOLzbBYzC004YdybvKzlzWVkrPa3Xe/zPef22v2ev+Gq/2VPG3gGOSOG717T/APQZJGKxpdwuk9sXIBOwTRR7sAnbmvzC/wCCPv7fsP7EvxF8QfCP4qef4b8P6lqblbi8TaPDmqriGWO57pDJ5aKXztjeMMQEeSRP1q+EPw5l+E/gCy0GbxJ4m8WNZGQjU9fuY7nUJgzs4EkiRoG27toJXO1QCT1rxv8AbE/4JcfCf9tXVf7Y8Sabf6N4o2pG2u6JMtteToowqShkeKUABQGd
C6qoCso4pcP55gaGGr5PmScsPUd1KO8ZLRSSfdJX9LWabRXEnD+YYjFYfOsrahiaSs4yfuyi9XFtdm3Z+d7ppH0FpWrWuvaZb31jc295Z3cazQXEEgkimRhlWVlyGUgggjgivyB/4OCvj7oPxk+Ivw/0jw5M2rWfhBNWtbrVrb95p817IbIzWcUo+WSW3WOIyhCQhuEU4YMo+nvhb/wQH+E/gS8xq3if4heJtJZy8ujTaklnp93ntKsCJI3/AAF1z3zXrn7U/wDwS++Gn7WNr8P9P1aPUvDvh/4dRXcGnaR4f8ixtXjuPI3RlfLO1V+zpgR7Ty3NdPDuOyXJs3p4uNWVWMebXk5bXi0tOZuTd7W0Sve72ObiXL88zvJauClShRlLl05+a9pJvXlSila9/ebtay3PRP2OdWt9c/ZI+F93ZzJcW8/hPS2R0OQf9Ei/UdCOoIxXkv8AwWglWH/gmj8TCzbQV01R9TqdoB+ZIrtf2d/+Cd3wl/ZX8Q2+reC/Dt7p+p2sMkEc8+t312qrJ9/EUszRKW7lUHU+pqp+2B/wT/8ADv7bOo6ePF3i74hWmh6fEqjQNJ1OG20u4lV2YXEsZhZ3lw20EvhQo2gEsT4uDxWApZ1TxanL2UZqd3FKWkua1lKS12vdeh7mOweYVsiqYJ04+1lBwSUm46x5btuMXpe9rP1Pn7/g3VcN+xX4sHdfHF3n2/4l+n1yn/Bx18R/EGg/CT4deF7Ga4h8O+JNQvLnVRGSqXEtqsBtopCPvLmaSTYeC0KNjKAj274b/wDBHXwN8Exc/wDCC/ET40eCXvWVrk6N4nWBbkrkKZIzC0b7cnBZTjJx1OfdP2kv2XvB/wC1n8JJvBnjiwk1XS5HWeGZX8q6s7hFZUuYnUDZIAzDIG0hmVlKMyn3KnEGXU+JY51C86bk5OLjZrSye7TabutenQ+fp8N5nU4VlkU7U6igoqSldSs7taJNJpWenXqc7/wT00fwzon7EPwu
i8I/Zf7Fk8O2k4eDb+9uHjDXDSbePOM5lMg6h94IBBA/ND/g4v8AGml+IP2n/C2i2V5b3WpaF4aP2+GJw5tGmmd40fB+VygD7Tg7ZEbowJ+yvgv/AMEatE+Bc8lroXxo+O+neHZJzcNo+m+JhptvOxxuMht40O5gAC8flucD5hivVfin/wAE4PhD8WfgP/wru78Lx6fosd0+owXNjKyajBeuu17w3D7nlncffeYyGTA37sCqynOssyrPP7TVSVaLcvs2aUr3bu9Wr7Lfe62ZnORZrm2Qf2W6cKMoqP2uZNwtZKy0Ttu9trPdeveAPEtn4z8CaLrGmzx3Wn6tYQXlrMh3LNFJGrowPcFSDn3r43/4OAb2G1/YLijkkSOS58T2McSk4MjBJ3IHqdqsfopr2L9kf/gnl4Z/Y7miOi+Mfih4it7OJ4bCy17xJJPp+mo/LiK0hWK3BY87mjZgc4Iyao/tOf8ABMvwJ+1/48fXPHeuePtUhj2/YtJTWzDpml4jVGMEKp8rPt3MxLMSTztCqPFyfEZdgM5p4n2kpUqcuZPl1dnorX+9t9/K/uZ3hcyzDI6mEVKMa1SPK1ze6rrV3t9yS7eduZ/4IiX8N3/wTR+H8ccscklrcatFMqsCYmOqXbgMOx2upwezA9CK+sK+Xvhl/wAEm/APwNSVfAfi74s+CRcTLPOmkeKpY4bl1wAZIWVonOBjLITjjNfRPj7wj/wn3gvU9FOpaxow1O3a3N9pVybW9td3G+KUAlHHZgMiuPP62ExOYVMVhptxqTlJ3jZx5nfu09+j6dDu4do4zC5bSwmKppSpQjFWldS5VbsmtluuvU/I/wCCl5Daf8HFNxJLLHHH/wAJXrce5mAG5tLvEVc+pYhQO5IFfsRXxyf+CG3wUm8SSa1cXXxCutemuzqD6pN4klN61yX8wzmUAMZS/wA+8ndu5znmvpD4EfBRfgR4UudJTxZ428XR3F010lz4o1Y6nd24KKvlJKyh
vLG3IDFjlmOea9ji7NcBmPsKuFnK9OnGm042vy31TTffZnicF5RmOWKvSxcI8tWpOompXtzW0asu26O2rxb4TXn9s/tufGCRvmOkaR4f05D/AHdyXk7L/wCRFP412Pxu+PWl/A6x0v7Vp+ta9q2vXJtNM0bRrdbjUNQdUMkhjjZlGyONSzsWCqMc5IB83/Yk8caV8avG/wAWviFockk2j+JtbsILWR4zG5FvpVorKVPIKs7A9sg4JrysvwdaGX4nFTg1CUEk+l/aw/NKVu9nbZ2682zLD1c2wWAp1E6kajlKPVR9hV1+TlBvtzRb+JX1v2M45dMvPi5psx+ax+IuqSRg9QlwkF2P1nb8q9qrxX4CzSaf+1t8ddNK7YXudE1WIdv31h5LH8WtWr2TUdSt9HsZrq7uIbW1t1LyzTOEjjUdSzHgD3Nc+epyxnN1lGnL5ypxl+p2cKyjDLeTpTnVhr2hVnH8o/cfJ/8AwUY8MD4z+LrXwrGZJW8L+CPEHi9rdf47hYo7ezYf7QkeUj3GR0r6Y+F3jNPiN8MvDviGPHl69pdtqK46YmiWQf8AoVeW/Ce0j+JH7Wvxg1m4hjutO0e00zwfayjlW2QyXd1Hn2e7jBHUEfSrX7AWpzv+y14d0e/kVtW8JyXfh29jz80MllcyW4UjsdiRnHYMK9fNtcrp4f8A58cn/laMqj+5pJ/JHzvD7tnlbGf9BXtf/LecaUdfNOUlbdXZ7NRRRXx5+jBXH/G745+Hf2ffBTa54iupI4ZJktbW2t4/OutQnc4SCCMcySN6DoASSACRH8ePjxoP7PHgKXXtelmZWkW3srK2TzLvVLlv9XbwR9XkY9B0AySQASPOfgR8B9e8eePofip8VoYm8VlGXQNAV/MtPCFs38K9numGPMlxnPAwAAPZwGX0/ZfXcbdUk7JLSU5L7MfT7UrNRXdtJ/N5tm9b26y3LUpV2rtvWNKL+3O1rt68kLpza3UVKSef29tNB/5J
l8bP/CNn/wAa4j9nrwfof7b/AMVvHHxA8daPPqlr4b1qTw34f8Oa7aYi0WGOGF5ZJLV8r50rSAt5gJUpjPyqF+rq8N+JH7OHjDwv8WNT8efCXxBo2jax4hjjXXNF1y3km0jV5I12x3GYyJIZVXglOH4Jwdxf1stzDBKNWlhl9XqTjaM3JtLVNxuo3jzJNcy/wu0ZNrwM6yfMnOhXxkvrdGnO8qShGLfutRlZytPkk0+R/wCKN5RinyPxn+GWi/slfHH4aeMfAOn2/hyPxh4mtPCGu6Pp8QgsNTguUk8uYwrhEkgZCwZFBO45JGQfqCvDPh1+zj4y8W/FbSvHXxa8QaJrGqeHFkGh6JoVtJDpGlyuu17jMpMk0pXhd/CckZONul8Wv2spPC3xFn8E+CvB+s/ETxlZQpcX9lYzR2lppSON0YubqU7IndfmVcEkDPHGc8wpVMfKlh6M1WqU4Pnne0bcztecraRTS5pWWqim0ot65PXo5VDEYzE03h6NWovZ0rXlflSbVOHM+abTlyRTdk5tKTkl7DRXkXwa/axj8efEKXwT4r8Mav8AD7xylubuHS9Skjmh1KEZ3Pa3MZ8ubb/EBg/eIBCOV9dr5/GYGvhKnsq8bO11qmmns002mn3TaPrstzTC4+j7fCy5km09Gmmt4yjJKUZLqpJPbQKKKK5D0AooooAKKKKAPiz/AIK1fGO18VfDv/hWug2Gta14kj1O3utQjtdMneKyhWIyKTJs2szF48BC2Bu3bSADQ/4J0ftZaN8EP2fI/CHjvT/E/hy60W8ne0mk0C9nhuoZnaXrFExVldpAQwAxtIJ5C/cVFfaQ4own9ixyWphm4KXO5Kdpc9rNr3GkraWs9Orep+Z1OBcw/wBZZ8TUcalUcHTUHSvD2d7pP94pNp2baau+iWg2CZbiFJE5WRQykjHB9q8s8Q/s4a7ruv319H8XfiVpsd5cSTpaWr6eILVWYkRxhrVm2KDtG5icAZJOTXqt
FfK4XGVMO3KlbXvGMv8A0pM++xuX0cXFRr307SlH/wBJa/E8e/4Zb8Qf9Fq+Kv8A3803/wCQ6P8AhlvxB/0Wr4q/9/NN/wDkOvYaK7P7axX93/wXT/8AkTz/APVnAdp/+DKn/wAmePf8Mt+IP+i1fFX/AL+ab/8AIdH/AAy34g/6LV8Vf+/mm/8AyHXsNFH9tYr+7/4Lp/8AyIf6s4DtP/wZU/8Akzx7/hlvxB/0Wr4q/wDfzTf/AJDo/wCGW/EH/Ravir/3803/AOQ69hoo/trFf3f/AAXT/wDkQ/1ZwHaf/gyp/wDJnj3/AAy34g/6LV8Vf+/mm/8AyHR/wy34g/6LV8Vf+/mm/wDyHXsNFH9tYr+7/wCC6f8A8iH+rOA7T/8ABlT/AOTPHv8AhlvxB/0Wr4q/9/NN/wDkOj/hlvxB/wBFq+Kv/fzTf/kOvYaKP7axX93/AMF0/wD5EP8AVnAdp/8Agyp/8mePf8Mt+IP+i1fFX/v5pv8A8h0f8Mt+IP8AotXxV/7+ab/8h17DRR/bWK/u/wDgun/8iH+rOA7T/wDBlT/5M8e/4Zb8Qf8ARavir/3803/5Do/4Zb8Qf9Fq+Kv/AH803/5Dr2Gij+2sV/d/8F0//kQ/1ZwHaf8A4Mqf/Jnj3/DLfiD/AKLV8Vf+/mm//IdH/DLfiD/otXxV/wC/mm//ACHXsNFH9tYr+7/4Lp//ACIf6s4DtP8A8GVP/kzx7/hlvxB/0Wr4q/8AfzTf/kOj/hlvxB/0Wr4q/wDfzTf/AJDr2Gij+2sV/d/8F0//AJEP9WcB2n/4Mqf/ACZ49/wy34g/6LV8Vf8Av5pv/wAh0f8ADLfiD/otXxV/7+ab/wDIdew0Uf21iv7v/gun/wDIh/qzgO0//BlT/wCTPHv+GW/EH/Ravir/AN/NN/8AkOj/AIZb8Qf9Fq+Kv/fzTf8A5Dr2Gij+2sV/d/8ABdP/AORD/VnAdp/+DKn/AMmePf8ADLfiD/ot
XxV/7+ab/wDIdH/DLfiD/otXxV/7+ab/APIdew0Uf21iv7v/AILp/wDyIf6s4DtP/wAGVP8A5M8z+Gf7Mln4E+IbeLNW8TeKfGviCOyOnWV1rk8LjTYGbfIsKQxRorSELucgsQiqCBkH0LSNEs9AtGt7GztbGBpHmMdvEsaF3Yu7YUAbmYliepJJPJq1RXHisZWxEuarK+y7JJbJJWS67Lq31Z6GBy3DYSHJh4W1bvq227Xbbu23ZatvRJbJHiuiXY0X/goT4gsVXauveArG+Y/33tr+5iP5LOn51W/a8062+JvxE+E/w5v42uNJ8Ua5Pqmq2/WK7tNPtmn8qUdGjad7bIPUgVb+IM0fhz9uz4bXXHmeIvDGuaST7Qy2VyB+jfkabbiTxh/wUBupFZZrHwP4JSIr18i71C8LfgTDZj6hq+noy5atLHLTloOV+0o81KDXmpqNvQ+HxEeehXyx68+KUWujjJwrzi12dNyv5M9E+Efwb8M/AjwXD4e8J6VFpGkwyvMIlkeVnkc5Z3kkZndj0yzEgBQOAAOA+I37Dfgnxf4m1LxVo1vN4T8fXl3HqNv4j0+RjPaXcYwJBCzGIq4ysse0CYMd2Www9lor56jm2NpVpYinVlzy+J3b5tbtSv8AEm91K6fU+vxPD+W4jDQwdWhB04fAuVJQsrJwtbkaTsnGzXRo8Nufgf8AG9beQw/HSxabafLV/BFqqFscZPmkgZ9jXlnwS8VfGn4leNNY8G+Ivi9a+DfHugkyXGjz+DbS4S8tixCXVrN5iedCwxkhQVbIIHBP2LXmf7SX7Nln8etIsby0vpvDvjTw7IbrQPEFqP8ASNNm9D/fhfo8Z4YH1r3Mtz+MnKji4U0pbTVGk3B92uTWL+0rcy3js4y+UznhCUFHE5fUrScL81N4mulUXVKTq3jNbxd+Vu6ktVKPlerfsNfEjxB8WLDxtqPxqtdQ8QaTbtbadJc+C4JINOViCzQwm48tJGxzIBuI
4zgADrv+FG/G7/ovFj/4Qlr/APH6wfCn/BQXSfh1azeH/jFb3vhDx1o7+ReR2ul3d7Y6kuPlu7aSGNx5Ug5AbBU5XnFc98Uf2wdH/ap8ZeDPhj8M/FeraZJ4vvpv7a1eCxubC6sbG3gaaSOBpo1xLKFKh13bNpyPmFex7HiCtUUcRSj7KCf7x0KcqahFOTcZKm042u1bdvTVnzvt+EcPSlUwtep7apJfuliq8asqkmoKMoOspKV7RbkvdSu9Ed//AMKO+N3/AEXix/8ACEtf/j9H/Cjfjd/0Xix/8IS1/wDj9YfiL/gmp4G8N6BLf/DpdW8E+OrKNpdO1601W5eZp8EgTiR2WSN2wHUrypIGAcV6R+yT8Z5v2hP2cPCXjC6hSC81iyzdIg2p58btFKVHZS8bEDJwCBk9a83GY6Sw/wBbwcoTgpKMubD0YNNpuOiU001GVne907paX9rLsphLGfUMyhVp1JRc48mMxNSMoxcVLVyptSi5RuuWzUk03qlyf/Cjfjd/0Xix/wDCEtf/AI/XM/sH3q/Dr4gfFTwL4q1K3m+Ij+KbjWpppUW3l1yznjjMN1EmT+7wrZjQsISQpIzX0xXB/Gv9mTwH+0Va28fjLwzY601n/qJ2Lw3MIznas0bLIFJ5KhsE9RXHh89jVpVMLjkowmlrTp04yTTunaKhzLdOLkujTutfSxXCcsPXpY/K5SlVpN+7WrVpwkpJpq85VOSWzUoxezTTT08t/ae16x8e/tV/BvwroEkd34w8Pa4+uahJb/O2kaWsDLOszD/VifdEoBxuIUd1z9HVxPwW/Zz8Efs76TcWfgzw7Y6HFdkNcSR7pJ7jGdvmSyFpHAycBmIG44xk121efmmMo1Y0qGGvyU4tJysm7ycm2k2oq70im7b3bbPYyLLcTQnXxeN5fa1pKTjBtxioxjBJNqLk7Ru5OMb3tZJIKKKK8k+gCiiigAooooAKKKKACiiigAooooAKKKKACiiigAoo
ooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA8X/aWhj0j48fAzXpOlv4mu9J+pu9MulA/76iX8qj/AGUxH4q+Kvxq8XeW8cmo+Lf7CQkcNFptrDbgj2843H459ah/b3urfw58OvBvia8k8iy8IeONF1a6l/55Qi5EMh/74mYfjWl+wVpF1p37J3hG81Da2oeIYptfuXA/1j308l3k/wDAZlH0Ar7CemQrELd2o/LnlVf3Pl+9H5zTXNxXLCNaK+I+fs4UF5q65rd+VnsFFFFfHn6MFFFFABXl/wC058Bb/wCMWlaHqvhvVo/D/jnwbeHUdA1GVC8IdkKS28wHJgmQ7X28jAOGAKt6hRXTg8ZVwtZV6L95eV1qrNNPRpptNPRptM4cxy+hjsPLC4hXjK2zaaaaaaa1TTSaa1TSaPnDxPP+0V8YfDs3hOTw94P+HqX0ZtdS8Twau2oMYW+WR7O3VVdJCCdvmPx/eVsMPb/hV8NNL+Dnw40Xwtosckel6FaJaQeYQZHCjl3IABdjlmIABLE4FdBRXXjc0lXpKhCEacE78sU7N7Xbk5N6bK9ld2Su78GW5DDCV3iqlWdaq1y802m1G9+VKMYxSbs27c0rLmbsrFFFFeWe6FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAHPfFb4V6H8bPh/qXhfxJZ/2hourKq3EAleItsdZEIZCGBV0VgQeoFa+iaNa+HNGtNPsYI7WxsIUt7eGMYWGNFCqoHoAAPwq1RWrr1HTVFyfKm2lfS7sm7bXaSTfkjBYWiqzxCgudpRcrK7im2lfeybbS2Tb7hRRRWRuFFFFABRRRQAUUUUAFFFFABRRRQB/9k=</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b2de4a05-6f06-43df-bc15-76306f0e55b4</rd:ReportID>
</Report>