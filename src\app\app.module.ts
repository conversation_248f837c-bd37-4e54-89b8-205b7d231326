import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, isDevMode, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { environment } from '../environments/environment';
import { HttpMockRequestInterceptor } from 'src/app/shared-services/mock-data-interceptor/mock-data-interceptor';
import { LoginModule } from './modules/login/login.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { NgxSpinnerModule } from "ngx-spinner";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatTableModule } from '@angular/material/table';
import { LayoutModule } from 'src/app/modules/shared-modules/layouts/layout.module';
import { DocumentsModule } from 'src/app/modules/documents/documents.module';
import { RetentionInCareModule } from 'src/app/modules/retention-in-care/retention-in-care.module';
import { AdminModule } from 'src/app/modules/admin/admin.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDialogModule } from '@angular/material/dialog';
import {MatSelectModule} from '@angular/material/select';
import { FormsModule, ReactiveFormsModule} from '@angular/forms'
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {ToastrModule} from 'ngx-toastr'
import './../globals';

/* Report */
import { BoldReportViewerModule } from '@boldreports/angular-reporting-components';

//Bold Reports 2.0 Viewer
import '@boldreports/javascript-reporting-controls/Scripts/v2.0/common/bold.reports.common.min';
import '@boldreports/javascript-reporting-controls/Scripts/v2.0/common/bold.reports.widgets.min';
import '@boldreports/javascript-reporting-controls/Scripts/v2.0/bold.report-viewer.min';

// Report viewer 1.0 Viewer
//import '@boldreports/javascript-reporting-controls/Scripts/bold.report-viewer.min';
//import '@boldreports/javascript-reporting-controls/Scripts/data-visualization/ej.bulletgraph.min';
//import '@boldreports/javascript-reporting-controls/Scripts/data-visualization/ej.chart.min';

import { AuthInterceptor } from './shared-services/auth-interceptor/auth-interceptor/auth-interceptor.interceptor';
import { BldRptViewerComponent } from './modules/dashboard/report/report-viewer/bld-rpt-viewer/bld-rpt-viewer.component';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { ServiceWorkerModule } from '@angular/service-worker';
import { AppEnv, ConfigurationService, EnvironmentType } from './shared-services/configuration.service';
import { IframeJwtModule } from './shared-services/iframe-communication/iframe-jwt.module';
import { QualityMeasuresComponent } from './modules/dashboard/quality-measures/quality-measures.component';
import { QualityMeasureFiltersComponent } from './modules/dashboard/quality-measures/quality-measure-filters/quality-measure-filters.component';
import { BonusMeasuresComponent } from './modules/dashboard/bonus-measures/bonus-measures.component';

export function initializeApp(configService: ConfigurationService) {
  // This function returns another function that returns a Promise
  return (): Promise<any> => {
    return configService.getConfig().then((config: AppEnv) => {
      configService.setConfig(config);
      console.log('Config:', config);

      if (configService.isValidEnvironmentType(config.environment)) {
        const envVariables = configService.returnEnvVariables(config.environment);
        Object.keys(envVariables).forEach(variable => {
          environment[variable] = envVariables[variable];
        });
        return Promise.resolve(environment); // Return a resolved Promise with the environment
      } else {
        console.log('Error: Environment not found');
        return Promise.reject('Error: Environment not found'); // Return a rejected Promise
      }
    });
  };
}

export const useMockData = environment.mockData;

@NgModule({
  declarations: [AppComponent, BldRptViewerComponent],
  imports: [
    AppRoutingModule,
    BoldReportViewerModule,
    BrowserAnimationsModule,
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatDialogModule,
    MatListModule,
    MatTableModule,
    MatSidenavModule,
    MatTabsModule,
    MatToolbarModule,
    MatExpansionModule,
    MatInputModule,
    MatSortModule,
    MatPaginatorModule,
    NgxSpinnerModule,
    LoginModule,
    LayoutModule,
    DashboardModule,
    DocumentsModule,
    AdminModule,
    CommonModule,
    RouterModule,
    MatSelectModule,
    RetentionInCareModule,
    IframeJwtModule,
    ToastrModule.forRoot(),
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: !isDevMode(),
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000'
    })
  ],
  providers: [
    ConfigurationService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [ConfigurationService],
      multi: true
    },
    ...(useMockData ? [{
      provide: HTTP_INTERCEPTORS,
      useClass: HttpMockRequestInterceptor,
      multi: true
    }] : [{
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }])
  ],
  bootstrap: [AppComponent],
  exports: [],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class AppModule { }
