// Main wrapper - full width to stretch across the Select Report container
.reportNavWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: MuseoSans-300;
  min-height: 0; // Allow shrinking below content size
}

// Scrollable container for menu items
.itemsContainer {
  position: relative;
  flex: 1; // Take available space
  min-height: 0; // Allow shrinking below content size
  overflow-y: auto;
  padding: 8px;
}

// Section container for each category
.section-container {
  margin-bottom: 11px;

  &:last-child {
    margin-bottom: 0;
  }
}

// Section header styling
.section-header {
  margin-bottom: 0;
}

.section-title {
  margin: 0;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #9E9E9E;
  background-color: white;
  border-radius: 0;
  text-transform: none;
  letter-spacing: normal;
}

// Section content container
.section-content {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background-color: white;
}

// Individual report item styling
.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 7px 8px 7px 27px; // Left padding for indentation
  cursor: pointer;
  text-decoration: none;
  border-radius: 0;
  transition: background-color 0.2s ease;
  background-color: white;
  color: #696969;

  &:hover {
    background-color: #f8f9fa;
  }

  .reportText {
    color: #333;
    font-size: 13px;
    line-height: 1.3;
    font-weight: normal;
    flex: 1;
  }

  .chevron-icon {
    color: #9E9E9E;
    font-size: 17px;
    margin-left: 8px;
    flex-shrink: 0;
    transform: scaleY(1.5);
  }
}

// Active report highlighting
.activeReport {
  background-color: #66A9D7;

  &:hover {
    background-color: #66A9D7;
  }

  .reportText {
    color: white;
    font-weight: 500;
  }

  .chevron-icon {
    color: white;
  }
}
