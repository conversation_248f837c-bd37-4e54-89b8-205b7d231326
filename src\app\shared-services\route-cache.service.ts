import { Injectable } from '@angular/core';
import { EpividianCommon } from '../modules/utility/EpividianCommon';

@Injectable({
  providedIn: 'root'
})
export class RouteCacheService {
  private readonly CACHED_ROUTE_KEY = 'cachedRoute';

  constructor(private epividianCommon: EpividianCommon) {}

  /**
   * Caches the intended route for later redirect after authentication
   * @param route The route URL to cache
   */
  cacheRoute(route: string): void {
    if (route && route !== '/' && !route.includes('/Auth/')) {
      this.epividianCommon.upsertToStorage(this.CACHED_ROUTE_KEY, route, true);
      console.log('Route cached:', route);
    }
  }

  /**
   * Retrieves the cached route
   * @returns The cached route URL or null if none exists
   */
  getCachedRoute(): string | null {
    const cachedRoute = this.epividianCommon.readFromStorage(this.CACHED_ROUTE_KEY, true);
    return cachedRoute;
  }

  /**
   * Clears the cached route from storage
   */
  clearCachedRoute(): void {
    localStorage.removeItem(this.CACHED_ROUTE_KEY);
    console.log('Cached route cleared');
  }

  /**
   * Checks if there is a cached route available
   * @returns True if a cached route exists, false otherwise
   */
  hasCachedRoute(): boolean {
    const cachedRoute = this.getCachedRoute();
    return !!(cachedRoute && cachedRoute.trim() !== '');
  }

  /**
   * Validates that a cached route is safe to redirect to
   * @param route The route to validate
   * @returns True if the route is valid and safe for redirect
   */
  private isValidRoute(route: string): boolean {
    if (!route || route.trim() === '') {
      return false;
    }

    // Prevent redirect to auth routes
    if (route.includes('/Auth/')) {
      return false;
    }

    // Prevent redirect to root
    if (route === '/') {
      return false;
    }

    // Basic URL validation - should start with /
    if (!route.startsWith('/')) {
      return false;
    }

    return true;
  }

  /**
   * Gets the cached route if it exists and is valid, otherwise returns null
   * @returns Valid cached route or null
   */
  getValidCachedRoute(): string | null {
    const cachedRoute = this.getCachedRoute();
    
    if (cachedRoute && this.isValidRoute(cachedRoute)) {
      return cachedRoute;
    }
    
    // Clear invalid cached route
    if (cachedRoute) {
      this.clearCachedRoute();
    }
    
    return null;
  }
}
