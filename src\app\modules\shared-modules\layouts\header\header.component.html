<div class="header">
  <div class="dataLoadingWrapper">
    <div *ngIf="lblDataLoadMsg.length > 0" class="dataLoadingContainer">
      <img class="dataLoadingImage" src='{{"../../../../../assets/images/" + imgDataLoad}}'>
      <span class="lblDataLoadMsg">{{lblDataLoadMsg}}</span>
    </div>
  </div>
  
  <button mat-button [matMenuTriggerFor]="menu1" (contextmenu)="onRightClick($event)" (keydown)="onKeyDown($event)">
      <mat-icon class="helpIcon">help_outline</mat-icon>
  </button>
    
  <mat-menu class="menuForhelp"  #menu1="matMenu">
      <div id="menuforHelp" class="menuForhelp">   
      <button mat-menu-item>
      <a class="email-support" href="mailto: <EMAIL>"><span>Email Support</span></a>    
      </button>
      <hr class="menuDivider"/>
      <button mat-menu-item (click)="openHelpWindow()">      
      <span class="helpMenu">CHORUS Portal Help</span>
      </button>    
      </div>
  </mat-menu>
</div>
