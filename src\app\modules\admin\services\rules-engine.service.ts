import { Injectable } from '@angular/core';
import { Observable, of, forkJoin } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { Rule, Workflow, WorkflowStatus, RuleExecutionResult, RuleType, WorkflowProcess, WorkflowRuleDetail } from '../models/rule.model';
import { RuleDefinitionField, RuleDefinitionModel, RuleDefinitionSchema, SchemaDefinition } from '../models/rule-definition.model';

@Injectable({
  providedIn: 'root'
})
export class RulesEngineService {
  constructor(private apiHandler: ApiHandler) {}

  /**
   * Get rules by type, workflow ID, or both
   * @param options Options for filtering rules (ruleType, workflowId, id)
   * @returns An Observable of Rule array
   */
  getRules(options: { ruleType?: string, workflowId?: number, id?: number } = {}): Observable<Rule[]> {
    // Make sure at least one parameter is provided
    if (!options.ruleType && options.workflowId === undefined && options.id === undefined) {
      console.warn('At least one parameter (ruleType, workflowId, or id) must be provided to getRules');
      return of([]);
    }

    let url = ApiRoutes.GetRules.toString();

    // Build query string parameters
    const params: string[] = [];
    if (options.ruleType) {
      params.push(`ruleType=${options.ruleType}`);
    }
    if (options.workflowId !== undefined) {
      params.push(`workflowId=${options.workflowId}`);
    }
    if (options.id !== undefined) {
      params.push(`id=${options.id}`);
    }

    // Add query string to URL if there are parameters
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    return this.apiHandler.Get<Rule[]>(ApiTypes.V2, url);
  }

  /**
   * Get all rule types
   * @returns An Observable of RuleType array
   */
  getRuleTypes(): Observable<RuleType[]> {
    // This would typically call an API endpoint to get rule types
    // For now, return mock data with the correct rule type IDs
    return of([
      { id: 'FormRule', name: 'Form Rule', description: 'Rules for form processing and data collection' },
      { id: 'ApiRequesterRule', name: 'API Requester', description: 'Rules for making API requests to external systems' },
      { id: 'GetDataRule', name: 'Get Data', description: 'Rules for retrieving data from various sources' },
      { id: 'MapTransformRule', name: 'Map Transform', description: 'Rules for transforming data between different formats' },
      { id: 'PdfFillerRule', name: 'PDF Filler', description: 'Rules for filling and processing PDF documents' },
      { id: 'WriteFileToWorkflowRule', name: 'Write File', description: 'Rules for writing files to the workflow process' }
    ]);
  }

  /**
   * Get a rule by ID
   * @param siteId The site ID
   * @param ruleId The rule ID
   * @returns An Observable of Rule
   */
  getRuleById(siteId: string, ruleId: number): Observable<Rule> {
    const url = ApiRoutes.GetRuleBySite
      .replace('{{siteId}}', siteId)
      .replace('{{ruleId}}', ruleId.toString());
    return this.apiHandler.Get<Rule>(ApiTypes.V2, url);
  }

  /**
   * Create a new rule
   * @param rule The rule to create
   * @returns An Observable of the created Rule
   */
  createRule(rule: Rule): Observable<Rule> {
    const url = ApiRoutes.GetRules;
    return this.apiHandler.Post<Rule>(ApiTypes.V2, url, JSON.stringify(rule));
  }

  /**
   * Update an existing rule
   * @param ruleId The ID of the rule to update
   * @param rule The updated rule data
   * @returns An Observable of the updated Rule
   */
  updateRule(ruleId: number, rule: Rule): Observable<Rule> {
    // Make sure the rule ID is included in both the URL and the rule object
    const url = ApiRoutes.GetRuleById.replace('{{ruleId}}', ruleId.toString());

    // Ensure the rule object has the correct ID
    const ruleToUpdate = { ...rule, ruleID: ruleId };

    console.log('Updating rule with URL:', url);
    console.log('Rule data:', ruleToUpdate);

    return this.apiHandler.Put<Rule>(ApiTypes.V2, url, JSON.stringify(ruleToUpdate));
  }

  /**
   * Delete a rule
   * @param ruleId The ID of the rule to delete
   * @returns An Observable of the operation result
   */
  deleteRule(ruleId: number): Observable<boolean> {
    const url = ApiRoutes.GetRuleById.replace('{{ruleId}}', ruleId.toString());
    // Since Delete doesn't exist, use Post with an empty body
    return this.apiHandler.Post<boolean>(ApiTypes.V2, url, '');
  }

  /**
   * Get all workflows
   * @returns An Observable of Workflow array
   */
  getWorkflows(): Observable<Workflow[]> {
    const url = ApiRoutes.GetWorkflows;
    return this.apiHandler.Get<Workflow[]>(ApiTypes.V2, url);
  }

  /**
   * Get a workflow by ID
   * @param workflowId The workflow ID
   * @returns An Observable of Workflow
   */
  getWorkflowById(workflowId: number): Observable<Workflow> {
    const url = ApiRoutes.GetWorkflowById.replace('{{workflowId}}', workflowId.toString());
    return this.apiHandler.Get<Workflow>(ApiTypes.V2, url);
  }

  /**
   * Create a new workflow
   * @param workflow The workflow to create
   * @returns An Observable of the created Workflow
   */
  createWorkflow(workflow: Workflow): Observable<Workflow> {
    const url = ApiRoutes.GetWorkflows;
    return this.apiHandler.Post<Workflow>(ApiTypes.V2, url, JSON.stringify(workflow));
  }

  /**
   * Update an existing workflow
   * @param workflowId The ID of the workflow to update
   * @param workflow The updated workflow data
   * @returns An Observable of the updated Workflow
   */
  updateWorkflow(workflowId: number, workflow: Workflow): Observable<Workflow> {
    // Make sure the workflow ID is included in both the URL and the workflow object
    const url = ApiRoutes.GetWorkflowById.replace('{{workflowId}}', workflowId.toString());

    // Ensure the workflow object has the correct ID
    const workflowToUpdate = { ...workflow, workflowID: workflowId };

    console.log('Updating workflow with URL:', url);
    console.log('Workflow data:', workflowToUpdate);

    return this.apiHandler.Put<Workflow>(ApiTypes.V2, url, JSON.stringify(workflowToUpdate));
  }

  /**
   * Delete a workflow
   * @param workflowId The ID of the workflow to delete
   * @returns An Observable of the operation result
   */
  deleteWorkflow(workflowId: number): Observable<boolean> {
    const url = ApiRoutes.GetWorkflowById.replace('{{workflowId}}', workflowId.toString());
    // Since Delete doesn't exist, use Post with an empty body
    return this.apiHandler.Post<boolean>(ApiTypes.V2, url, '');
  }

  /**
   * Get all rules assigned to a workflow
   * @param workflowId The ID of the workflow
   * @returns An Observable of Rule array
   */
  getWorkflowRules(workflowId: number): Observable<Rule[]> {
    // We can use the getRules method with the workflowId parameter
    return this.getRules({ workflowId });
  }

  /**
   * Assign a rule to a workflow
   * @param workflowId The ID of the workflow
   * @param ruleId The ID of the rule to assign
   * @param orderNo Optional order number for the rule in the workflow
   * @param dependsOnRuleId Optional ID of the rule this rule depends on
   * @returns An Observable of the operation result
   */
  assignRuleToWorkflow(workflowId: number, ruleId: number, orderNo?: number, dependsOnRuleId?: number): Observable<boolean> {
    let url = ApiRoutes.AssignRuleToWorkflow
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());

    // Add query parameters if provided
    const params: string[] = [];
    if (orderNo !== undefined) {
      params.push(`orderNo=${orderNo}`);
    }
    if (dependsOnRuleId !== undefined) {
      params.push(`dependsOnRuleId=${dependsOnRuleId}`);
    }

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    return this.apiHandler.Post<boolean>(ApiTypes.V2, url, '');
  }

  /**
   * Remove a rule from a workflow
   * @param workflowId The ID of the workflow
   * @param ruleId The ID of the rule to remove
   * @returns An Observable of the operation result
   */
  removeRuleFromWorkflow(workflowId: number, ruleId: number): Observable<boolean> {
    const url = ApiRoutes.AssignRuleToWorkflow
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());
    // Since Delete doesn't exist, use Post with an empty body
    return this.apiHandler.Post<boolean>(ApiTypes.V2, url, '');
  }

  /**
   * Update the order of rules in a workflow
   * @param workflowId The ID of the workflow
   * @param ruleOrders A dictionary mapping rule IDs to their new order numbers
   * @returns An Observable of the operation result
   */
  updateRuleOrder(workflowId: number, ruleOrders: Record<number, number>): Observable<boolean> {
    const url = ApiRoutes.UpdateRuleOrder.replace('{{workflowId}}', workflowId.toString());
    return this.apiHandler.Put<boolean>(ApiTypes.V2, url, JSON.stringify(ruleOrders));
  }

  /**
   * Execute a workflow
   * @param siteId The site ID
   * @param workflowId The ID of the workflow to execute
   * @param guid Optional GUID for workflow process
   * @param rule Optional rule model
   * @returns An Observable of the operation result
   */
  executeWorkflow(siteId: string, workflowId: number, guid?: string, rule?: Rule): Observable<boolean> {
    let url = ApiRoutes.ExecuteWorkFlow
      .replace('{{siteId}}', siteId)
      .replace('{{workflowId}}', workflowId.toString());

    if (guid) {
      // Replace the guid placeholder if it exists in the URL
      url = url.replace('{{workflowGuid}}', guid);
    } else {
      // Remove the guid parameter if it's in the URL but not provided
      url = url.replace('?guid={{workflowGuid}}', '');
    }

    return this.apiHandler.Post<boolean>(ApiTypes.V2, url, rule ? JSON.stringify(rule) : '');
  }

  /**
   * Get the status of a workflow
   * @param siteId The site ID
   * @param workflowId The ID of the workflow
   * @param guid The GUID of the workflow process
   * @returns An Observable of WorkflowStatus
   */
  getWorkflowStatus(siteId: string, workflowId: number, guid: string): Observable<WorkflowStatus> {
    // Debug information
    console.log('getWorkflowStatus called with:');
    console.log('siteId:', siteId);
    console.log('workflowId:', workflowId);
    console.log('guid:', guid);

    // Construct the URL
    const url = ApiRoutes.WorkflowStatus
      .replace('{{siteId}}', siteId)
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{guid}}', guid);

    console.log('Making API call to:', url);

    // Make the API call with additional debugging
    return this.apiHandler.Get<WorkflowStatus>(ApiTypes.V2, url).pipe(
      map(response => {
        console.log('Workflow status API response:', response);

        // Check if the response has the expected structure
        if (response && response.Status) {
          console.log('Found Status object in response');

          // Check for executor transactions
          if (response.Status.executorTransactions) {
            console.log('Found executor transactions:', response.Status.executorTransactions.length);
          } else {
            console.warn('No executor transactions found in response');
          }

          // Check for rule details
          if (response.Status.RuleDetails) {
            console.log('Found rule details:', response.Status.RuleDetails.length);
          } else {
            console.warn('No rule details found in response');
          }
        } else {
          console.warn('Response does not have expected structure:', response);
        }

        return response;
      }),
      catchError(error => {
        console.error('Error in getWorkflowStatus API call:', error);
        throw error;
      })
    );
  }

  /**
   * Get the result of a rule execution in a workflow
   * @param siteId The site ID
   * @param workflowId The ID of the workflow
   * @param ruleId The ID of the rule
   * @param workflowProcessId Optional GUID for workflow process
   * @returns An Observable of RuleExecutionResult
   */
  getWorkflowRuleResult(siteId: string, workflowId: number, ruleId: number, workflowProcessId?: string): Observable<RuleExecutionResult> {
    let url = ApiRoutes.WorkflowRuleResult
      .replace('{{siteId}}', siteId)
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());

    if (workflowProcessId) {
      url += `?workFlowProcessId=${workflowProcessId}`;
    }

    return this.apiHandler.Get<RuleExecutionResult>(ApiTypes.V2, url);
  }

  /**
   * Get the file result of a rule execution in a workflow
   * @param siteId The site ID
   * @param workflowId The ID of the workflow
   * @param ruleId The ID of the rule
   * @param workflowProcessId Optional GUID for workflow process
   * @returns An Observable of the file result
   */
  getWorkflowFileResult(siteId: string, workflowId: number, ruleId: number, workflowProcessId?: string): Observable<Blob> {
    let url = ApiRoutes.WorkflowFileResult
      .replace('{{siteId}}', siteId)
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());

    if (workflowProcessId) {
      url += `?workFlowProcessId=${workflowProcessId}`;
    }

    return this.apiHandler.Get<Blob>(ApiTypes.V2, url, false, false, 'blob');
  }

  /**
   * Get rule definition by type
   * @param ruleType The type of the rule
   * @returns An Observable of the rule definition
   */
  getRuleDefinitionByType(ruleType: string): Observable<RuleDefinitionSchema> {
    const url = ApiRoutes.RuleDefinitionByType.replace('{{ruleType}}', ruleType);
    return this.apiHandler.Get<RuleDefinitionSchema>(ApiTypes.V2, url);
  }

  /**
   * Parse rule definition schema into a usable format
   * @param schema The rule definition schema
   * @returns A structured rule definition model array
   */
  parseRuleDefinition(schema: RuleDefinitionSchema): RuleDefinitionModel[] {
    const models: RuleDefinitionModel[] = [];

    // Process each top-level object in the schema
    for (const modelName in schema) {
      if (schema.hasOwnProperty(modelName)) {
        const modelSchema = schema[modelName];
        const fields: RuleDefinitionField[] = [];

        // Process each field in the model
        for (const fieldName in modelSchema) {
          if (modelSchema.hasOwnProperty(fieldName)) {
            const fieldType = modelSchema[fieldName];
            const field = this.parseField(fieldName, fieldType, `${modelName}.${fieldName}`);
            fields.push(field);
          }
        }

        models.push({
          name: modelName,
          fields: fields
        });
      }
    }

    return models;
  }

  /**
   * Parse a field from the schema
   * @param name The field name
   * @param type The field type
   * @param path The full path to the field
   * @returns A structured rule definition field
   */
  private parseField(name: string, type: string | SchemaDefinition, path: string): RuleDefinitionField {
    // Check if the type is a string (primitive type) or an object (complex type)
    if (typeof type === 'string') {
      // Handle array types (e.g., "List<MapModel>", "Int32[]")
      const isArray = type.includes('List<') || type.includes('[]');
      let baseType = type;

      if (isArray) {
        if (type.includes('List<')) {
          baseType = type.replace('List<', '').replace('>', '');
        } else if (type.includes('[]')) {
          baseType = type.replace('[]', '');
        }
      }

      // Special handling for boolean fields
      if (baseType.toLowerCase() === 'boolean' && (name === 'isEditable' || name === 'isRequired')) {
        console.log(`Found boolean field: ${name} with type ${baseType}`);
      }

      return {
        name: name,
        path: path,
        type: baseType,
        isArray: isArray,
        isObject: false
      };
    } else {
      // Handle object types
      const children: RuleDefinitionField[] = [];

      for (const childName in type) {
        if (type.hasOwnProperty(childName)) {
          const childType = type[childName];
          const childField = this.parseField(childName, childType, `${path}.${childName}`);
          children.push(childField);
        }
      }

      return {
        name: name,
        path: path,
        type: 'object',
        isArray: false,
        isObject: true,
        children: children
      };
    }
  }

  /**
   * Get rule data by name for a specific site
   * @param siteId The site ID
   * @param ruleName The name of the rule
   * @param options Optional parameters (guid, page, size, workflowid)
   * @returns An Observable of the rule data
   */
  getRuleDataByName(siteId: string, ruleName: string, options?: { guid?: string, page?: number, size?: number, workflowid?: number }): Observable<object> {
    let url = ApiRoutes.RuleDataByName
      .replace('{{siteId}}', siteId)
      .replace('{{ruleName}}', ruleName);

    if (options) {
      const params: string[] = [];
      if (options.guid) params.push(`guid=${options.guid}`);
      if (options.page !== undefined) params.push(`page=${options.page}`);
      if (options.size !== undefined) params.push(`size=${options.size}`);
      if (options.workflowid !== undefined) params.push(`workflowid=${options.workflowid}`);

      if (params.length > 0) {
        url += `?${params.join('&')}`;
      }
    }

    return this.apiHandler.Get<object>(ApiTypes.V2, url);
  }

  /**
   * Get workflow process history
   * @param siteId The site ID
   * @param options Optional filter parameters
   * @returns An Observable of WorkflowProcess array
   */
  getWorkflowProcessHistory(siteId: string, options?: {
    workflowId?: number,
    workflowProcessId?: string,
    startDate?: Date,
    endDate?: Date,
    includeDetails?: boolean
  }): Observable<WorkflowProcess[]> {
    let url = ApiRoutes.WorkflowProcessIds.replace('{{siteId}}', siteId);

    if (options) {
      const params: string[] = [];
      if (options.workflowId !== undefined) {
        params.push(`workFlowId=${options.workflowId}`);
      }
      if (options.workflowProcessId) {
        params.push(`workFlowProcessId=${options.workflowProcessId}`);
      }
      if (options.startDate) {
        params.push(`startDate=${options.startDate.toISOString()}`);
      }
      if (options.endDate) {
        params.push(`endDate=${options.endDate.toISOString()}`);
      }
      if (options.includeDetails !== undefined) {
        params.push(`includeDetails=${options.includeDetails}`);
      }

      if (params.length > 0) {
        url += `?${params.join('&')}`;
      }
    }

    console.log('Fetching workflow history from URL:', url);

    return this.apiHandler.Get<WorkflowProcess[]>(ApiTypes.V2, url).pipe(
      catchError(error => {
        console.error('Error fetching workflow history:', error);
        throw error;
      })
    );
  }

  /**
   * Get detailed rule execution results for a specific workflow process
   * @param siteId The site ID
   * @param workflowId The workflow ID
   * @param ruleId The rule ID
   * @param workflowProcessId The workflow process ID
   * @returns An Observable of RuleExecutionResult
   */
  getDetailedRuleResult(siteId: string, workflowId: number, ruleId: number, workflowProcessId: string): Observable<RuleExecutionResult> {
    const baseUrl = ApiRoutes.WorkflowRuleResult
      .replace('{{siteId}}', siteId)
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());

    const url = `${baseUrl}?workFlowProcessId=${workflowProcessId}`;

    return this.apiHandler.Get<RuleExecutionResult>(ApiTypes.V2, url);
  }

  /**
   * Get all rule execution results for a workflow execution
   * @param siteId The site ID
   * @param workflowId The workflow ID
   * @param workflowProcessId The workflow process ID
   * @returns An Observable of RuleExecutionResult array
   */
  getWorkflowExecutionResults(siteId: string, workflowId: number, workflowProcessId: string): Observable<RuleExecutionResult[]> {
    // First get the workflow rules
    return this.getWorkflowRules(workflowId).pipe(
      switchMap(rules => {
        if (rules.length === 0) {
          return of([]);
        }

        // Create an array of observables for each rule result
        const resultObservables = rules.map(rule =>
          this.getDetailedRuleResult(siteId, workflowId, rule.ruleID, workflowProcessId)
            .pipe(
              catchError(error => {
                console.error(`Error loading rule result for rule ${rule.ruleID}:`, error);
                return of(null);
              })
            )
        );

        // Use forkJoin to wait for all observables to complete
        return forkJoin(resultObservables).pipe(
          // Filter out null results and sort by execution time
          map(results =>
            results
              .filter((result): result is RuleExecutionResult => result !== null)
              .sort((a, b) => new Date(a.executionTime).getTime() - new Date(b.executionTime).getTime())
          )
        );
      })
    );
  }

  /**
   * Get detailed workflow rule information including dependencies
   * @param workflowId The ID of the workflow
   * @returns An Observable of WorkflowRuleDetail array
   */
  getWorkflowRuleDetails(workflowId: number): Observable<WorkflowRuleDetail[]> {
    const url = ApiRoutes.GetWorkflowRules.replace('{{workflowId}}', workflowId.toString());
    return this.apiHandler.Get<WorkflowRuleDetail[]>(ApiTypes.V2, url);
  }

  /**
   * Update rule dependencies for a workflow rule
   * @param workflowId The ID of the workflow
   * @param ruleId The ID of the rule
   * @param dependencyRuleIds Array of rule IDs that this rule depends on
   * @returns An Observable of the operation result
   */
  updateRuleDependencies(workflowId: number, ruleId: number, dependencyRuleIds: number[]): Observable<boolean> {
    const url = ApiRoutes.AssignRuleToWorkflow
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{ruleId}}', ruleId.toString());

    return this.apiHandler.Put<boolean>(ApiTypes.V2, url, JSON.stringify({
      dependencyRules: dependencyRuleIds
    }));
  }
}
