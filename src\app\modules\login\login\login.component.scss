@import 'bootstrap/scss/_functions';
@import 'bootstrap/scss/_variables';
@import 'bootstrap/scss/mixins/_breakpoints';

.cover-container {
    background-color: #0071bc;
    min-height: 100vh;
    overflow: hidden;
    position: relative;

    a:visited {
        color: whitesmoke;
      }

    .forgot-password {
        display: flex;
        width: 100%;
        color: #fff;
        font-family: "MuseoSans-300","sans-serif";
        text-underline-offset: 1px;
        justify-content: flex-end;
    }
    @include media-breakpoint-up(md) {
        padding-left: 8rem;
    }

    .header-row-1  {
        background-image: url("../../../../assets/images/Bubbles.svg");
        background-position: right -90px;
        background-repeat: no-repeat;
        background-size: contain;
        @include media-breakpoint-down(md) {
            background-position: right top;
            transform: scale(160%) translate(35%, 6%);
        }
        .logo-container {
            background-image: url("../../../../assets/images/Logo.svg");
            background-position: top center;
            background-repeat: no-repeat;
            background-size: contain;
            padding-top: 32.5%;

            @include media-breakpoint-down(md) {
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                padding-top: 90%;
                transform: scale(70%) translate(-50%, -50%);
            }
        }
    }

    .header-row-2 {
        @include media-breakpoint-down(md) {
            margin-top: -35%;
        }
    }

    .p-bottom-override {
        margin-bottom: 0rem !important;
    }

    .footer-row {
        bottom: 0;
        margin-left: -0.75rem;
        position: absolute;
        width: 100%;
        .certified-version {
            color: #fff;
            font-family: "MuseoSans-300","sans-serif";
        }
        .contact-support {
            color: #fff;
            font-family: "MuseoSans-300","sans-serif";
            text-underline-offset: 1px;
        }
        .version {
            color: #fff;
            font-family: "MuseoSans-300","sans-serif";
            text-underline-offset: 1px;
            font-size: 12px;
        }
    }

    ::ng-deep .form-control {
        border-radius: 0.7rem;
        font-family: "Museo300-Regular", "sans-serif";
        font-size: 18px;
        color: #000;
        &:focus {
            box-shadow: none;
        }
    }

    ::ng-deep .btn-light {
        background-color: #fff;
        background-image: url("../../../../assets/images/right_arrow.png");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 10px 15px;
        border-left: none;
        border-radius: 0.7rem;
    }

    ::ng-deep .spinner-container {
        position: relative;
        .spinner-wrapper {
            position: absolute;
            z-index: 99;
            .img-spinner {
                height: 6rem;
                width: 6rem;
            }
        }
    }

    .login-error-message {
        color: #fff
    }
}

.input-group{
    background-color: white;
    border-radius: 0.7rem;
    border: 1px solid #ced4da;
}

input::-ms-reveal {
    display:none;
  }

#password{
border: none;
}

.passShowHide{
    display: flex;
    align-items: center;
    color: darkgray;
    background-color: white;
    height: auto !important;
    width: auto !important;
    font-size: 18px !important;
    padding-top: 1px;
    margin-right: 8px;
}

.input-group .btn:hover{
    margin: -1px;
    border: 1px solid black;
    border-radius: 0.7rem;
    animation: 0;
}

.capsLockAlert{
    display: flex;
    justify-content: flex-start;
    width: 100%;
    color: white;
}
.capsLockIcon{
    height: 100%;
    line-height: 39px;
    font-weight: 600;
}