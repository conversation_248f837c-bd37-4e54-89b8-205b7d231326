{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "f69c776e-9aef-4c7e-8a31-e594137359a4", "cache": {"enabled": false}}, "version": 1, "newProjectRoot": "projects", "projects": {"Chorus_Portal": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/chorus-portal", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/assets/styles/styles.scss", "node_modules/ngx-spinner/animations/ball-spin-rotate.css", "node_modules/@boldreports/javascript-reporting-controls/Content/v2.0/tailwind-light/bold.report-viewer.min.css", "node_modules/ngx-toastr/toastr.css", "src/assets/styles/tabulator_epividian/tabulator_epividian.scss", "src/assets/styles/ng-select.scss"], "scripts": ["src/app/modules/scripts/index.js", "node_modules/tabulator-tables/dist/js/tabulator.min.js"], "allowedCommonJsDependencies": ["canvg", "core-js", "jspdf-autotable", "lodash", "file-saver", "moment", "raf", "rgbcolor"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "5kb", "maximumError": "15kb"}], "outputHashing": "all", "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": false, "sourceMap": false, "namedChunks": true}, "test": {"outputHashing": "all", "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": false, "sourceMap": false, "namedChunks": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "demo": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "Chorus_Portal:build:production"}, "development": {"buildTarget": "Chorus_Portal:build:development"}, "test": {"buildTarget": "Chorus_Portal:build:test"}, "demo": {"buildTarget": "Chorus_Portal:build:demo"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "Chorus_Portal:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/assets/styles/styles.scss", "node_modules/ngx-spinner/animations/ball-spin-rotate.css", "../node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["../node_modules/bootstrap/dist/js/bootstrap.js"]}}}}}}