import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LoginRoutingModule } from './login-routing.module';
import { RouterModule } from '@angular/router';
import { TermsConditionComponent } from './terms-condition/terms-condition.component';
import { LoginComponent } from 'src/app/modules/login/login/login.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { BoldReportViewerModule } from '@boldreports/angular-reporting-components';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { TfaNewDevicLinkComponent } from './tfa-new-device-link/tfa-new-device-link..component';
import { TfaVerificationComponent } from './tfa-verification/tfa-verification.component';
import { MatIconModule } from '@angular/material/icon';

@NgModule({
  declarations: [LoginComponent,ForgotPasswordComponent,
    TermsConditionComponent,ChangePasswordComponent,TfaNewDevicLinkComponent, TfaVerificationComponent
  ],
  imports: [
    CommonModule,
    LoginRoutingModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    BoldReportViewerModule,
    MatIconModule
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
  exports: []
})
export class LoginModule {  }
