export interface QualityMeasure {
  measureId: number;
  qualityName: string;
  groupName: string;
  locationName: string;
  providerName: string;
  providerId: number;
  trend: number;
  measuresSatisfied: number;
  measuresUnsatisfied: number;
  newMeasures: number;
  ongoingMeasures: number;
  recentlySatisfied: number;
  operaScore: string;
  operaAverage: number;
  totalMeasures?: number;
  satisfiedPercentage?: number;
  unsatisfiedPercentage?: number;
}