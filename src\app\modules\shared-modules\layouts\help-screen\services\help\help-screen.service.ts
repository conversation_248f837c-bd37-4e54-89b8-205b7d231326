import { Injectable } from '@angular/core';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models/report-list.model'
import { Api<PERSON>and<PERSON> } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { IMenuSections } from 'src/app/modules/shared-modules/layouts/models/menu-item.model';
import { BehaviorSubject, map, Observable, of, ReplaySubject, Subject } from 'rxjs';
import { HttpParams, HttpResponseBase } from '@angular/common/http';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

@Injectable({
  providedIn: 'root'
})

export class helpScreenService {
    //Loads our ApiService for making backend calls and the userContext which handles users current sessions/context.
    constructor(private apiHandler: <PERSON>pi<PERSON>and<PERSON>, private userContext: UserContext) {}

    

    public getHelpHeaders(siteId: string){

        var url = ApiRoutes.GetHelpIds.toString().replace("{{siteId}}",siteId);

        return this.userContext.apihandler.Get(ApiTypes.V2, url)   
    }

    public getHelpContent(helpId: number, siteId: string) {

        var url = ApiRoutes.GetHelpContent.toString().replace("{{siteId}}", siteId);
        const httpParams = new HttpParams().set('helpId', helpId); 

        return this.userContext.apihandler.Get(ApiTypes.V2, url, true, true, "text", httpParams)
    }

}