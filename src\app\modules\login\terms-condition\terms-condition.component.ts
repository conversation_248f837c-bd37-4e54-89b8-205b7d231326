import { ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { Subscription, timeout } from 'rxjs';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { RouteCacheService } from 'src/app/shared-services/route-cache.service';



@Component({
  selector: 'app-terms-condition',
  templateUrl: './terms-condition.component.html',
  styleUrls: ['./terms-condition.component.css']
})
export class TermsConditionComponent implements On<PERSON><PERSON>roy, OnInit {

  acceptanceText: string = 'I have read and agree to the Accepatable Use Policy';
  linkText: string = 'Download a copy of the Acceptable Use Policy';
  disabledContinueBtn: boolean = true;
  isSubmitTnC: boolean = true;
  content: string = '';
  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    private router: Router,
    private ref: ChangeDetectorRef,
    private userContext: UserContext,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private auditService: AuditService,
    private routeCacheService: RouteCacheService
  ) {
    this.auditService.setPageAudit(Page.TermsAgreement);
  }

  ngOnInit(): void {
    this.pageSubscriptions.add(
    this.userContext.getUserSession().subscribe(s => {
      this.getTermsConditionContent();
    })
    );
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  getTermsConditionContent() {
    this.userContext.apihandler.Get(ApiTypes.V2, ApiRoutes.Eulacontent).subscribe(res => {
      this.content = res.text;
      this.ref.detectChanges();
    });
  }

  onCancelClick(): void {
    // On Cancel Event redirecting to dashborad page
    this.userContext.ClearSession();
    this.router.navigate(['/']);
  }

  onContinueClick(result): void {
    this.spinnerService.show();
    // On Continue Event calling Save API
    if (result) {
      this.userContext.apihandler.Post(
        ApiTypes.V2,
        ApiRoutes.SaveEulaStatus+result,"", '', true
      ).subscribe(res => {
        if (res===true) {
            this.spinnerService.hide();
            // Check for cached route before defaulting to Dashboard
            const cachedRoute = this.routeCacheService.getValidCachedRoute();
            if (cachedRoute) {
              //console.log('Terms accepted: Redirecting to cached route:', cachedRoute);
              this.routeCacheService.clearCachedRoute();
              this.router.navigateByUrl(cachedRoute);
            } else {
              //console.log('Terms accepted: No cached route, using default Dashboard');
              this.router.navigate(['/Dashboard']);
            }
        }
      });
    }
  }

  changeFn(event): void {
    this.disabledContinueBtn = !event.currentTarget.checked;
    this.isSubmitTnC = !this.disabledContinueBtn;
  }
}
