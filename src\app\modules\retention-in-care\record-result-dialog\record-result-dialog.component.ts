import { Component, On<PERSON><PERSON>roy, OnInit, Inject, Optional} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../../dashboard/panels/PanelService';
import { btnColor } from '../../dashboard/panels/report-panel-enums';
import { RCOutreachModel, RetentionInsertModel } from '../retention-models';
import { RetentionService } from '../retention.service';
import {ToastrService} from 'ngx-toastr'
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Subscription } from 'rxjs';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-record-result-dialog',
  templateUrl: './record-result-dialog.component.html',
  styleUrls: ['./record-result-dialog.component.css']
})
export class RecordResultDialogComponent implements OnInit, OnDestroy {

  recordResultMenuList: any = [];
  activePage: string = 'OutreachAndRetention';
  patientDetails: RCOutreachModel = new RCOutreachModel();
  resultText: FormControl;
  isSubmitButtonEnable: boolean = false;
  recordResultOptions: IReportParamData[] = [];
  rrOption: FormControl;
  rptbtnColor: string;
  site: string = '';
  public demographicsId : number = 0;
  public locationId : number = 0;
  public annotationId : number = 0;
  public patientName : string = '';


  private pageSubscriptions: Subscription = new Subscription;

  constructor(@Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    
    private _Activatedroute: ActivatedRoute,
    public dialogRef: MatDialogRef<RecordResultDialogComponent>,
    private retentionService: RetentionService,
    public panelService: PanelService,
    private router: Router,
    public toastr: ToastrService,
    private userContext: UserContext,
    private auditService: AuditService
  ) {

    if (data) {
      this.demographicsId = data.demographicsId;
      this.locationId = data.locationId;
      this.annotationId = data.annotationId;
      this.patientName = data.patientName;
      // ...
    }
  

    this.auditService.setPageAudit(Page.OutreachList);
    this.rrOption = new FormControl('-1');
    this.resultText = new FormControl;
    this.rptbtnColor = btnColor.btnSecondColor;
  }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {

    this.patientDetails = this.retentionService.selecetdPatient;

    this.recordResultOptions = [];
    var listName = this._Activatedroute.snapshot['_routerState'].url.indexOf('/AdministrativeActionItems') > -1 ? "S" : "P";
    
    this.site = this.userContext.GetCurrentSiteValue().toString();

    this.pageSubscriptions.add(
      this.retentionService.GetRecordResultOptions(this.site,this.patientDetails ? this.patientDetails.demographicsId : this.demographicsId, listName).subscribe(res => {
        if (res)
          this.recordResultOptions.push(res);
      })
    );
  
  }

  // Function is used to close dialog box
  onNoClick(): void {
    this.dialogRef.close();
  }

  // For the save button on dialog box
  onClickSave() {
    let obj = new RetentionInsertModel();
    obj.ANNOTATE_FREETEXT = this.resultText.value;
    obj.ANNOTATE_ID = this.patientDetails ?  this.patientDetails.annotateId : this.annotationId;
    obj.ANNOTATE_OPTION_ID = this.rrOption.value;
    obj.DEMOGRAPHICS_ID= this.patientDetails ? this.patientDetails.demographicsId : this.demographicsId;
    obj.LOCATION_ID =  this.patientDetails ? this.patientDetails.locationId : this.locationId;
    obj.PATIENT_NAME = this.patientDetails ? this.patientDetails.patientName : this.patientName;
  
    this.retentionService.UpdateRetentionResult(this.site,obj).subscribe(res => {
      if (res.ok == true) {
        this.toastr.success('Retention status updated successfully.', '', {
          timeOut: 3000,
        });
        this.dialogRef.close();
        this.router.navigate(['/OutreachAndRetention']);
      }
    });
  }

  onSelectionChange() {
    if (this.rrOption.value == -1) {
      this.rptbtnColor = btnColor.btnSecondColor;
      this.isSubmitButtonEnable = false;
    } else {
      this.rptbtnColor = btnColor.btnPrimaryColor;
      this.isSubmitButtonEnable = true;
    }
  }
}
