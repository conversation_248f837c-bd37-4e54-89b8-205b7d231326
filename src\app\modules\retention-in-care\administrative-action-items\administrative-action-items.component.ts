import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { RecordResultDialogComponent } from '../record-result-dialog/record-result-dialog.component';
import { RetentionService } from '../retention.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Subscription } from 'rxjs';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
  selector: 'administrative-action-items',
  templateUrl: './administrative-action-items.component.html',
  styleUrls: ['./administrative-action-items.component.scss']
})

export class AdministrativeActionItemsComponent implements OnInit, OnDestroy {
  
  administrativeActionItems: any = [];
  awaitingSchedulingList: any = [];
  awaitingWrongNumberList: any = [];
  siteId: number = 0;

  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    public dialog: MatDialog,
    public retentionInCare: RetentionService,
    private userContext: UserContext,
    private auditService: AuditService
  ) { 
    this.auditService.setPageAudit(Page.OutreachAdminActions);
  }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {

    this.siteId = this.userContext.GetCurrentSiteValue();
    if (this.siteId != 0) {
      this.loadAdministrativeActionItems(this.siteId.toString());
    }

    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(site => {
        this.loadAdministrativeActionItems(site.toString());
      })
    );
    
  }

  private loadAdministrativeActionItems(siteId: string) {
    // Administrative Action Items API call to show Patients list
    this.pageSubscriptions.add(
      this.retentionInCare.GetAdminActionDetails(siteId).subscribe(result => {
        if(result) {
          this.administrativeActionItems = result;
          // Awaiting Scheduling List data
          this.awaitingSchedulingList = result.lstScheduleList;
          // Wrong Number/No Service list data
          this.awaitingWrongNumberList = result.lstWrongNumberList;
        }
      })
    );
  }

  // Function is used to open Dialog box when user clicks on Record Result list
  openDialog(): void {
    const dialogRef = this.dialog.open(RecordResultDialogComponent, {
      width: '430px',
      height: '290px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      //console.log('The dialog was closed');
    });
  }
}
