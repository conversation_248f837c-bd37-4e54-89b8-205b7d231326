<div class="cover-container container-fluid">
  <!-- BEGIN: Chorus login header section -->
  <div class="header-row-1 row g-0 d-flex justify-content-xxl-start justify-content-xl-start justify-content-lg-start justify-content-md-start justify-content-sm-center justify-content-center">
    <div class="logo-container col-lg-3 col-md-3 col-sm-12"></div>
  </div>
  <!-- END: Chorus login header section -->

  <div class="eTFALoginInfo">
    <label class="eTFAMessage" id="eTFAMessage">Your organization requires two-factor authentication. Scan your QR code with your preferred authenticator, then click Choose Authenticator to continue.</label>
    <div class="eTFALoginDetails">
      <div class="eTFAQRCode">
        <img src={{this.qrCodeImageUrl}} alt="" height="150px" width="150px"  class="img-thumbnail" 
        class="eTFAQRImage">
        <button class="eTFAContinue" tabindex="0" (click)="linkToAuthenticator()">Choose Authenticator &nbsp; <img width="12px" height="12px" 
          src="../../../assets/images/right_arrow.png"></button>
      </div>
      <label class="eTFAMessage">Or input a Phone number for SMS.</label>
      <div class="eTFASMS">
        <div class="eTFAQRCode">
          <input id="sms" type="text" maxlength="13" #sms [(value)]=mobile [class]="isMobileValid" class="eTFASMSInput"
           (keyup)="sms.value=formatPhoneNumber(sms.value)" (keydown.enter)="linkToSMS(sms.value)" tabindex="2"
           name="sms" placeholder="### - ### - ####">
          <button class="eTFAContinue" (click)="linkToSMS(sms.value)" tabindex="3" type="submit">Choose SMS &nbsp; 
            <img width="12px" height="12px" src="../../../assets/images/right_arrow.png"></button>
        </div>
        <label class="eTFAErrorMessage" style="display: none; color: #741c23; text-align: center;"></label>
      </div>
    </div>
  </div>
</div>


