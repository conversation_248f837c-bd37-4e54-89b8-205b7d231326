# Starter pipeline
# Start with a minimal pipeline for an Angular app.
# https://aka.ms/yaml

trigger: none

pool:
  vmImage: 'windows-latest'

variables:
  nodeVersion: '^18.13.0'
  NODE_OPTIONS: '--max_old_space_size=4096'

steps:
- task: NodeTool@0
  inputs:
    versionSpec: $(nodeVersion)
  displayName: 'Install Node.js'

- task: CmdLine@2
  displayName: 'Install dependencies and build Angular App'
  inputs:
    script: | 
      npm install -g @angular/cli@~17.3.7

- script: npm install
  displayName: 'npm install packages'
  
- script: ng build --aot --output-hashing=all
  displayName: 'Build Angular App'
  

- task: CopyFiles@2
  displayName: 'Copy dist to artifacts'
  inputs:
    SourceFolder: '$(Build.SourcesDirectory)/dist'
    Contents: '**'
    TargetFolder: '$(build.artifactstagingdirectory)/dist'


- task: PublishBuildArtifacts@1
  displayName: 'Publish Artifact'
  inputs:
    PathtoPublish: '$(build.artifactstagingdirectory)'
    ArtifactName: 'Chorus_Portal'