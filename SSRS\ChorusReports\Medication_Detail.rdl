﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="PQRS_Measure_Medication_Details">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@userId">
            <Value>=Parameters!userId.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@reportingPeriod">
            <Value>=Parameters!reportingPeriod.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@rollingWeek">
            <Value>=Parameters!rollingWeek.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@measureCd">
            <Value>=Parameters!measureCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@alertLvl">
            <Value>=Parameters!alertLvl.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT	MRN, MED.PATIENT_ID, MED.PATIENT_NAME, MED.BIRTH_DT, MED.LAST_VISIT_DT, MED.START_DT, MED.END_DT, MED.MEDICATION_NM, MED.ALERT_LVL, 
		MED.MEASURE_ID, MED.PROVIDER_ID, MED.MEASURE_ANNOTATE_ID, OPEN_FLG
FROM REPORT.GET_PQRS_MEDICATION_DETAIL(@userId, @reportingPeriod, @rollingWeek, @measureCd, @providerCd, @locationCd) AS MED
LEFT OUTER JOIN	(
					SELECT	PATIENT_ID, PROVIDER_ID, MEASURE_ID, REPORTING_PERIOD, OPEN_FLG,  
							ROW_NUMBER() OVER(PARTITION BY PATIENT_ID, PROVIDER_ID, MEASURE_ID, REPORTING_PERIOD ORDER BY ANNOTATE_ID DESC) AS ROWNUM
					FROM DM.ANNOTATE_MEASURE AS AM
				) AM ON (AM.PATIENT_ID = MED.PATIENT_ID AND AM.PROVIDER_ID = MED.PROVIDER_ID AND AM.MEASURE_ID = MED.MEASURE_ID AND AM.REPORTING_PERIOD = @reportingPeriod AND ROWNUM = 1)
WHERE (CASE WHEN @alertLvl IS NULL THEN ALERT_LVL WHEN @alertLvl = '' OR @alertLvl = 0 THEN 0 ELSE 1 END = ALERT_LVL) 
AND COHORT_ID = @COHORT_ID</CommandText>
      </Query>
      <Fields>
        <Field Name="MRN">
          <DataField>MRN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PATIENT_ID">
          <DataField>PATIENT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PATIENT_NAME">
          <DataField>PATIENT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BIRTH_DT">
          <DataField>BIRTH_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LAST_VISIT_DT">
          <DataField>LAST_VISIT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="START_DT">
          <DataField>START_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="END_DT">
          <DataField>END_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="MEDICATION_NM">
          <DataField>MEDICATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ALERT_LVL">
          <DataField>ALERT_LVL</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROVIDER_ID">
          <DataField>PROVIDER_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ANNOTATE_ID">
          <DataField>MEASURE_ANNOTATE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="OPEN_FLG">
          <DataField>OPEN_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.59708in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.40922in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.88807in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.8875in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.88807in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.88807in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.59509in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.80625in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.46759in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>MRN</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Name</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Birth</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox45">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Last Visit Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox44">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Start</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>End Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Medication Name</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Comments</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.18634in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox307">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox307</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox308">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!PATIENT_NAME.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox308</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox309">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!BIRTH_DT.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox309</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox310">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!LAST_VISIT_DT.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox310</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox311">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!START_DT.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox311</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!END_DT.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox312">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!MEDICATION_NM.Value</SortExpression>
                            <SortTarget>PQRS_Measure_Medication_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox312</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25926in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MRN">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MRN.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MRN</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PATIENT_NAME">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PATIENT_NAME.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PATIENT_NAME</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="BIRTH_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Format(Fields!BIRTH_DT.Value,"yyyy-MM-dd")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>BIRTH_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAST_VISIT_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Format(Fields!LAST_VISIT_DT.Value,"yyyy-MM-dd")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAST_VISIT_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="GIVEN_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(FormatDateTime(Fields!START_DT.Value,DateFormat.ShortDate) = DateValue("January 1, 0001"), "", Format(Fields!START_DT.Value,"yyyy-MM-dd"))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                    <Color>= IIF(Fields!ALERT_LVL.Value = 0, IIF(YEAR(Fields!START_DT.Value) &lt;= Parameters!reportingPeriod.Value AND YEAR(Fields!END_DT.Value) &gt;= Parameters!reportingPeriod.Value,"Black","Red"),"Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>GIVEN_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, IIF(Fields!START_DT.Value = DateValue("January 1, 0001"),"Red", "Silver"), IIF(Fields!START_DT.Value = DateValue("January 1, 0001"),"Red","White"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(FormatDateTime(Fields!END_DT.Value,DateFormat.ShortDate) = DateValue("January 1, 0001"), "", Format(Fields!END_DT.Value,"yyyy-MM-dd"))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                    <Color>= IIF(Fields!ALERT_LVL.Value = 0, IIF(YEAR(Fields!START_DT.Value) &lt;= Parameters!reportingPeriod.Value AND YEAR(Fields!END_DT.Value) &gt;= Parameters!reportingPeriod.Value,"Black","Red"),"Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox3</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, IIF(Fields!END_DT.Value = DateValue("January 1, 0001"),"Red", "Silver"), IIF(Fields!END_DT.Value = DateValue("January 1, 0001"),"Red","White"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MEDICATION_NM">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MEDICATION_NM.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>= IIF(Fields!ALERT_LVL.Value = 0, IIF(YEAR(Fields!START_DT.Value) &lt;= Parameters!reportingPeriod.Value AND YEAR(Fields!END_DT.Value) &gt;= Parameters!reportingPeriod.Value,"Black","Red"),"Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MEDICATION_NM</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, IIF(ISNOTHING(Fields!MEDICATION_NM.Value) OR Fields!MEDICATION_NM.Value="" ,"Red", "Silver"), IIF(ISNOTHING(Fields!MEDICATION_NM.Value) OR Fields!MEDICATION_NM.Value="" ,"Red","White"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Annotate</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>= IIf(isNothing(Fields!OPEN_FLG.Value),"Blue",IIF(Fields!OPEN_FLG.Value=TRUE,"Red","Green"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Hyperlink>= "javascript:var a=  OpenModalbox('../_layouts/15/Chorus/Member/Anotation.aspx?DemographicId="+Fields!PATIENT_ID.Value.ToString()+"&amp;ProviderId="+Fields!PROVIDER_ID.Value.ToString()+"&amp;MeasureId="+Fields!MEASURE_ID.Value.ToString()+"&amp;ReportingYear="+Parameters!reportingPeriod.Value.ToString()+"&amp;MeasureAnnotateId="+Fields!MEASURE_ANNOTATE_ID.Value.ToString()+"&amp;VisitDate=0"+"', '500px', '730px');"</Hyperlink>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>PQRS_Measure_Medication_Details</DataSetName>
            <Top>0.55625in</Top>
            <Height>0.91319in</Height>
            <Width>7.95935in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <LineHeight>1pt</LineHeight>
            </Style>
          </Tablix>
          <Textbox Name="Textbox16">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=FormatNumber(Parameters!measureCnt.Value, 0) &amp; IIf(Parameters!alertLvl.Value=1, " patient/s meet the ", " patient/s do not meet the")</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> g</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>uideline: </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!guidelineDesc.Value</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox13</rd:DefaultName>
            <Top>0.17639cm</Top>
            <Height>1.23648cm</Height>
            <Width>20.30295cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>Lavender</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>1.46944in</Height>
        <Style />
      </Body>
      <Width>8.00602in</Width>
      <Page>
        <PageHeader>
          <Height>0.65277in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Left>5.44445in</Left>
              <Height>0.58333in</Height>
              <Width>2.5449in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox48">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIf(IsNothing(Parameters!locationCd.Value) OR Parameters!locationCd.Value ="", IIF(Parameters!reportingPeriod.Value = 2016, "PQRS ", "MIPS ") &amp; Parameters!measureCd.Value, Parameters!locationCd.Value) &amp; IIf(IsNothing(Parameters!providerCd.Value) OR Parameters!providerCd.Value="","", " -&gt; " &amp; Parameters!providerCd.Value) &amp; ": " &amp; Parameters!totPatients.Value &amp; " Total Patients"</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.00882cm</Top>
              <Left>0.00001in</Left>
              <Height>0.57986in</Height>
              <Width>5.30555in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Line Name="Line1">
              <Top>0.23138in</Top>
              <Left>0.00001in</Left>
              <Height>0in</Height>
              <Width>4.89312in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
              </Style>
            </Line>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>1.39583in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox52">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>DATA NOTES</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIF(Parameters!COHORT_ID.Value=6,"The Physician Quality Reporting System (PQRS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All 2016 PQRS measures can be found at: ",  "The Merit-based Incentive Payment System (MIPS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All "+CStr(Parameters!reportingPeriod.Value)+" MIPS measures can be found at: "    )</Value>
                      <MarkupType>HTML</MarkupType>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=iif(Parameters!COHORT_ID.Value=6,"https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp", "https://qpp.cms.gov/")</Value>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Hyperlink>=iif(Parameters!COHORT_ID.Value=6,"javascript:void(window.open('https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp'))","javascript:void(window.open(' https://qpp.cms.gov/ '))")</Hyperlink>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>#0000ff</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox52</rd:DefaultName>
              <Height>1.39583in</Height>
              <Width>7.98935in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <BackgroundColor>#0b6c9f</BackgroundColor>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.24in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="measureCd">
      <DataType>String</DataType>
      <Prompt>measurecd</Prompt>
    </ReportParameter>
    <ReportParameter Name="alertLvl">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>Visable:</Prompt>
      <ValidValues>
        <ParameterValues>
          <ParameterValue>
            <Label>All</Label>
          </ParameterValue>
          <ParameterValue>
            <Value>0</Value>
            <Label>Does Not Meet Guideline</Label>
          </ParameterValue>
          <ParameterValue>
            <Value>1</Value>
            <Label>Meets Guideline</Label>
          </ParameterValue>
        </ParameterValues>
      </ValidValues>
    </ReportParameter>
    <ReportParameter Name="guidelineDesc">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Guideline:</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="totPatients">
      <DataType>String</DataType>
      <Prompt>Total Patients:</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="measureCnt">
      <DataType>String</DataType>
      <Prompt>measureCnt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>locationCd</Prompt>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>providerCd</Prompt>
    </ReportParameter>
    <ReportParameter Name="toggleLocation">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleLocation</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="toggleProvider">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleProvider</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="userId">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>88D180EC-184E-4F88-8794-1A4F0BC0748C</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>userId</Prompt>
    </ReportParameter>
    <ReportParameter Name="reportingPeriod">
      <DataType>String</DataType>
      <Prompt>reporting Period</Prompt>
    </ReportParameter>
    <ReportParameter Name="rollingWeek">
      <DataType>String</DataType>
      <Prompt>rolling Week</Prompt>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>COHORT_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="cohortCnt">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>cohortCnt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="parentLabCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>parentLabCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd2">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd2</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd3">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd3</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="cohortNm">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>cohortNm</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="inverted_flg">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>inverted_flg</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>6</NumberOfColumns>
      <NumberOfRows>8</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>measureCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>alertLvl</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>guidelineDesc</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>totPatients</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>measureCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>toggleLocation</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>toggleProvider</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>userId</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>reportingPeriod</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>rollingWeek</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>cohortCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>parentLabCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>labCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>labCd2</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>labCd3</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>cohortNm</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>7</RowIndex>
          <ParameterName>inverted_flg</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Language>en-US</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>6abdf4ea-82a8-4e68-977e-c644bc4f81d6</rd:ReportID>
</Report>