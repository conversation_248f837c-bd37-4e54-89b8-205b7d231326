.eTncContainer {
    background-color: #0071bc;
    height: 100vh;
    width: 100vw;
    overflow:Hide; /*scrollbars */
    position: relative;
}
.eTncBubbleContainer{
    background-image: url("../../../../assets/images/Bubbles.svg");
    background-repeat: no-repeat;
    background-size: 80%;
    position: absolute;
    height: 100vh;
    width: 100vw;
    background-position-x: right;
    top: -10%;
    left: 0%;
}
.eTncLogoContainer{
    background-image: url("../../../../assets/images/Logo.svg");
    background-repeat: no-repeat;
    position: absolute;
    background-size: 22%;
    background-position-x: right;
    top: -13%;
    left: -58%;
    height: 100vh;
    width: 100vw;
}
.eTermsCondition{
    position: relative;
    float: right;
    top: 5%;
    right: 3%;
    bottom: 5%;
    width: 50%;
}
.eTermsConditionBlock{
    display: flex;
    border: 1px solid #FFF;
    border-radius: 10px;    
    background-color: #FFF;
    padding: 20px;
    color: #0071bc;
    font-family: "MuseoSans-500";
    font-size: 11pt;
}
.term-text-container {
    overflow: auto;
    max-height: calc(100vh - 100px);
}
::ng-deep h1 {
    font-family: "MuseoSans-500";
    font-size: 12pt;
}
::ng-deep h2 {
    font-family: "MuseoSans-500";
    font-size: 12pt;
}
::ng-deep h3 {
    font-family: "MuseoSans-500";
    font-size: 12pt;
}

::ng-deep h4 {
    font-family: "MuseoSans-500";
    font-size: 12pt;
}
.headerContent {
    font-family: "MuseoSans-500";
    font-size: 20pt;
    text-align: center;
    padding-bottom: 10px;
}
.content {
    padding: 5px;
    color: #6699bb !important;
}
.acceptance-row {
    display: flex;
    justify-content: space-between;
}
.actionButton {
    display: flex; 
    justify-content: flex-end;
}
::ng-deep .btn:disabled {
    background-color: rgb(46 36 36 / 66%);
}
::ng-deep .mat-button.mat-button-disabled.mat-button-disabled{
    color: #FFF;
}

/* Scrollbar CSS */
::-webkit-scrollbar {
   width: 8px;
}
::-webkit-scrollbar-track {
    background: #77c0f1;
    border-radius: 15px;
}
::-webkit-scrollbar-thumb {
    background: #0071bc;
    border-radius: 15px;
}
::-webkit-scrollbar-thumb:hover {
    background: #0071bc; 
}

::ng-deep p {

    font-size: 14px !important;
}

.btn-sm {
    background: #0071bc; 
    color:#FFF !important;
    accent-color:#0071bc;
}