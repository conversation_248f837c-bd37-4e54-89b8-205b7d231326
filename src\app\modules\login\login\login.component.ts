import { Component, OnIni<PERSON>,Inject, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from "ngx-spinner";
import { IapiErrorResponse } from 'src/app/shared-services/ep-api-handler/models/index';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { DeviceDetectorService } from 'ngx-device-detector';
import { Subscription, interval } from 'rxjs';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { LoginService } from '../services/login/login.service';
import { BrowserModule } from '@angular/platform-browser';
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { environment } from 'src/environments/environment';
import { SwUpdate } from '@angular/service-worker';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

BrowserModule
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit, OnDestroy {

   /* BEGIN: Form Group section */
   epSignInForm: FormGroup = new FormGroup({
    epEmail: new FormControl('', [Validators.required]),
    epPassword:  new FormControl('', [Validators.required])
  });;
   /* END: Form Group section */


   /* BEGIN:  Class Instance variables / properties */
   currentIP: any = '';
   errorMsg: string = '';
   isLoginAttemptsOver: boolean = false;
   showSpinner: boolean = false;
   userDeviceCode: string = '';
   loginInProgress: boolean = false;
   /* END:  Class Instance variables / properties */
   private updateCheckSubscription: Subscription = new Subscription();
   private pageSubscriptions: Subscription = new Subscription();


  public AppVersion: string = environment.appVersion;

  serviceData:any;
  accessToken:string = "";
  successMsg: any;
  //errorMsg: any;
  response: any[] = [];
  userString: string="";
  ip: string="";
  date: string;
  dispoCode: any;
  error_description: string="";
  currentDate = new Date();
  userFName: string="";
  userLName: string="";
  hide : boolean = true;
  capsLockOn : boolean = false;


  constructor(private router: Router,
              @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
              private userContext: UserContext,
              private deviceService: DeviceDetectorService,
              private _fb: FormBuilder,
              private _loginService: LoginService,
              private swUpdate: SwUpdate,
              private auditService: AuditService) {
                 this.auditService.setPageAudit(Page.Login);
                  /* BEGIN: Registering form validations on form elements */
                this.epSignInForm = this._fb.group({
                  epEmail: ['', [Validators.required, Validators.email]],
                  epPassword: ['', [Validators.required]],
          });
          /* END: Registering form validations on form elements */

    this.date = this.currentDate.toDateString();
    this.errorMsg = " ";
   }

  ngOnInit(): void {

    // Set up the interval timer to check for updates every 30 seconds
    this.updateCheckSubscription = interval(30000).subscribe(() => {
      if (this.swUpdate.isEnabled) {
        this.swUpdate.checkForUpdate();
      }
    });

    //if they we're force out of the system do to getting unauthorized requests
    //we must redirect the home page to refresh the angular app
    const doRedirect = history.state.data;
    if (doRedirect==true)
    {
      this.userContext.ClearSession();
      window.location.reload();
    }

     //checks application terms agreement on entering dashboard
     this.userContext.LoadUserObjFromLocalStorage();
     this.pageSubscriptions.add(
      this.userContext.getUserSession().subscribe((res: any) => {
        if(res.value != undefined  && res.value.access_token != undefined)
        {
          this._loginService.handlePostLoginRouting();
        }
        else
        {
          this.spinnerService.hide();
        }
      })
     );
  }

  @HostListener('window:click', ['$event']) onClick(event){
    if (event.getModifierState && event.getModifierState('CapsLock')) {
      this.capsLockOn = true;
     } else {
      this.capsLockOn = false;
     }
    }

   @HostListener('window:keydown', ['$event'])
   onKeyDown(event){
   if (event.getModifierState && event.getModifierState('CapsLock')) {
     this.capsLockOn = true;
     } else {
      this.capsLockOn = false;
     }
   }

   @HostListener('window:keyup', ['$event'])
    onKeyUp(event){
    if (event.getModifierState && event.getModifierState('CapsLock')) {
     this.capsLockOn = true;
    } else {
     this.capsLockOn = false;
    }
   }

  ngOnDestroy(): void {
     // Clear the interval timer when the component is destroyed
     if (this.updateCheckSubscription) {
      this.updateCheckSubscription.unsubscribe();
    }
    this.pageSubscriptions.unsubscribe();
  }

  private userNameEmail(): string {
    let obj = this.epSignInForm.get('epEmail');
    if (obj)
      {
        return obj.value
      }
      else
        return "";
  }

  private password(): string {
    let obj = this.epSignInForm.get('epPassword');
    if (obj)
      {
        return obj.value
      }
      else
        return "";
  }

   submitForm()
   {
      this.onEpSignIn(this.epSignInForm);
   }

    /* BEGIN: Handler method called on form submission */
    public onEpSignIn(signIn: FormGroup) {
      this.errorMsg = "";
      //only allows a click event and Enter Key Press on login container not contained to just password field.
      // if ((event != undefined && event.type != 'click') && (event.code != undefined && event.code != "Enter"))
      if(!signIn.valid)
        { return;}

      if (this.password()== "")
        { return; }

        if (this.loginInProgress==false)
        {
          this.loginInProgress = true;
          this.spinnerService.show().then(() =>{});

          //let deviceInfo = this.deviceService.getDeviceInfo();
          this.userContext.SetUsername(this.userNameEmail());
          this.userString = this.userNameEmail();
          this.pageSubscriptions.add(
            this._loginService.login<IChorusResponseToken|IapiErrorResponse>(this.userString,this.password(), this.userDeviceCode).subscribe((res: any) => {
              this.loginInProgress = false;
              if (res.token_type =='bearer') {
                this.userContext.SaveTokenToLocalStorage(res)

                //If Access Token is returned, save it and redirect to dashboard
                if (res.access_token!=null)
                {
                  this._loginService.UpdateUserLoginStatus();
                  this.errorMsg = "Login Successful - Redirecting";
                  //checks application terms agreement on entering dashboard and handles cached routes
                  this._loginService.handlePostLoginRouting();
                }
                else if (res.callback_redirect==1 || (res.tfaMethod==0 && res.tfaEnabled==true) || (res.tfaMethod==1 && res.tfaEnabled==true && res.last4=="new#") )
                {
                  this.errorMsg = "Login Successful - Redirecting";
                  this.userContext.SetUsername(res.userName);
                  this.router.navigate(['/Auth/RegisterTfa'], {state: { digits:  res.last4, userInfo: res}});
                }
                else if (res.callback_redirect==2 && res.tfaEnabled==false)
                {
                  this.errorMsg = "Login Successful - Redirecting";
                  let changePasswordPath = '/Auth/ChangePassword?ref=' + encodeURIComponent(res.resetToken);
                  this.router.navigateByUrl(changePasswordPath);
                }
                else
                {
                  this.router.navigate(['/Auth/VerifyTfa'], {state: { digits:  res.last4, tfaMethod: res.tfaMethod, resetToken: res.resetToken, userInfo: res}});
                }

              }
              else if(res.error=='Invalid login attempt.'){
                this.errorMsg = "Invalid user name or password";
              }
              else
              {
                this.errorMsg = res.error[0];
              }

              this.spinnerService.hide();
            })
          );

        }
    }

  loadUsersDeviceCode() {
      this.userDeviceCode = this.userContext.GetUserDeviceCode();
  }


  passwordShowHide() {
    this.hide = !this.hide;
  }

}
