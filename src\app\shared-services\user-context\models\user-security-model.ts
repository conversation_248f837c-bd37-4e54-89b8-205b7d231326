export enum ProviderAssociationTypes {
  None = 0,
  Self = 1,
  Delegate = 2,
  Case_Manager = 3,
}

export enum RoleTypes {
  None = 0,
  HCP = 1,
  Location_Director = 2,
  Site_Director = 3,
  Staff = 4,
  EHI_Export = 5,
  QRDA_Export = 6,
  Epividian_Admin = 7,
  Chorus_Site_Admin = 8
}

export interface IChorusAccessViewModel {
  isDefault: boolean;
  siteId: number;
  enableOutreachRetention: boolean;
  userRoles: IRoleAssocation[];
  providerAssocations: IProviderAssocation[];
  isDataLoading:boolean;
}

export interface IProviderAssocation {
  providerId: number;
  isPrimary: boolean;
  type: ProviderAssociationTypes;
  locationId: number[];
}

export interface IRoleAssocation {
  role: RoleTypes;
  locationId: number[];
}
