<!--Report Dashboard with Left Stacked Panels-->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h2 class="section-title">Reports</h2>
    </div>
    <div class="header-center">
      <h2 class="report-title">{{reportTitle}}</h2>
    </div>
    <div class="header-right">
      <button
        type="button"
        mat-icon-button
        *ngIf="isPopUp"
        (click)="closeRptPop()"
        aria-label="Close report popup"
        title="Close">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Report Content Layout -->
  <div class="report-content-layout">
    <!-- Left Sidebar: Stacked Panels -->
    <div class="left-sidebar">
      <!-- Reports Panel -->
      <div class="sidebar-panel reports-panel"
           [class.collapsed]="reportNavCollapsed">
        <div class="panel-inner">
          <div class="panel-header"
               (click)="toggleReportNav()"
               [attr.aria-label]="reportNavCollapsed ? 'Show reports and hide filters' : 'Show filters and hide reports'"
               title="Toggle Reports">
            <h3>{{ getSelectReportTitle() }}</h3>
            <button
              type="button"
              mat-icon-button
              class="panel-toggle-btn"
              (click)="$event.stopPropagation(); toggleReportNav()"
              [attr.aria-label]="reportNavCollapsed ? 'Show reports and hide filters' : 'Show filters and hide reports'"
              title="Toggle Reports">
              <mat-icon>{{ reportNavCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
            </button>
          </div>
          <div class="panel-content" [class.hidden]="reportNavCollapsed">
            <epividian-reportNav
              (currentReportChanged)="onCurrentReportChanged($event)"
              (reportSelected)="onReportSelected()">
            </epividian-reportNav>
          </div>
        </div>
      </div>

      <!-- Filters Panel -->
      <div class="sidebar-panel filters-panel"
           [class.collapsed]="filtersCollapsed">
        <div class="panel-inner">
          <div class="panel-header"
               (click)="toggleFilters()"
               [attr.aria-label]="filtersCollapsed ? 'Show filters and hide reports' : 'Show reports and hide filters'"
               title="Toggle Filters">
            <h3>Select Filters</h3>
            <button
              type="button"
              mat-icon-button
              class="panel-toggle-btn"
              (click)="$event.stopPropagation(); toggleFilters()"
              [attr.aria-label]="filtersCollapsed ? 'Show filters and hide reports' : 'Show reports and hide filters'"
              title="Toggle Filters">
              <mat-icon>{{ filtersCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
            </button>
          </div>
          <div class="panel-content" [class.hidden]="filtersCollapsed">
            <div class="filters-container">
              <div class="filters-widget">
                <ng-template #panelContainerRef></ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right: Report Viewer -->
    <div class="report-viewer-area">
      <!-- Placeholder content when no report is loaded -->
      <div class="report-placeholder" *ngIf="!isReportLoaded">
        <div class="placeholder-content">
          <div class="placeholder-icon">
            <div class="checklist-icon">
              <div class="checklist-row">
                <mat-icon class="check-icon">check</mat-icon>
                <div class="line-icon"></div>
              </div>
              <div class="checklist-row">
                <mat-icon class="check-icon">check</mat-icon>
                <div class="line-icon"></div>
              </div>
            </div>
          </div>
          <h3 class="placeholder-title">Select filters</h3>
          <p class="placeholder-subtitle">Use the left-hand menu to select filters, then run your report</p>
        </div>
      </div>

      <!-- Dynamic report container -->
      <ng-template #bldReportContainerRef></ng-template>
    </div>
  </div>
</div>

