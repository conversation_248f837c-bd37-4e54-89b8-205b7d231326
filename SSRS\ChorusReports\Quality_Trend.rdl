﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:cl="http://schemas.microsoft.com/sqlserver/reporting/2010/01/componentdefinition" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2010/01/reportdefinition">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>       DECLARE @SITE_ID INT
          SET                @SITE_ID =
          (SELECT        SITE_ID
                               FROM            CLEAN.SITE)
          SELECT   TOP 1     SITE_NM, CITY_TXT, STATE_TXT,IIF(OS.SITE_ID IS NULL ,0,1)IS_OPERA
          FROM            [CHORUS].[ADMIN].[SITE] CS LEFT OUTER JOIN   OPERA.CLEAN.SITE OS ON CS.SITE_ID = OS.SITE_ID
          WHERE        (STATUS_CD = 'A') AND (CS.SITE_ID = @SITE_ID)</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IS_OPERA">
          <DataField>IS_OPERA</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
          SELECT EXTRACT_DT
          FROM BASE.SITE
        </CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_PQRS_MEASURES">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@measure_cd">
            <Value>=Parameters!measure_cd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@REPORTING_YEAR">
            <Value>=Parameters!REPORTING_YEAR.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT REPORTING_PERIOD, ROLLING_WEEK, LOCATION, PRIMARY_PROVIDER, TOT_CNT, TOT_PTNT_CNT, MEASURE_CD, INVERTED_FLG
, MEASURE_NM, REPORT_LEVEL_DESC_1, REPORT_LEVEL_DESC_2, REPORT_LEVEL_DESC_3, MEETING_DESC, NOT_MEETING_DESC, TOT_MEASURE_CNT
, MEASURE_CNT, PQRS_25TH_PCTL, PQRS_75TH_PCTL, PQRS_100TH_PCTL, PQRS_AVG
FROM [REPORT].[GET_QUALITY_TREND](@USER_ID , @COHORT_ID , @REPORTING_YEAR , @locationCd , @providerCd , @measure_cd)</CommandText>
      </Query>
      <Fields>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ROLLING_WEEK">
          <DataField>ROLLING_WEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>LOCATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER">
          <DataField>PRIMARY_PROVIDER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOT_CNT">
          <DataField>TOT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOT_PTNT_CNT">
          <DataField>TOT_PTNT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INVERTED_FLG">
          <DataField>INVERTED_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="MEASURE_NM">
          <DataField>MEASURE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_1">
          <DataField>REPORT_LEVEL_DESC_1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_2">
          <DataField>REPORT_LEVEL_DESC_2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_3">
          <DataField>REPORT_LEVEL_DESC_3</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEETING_DESC">
          <DataField>MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOT_MEETING_DESC">
          <DataField>NOT_MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOT_MEASURE_CNT">
          <DataField>TOT_MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CNT">
          <DataField>MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PQRS_25TH_PCTL">
          <DataField>PQRS_25TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PQRS_75TH_PCTL">
          <DataField>PQRS_75TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PQRS_100TH_PCTL">
          <DataField>PQRS_100TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PQRS_AVG">
          <DataField>PQRS_AVG</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Chart Name="Sparkline4">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Sparkline2_CategoryGroup4">
                    <GroupExpressions>
                      <GroupExpression>=Fields!ROLLING_WEEK.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!ROLLING_WEEK.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Label>MEASURE CNT</Label>
                </ChartMember>
                <ChartMember>
                  <Label>MEASURE CNT</Label>
                </ChartMember>
                <ChartMember>
                  <Label>TOT MEASURE CNT</Label>
                </ChartMember>
                <ChartMember>
                  <Label>TOT MEASURE CNT</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="MEASURE_CNT">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=IIF( Fields!INVERTED_FLG.Value=TRUE, Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value),
Sum(Fields!MEASURE_CNT.Value)
    )</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style>
                          <FontStyle>Normal</FontStyle>
                          <FontSize>7.5pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Format>="#,##0"</Format>
                          <TextDecoration>None</TextDecoration>
                        </Style>
                        <UseValueAsLabel>true</UseValueAsLabel>
                        <Visible>true</Visible>
                        <Position>Center</Position>
                        <Rotation>-90</Rotation>
                      </ChartDataLabel>
                      <ToolTip>=Fields!ROLLING_WEEK.Value</ToolTip>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Quality_Measures</ReportName>
                              <Parameters>
                                <Parameter Name="extractDt">
                                  <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                </Parameter>
                                <Parameter Name="rollingWeek">
                                  <Value>=Fields!ROLLING_WEEK.Value</Value>
                                </Parameter>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="locationCd">
                                  <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="providerCd">
                                  <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="drillthroughProviderCd">
                                  <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="drillthroughLocationCd">
                                  <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="COHORT_ID">
                                  <Value>=Parameters!COHORT_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="REPORTING_YEAR">
                                  <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Width>0.5pt</Width>
                        </Border>
                        <Color>CornflowerBlue</Color>
                      </Style>
                      <ChartMarker>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Subtype>Stacked</Subtype>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <CustomProperties>
                    <CustomProperty>
                      <Name>PointWidth</Name>
                      <Value>0.75</Value>
                    </CustomProperty>
                  </CustomProperties>
                  <ChartItemInLegend>
                    <LegendText>Performance Met</LegendText>
                  </ChartItemInLegend>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <Disabled>true</Disabled>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
                <ChartSeries Name="Series">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=IIF( Fields!INVERTED_FLG.Value=TRUE, Sum(Fields!MEASURE_CNT.Value),
Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value)
    )</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style>
                          <FontStyle>Normal</FontStyle>
                          <FontSize>7.5pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Format>="#,##0"</Format>
                          <TextDecoration>None</TextDecoration>
                        </Style>
                        <UseValueAsLabel>true</UseValueAsLabel>
                        <Visible>true</Visible>
                        <Position>Center</Position>
                        <Rotation>-90</Rotation>
                      </ChartDataLabel>
                      <ToolTip>=Fields!ROLLING_WEEK.Value</ToolTip>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Quality_Measures</ReportName>
                              <Parameters>
                                <Parameter Name="extractDt">
                                  <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                </Parameter>
                                <Parameter Name="rollingWeek">
                                  <Value>=Fields!ROLLING_WEEK.Value</Value>
                                </Parameter>
                                <Parameter Name="locationCd">
                                  <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="providerCd">
                                  <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="drillthroughProviderCd">
                                  <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="drillthroughLocationCd">
                                  <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                </Parameter>
                                <Parameter Name="COHORT_ID">
                                  <Value>=Parameters!COHORT_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="REPORTING_YEAR">
                                  <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Color>Red</Color>
                      </Style>
                      <ChartMarker>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Subtype>Stacked</Subtype>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <CustomProperties>
                    <CustomProperty>
                      <Name>PointWidth</Name>
                      <Value>0.75</Value>
                    </CustomProperty>
                  </CustomProperties>
                  <ChartItemInLegend>
                    <LegendText>Performance Not Met</LegendText>
                  </ChartItemInLegend>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <Disabled>true</Disabled>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
                <ChartSeries Name="PQRS_25TH_PCTL">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=IIF(Sum(Fields!IS_OPERA.Value, "SITE_INFO")=1, Max(Fields!PQRS_25TH_PCTL.Value) * Sum(Fields!TOT_MEASURE_CNT.Value),0)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <ToolTip>=IIF(Sum(Fields!IS_OPERA.Value, "SITE_INFO")=1, floor(Max(Fields!PQRS_25TH_PCTL.Value) * Sum(Fields!TOT_MEASURE_CNT.Value)),0)</ToolTip>
                      <Style>
                        <Border>
                          <Color>Black</Color>
                          <Width>3.5pt</Width>
                        </Border>
                        <Color>Black</Color>
                      </Style>
                      <ChartMarker>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Subtype>Smooth</Subtype>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ChartItemInLegend>
                    <LegendText>OPERA 25th Percentile</LegendText>
                  </ChartItemInLegend>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
                <ChartSeries Name="PQRS_75TH_PCTL">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=IIF(Sum(Fields!IS_OPERA.Value, "SITE_INFO")=1, Max(Fields!PQRS_75TH_PCTL.Value) * Sum(Fields!TOT_MEASURE_CNT.Value),0)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <ToolTip>=IIF(Sum(Fields!IS_OPERA.Value, "SITE_INFO")=1, floor(Max(Fields!PQRS_75TH_PCTL.Value) * Sum(Fields!TOT_MEASURE_CNT.Value)),0)</ToolTip>
                      <Style>
                        <Border>
                          <Color>Black</Color>
                          <Style>Dashed</Style>
                          <Width>4pt</Width>
                        </Border>
                        <Color>Black</Color>
                      </Style>
                      <ChartMarker>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Subtype>Smooth</Subtype>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ChartItemInLegend>
                    <LegendText>OPERA 75th Percentile</LegendText>
                  </ChartItemInLegend>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <FontSize>7pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Format>=0</Format>
                    </Style>
                    <ChartAxisTitle>
                      <Caption>Rolling Week</Caption>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </ChartAxisTitle>
                    <Margin>False</Margin>
                    <Interval>1</Interval>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Enabled>False</Enabled>
                      <Interval>=1</Interval>
                      <IntervalType>Weeks</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Enabled>True</Enabled>
                      <Length>0.5</Length>
                      <Interval>=1</Interval>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <IncludeZero>false</IncludeZero>
                    <LabelsAutoFitDisabled>true</LabelsAutoFitDisabled>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Visible>False</Visible>
                    <Style>
                      <FontSize>8pt</FontSize>
                    </Style>
                    <ChartAxisTitle>
                      <Caption>Axis Title</Caption>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMinorTickMarks>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <FontSize>7pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMinorTickMarks>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <LabelsAutoFitDisabled>true</LabelsAutoFitDisabled>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Visible>False</Visible>
                    <Style>
                      <FontSize>8pt</FontSize>
                    </Style>
                    <ChartAxisTitle>
                      <Caption>Axis Title</Caption>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMinorTickMarks>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>BottomCenter</Position>
                <DockOutsideChartArea>true</DockOutsideChartArea>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>=Fields!MEASURE_NM.Value</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontWeight>Bold</FontWeight>
                  <TextAlign>General</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>BrightPastel</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>DS_PQRS_MEASURES</DataSetName>
            <Top>0.23091in</Top>
            <Left>0.22979in</Left>
            <Height>2.87393in</Height>
            <Width>9.84212in</Width>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
                <Width>0.25pt</Width>
              </Border>
              <BottomBorder>
                <Color>Gray</Color>
                <Style>None</Style>
                <Width>0.25pt</Width>
              </BottomBorder>
              <BackgroundColor>#d4d4e5</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
        </ReportItems>
        <Height>3.33576in</Height>
        <Style />
      </Body>
      <Width>10.30169in</Width>
      <Page>
        <PageHeader>
          <Height>0.65257in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox13">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIf(Parameters!REPORTING_YEAR.Value = 2016, Parameters!REPORTING_YEAR.Value &amp; " - PQRS Measures", Parameters!REPORTING_YEAR.Value &amp; " - MIPS Measures")</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Top>0.33365cm</Top>
              <Left>9.40062cm</Left>
              <Height>0.83087cm</Height>
              <Width>7.84933cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Height>0.97931cm</Height>
              <Width>9.19396cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.00289in</Top>
              <Left>8.11344in</Left>
              <Height>0.38267in</Height>
              <Width>2.16862in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox14">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Normal</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox14</rd:DefaultName>
              <Top>1.41146cm</Top>
              <Height>0.14023cm</Height>
              <Width>26.15629cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Color>Purple</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </TopBorder>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>0.60764in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox52">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>DATA NOTES</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIF(Parameters!REPORTING_YEAR.Value=2016,"The Physician Quality Reporting System (PQRS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All 2016 PQRS measures can be found at: ",  "The Merit-based Incentive Payment System (MIPS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All "+CStr(Parameters!REPORTING_YEAR.Value)+" MIPS measures can be found at: "    )</Value>
                      <MarkupType>HTML</MarkupType>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=iif(Parameters!REPORTING_YEAR.Value=2016,"https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp", "https://qpp.cms.gov/")</Value>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Hyperlink>=iif(Parameters!REPORTING_YEAR.Value=2016,"javascript:void(window.open('https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp'))","javascript:void(window.open(' https://qpp.cms.gov/ '))")</Hyperlink>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>#0000ff</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox52</rd:DefaultName>
              <Top>0.14111cm</Top>
              <Height>0.55208in</Height>
              <Width>26.11642cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>8.5in</PageHeight>
        <PageWidth>11in</PageWidth>
        <LeftMargin>0.35in</LeftMargin>
        <RightMargin>0.35in</RightMargin>
        <TopMargin>0.35in</TopMargin>
        <BottomMargin>0.35in</BottomMargin>
        <ColumnSpacing>0.05118in</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="extractDt">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <DataSetReference>
          <DataSetName>Extract_Date</DataSetName>
          <ValueField>EXTRACT_DT</ValueField>
        </DataSetReference>
      </DefaultValue>
      <Prompt>ReportParameter1</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>location Cd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>provider Cd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughProviderCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughProviderCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughLocationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughLocationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>88D180EC-184E-4F88-8794-1A4F0BC0748C</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>USER_ID</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="rollingWeek">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>rollingWeek</Prompt>
    </ReportParameter>
    <ReportParameter Name="measure_cd">
      <DataType>String</DataType>
      <Prompt>measure cd</Prompt>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>Integer</DataType>
      <Prompt>COHORT_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="REPORTING_YEAR">
      <DataType>Integer</DataType>
      <Prompt>Reporting Year</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Variables>
    <Variable Name="MeasureCnt">
      <Value>9</Value>
    </Variable>
  </Variables>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>89b38ba8-54d5-4ac9-bad1-50c5d0e92c91</rd:ReportID>
</Report>