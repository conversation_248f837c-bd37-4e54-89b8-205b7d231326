export class RCOurReachCallVM {
    currentListCount: number=0;
    tryLaterListCount: number=0;
    callHistoryListCount: number=0;
    lstCurrentList: RCOutreachModel[]=[];
    lstTryLaterList: RCOutreachModel[]=[];
    lstCallHistoryList: RCOutreachModel[]=[];
}

export interface RCAdministrativeActionVM {
    scheduleListCount: number;
    wrongNumberListCount: number;
    lstScheduleList: RCOutreachModel[];
    lstWrongNumberList: RCOutreachModel[];
}

export class RCOutreachModel {
    patientName:string='';
    patientNameRiskDate: string='';
    annotationDescPrName: string='';
    mobilePhone: string='';
    homePhone: string='';
    demographicsId: number=0;
    annotateId: number=0;
    annotateOptionId: number=0;
    consecutive: string='';
    doNotCallFlg: boolean=false;
    locationId: number=0;
}

export class RetentionInsertModel {
    DEMOGRAPHICS_ID: number=0;
    ANNOTATE_OPTION_ID: number=0;
    ANNOTATE_ID: number=0;
    ANNOTATE_FREETEXT: string='';
    VALID_FROM_DT: string='';
    LOCATION_ID: number=0;
    PATIENT_NAME: string = '';
}

export interface RCMeasureDetailVM {
    demographicS_ID: number;
    patienT_NM: string;
    reportinG_PERIOD: string;
    primarY_PROVIDER_ID: string;
    measurE_ID: string;
    measurE_CD: string;
    measurE_IND: string;
    hiV_DIAG_DT: string;
    mosT_RECENT_APPOINTMENT: string;
    initiaL_VISIT_DT: string;
    lasT_MISSED_VISIT: string;
    prioR_MISSED_VISIT: string;
    measurE_ANNOTATE_ID: string;
    lasT_OUTREACH_DT: string;
    prioR_OUTREACH_DT: string;
    opeN_FLG: boolean;
    detaiL_TXT: string;
    homE_PHONE_NUM: string;
    mobilE_PHONE_NUM: string;
    birtH_DT: string;
    fulL_NM: string;
    annotatE_DESCRIPTION: string;
    dO_NOT_CALL_FLG: boolean;
    locatioN_ID: number
}


export interface MeasureFullOutreachVm
{
    dEMOGRAPHICS_ID :number;
    vALID_FROM_DT :string;
    aNNOTATE_DESCRIPTION :string;
    aNNOTATE_FREETEXT :string;
}

export class RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result
{
    locatioN_NM: string='';
    provideR_NM: string='';
    retentioN_PARTICIPATION_TOTAL_PATIENTS: number=0;
    otheR_ANNOTATION_PATIENTS: number=0;
    neW_ALERTS: number=0;
    agreeD_TO_VISIT: number=0;
    scheduleD_VISIT: number=0;
    nO_VOICEMAIL: number=0;
    lefT_VOICEMAIL: number=0;
    wronG_NUMBER: number=0;
    refuseD_VISIT: number=0;
    moved: number=0;
    transferred: number=0;
    deceased: number=0;
    incarcerated: number=0;
    stafF_DECLINED_TO_CONTACT: number=0;
    custom: number=0;
    nO_PHONE_NUMBER: number=0;
    cannoT_BE_REACHED: number=0;
    phonE_NUMBER_CORRECTED: number=0;
    provideR_ID: number=0;
    locatioN_ID: number=0;
    alreadY_RECEIVED_INJECTION: number=0;
}

export class RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result_Group
{
    providerList: RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result[]=[];
    expanded: boolean=true;
}

export interface State
{
    patienT_NAME: string;
    mrn: string;
    alerT_DATE: string;
    mosT_RECENT_APPOINTMENT: string;
    primarY_MEASURE_ID: number;
    statuS_DATE: string;
    ship_via: string;
    notes?: any;
    changeStatusTo: string;
    annotatE_ID: number;
    provideR_ID: string;
    locationId?: any;
    demographiC_ID: number;
    annotatE_OPTION_ID: number;
    locatioN_ID: number;
    isEdit:boolean;
    status:boolean;
}


