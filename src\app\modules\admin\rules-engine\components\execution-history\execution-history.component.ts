import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { WorkflowProcess, Workflow } from '../../../models/rule.model';
import { RulesEngineService } from '../../../services/rules-engine.service';
import { LayoutService } from '../../../../shared-modules/layouts/services/layout/layout.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-execution-history',
  templateUrl: './execution-history.component.html',
  styleUrls: ['./execution-history.component.scss']
})
export class ExecutionHistoryComponent implements OnInit {
  @Input() siteId: string = '';
  @Input() workflows: Workflow[] = [];
  @Input() executionHistory: WorkflowProcess[] = [];
  @Input() selectedWorkflowId: number | null = null;

  @Output() viewProcessDetails = new EventEmitter<WorkflowProcess>();
  @Output() viewWorkflowStatus = new EventEmitter<WorkflowProcess>();
  @Output() workflowFilterChanged = new EventEmitter<number | null>();
  @Output() refreshHistory = new EventEmitter<void>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  displayedColumns: string[] = ['workflowName', 'processId', 'startTime', 'duration', 'status', 'actions'];
  dataSource = new MatTableDataSource<WorkflowProcess>([]);

  constructor(
    private rulesService: RulesEngineService,
    private layoutService: LayoutService
  ) { }

  ngOnInit(): void {
    this.updateDataSource();
  }

  ngOnChanges(): void {
    console.log('ExecutionHistoryComponent.ngOnChanges - executionHistory:', this.executionHistory);
    this.updateDataSource();
  }

  ngAfterViewInit(): void {
    if (this.paginator) {
      this.dataSource.paginator = this.paginator;
    }
  }

  updateDataSource(): void {
    console.log('ExecutionHistoryComponent.updateDataSource - executionHistory:', this.executionHistory);
    if (this.executionHistory && this.executionHistory.length > 0) {
      console.log('Sample item:', this.executionHistory[0]);
    }
    this.dataSource.data = this.executionHistory || [];
    if (this.paginator) {
      this.dataSource.paginator = this.paginator;
    }
  }

  onWorkflowFilterChange(workflowId: number | null): void {
    this.workflowFilterChanged.emit(workflowId);
  }

  onViewProcessDetails(process: WorkflowProcess): void {
    this.viewProcessDetails.emit(process);
  }

  onViewWorkflowStatus(process: WorkflowProcess): void {
    console.log('ExecutionHistoryComponent.onViewWorkflowStatus - process:', process);

    // Make sure we have the required properties
    const processId = process.workflowProcessId || process.workFlowProcessId;
    const workflowId = process.workflowId || process.workFlowId;

    if (!processId || !workflowId) {
      console.error('Missing required properties for viewWorkflowStatus:',
        'workflowId =', workflowId,
        'processId =', processId);
      alert('Cannot view workflow status: Missing required parameters');
      return;
    }

    // Show spinner while loading
    this.layoutService.showSpinner();

    // Emit the event to the parent component
    this.viewWorkflowStatus.emit(process);
  }

  onRefreshHistory(): void {
    this.refreshHistory.emit();
  }

  getWorkflowName(workflowId?: number): string {
    // If the process already has a workflowName, use it
    if (!workflowId) return 'Unknown';

    // Try to find the workflow in the workflows array
    const workflow = this.workflows.find(w => w.workflowID === workflowId);
    return workflow ? workflow.name : `Workflow ${workflowId}`;
  }

  calculateDuration(process: WorkflowProcess): string {
    // Use firstExecutionTime as the start time
    const startTimeField = process.firstExecutionTime || process.startTime;
    if (!startTimeField) return 'N/A';

    // Use lastExecutionTime as the end time, or current time if not available
    const start = new Date(startTimeField).getTime();
    const end = process.lastExecutionTime ? new Date(process.lastExecutionTime).getTime() :
                process.endTime ? new Date(process.endTime).getTime() :
                new Date().getTime();

    const durationMs = end - start;
    const seconds = Math.floor(durationMs / 1000);

    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes} min`;
    } else {
      const hours = Math.floor(seconds / 3600);
      return `${hours} hr`;
    }
  }

  formatGuid(guid?: string): string {
    if (!guid) return '';

    // Split the GUID by hyphens and get the last part
    const parts = guid.split('-');
    if (parts.length > 0) {
      return parts[parts.length - 1];
    }

    // If the GUID doesn't have hyphens, return the last 8-12 characters
    return guid.length > 8 ? guid.substring(guid.length - 8) : guid;
  }

  isValidGuid(guid: string): boolean {
    if (!guid) return false;

    // Regular expression for GUID format
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return guidRegex.test(guid);
  }

  getStatusText(process: WorkflowProcess): string {
    // If the process has a status property, use it
    if (process.status) return process.status;

    // Otherwise, determine status based on success and execution times
    if (process.lastExecutionTime === undefined) {
      return 'Running';
    } else if (process.success) {
      return 'Completed';
    } else {
      return 'Failed';
    }
  }
}
