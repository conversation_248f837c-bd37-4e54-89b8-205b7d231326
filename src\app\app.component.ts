import { Component, HostListener } from '@angular/core';
import { environment } from 'src/environments/environment';
import { SwUpdate } from '@angular/service-worker';
import { Router } from '@angular/router';
import { EpividianCommon } from './modules/utility/EpividianCommon';
import { TimeoutSessionService } from './shared-services/timeout-session-service';
import { EnvironmentService } from './modules/shared-modules/layouts/dialogs/support-details-dialog/supportService.service';
import { EnvironmentType} from './shared-services/configuration.service';
import { Title } from '@angular/platform-browser';
import { ApiHandler } from './shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from './shared-services/ep-api-handler/api-option-enums';

const ROUTE_SNAPSHOT = 'postUpdateRoute';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})

export class AppComponent {
  updateAvailable = false; 
  private updateCheckInterval: any;
  title = 'Chorus Portal';
  
  constructor(private swUpdate: SwUpdate, 
              private router: Router, 
              private epividianCommon: EpividianCommon,
              private timeoutSessionService: TimeoutSessionService, 
              private envService: EnvironmentService,
              private titleService: Title,
              private apiHandler: ApiHandler) {
                //will Remove this later after support role is added
                (window as any).envService = this.envService;

              }
  ngOnInit() {

    // Store the current time as the last active time in local storage
    this.epividianCommon.upsertToStorage("lastActiveTime", new Date(), true, true);

    // Navigate to a specific route if it was saved before an update
    const postUpdateRoute = localStorage.getItem(ROUTE_SNAPSHOT);
    if (postUpdateRoute) {
      localStorage.removeItem(ROUTE_SNAPSHOT); // Clear the stored route
      if (this.epividianCommon.LoadValidSessionObj())
      {
        this.router.navigateByUrl(postUpdateRoute); // Navigate to the stored router
      } else
      {
        this.router.navigateByUrl('/'); // Navigate to root no access
      }
    }
    
     // Service Worker logic to detect updates
     if (this.swUpdate.isEnabled) {
      this.swUpdate.checkForUpdate();
      this.swUpdate.versionUpdates.subscribe((evt) => {
        if (evt.type === 'VERSION_READY') {
          this.updateAvailable = true;
          // Note: Now you're setting updateAvailable based on version being ready
        }
      });
    }

    this.envTitleDisplay();

  }
  
  reloadApp() {
    localStorage.setItem(ROUTE_SNAPSHOT, '/Dashboard'); // Store the route to navigate to after the update
    window.location.reload(); // Reloads the app
  }

  postponeUpdate(): void {
    this.updateAvailable = false; // Hide the overlay
    if (this.updateCheckInterval) {
      clearTimeout(this.updateCheckInterval); // Clear previous timer if it exists
    }
    this.updateCheckInterval = setTimeout(() => {
      this.updateAvailable = true; // Show the overlay again after 15 minutes
    }, 900000); // 15 minutes in milliseconds
  }

  // Event listener for document visibility change events
  // Validates the session and handles re-initializing the session tracking on visibility change.
  @HostListener('document:visibilitychange', ['$event'])
  onFocus(visibilityEvent: any): void {
    
    // If the current URL contains '/Auth/' or
    // If the tab is no longer visible exit.
    if (this.router.url.includes('/Auth/') || this.router.url.includes('/Download/')  || visibilityEvent.currentTarget.visibilityState !== "visible") {
      this.timeoutSessionService.clearSessionTracker();
      return;
    }

    // Checks the window last active time and the last refresh check/update time
    let lastRefreshCheck = this.epividianCommon.readFromStorage("lastRefreshCheck");
    let currentTime = new Date();
    if (!lastRefreshCheck)
    {
      lastRefreshCheck = new Date();
      this.epividianCommon.upsertToStorage("lastRefreshCheck", lastRefreshCheck, true, true);

      // Back date the lastRefreshCheck by 16 seconds to force the below code to fire.
      lastRefreshCheck.setSeconds(lastRefreshCheck.getSeconds() - 16);
    }
    else
    {
      lastRefreshCheck = new Date(lastRefreshCheck);
    }

    // We have performed this check in the last 15 seconds then skip
    let timeDifference = currentTime.getTime() - lastRefreshCheck.getTime();
    if (timeDifference > 15000)
    {
        lastRefreshCheck = new Date();
        const jwtToken = this.epividianCommon.GetSessionObjFromLocalStorage();

        // Assuming LoadSessionTimeout returns the session validity period
        if (jwtToken)
        {
          const sessionValidityPeriod = this.epividianCommon.LoadSessionTimeout(jwtToken, 0) * 60 * 1000;
          // Check if the timeStamp is within the current time (session is still valid)
          let isSessionValid = timeDifference <= sessionValidityPeriod;

          // If the session is valid mark as such, else clear the session
          if (isSessionValid) {
            this.userActivityDetected();
            return;
          }
        }

        let getSession = this.epividianCommon.GetSessionObjFromLocalStorage();
        if (getSession?.access_token)
        {
          let logoutRoute = ApiRoutes.Logout.replace('{{fromInactive}}', true.toString());
          this.apiHandler.Post(ApiTypes.AuthProvider, logoutRoute, '', true, true).subscribe((s) => {
            this.epividianCommon.ClearSession();
            this.timeoutSessionService.clearSessionTracker();
            this.router.navigateByUrl('/');
          });
        }
        return;
    }

    this.userActivityDetected();
  }
  
  // Below are the various user events we watch for and consider as activity
  @HostListener('window:scroll', ['$event'])
  onWindowScroll(event: Event): void {
    this.userActivityDetected();
  }

  @HostListener('window:keydown', ['$event'])
  onWindowKeydown(event: KeyboardEvent): void {
    this.userActivityDetected();
  }

  @HostListener('window:click', ['$event'])
  onWindowClick(event: MouseEvent): void {
    this.userActivityDetected();
  }

  // Adjusts the application title based on the environment
  private envTitleDisplay() {

    if (environment.env == EnvironmentType.Production) {
      this.title = "Chorus Portal"
    } else {
      this.title = environment.env + " Chorus Portal";
    }

    this.titleService.setTitle(this.title);
  };

  private userActivityDetected(){
    const currentTimeStamp = new Date();
    this.epividianCommon.upsertToStorage("lastActiveTime", currentTimeStamp, true, true);
    this.timeoutSessionService.updateWindowLastActiveTime(currentTimeStamp);
  }

}
