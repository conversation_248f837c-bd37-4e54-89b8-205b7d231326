.PatientinfoLable {
    color: #6e6e6e;
    float: left;
    font-weight: 500;
    font-size: smaller;
    padding: 7px 0 0 10px;
    // width: 114px;
  }
  
  .PatientinfoLableDrag {
    color: #6e6e6e;
    float: left;
    font-weight: 500;
    font-size: smaller;
    padding: 7px 0 0 10px;
    text-align: center; 
    padding-top: 15px; 
    float: none; 
    width: 96%;   
  }
  
  .PatientInfoDDLDiv {
    float: left;
    padding-left: 2px;
  }
  
  .dropdownMenu {
    width: 340px;
    font-size: smaller;
    height: 25px;
  }
  
  .dropdownMenuSmall {
    width: 150px;
    font-size: smaller;
    height: 25px;
  }

  .dropdownMenuSmallWidth {
    width: 52px;
    font-size: smaller;
    height: 25px;
    position: absolute;
  }
  
  .currentLookback {
    float: left; 
    position: relative; 
    font-weight: 500; 
    padding: 0px 0px 0px 80px; 
    width: 100%;
    font-size: smaller;
  }
  
  /*CLEAR TEXT BOX SCRIPT ADD IN COMMON DOWN SIDE*/
  .clearable {
    // background: #fff url("../css/Images/cleartext.gif") no-repeat right -10px center !important;
    border: 1px solid #999 !important;
    padding: 3px 18px 3px 4px !important; /* Use the same right padding (18) in jQ! */
    border-radius: 3px !important;
    transition: background 0.4s !important;
    font-size: smaller;
  }
  
  .clearable.x {
    background-position: right 5px center !important;
  }
  
  .clearable.onX {
    cursor: pointer !important;
  }
  
  .clearable::-ms-clear {
    display: none;
    width: 0;
    height: 0;
  }
  
  .field-validation-error {
    font-size: 12px;
    color: #FF0000;
    font-weight: bold;
    text-align: left;
    margin-bottom: 5px;
    line-height: 10px;
  }
  
  .txtcurrentLookback {
    width: 50px; 
    border-radius: 3px;
  }
  
  .txtAddRegimen {
    background-color: white;
    min-height: 50px;
    cursor: pointer;
    border: 1px solid #a1a1a1;
    margin-top: 10px;
    float: left;
    width: 99%;
    margin-left: 40px;
  }
  
  .txtAddRegimen:hover {
    background-color: #DBDBDB;
  }
  
  .droppable {
    width: 85%; padding: 0px 10px 10px 0; float: left;
  }
  
  .RunMedReport {
    float: left;
    margin-left: 10px;
  }
  
  .CMbtnDisabled {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    border: medium none;
    cursor: pointer;
    float: left;
    font-size: 14px;
    padding: 0 10px;
    text-align: center;
    height: 30px;
    width: 115px;
    color: white
  }

  .CMbtnDisabledRightAlign {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    border: medium none;
    cursor: pointer;
    float: right;
    font-size: 14px;
    padding: 0 10px;
    text-align: center;
    height: 30px;
    width: 115px;
    color: white
  }
  
  .CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    cursor: pointer;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    height: 30px;
    width: 115px;
    float: right;
    color: white;
    margin-right: 15px;
  }

  .CMbtnLeftAlign {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    cursor: pointer;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    height: 30px;
    width: 115px;
    float: left;
    color: white;
    margin-right: 15px;
  }
  
  .CMbtn:hover {
    background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
  }
  
  /* Main Regimen Scroll */
  .LeftCustomPatientInfoDDLMedium {
    height: 30px;
    /*padding-top: 5px;*/
    padding-left: 10px;
    float: left;
    width: 100%;
    position: relative; 
    padding-bottom: 3px; 
    padding-top:5px;
  }
  
  .mat-tree {
    background-color: #eff1f6 !important;
  }
  
  .contentDroppedElement {
    display: flex;
    padding-bottom: 10px;
  }
  
  .RegimenHeaderlable {
    color: #6e6e6e;
    float: left;
    font-weight: 500;
    padding: 4px 0 0 5px;
    width:auto;
    font-size: smaller;
  }
  
  .MainRegimenScroll {
    width: 105%;
    float: left;
    margin-bottom: 15px;
    margin-left: 15px;
    width: 105%;
  }

  #HeadCustomMedicationTree{
    width: 100%;
    height: 70vh;
    float: left
  }

  .CustomMedicationTree {
    height: 100%;
    overflow-y: scroll;
  }

  .CustomMedicationTree::-webkit-scrollbar {
    background-color: rgb(225, 225, 225);
    border-radius: 5px;
  }
  
  .MainRegimens {
    width: 90%;
    padding: 25px 10px 10px 0;
    float: left;
  }
  
  .mfs-container {
    color: #666666;
    font-family: Helvetica,Arial;
    font-size: 11px;
    height: auto;
    position: relative;
    text-align: left;
    width: 184px;
  }
  
  .mfs-container select {
    position: absolute;
    width: 100%;
    top: 0;
    z-index: 1;
    opacity: 0;
  }
  
  .mfs-container.notouch select {
    // display: none;
  }
  /* Main Regimen Scroll */
  
  hr.divider {
    border: 1;
    height: 2px;
    width: 100%;
    background-image: -webkit-linear-gradient(left, #f0f0f0, #0097B9, #f0f0f0);
    background-image: -moz-linear-gradient(left, #f0f0f0, #0097B9, #f0f0f0);
    background-image: -ms-linear-gradient(left, #f0f0f0, #0097B9, #f0f0f0);
    background-image: -o-linear-gradient(left, #f0f0f0, #0097B9, #f0f0f0);
  }
  
  .divider-div {
    // height: auto;
    width: 99.2%;
    float: left;
  }
  
  // Divider for search medication and tree structure menus
  .hr-text {
    line-height: 1px;
    position: relative;
    outline: 0;
    border: 0;
    color: #0097B9;
    text-align: center;
    height: 0.5em;
    opacity: 2.5;
    &:before {
      content: '';
      // use the linear-gradient for the fading effect
      // use a solid background color for a solid bar
      background: linear-gradient(to right, transparent, #0097B9, transparent);
      position: absolute;
      left: 0;
      top: 50%;
      width: 100%;
      height: 1px;
    }
    &:after {
      content: attr(data-content);
      position: relative;
      display: inline-block;
      color: 0097B9;  
      padding: 0 .5em;
      line-height: 1.5em;
      // this is really the only tricky part, you need to specify the background color of the container element...
      color: #0097B9;
      background-color: #fcfcfa;
    }
  }

  .droppedContentOuterDiv {
    background-color: transparent;
    min-height: 50px;
    cursor: pointer;
    float: left;
    width: 99%;
    margin-left: 20px;
  }
  
  .droppedContentDiv {
    background-color: transparent;
    min-height: 50px;
    cursor: pointer;
    border: 1px solid #a1a1a1;
    margin-top: 10px;
    float: left;
    width: 95%;
    margin-left: 60px;
  }
  
  .droppedContentDiv:hover {
    background-color: #DBDBDB;
  }
  
  .scrollableContentDiv {
    width: 85%;   
    padding: 0px 10px 10px 0; 
    float: left; 
   }
  
   #contentInside {
    // width: 100px;  
    height: 70px;  
    width: 90%;
    margin: 7px;  
    padding: 15px;
    border: 1px solid black;  
    display: block;  
    width: max-content;  
    margin-left: 110px;  
  }
  
  button {
    border: none;
    background: transparent;
  }
  
  button:hover {
    cursor: pointer;
  }
  
  /**
  * TREE DRAG AND DROP STYLING
  */
    ::ng-deep mat-tree mat-tree-node {  
    color: #000 !important;
    // user-select: none;
    cursor: move !important;
    font-weight: bold !important;
    min-height: 25px !important;
    font-family: Arial,Helvetica,sans-serif !important;
    font-size: 12px !important
  }
  
  /* items moving away to make room for drop */
  .cdk-drop-list {
    padding: 10px;
  }
  
  .cdk-drop-list-dragging .mat-tree-node:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  /* item being dropped */
  .cdk-drag-animating {
    transition: transform 200ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  .example-box.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1) !important;
  }
  
  .example-chip .cdk-drop-list-dragging {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1) !important;
  }

  .mat-chip.mat-standard-chip {
    background-color: #5bc0de;;
    color: #FFF;
  }

  .mat-standard-chip {
    min-height: 22px;
    border-radius: 3px;
  }

  .genericNM {
    padding-left: 5px;
  }