import { Component, HostListener, OnInit } from '@angular/core';
import { NgxSpinnerService } from "ngx-spinner";
import { Router } from '@angular/router';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Api<PERSON>andler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { IChangePassword } from '../models/changepassword-model';
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { LoginService } from '../services/login/login.service';
import { AuthRedirectOption } from '../models/tfaMethod-enum';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
	selector: 'app-change-password',
	templateUrl: './change-password.component.html',
	styleUrls: ['./change-password.component.scss'],
})
export class ChangePasswordComponent implements OnInit {
	resetIsSucess: boolean = false;
	errorMsg: string = "Choose a new password";
	hidenew: boolean = true;
	hideconfirm: boolean = true;
	url: string;
	queryParams: any;
	path: string;
	isValidPassword: boolean = true;
	newPasswordInput: string = "";
	confirmPasswordInput: string = "";
	isValidUser: boolean = false;

	constructor(private apiHandler: ApiHandler,
		private spinnerService: NgxSpinnerService,
		private router: Router,
		private _loginService: LoginService,
		private userContext: UserContext,
		private auditService: AuditService) {

		this.auditService.setPageAudit(Page.Login);

		this.url = this.router.url; // Gets the current URL.
		this.queryParams = this.router.parseUrl(this.url).queryParams; // Gets the query parameters from the URL.
		this.path = this.router.parseUrl(this.url).root.children['primary'].segments.map(segment => segment.path).join('/'); // Gets the path from the URL
	}

	ngOnInit(): void {
		this.spinnerService.show().then(() => {
			// Only call the api if the query params are present.
			if (!this.queryParams.ref || !this.queryParams.token) {
				this.ApiCallUpdatePassword("", "");
			}
			else {
				this.spinnerService.hide();
				this.isValidUser = true;
			}
		});
	}

	// If the user presses the enter key, fire off the change password request.
	@HostListener('document:keydown.enter')
	handleEnterKey() {
		// NOTE: UpdatePasswordWithLink validates the values before calling the API.
		this.UpdatePasswordWithLink(this.newPasswordInput, this.confirmPasswordInput);
	}

	// When either the new password or confirm password field values are changed, then validate the entries.
	onChangePassword(newPassword: string, confirmPassword: string): boolean {
		this.isValidPassword = true;
		this.errorMsg = "";

		// Did they actually enter the same password twice.
		if (newPassword != confirmPassword) {
			this.errorMsg = "New password and confirm password should match";
			this.isValidPassword = false;
		}

		// Check Length.
		if (newPassword.length <= 7) {
			$('.passwordLength').css("color", "#36b4e5");
			this.errorMsg = "Password does not meet the requirements";
			this.isValidPassword = false;
		} else {
			$(".passwordLength").css("color", "#ffffff");
		}

		// Expression for password
		// between 5 - 15 characters which contain at least
		// one lowercase letter, one uppercase letter, one numeric digit, and one special character
		var verifyPassword = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s)/;

		if (newPassword.match(verifyPassword)) {
			$(".passwordAlphaNumeric").css("color", "#ffffff");
		} else {
			$('.passwordAlphaNumeric').css("color", "#36b4e5");
			this.errorMsg = "Password does not meet the requirements";
			this.isValidPassword = false;
		}

		return this.isValidPassword;
	}

	// Validate the passwords and call the API if appropriate.
	UpdatePasswordWithLink(newPassword: string, confirmPassword: string) {

		if (!this.onChangePassword(newPassword, confirmPassword) || this.isValidPassword == false) {
			return;
		}

		this.ApiCallUpdatePassword(newPassword, confirmPassword);
	}

	ApiCallUpdatePassword(newPassword: string, confirmPassword: string) {

		// If we have already reset the password exit early to prevent a bad state.
		if (this.resetIsSucess == true)
		{
			return;
		}

		// Display the loading screen.
		this.spinnerService.show();

		// Create our model.
		let updatedPassword: IChangePassword = {} as IChangePassword;
		updatedPassword.newPassword = newPassword;
		updatedPassword.confirmPassword = confirmPassword;
		updatedPassword.statusMessage = "ok";

		// If a tfa token is present send that as well.
		let tfaToken = "";
		if (this.queryParams.token != null) {
			tfaToken = "&token=" + this.queryParams.token;
		}

		// URI encode.
		let refToken = encodeURIComponent(this.queryParams.ref);

		// Generate the API path.
		const path = ApiRoutes.ResetPassword.replace('{{resetToken}}', refToken + tfaToken)

		// Request a password change now with the API.
		this.apiHandler
			.Post(ApiTypes.AuthProvider, path, JSON.stringify(updatedPassword), null, true)
			.subscribe((s: IChorusResponseToken | any) => {

			// We've received a response back, hide the loading screen.
			this.spinnerService.hide();

			// If the change password was not successful, report back why and EXIT EARLY!
			if (s.status != null && s.status == 400) {
				var msg = s.error[0];

				// BUG 25336
				// Product Management requested a custom/domain specific message here.
				if (msg === "Invalid User.") {
					this.errorMsg = "The temporary password link has expired. Return to the login page and click 'forgot password' in order to generate a new link.";
				} else {
					this.errorMsg = msg;
				}

				return;
			}

			this.isValidUser = true;

			// Detemine what next steps need to happen.
			// Those can be:
			// - Setup TFA
			// - Verify TFA
			// - Dashboard
			// 
			// There is a failsafe fallthrough should none of expected criteria be met 
			// that will result from a token expired condition.
			if (s.callback_redirect != null && s.callback_redirect == AuthRedirectOption.SetupTfa) {
				this.userContext.SaveTokenToLocalStorage(s);
				this.userContext.SetUsername(s.userName);
				if (this.queryParams.ref != null) {
					s.resetToken = this.queryParams.ref;
				}
				this.router.navigate(['/Auth/RegisterTfa'], { state: { digits: s.last4, userInfo: s } });
			} 
			else if (s.callback_redirect != null && s.callback_redirect == AuthRedirectOption.VerifyTfa) {
				if (this.queryParams.ref != null) {
					s.resetToken = this.queryParams.ref;
				}
				this.router.navigate(['/Auth/VerifyTfa'], { state: { digits: s.last4, tfaMethod: s.tfaMethod, isRedirect: 1, refreshToken: s.refreshToken, userInfo: s } });
			}
			else if (s.access_token != "" && s.access_token != undefined) {
				this.resetIsSucess = true;
				this.errorMsg = "User Password Update Success!";
				this.userContext.apihandler.setbearToken(s.access_token);

				// Let the user see that their password change was successful.
				// Then route the user to either the Dashboard or the Terms and Conditions page
				setTimeout(() => {
					this.userContext.SaveTokenToLocalStorage(s);
					this._loginService.UpdateUserLoginStatus();
					this._loginService.TermsDashboardRoute();
				}, 3500);
			}
			else if (s.callback_redirect != null && s.callback_redirect == AuthRedirectOption.ResetPassword) {
				// This is a special case that occurs via: ngOnInit
				// When this page is first loaded, we verify the reset token is valid by initiating 
				// an empty change password request. If we receive a redirect to ResetPassword we know to stay
				// here.

				return;
			}
			else {
				this.errorMsg = "Password Reset Token has expired, Please try again!";
				this.isValidUser = false;
			}

		})
	}

	newpasswordEvent() {
		this.hidenew = !this.hidenew;
	}
	
	confirmpasswordEvent() {
		this.hideconfirm = !this.hideconfirm;
	}
}
