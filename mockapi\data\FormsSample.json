{"FormName": "PrescriptionForm", "Fields": [{"FieldName": "DaysSupply", "FieldType": "number", "Label": "Days Supply for Quantity Dispensed", "SelectedValue": null, "isEditable": true, "isRequired": false, "LayoutOption": "row1", "ValidationCondition": "DaysSupply > 0", "Attributes": null}, {"FieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FieldType": "number", "Label": "Days supply written on original RX", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row1", "ValidationCondition": "DaysSupplyWritten > 0", "Attributes": null}, {"FieldName": "DispenseAs<PERSON><PERSON>ten", "FieldType": "number", "Label": "DAW Code", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row1", "ValidationCondition": "DispenseAsWritten > 0 | DispenseAsWritten < 5", "Attributes": null}, {"FieldName": "Dosage", "FieldType": "number", "Label": "Milligrams per pill or per injection", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row2", "ValidationCondition": "Dosage > 0", "Attributes": null}, {"FieldName": "NDC", "FieldType": "text", "Label": "NDC of drug dispensed (11-digit unformatted)", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row2", "ValidationCondition": "RegEx=^\\d{5}-\\d{3}-\\d{2}$", "Attributes": null}, {"FieldName": "Notes", "FieldType": "textarea", "Label": "Details or additional information", "SelectedValue": null, "isEditable": true, "isRequired": false, "LayoutOption": "row3", "ValidationCondition": "", "Attributes": {"DefaultValue": "No Notes"}}, {"FieldName": "ProductDescription", "FieldType": "text", "Label": "Product Name/Strength/Form", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row4", "ValidationCondition": "", "Attributes": null}, {"FieldName": "Quant<PERSON><PERSON><PERSON><PERSON>", "FieldType": "number", "Label": "Quantity Written on RX", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row4", "ValidationCondition": "QuantityWritten > 0", "Attributes": null}, {"FieldName": "RefillsAuthorized", "FieldType": "number", "Label": "Number of Refills specified by prescriber on prescription", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row4", "ValidationCondition": "", "Attributes": null}, {"FieldName": "DiagnosisCodes", "FieldType": "dropdown", "Label": "List of ICD-10 codes for this prescription", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row5", "ValidationCondition": "", "Attributes": {"Default": "prescriptionCode1", "1": "prescriptionCode1", "2": "prescriptionCode2"}}, {"FieldName": "PatientNeedsByDate", "FieldType": "date", "Label": "The Date when the patient needs the prescription", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row6", "ValidationCondition": "DateTime.Parse(PatientNeedsByDate) > DateTime.Now", "Attributes": null}, {"FieldName": "OriginCode", "FieldType": "dropdown", "Label": "Code indicating the prescription's origin (ECL Code 419-DJ)", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row7", "ValidationCondition": "", "Attributes": {"DefaultValue": "Select Value", "0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5"}}, {"FieldName": "PriorAuthorizationExpirationDate", "FieldType": "date", "Label": "Expiration Date for Prior Authorization", "SelectedValue": null, "isEditable": true, "isRequired": false, "LayoutOption": "row8", "ValidationCondition": "DateTime.Parse(PriorAuthorizationExpirationDate) > DateTime.Now", "Attributes": null}, {"FieldName": "PriorAuthRequired", "FieldType": "dropdown", "Label": "Indicates whether a prior authorization is required", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row8", "ValidationCondition": "", "Attributes": {"DefaultValue": "No", "1": "Yes", "2": "No"}}, {"FieldName": "Directions", "FieldType": "text", "Label": "Prescription directions", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row9", "ValidationCondition": "", "Attributes": null}, {"FieldName": "DispensedQtyUnitOfMeasure", "FieldType": "text", "Label": "Unit of measure for Dispensed Quantity", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row10", "ValidationCondition": "", "Attributes": null}, {"FieldName": "ExhaustDate", "FieldType": "date", "Label": "Date when the patient is expected to run out of medication", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row11", "ValidationCondition": "DateTime.Parse(ExhaustDate) > DateTime.Now", "Attributes": null}, {"FieldName": "testCheckbox", "FieldType": "checkbox", "Label": "test checkbox", "SelectedValue": null, "isEditable": true, "isRequired": true, "LayoutOption": "row12", "ValidationCondition": "", "Attributes": {"1": "option 1", "2": "option 2", "3": "option 3"}}], "Layout": {"LayoutType": "Group", "LayoutAttributes": null}}