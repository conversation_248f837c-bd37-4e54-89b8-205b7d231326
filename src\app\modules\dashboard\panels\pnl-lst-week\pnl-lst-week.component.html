<div class="searchCriteriaDiv">
        <!-- Reporting Week Row -->
        <div class="field-row">
            <label class="field-label" for="ddlLstWeek">Reporting Week*:</label>
            <select class="form-select form-select-sm week-select" (change)="readyToRun()"
            name="ddlLstWeek" id="ddlLstWeek" [formControl]="rptpanLstWeek" title="Select reporting week">
                <option *ngFor="let option of lstWeekData" [value]="option.key | date">{{option.value| date}}</option>
            </select>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="panelService.InitBoldReport()" id="reportViewer_Control_viewReportClick"
            aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
</div>
