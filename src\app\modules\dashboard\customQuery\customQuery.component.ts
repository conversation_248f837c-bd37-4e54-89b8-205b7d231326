import { Component, Inject, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable'
import { NgxSpinnerService } from 'ngx-spinner';
import { firstValueFrom, Observable, Subject } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { CustQueryReportData } from '../models/custom-query-model';
import { CriteriaSummaryResults, PatientSelectionCriteria } from '../panels/models/custom-query-model';
import { ARTMedication, HCVMedication, HCVStatus, HIVStatus, LastActiveVisit } from '../panels/models/patient-criteria.model';
import { PanelService } from '../panels/PanelService';
import { IRegimenPanel } from '../panels/pnl-custom-query/regimen-builder/regimen-builder.models';
import { ReportPanelTypes } from '../panels/report-panel-enums';
import { formatDate } from '@angular/common';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
  selector: 'customQuery',
  templateUrl: './customQuery.component.html',
  styleUrls: ['./customQuery.component.scss']
})
export class CustomQueryComponent implements OnInit {

  //loading$: Observable<boolean>;
  customQuery!: CustomQueryComponent;
  //dataSource!: CustomQueryDataSource;
  ds: MatTableDataSource<CustQueryReportData> = new MatTableDataSource<CustQueryReportData>();
  //Creates the References to the html Contains to populate the panel controls
  @ViewChild('panelContainerRef', { read: ViewContainerRef, static: true })
  public _panelContainer!: ViewContainerRef;

  public reportData: any;

  //Current Report Columns sequence need to lin up
  displayedColumns= ["mrn","patientName","birthDate","age","genderDesc","ethnicityDesc","raceDesc","lastVisitDate",
  "hivDiagDate","hivDiagFlg","hcvDiagFlg","lastVacsMortalityRatio","lastCD4CollectDate","lastCD4Count","nadirCD4Date","nadirCD4Count",
  "lastViralLoadCollectDate","lastViralLoadCount","lastViralLoadNonDetInd"];

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  demographicId = 0;
  dataCount = 0;

  criteriaSummary: CriteriaSummaryResults = new CriteriaSummaryResults();
  public regimenSummary: IRegimenPanel[] = [];
  //These are temporary
  hidePanel: boolean = false;
  anotherTest: any;
  site: string = '';

  // Panel state management for new report selection widget
  public reportNavCollapsed: boolean = false;

  // Current report name for dynamic panel title
  public currentSelectedReport: string = "";

  constructor(private userContext: UserContext,
              public panelService: PanelService,
              public activeRoute: ActivatedRoute,
              public router: Router,
              @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
              private layoutService: LayoutService,
              private auditService: AuditService)
    {
      this.regimenSummary = PanelService.createdRegimenList;
     //userContext Must Be assigned first other Serice may depend on stuff there..
      let tmpSite = this.activeRoute.snapshot.paramMap.get('siteId');

      if(tmpSite)
      {
        this.site = tmpSite;
      }

      this.auditService.setPageAudit(Page.CustomQuery);

      this.userContext.SetCurrentSite(Number(this.site));
      let dataDate = this.layoutService.GetDateLastUpdated(this.site);
      this.panelService.CreateReportPanel(ReportPanelTypes.pnlCustomQuery).then((reportPanelRef: any) => {
        this._panelContainer.createComponent(reportPanelRef);
      });

      //this.loading$ = this.dataSource.loading$;
  }

  ngOnInit(): void {
    // Check sessionStorage for panel collapse state
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';

    // Apply collapse logic: collapse if flag is set AND it's not initial navigation
    if (shouldCollapse && !isInitialNavigation) {
      // User selected a specific report after initial navigation, collapse the reports panel
      this.reportNavCollapsed = true;
    }

    // Always clear the collapse flag after checking it
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }

    // If this is not initial navigation and no collapse flag, set initial navigation flag for direct access
    if (!isInitialNavigation && !shouldCollapse) {
      sessionStorage.setItem('chorus_initial_reports_navigation', 'true');
    }

     //Handles the report data from the api
    this.customQuery = this.activeRoute.snapshot.data["customQuery"];
  }

  ngOnDestroy()
  {
    this.panelService.RunReportSub.complete();
  }
  ngAfterViewInit() {

   if (PanelService.createdRegimenList.length > 0)
    {
      this.anotherTest = PanelService.createdRegimenList;
    }

    //Subscribes to the panService Run Report Event
    this.panelService.RunReportSub = new Subject<any>();
    this.panelService.RunReportSub.subscribe(rptData => {
       this.spinnerService.show();

      try {
        this.criteriaSummary = this.CustomQueryShowCriteriaSummary(rptData.PatientSelectionCriteria[0]);

        this.getData(rptData).subscribe({
          next: (rawData: CustQueryReportData[]) => {
            this.hidePanel = true;

            this.ds = new MatTableDataSource<CustQueryReportData>(rawData);
            this.ds.paginator = this.paginator;
            this.ds.sort = this.sort;
            this.spinnerService.hide();
          },
          error: (error) => {
            console.error('CustomQuery: API call failed:', error);
            this.spinnerService.hide();
            // You might want to show an error message to the user here
          }
        });
      } catch (error) {
        console.error('CustomQuery: Error processing report data:', error);
        this.spinnerService.hide();
      }
    });
}

//shows the report panel
showPanel()
{
  this.hidePanel = false;
}

  //Gets the Report Data from the api
  getData(apiPostData: any): Observable<any>
  {
    try {
      // Sanitize Unicode characters that might cause HTTP header issues
      const sanitizedData = this.sanitizeUnicodeForApi(apiPostData);
      apiPostData = JSON.stringify(sanitizedData);
    } catch (error) {
      console.error('CustomQuery: Error stringifying API data:', error);
      throw error;
    }

    let rptDataUrl = ApiRoutes.CustomQueryData.replace("{{siteid}}", this.site);
    return this.userContext.apihandler.Post<CustQueryReportData[]>(ApiTypes.V2,rptDataUrl,apiPostData);
  }

  // Sanitize Unicode characters that might cause HTTP header encoding issues
  private sanitizeUnicodeForApi(data: any): any {
    if (typeof data === 'string') {
      // Replace common Unicode characters with ASCII equivalents
      return data
        .replace(/–/g, '-')  // En dash to hyphen
        .replace(/—/g, '-')  // Em dash to hyphen
        .replace(/'/g, "'")  // Smart quote to straight quote
        .replace(/'/g, "'")  // Smart quote to straight quote
        .replace(/"/g, '"')  // Smart quote to straight quote
        .replace(/"/g, '"')  // Smart quote to straight quote
        .replace(/…/g, '...') // Ellipsis to three dots
        .replace(/®/g, '(R)') // Registered trademark
        .replace(/™/g, '(TM)') // Trademark
        .replace(/°/g, ' deg'); // Degree symbol
    } else if (Array.isArray(data)) {
      return data.map(item => this.sanitizeUnicodeForApi(item));
    } else if (data && typeof data === 'object') {
      const sanitized: any = {};
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          sanitized[key] = this.sanitizeUnicodeForApi(data[key]);
        }
      }
      return sanitized;
    }
    return data;
  }


//This formats the criteria summary section of the report
 CustomQueryShowCriteriaSummary(currentReportData: PatientSelectionCriteria): CriteriaSummaryResults {
    let tmpCriteriaSummary = new CriteriaSummaryResults();
    let criteriaKeys = Object.keys(currentReportData);
    criteriaKeys.forEach(key => {
      //Doesn't display none changed or all marked options in summary lists
      if (currentReportData[key]!="-1" && currentReportData[key]!="|" && currentReportData[key]!='')
      {
      switch (key) {
          case "Sex":
            tmpCriteriaSummary.column1 += '<li> Patient Sex: ' + this.GetTextForSummary(key,currentReportData.Sex) + '</li>';
            break;
          case "Ethnicity":
            tmpCriteriaSummary.column2 += '<li> Patient Ethnicity: ' + this.GetTextForSummary(key,currentReportData.Ethnicity) + '</li>';
            break;
          case "PatientRace":
            tmpCriteriaSummary.column1 +=  '<li> Patient Race: ' + this.GetTextForSummary(key,currentReportData.PatientRace) + '</li>';
            break;
          case "Age":
            tmpCriteriaSummary.column2 +=  '<li> Patient Age: ' + this.GetGtLtForSummary(currentReportData.Age) + '</li>';
            break;
          case "HivStatus":
            tmpCriteriaSummary.column1 += '<li> HIV Status: ' + this.GetTextForSummary(key,currentReportData.HivStatus) + '</li>';
            break;
          case "ViralLoad":
            tmpCriteriaSummary.column2 += '<li> Last Viral Load: ' + this.GetGtLtForSummary(currentReportData.ViralLoad) + '</li>';
            break;
          case "CDCount":
            tmpCriteriaSummary.column1 += '<li> Last CD4 Count: ' + this.GetGtLtForSummary(currentReportData.CDCount) + '</li>';
            break;
          case "NadirCD4Count":
            tmpCriteriaSummary.column2 +=  '<li> Nadir CD4 Count: ' + this.GetGtLtForSummary(currentReportData.NadirCD4Count) + '</li>';
            break;
          case "HCVStatus":
            tmpCriteriaSummary.column2 += '<li> HCV Status: ' + this.GetTextForSummary(key,currentReportData.HCVStatus) + '</li>';
            break;
          case "ARTMedication":
            if(!this.panelService.isDefinedArt())
              tmpCriteriaSummary.column1 += '<li> ART Medication: ' + this.GetTextForSummary(key,currentReportData.ARTMedication) + '</li>';
              break;
          case "HCVMedication":
            if(!this.panelService.isDefinedHCV())
              tmpCriteriaSummary.column2 += '<li> HCV Medication: ' + this.GetTextForSummary(key,currentReportData.HCVMedication) + '</li>';
              break;
          case "PatientStatus":
            tmpCriteriaSummary.column1 += '<li> Patient Status: ' + this.GetTextForSummary(key,currentReportData.PatientStatus) + '</li>';
            break;
          case "LastActiveVisit":
            tmpCriteriaSummary.column2 += '<li> Last Active Visit: ' + this.GetTextForSummary(key,currentReportData.LastActiveVisit) + '</li>';
            break;
          case "PatientLocation":
            tmpCriteriaSummary.column1 += '<li> Patient Location: ' + this.GetTextForSummary(key,currentReportData.PatientLocation) + '</li>';
            break;
          case "PrimaryProvider":
            tmpCriteriaSummary.column2 += '<li> Primary Provider: ' + this.GetTextForSummary(key,currentReportData.PrimaryProvider) + '</li>';
            break;
          default:

            }

          }
          });
      return tmpCriteriaSummary;
  }

  //Gets the text for the Greater Than Less Than Selection
  //splits lt|gt and handles 1 as Undetectable else All
  GetGtLtForSummary(gtltValue: string)
  {
    if (gtltValue != undefined)
    {
      let gtltSplit = gtltValue.split("|");

      if (gtltValue=="1")
      {
        return "Undetectable";
      }
      else if(gtltSplit[0]!="" && gtltSplit[1]!="") {
        return "Between "  + gtltSplit[1] + " and " + gtltSplit[0]
      }
      else if(gtltSplit[0]!="")
      {
        return "Less Than  "  + gtltSplit[0];
      }
      else if(gtltSplit[1]!="")
      {
        return "Greater Than " + gtltSplit[1];
      }
      else{
        return "All";
      }
    }
    return "All";
  }

  //Gets the text for any object on the form
  GetTextForSummary(formKeyName: string, selectedValue: string): string
  {
      switch (formKeyName) {
          case "Sex":
            return PanelService.FormControlsData.sex.filter(f=>f.code==selectedValue)[0].description;
          case "Ethnicity":
            return PanelService.FormControlsData.ethnicity.filter(f=>f.code==selectedValue)[0].description;
          case "PatientRace":
            return PanelService.FormControlsData.race.filter(f=>f.code==selectedValue)[0].description;
          case "HivStatus":
            return HIVStatus.filter(f=>f.value==selectedValue)[0].text;
          case "HCVStatus":
            return HCVStatus.filter(f=>f.value==selectedValue)[0].text;
          case "ARTMedication":
            return ARTMedication.filter(f=>f.value==selectedValue)[0].text;
          case "HCVMedication":
            return HCVMedication.filter(f=>f.value==selectedValue)[0].text;
          case "PatientStatus":
            return PanelService.FormControlsData.patientStatus.filter(f=>f.code==selectedValue)[0].description;
          case "LastActiveVisit":
            return LastActiveVisit.filter(f=>f.value==selectedValue)[0].text;
          case "PatientLocation":
            return PanelService.FormControlsData.patientLocation.filter(f=>f.code==selectedValue)[0].description;
          case "PrimaryProvider":
            return PanelService.FormControlsData.patientProvider.filter(f=>f.code==selectedValue)[0].description;
          default:
      }

    return "";
  }

  //Prints the report to PDF
  async printRpt() {
    const siteId = this.site.toString();

    const dataLastRunDate = await firstValueFrom(this.layoutService.GetDateLastUpdated(siteId));

    const doc = new jsPDF('l', 'pt', 'legal');
    const currentDate = new Date();
    const formattedDate = `${currentDate.getMonth() + 1}/${currentDate.getDate()}/${currentDate.getFullYear()}`;

    const tableRows = document.querySelectorAll('#rptViewer tr');
    const rowCount = tableRows.length - 1;

    const captionText = `Custom Patient Query\nRun Date: ${formattedDate},  ${dataLastRunDate}\n ${rowCount} Records`;
    doc.text(captionText, doc.internal.pageSize.getWidth() / 2, 30, { align: 'center' });

    autoTable(doc, {
      html: '#rptViewer',
      startY: 80,
      headStyles: { fontSize: 8 },
      styles: { fontSize: 8 },
      tableWidth: 'auto'
    });

    doc.save('CustomQuery.pdf');
  }

  // Prints the report to a csv file.
  printCSV()
  {
    var csvStr = this.ConvertToCSV(this.ds.filteredData);
    var currentDate = formatDate(new Date(), 'ddMMyyyy', 'en-US');
    this.saveFile('csv', csvStr, ('CustomQuery' + currentDate));
  }

  ConvertToCSV(objArray) {
    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
    var headers = "MRN, Patient Name, Birth Date, Age, Gender, Ethnicity, Race, Last Visit Date, HIV Diag Date, HIV Status, HCV Status, Last Vacs Mortality Ratio, Last CD4 Collection Date, Last CD4 Count, Nadir CD4 Collection Date, Nadir CD4 Count, Last Viral Load Collection Date, Last Viral Load Count, Last Viral Load Non-Detect Indicator";
    var str = headers + '\r\n';

    for (var i = 0; i < array.length; i++) {
        var line = '';
        //Get data for each of the columns that are in the displayColumns list
        this.displayedColumns.forEach(element => {
          if (line != '')
          {
            line += ',';
          }
          if (element == "hivDiagFlg" || element == "hcvDiagFlg")
          {
            line += '"' + this.getStatusCodeValue(array[i][element]) + '"';
          }
          else
          {
            line += '"' + array[i][element] + '"';
          }
        });
        str += line + '\r\n';
    }
    return str.replaceAll("null", "");
  }

  getStatusCodeValue (value: number) : string{
    switch(value)
    {
    case 1:
      return "Positive";
    case 0:
      return "Negative";
    default:
      return "Negative, Cured";
    }
  }

  saveFile(fileType: string, content: any, fileName: string ){
    const blob = new Blob([content], { type: 'text/csv'});
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = fileName;
    a.click();
  }

  patientFlowsheetRedirect(demographicId){
    const jsonString = JSON.stringify(this.criteriaSummary);

    // Save the JSON string to session storage
    sessionStorage.setItem('criteriaSummaryResults', jsonString);
    this.router.navigate(['/Dashboard/Report/' + this.site + '/PatientFlowsheet'], { queryParams: {DEMOGRAPHICS_ID: '[' + demographicId + ']'}});
  }

  // Panel management methods for new report selection widget
  toggleReportNav(): void {
    this.reportNavCollapsed = !this.reportNavCollapsed;
  }

  // Handle current report name change from report navigation component
  onCurrentReportChanged(reportName: string): void {
    this.currentSelectedReport = reportName;
  }

  // Handle report selection event - automatically collapse the reports panel
  onReportSelected(): void {
    this.reportNavCollapsed = true;
  }

  // Get the dynamic title for the Select Report panel
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }
}
