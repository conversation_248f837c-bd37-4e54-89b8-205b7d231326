<div class="searchCriteriaDiv">
        <!-- Date Fields Row -->
        <div class="date-fields-row">
            <div class="date-field-group">
                <label class="field-label" for="rptpanFrmDate">From Date*:</label>
                <input type="date" (change)="readyToRun()" mat-formfield name="rptpanFrmDate"
                id="rptpanFrmDate" [formControl]="rptpanFrmDate" class="date-input" title="Select from date">
            </div>
            <div class="date-field-group">
                <label class="field-label" for="rptpanToDate">To Date*:</label>
                <input type="date" (change)="readyToRun()" mat-formfield name="rptpanToDate"
                id="rptpanToDate" [formControl]="rptpanToDate" class="date-input" title="Select to date">
            </div>
        </div>

        <!-- Error Message -->
        <div class="error-row" *ngIf="fg.invalid">
            <p class="error-message">Please add a valid from and to date</p>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="runReport()" id="reportViewer_Control_viewReportClick"
            [disabled]="!allowReport" aria-describedby="reportViewer_Control_viewReportClick"
            [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
</div>
