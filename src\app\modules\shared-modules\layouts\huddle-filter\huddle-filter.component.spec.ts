import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HuddleFilterComponent } from './huddle-filter.component';

describe('HuddleFilterComponent', () => {
  let component: HuddleFilterComponent;
  let fixture: ComponentFixture<HuddleFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HuddleFilterComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(HuddleFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
