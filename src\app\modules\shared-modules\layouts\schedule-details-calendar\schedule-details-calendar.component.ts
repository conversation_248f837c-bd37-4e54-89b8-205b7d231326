import { ChangeDetector<PERSON><PERSON>, Component, EventE<PERSON>ter, OnDestroy, Output } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ScheduleDetailsCalendarService } from './services/schedule-details-calendar.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ActivatedRoute } from '@angular/router';
import { IData, IParamGroup, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { ILocationGroup } from '../models/location-group.model';
import { MatCalendar, MatDatepickerModule } from '@angular/material/datepicker';
import { ILocation } from '../models/location.model';
import { AppointmentProviders } from 'src/app/modules/dashboard/models/Appointment-Info-model';
import { PROVIDER_GROUP_LABELS, ProviderGroupKey } from 'src/app/shared/constants/provider-group-labels';
import { HuddleCalendar } from '../models/huddle-calendar.model';
import { RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'epividian-schedule-details-calendar',
  templateUrl: './schedule-details-calendar.component.html',
  styleUrl: './schedule-details-calendar.component.scss'
})
export class ScheduleDetailsCalendarComponent implements OnDestroy {
  public selectedDropModifier: string = "Location";
  public selectedDropValue: any; //{ key: number; value: string } | null = null;
  public selectedDate: Date = new Date();
  public currentDate: Date = new Date();
  public yesterdayDate: Date = new Date(new Date().setDate(new Date().getDate() - 1));
  public tomorrowDate: Date = new Date(new Date().setDate(new Date().getDate() + 1));
  public sections: any;
  public locations: IReportParamData = {} as IReportParamData;
  public siteLocationsList: any;
  public searchLocations: any;
  public providers: AppointmentProviders[] = [];
  public selectedLocation: ILocation = {} as ILocation;
  public selectedProvider: string = "";
  public siteId: string = "";
  public isSiteDirector = false;
  locationSearchControl = new FormControl();

  private destroy$ = new Subject<void>();

  @Output() calendarSelected: EventEmitter<HuddleCalendar> = new EventEmitter<HuddleCalendar>();

  constructor(
    public calendarService: ScheduleDetailsCalendarService,
    private userContext: UserContext,
  ) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    this.getLocations(this.siteId, new Date());
    this.getproviders(this.siteId, new Date());

    // Use takeUntil to properly manage subscription lifecycle
    this.userContext.hasRole(RoleTypes.Site_Director, Number(this.siteId))
      .pipe(takeUntil(this.destroy$))
      .subscribe(hasRoleOnCurrentSite => {
        this.isSiteDirector = hasRoleOnCurrentSite;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getLocations(siteId: string, date: Date){
    this.calendarService.getLocations(this.siteId, date)
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.locations = res;
        this.updateDropSections(this.selectedDropModifier);
      });
  }

  getLocationGroups(locations: IReportParamData){
    if(locations.data){
      if (this.isSiteDirector){
        this.siteLocationsList = Array.from(
          new Map(
            locations.data.map(item => [item.key.toString(), { id: item.key.toString(), name: item.value }])
          ).values()
        );
        this.searchLocations = this.siteLocationsList;
      }
      else{
        let locationgroups: any[] = [];
        locations.groups.forEach((group, i) => {
          let temp = {
            label: group.name,
            options: locations.data.filter((f) => f.groupId === i)
          };
          locationgroups.push(temp);
        });
        this.sections = locationgroups
      }
      // Set default location
      if (!this.selectedLocation.id || !locations.data.some(loc => loc.key.toString() == this.selectedLocation.id)){
          let location = {
            id: locations.data[0].key.toString(),
            name: locations.data[0].value
          }
          this.selectedLocation = location;
          this.selectedDropValue = locations.data[0];
          this.emitSelectedValues();
      }
    }
  }

  getproviders(siteId: string, date: Date){
    this.calendarService.getProviders(siteId,date)
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.providers = res;
        this.updateDropSections(this.selectedDropModifier);
      });
  }

  getProviderGroups(providers: AppointmentProviders[]){
    const groups = [...new Set(providers.map(item => item.group))];
    let provider: any[] = [];
    let providergroups: any[] = [];

    groups.forEach((group, i) => {
      let filteredData = providers.filter((f) => f.group === group);
      provider = filteredData.map(item => ({
          groupId: i,
          key: item.provideR_ID,
          value: item.fulL_NM
      }));
      let temp = {
        label: group,
        options: provider
      }

      // Apply user-friendly label mapping
      temp.label = PROVIDER_GROUP_LABELS[group as ProviderGroupKey] || group;

      providergroups.push(temp);
    });
    if (this.selectedDropModifier == "Provider"){
      if (this.selectedProvider == "" || !this.providers.some(p => p.provideR_ID.toString() === this.selectedProvider)){
        this.selectedProvider = providergroups[0].options[0].key;
        this.selectedDropValue = providergroups[0].options[0];
      }
      this.sections = providergroups;
    }
  }

  updateDropSections(modifier: string){
    if(modifier == "Provider"){
      this.selectedDropModifier = "Provider";
      this.getProviderGroups(this.providers)
      this.selectedLocation = {} as ILocation;
      this.emitSelectedValues();
    }
    else{
      this.selectedDropModifier = "Location";
      this.getLocationGroups(this.locations);
      this.selectedProvider = "";
      this.emitSelectedValues();
    }
  }

  optionSelected(selectedValue: { key: number; value: string } | null){
    if (selectedValue != null){
      if(this.selectedDropModifier == "Provider"){
        this.selectedProvider = selectedValue.key.toString();
        this.emitSelectedValues();
      }
      else{
        let location = {
          id: selectedValue.key.toString(),
          name: selectedValue.value
        }
        this.selectedLocation = location;
        this.emitSelectedValues();
      }
    }
  }

  setDate(day: string, calendar: MatCalendar<Date>){
    switch (day) {
      case 'Yesterday':
        this.selectedDate = this.yesterdayDate
        break;

      case 'Today':
        this.selectedDate = this.currentDate;
        break;

      case 'Tomorrow':
        this.selectedDate = this.tomorrowDate
        break;
    }
    calendar.activeDate = this.selectedDate;
    if(this.selectedDropModifier == "Provider"){
      this.getproviders(this.siteId, this.selectedDate);
    }
    else{
      this.getLocations(this.siteId, this.selectedDate);
    }
  }

  searchLocation(searchString: string) {
    const lowerSearchString = searchString.toLowerCase();

    this.searchLocations = this.siteLocationsList.filter(location =>
      location.name.toLowerCase().includes(lowerSearchString)
    );
  }

  onLocationSelected(location: ILocation){
    this.selectedLocation = location;
    this.emitSelectedValues();
  }

  isSameDate(date1: Date, date2: Date): boolean {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear();
  }

  displayFn(option: ILocation): string {
    return option ? option.name : '';
  }

  // Needed for the dropdown selection so that when you refresh the options if the
  // previously selected value is still in the option list it will stay active.
  compareOptions = (a: any, b: any): boolean => {
    return a?.value === b?.value;
  };

  emitSelectedDate() {
    if(this.selectedDropModifier == "Provider"){
      this.getproviders(this.siteId, this.selectedDate);
    }
    else{
      this.getLocations(this.siteId, this.selectedDate);
    }
  }

  emitSelectedValues() {
    const calendarSelection: HuddleCalendar = {
      date: this.selectedDate,
      location: this.selectedLocation,
      provider: this.selectedProvider
    };

    this.calendarSelected.emit(calendarSelection);
  }
}
