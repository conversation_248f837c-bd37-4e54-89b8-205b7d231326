import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl,  Validators,
  ValidatorFn,  AbstractControl, FormGroup } from '@angular/forms';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor } from '../report-panel-enums';

@Component({
  selector: 'pnl-usage-dates',
  templateUrl: './pnl-usage-dates.component.html',
  styleUrls: ['./pnl-usage-dates.component.scss']
})
export class PnlUsageDatesComponent implements OnInit {

  @Input() rptpanStartDt: FormControl;
  @Input() rptpanStopDt: FormControl;
  @Input() rptpanFirstLoginMonth: FormControl;
  allowReport: boolean = false;
  rptbtnColor: string;
  noSearchPan: boolean = false;
  fg: FormGroup;
  
  constructor(
    public panelService:PanelService,
    private formBuilder: FormBuilder
  ) {
    this.rptpanStartDt = new FormControl;
    this.rptpanStopDt = new FormControl;
    this.rptpanFirstLoginMonth = new FormControl;
    this.fg = new FormGroup(
      {
        startDt: this.rptpanStartDt,
        stopDt: this.rptpanStopDt,
        firstLoginMonth: this.rptpanFirstLoginMonth
      },
      [Validators.required, this.dateRangeValidator]
    );
    this.rptbtnColor = this.rptbtnColor= btnColor.btnPrimaryColor;
    this.enableReportButton();
  }

  ngOnInit(): void {
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    
    const startDt = this.fg &&  this.fg.get("startDt")
    const stopDt = this.fg && this.fg.get("stopDt");
    if (startDt && stopDt && startDt.value && stopDt.value) {
      invalid = new Date(startDt.value).valueOf() > new Date(stopDt.value).valueOf();
    }
   
    return invalid ? { invalidRange: { startDt, stopDt } } : null;
  };

  readyToRun(): void{
    this.enableReportButton();    
  }

  runReport(): void {
    if (this.allowReport)
    {
      this.panelService.InitBoldReport();
    }
  }

  enableReportButton() : void
  {
    this.allowReport = this.rptpanStartDt.value != null && 
                      this.rptpanStopDt.value != null && 
                      this.rptpanFirstLoginMonth.value != null && 
                      !this.fg.invalid;
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
