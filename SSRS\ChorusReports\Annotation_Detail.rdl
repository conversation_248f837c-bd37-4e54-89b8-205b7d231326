﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Line Name="Line1">
        <Top>0.25192in</Top>
        <Left>0.0125in</Left>
        <Height>0in</Height>
        <Width>4.35146in</Width>
        <Style>
          <Border>
            <Style>Solid</Style>
          </Border>
        </Style>
      </Line>
      <Tablix Name="Tablix4">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.39116in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.19324in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.50574in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.8553in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.82866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.94616in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>4.83381in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.66199in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.57292in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox63">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>#</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox63</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox65">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Patient</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox65</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox67">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>MRN</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox67</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Reporting</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Period</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox75">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Original Date</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox75</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox73">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Description</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox73</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Tablix Name="Tablix7">
                      <TablixBody>
                        <TablixColumns>
                          <TablixColumn>
                            <Width>0.82533in</Width>
                          </TablixColumn>
                          <TablixColumn>
                            <Width>1.44948in</Width>
                          </TablixColumn>
                          <TablixColumn>
                            <Width>2.55899in</Width>
                          </TablixColumn>
                        </TablixColumns>
                        <TablixRows>
                          <TablixRow>
                            <Height>0.28646in</Height>
                            <TablixCells>
                              <TablixCell>
                                <CellContents>
                                  <Textbox Name="Textbox154">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>Response</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>12pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                              <Color>White</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox154</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <TopBorder>
                                        <Width>0.5pt</Width>
                                      </TopBorder>
                                      <LeftBorder>
                                        <Style>None</Style>
                                      </LeftBorder>
                                      <RightBorder>
                                        <Style>None</Style>
                                      </RightBorder>
                                      <BackgroundColor>#0b6c9f</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                  <ColSpan>3</ColSpan>
                                </CellContents>
                              </TablixCell>
                              <TablixCell />
                              <TablixCell />
                            </TablixCells>
                          </TablixRow>
                          <TablixRow>
                            <Height>0.28646in</Height>
                            <TablixCells>
                              <TablixCell>
                                <CellContents>
                                  <Textbox Name="Textbox148">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>Date</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>12pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                              <Color>White</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox148</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BottomBorder>
                                        <Style>None</Style>
                                      </BottomBorder>
                                      <LeftBorder>
                                        <Style>None</Style>
                                      </LeftBorder>
                                      <BackgroundColor>#0b6c9f</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixCell>
                              <TablixCell>
                                <CellContents>
                                  <Textbox Name="Textbox2">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>User Name</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>12pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                              <Color>White</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox2</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BottomBorder>
                                        <Style>None</Style>
                                      </BottomBorder>
                                      <RightBorder>
                                        <Style>None</Style>
                                      </RightBorder>
                                      <BackgroundColor>#0b6c9f</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixCell>
                              <TablixCell>
                                <CellContents>
                                  <Textbox Name="Textbox152">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>Description</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>12pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                              <Color>White</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox152</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BottomBorder>
                                        <Style>None</Style>
                                      </BottomBorder>
                                      <RightBorder>
                                        <Style>None</Style>
                                      </RightBorder>
                                      <BackgroundColor>#0b6c9f</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixCell>
                            </TablixCells>
                          </TablixRow>
                        </TablixRows>
                      </TablixBody>
                      <TablixColumnHierarchy>
                        <TablixMembers>
                          <TablixMember />
                          <TablixMember />
                          <TablixMember />
                        </TablixMembers>
                      </TablixColumnHierarchy>
                      <TablixRowHierarchy>
                        <TablixMembers>
                          <TablixMember />
                          <TablixMember />
                        </TablixMembers>
                      </TablixRowHierarchy>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                        <PaddingTop>2pt</PaddingTop>
                      </Style>
                    </Tablix>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox69">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Status</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox69</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1.5pt</Width>
                        </TopBorder>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.18634in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox141">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox141</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox156">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox156</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox143">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox143</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox9">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox144">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox144</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox138">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox138</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox139">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox139</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox145">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox145</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Black</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MEASURE_CD">
                      <CanGrow>true</CanGrow>
                      <CanShrink>true</CanShrink>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MEASURE_CD.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MEASURE_CD</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>PatientFlowsheet</ReportName>
                              <Parameters>
                                <Parameter Name="DEMOGRAPHICS_ID">
                                  <Value>=Fields!PATIENT_ID.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>Solid</Style>
                        </LeftBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PATIENT_NAME">
                      <CanGrow>true</CanGrow>
                      <CanShrink>true</CanShrink>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!PATIENT_NAME.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PATIENT_NAME</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>PatientFlowsheet</ReportName>
                              <Parameters>
                                <Parameter Name="DEMOGRAPHICS_ID">
                                  <Value>=Fields!PATIENT_ID.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MRN">
                      <CanGrow>true</CanGrow>
                      <CanShrink>true</CanShrink>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MRN.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MRN</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>PatientFlowsheet</ReportName>
                              <Parameters>
                                <Parameter Name="DEMOGRAPHICS_ID">
                                  <Value>=Fields!PATIENT_ID.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="REPORTING_PERIOD">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!REPORTING_PERIOD.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>REPORTING_PERIOD</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ORIGINAL_DT">
                      <CanGrow>true</CanGrow>
                      <CanShrink>true</CanShrink>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ORIGINAL_DT.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <Format>yyyy-MM-dd</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ORIGINAL_DT</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>PatientFlowsheet</ReportName>
                              <Parameters>
                                <Parameter Name="DEMOGRAPHICS_ID">
                                  <Value>=Fields!PATIENT_ID.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ANNOTATE_DESCRIPTION.Value</Value>
                              <MarkupType>HTML</MarkupType>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=" *" &amp; Fields!ANNOTATE_FREETEXT.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                        </BottomBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Rectangle Name="Rectangle1">
                      <ReportItems>
                        <Subreport Name="Sub_Annotation_Details">
                          <ReportName>Sub_Annotation_Detail</ReportName>
                          <Parameters>
                            <Parameter Name="MEASURE_ID">
                              <Value>=Fields!MEASURE_ID.Value</Value>
                            </Parameter>
                            <Parameter Name="PATIENT_ID">
                              <Value>=Fields!PATIENT_ID.Value</Value>
                            </Parameter>
                            <Parameter Name="RowNumber">
                              <Value>=RowNumber("DS_Annotation_Detail")</Value>
                            </Parameter>
                          </Parameters>
                          <NoRowsMessage>No Response Available</NoRowsMessage>
                          <MergeTransactions>true</MergeTransactions>
                          <KeepTogether>true</KeepTogether>
                          <Height>0.25in</Height>
                          <Width>4.83381in</Width>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Subreport>
                      </ReportItems>
                      <KeepTogether>true</KeepTogether>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </BottomBorder>
                      </Style>
                    </Rectangle>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox146">
                      <CanGrow>true</CanGrow>
                      <CanShrink>true</CanShrink>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIF(Fields!OPEN_FLG.Value = true,"Open","Closed")</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox146</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>PatientFlowsheet</ReportName>
                              <Parameters>
                                <Parameter Name="DEMOGRAPHICS_ID">
                                  <Value>=Fields!PATIENT_ID.Value</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>None</Style>
                        </Border>
                        <BottomBorder>
                          <Style>Solid</Style>
                        </BottomBorder>
                        <RightBorder>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DS_Annotation_Detail</DataSetName>
        <Top>0.88208in</Top>
        <Height>1.00926in</Height>
        <Width>12.21606in</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox16">
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=Parameters!measureCnt.Value &amp; " Total " &amp; IIF(Parameters!StatusFlg.Value = true,"Open","Closed") &amp; " Annotations"</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>12pt</FontSize>
                </Style>
              </TextRun>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>13pt</FontSize>
                </Style>
              </TextRun>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>13pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#272424</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>13pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#272424</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox13</rd:DefaultName>
        <Top>1.57374cm</Top>
        <Left>0.0125in</Left>
        <Height>0.62795cm</Height>
        <Width>12.20357in</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <BackgroundColor>Lavender</BackgroundColor>
        </Style>
      </Textbox>
      <Textbox Name="Textbox48">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=IIf(IsNothing(Parameters!locationCd.Value) OR Parameters!locationCd.Value ="", "All Location" , Parameters!locationCd.Value) &amp; IIf(IsNothing(Parameters!providerCd.Value) OR Parameters!providerCd.Value="","", " -&gt; " &amp; Parameters!providerCd.Value) &amp; IIf(IsNothing(Parameters!measureCd.Value) OR Parameters!measureCd.Value="","", " -&gt; " &amp; Parameters!measureCd.Value)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox46</rd:DefaultName>
        <Top>0cm</Top>
        <Left>0.0125in</Left>
        <Height>0.21998in</Height>
        <Width>6.88485in</Width>
        <ZIndex>3</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox47">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Epividian® CHORUS</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>™</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> Report</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Format>dd-MMM-yyyy</Format>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> (data)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=Now()</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Format>dd-MMM-yyyy</Format>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> (run)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox46</rd:DefaultName>
        <Top>0cm</Top>
        <Left>6.96679in</Left>
        <Height>0.58333in</Height>
        <Width>5.24928in</Width>
        <ZIndex>4</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
    </ReportItems>
    <Height>1.89134in</Height>
    <Style />
  </Body>
  <Width>12.22648in</Width>
  <Page>
    <PageHeight>8.5in</PageHeight>
    <PageWidth>13in</PageWidth>
    <LeftMargin>0.5in</LeftMargin>
    <RightMargin>0.25in</RightMargin>
    <TopMargin>0.75in</TopMargin>
    <BottomMargin>0.75in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_Annotation_Detail">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@REPORTING_PERIOD">
            <Value>=Parameters!REPORTING_PERIOD.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@StatusFlg">
            <Value>=Parameters!StatusFlg.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@measureCd">
            <Value>=Parameters!measureCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT MRN, PATIENT_ID, PATIENT_NAME, MEASURE_CD, MEASURE_ID, ORIGINAL_DT, REPORTING_PERIOD, ANNOTATE_DESCRIPTION, ANNOTATE_FREETEXT, OPEN_FLG
FROM [REPORT].[GET_MEASURE_ANNOTATE_DTL](@USER_ID, @REPORTING_PERIOD, @StatusFlg, @measureCd, @locationCd, @providerCd)
ORDER BY MEASURE_CD, PATIENT_NAME</CommandText>
      </Query>
      <Fields>
        <Field Name="MRN">
          <DataField>MRN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PATIENT_ID">
          <DataField>PATIENT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PATIENT_NAME">
          <DataField>PATIENT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ORIGINAL_DT">
          <DataField>ORIGINAL_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ANNOTATE_DESCRIPTION">
          <DataField>ANNOTATE_DESCRIPTION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ANNOTATE_FREETEXT">
          <DataField>ANNOTATE_FREETEXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OPEN_FLG">
          <DataField>OPEN_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="StatusFlg">
      <DataType>Boolean</DataType>
      <Prompt>Status </Prompt>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Location</Prompt>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Provider</Prompt>
    </ReportParameter>
    <ReportParameter Name="measureCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Measure</Prompt>
    </ReportParameter>
    <ReportParameter Name="measureCnt">
      <DataType>String</DataType>
      <Prompt>Measure Count</Prompt>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Prompt>USER ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="REPORTING_PERIOD">
      <DataType>String</DataType>
      <Prompt>REPORTING PERIOD</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>d57a4c7d-bef2-4052-b3a6-fa0432b4c161</rd:ReportID>
</Report>