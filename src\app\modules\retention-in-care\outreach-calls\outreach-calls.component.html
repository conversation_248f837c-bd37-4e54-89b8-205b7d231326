<div class="container">
    <div class="row">
      <div class="col-md-4">
        <label style="color: black;font-weight: bold;">
              Current [{{this.outreachCallsData.currentListCount}} Total 
                  Patient<span *ngIf="this.outreachCallsData.currentListCount > 1">s</span>]
        </label>
      </div>
      <div class="col-md-4">
        <label style="color: black;font-weight: bold;">
              Try Later [{{this.outreachCallsData.tryLaterListCount}} Total 
                  Patient<span *ngIf="this.outreachCallsData.tryLaterListCount > 1">s</span>]
        </label>
      </div>
      <div class="col-md-4">
        <label style="color: black;font-weight: bold;">
            Call History
        </label>
      </div>
    </div>
    <div class="row" style="height:83vh; overflow-x: scroll">
        <div class="col-md-4">
            <div *ngIf="this.outreachCallsData.currentListCount > 0">
                    <div class="OutReachDetailInnerDiv">
                        <div style="float: left;">                        
                            <div class="administrativeActionDiv" *ngFor="let item of this.outreachCallsData.lstCurrentList;">
                                <div>
                                    <a style="cursor: pointer" [innerHTML]="retentionService.safeHtml(item.patientNameRiskDate)"></a>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.annotationDescPrName)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.homePhone)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.mobilePhone)"></label>
                                </div>
                                <div class="hiddenDiv">
                                    <label [innerHTML]="item.annotateId"></label>
                                    <label [innerHTML]="item.annotateOptionId"></label>
                                    <label [innerHTML]="retentionService.safeHtml(item.consecutive)"></label>
                                    <label [innerHTML]="item.demographicsId"></label>
                                    <label [innerHTML]="item.doNotCallFlg"></label>
                                    <label [innerHTML]="item.locationId"></label>
                                </div>
                                <div class="linkDiv">
                                    <a class="link-a" [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', item.demographicsId]">
                                        View Report
                                    </a>
                                    <a style="cursor: pointer" (click)="openDialog(item);">Record Result</a>
                                </div>                            
                            </div>
                        </div>
                    </div>
                </div>           
        </div>
        <div class="col-md-4">
            <div *ngIf="this.outreachCallsData.tryLaterListCount > 0">
                    <div class="OutReachDetailInnerDiv">
                        <div style="float: left; width: 100%;">                                            
                            <div class="administrativeActionDiv" *ngFor="let item of this.outreachCallsData.lstTryLaterList;">
                                <div>
                                    <a style="cursor: pointer" [innerHTML]="retentionService.safeHtml(item.patientNameRiskDate)"></a>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.annotationDescPrName)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.homePhone)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.mobilePhone)"></label>
                                </div>
                                <div class="hiddenDiv">
                                    <label [innerHTML]="item.annotateId"></label>
                                    <label [innerHTML]="item.annotateOptionId"></label>
                                    <label [innerHTML]="retentionService.safeHtml(item.consecutive)"></label>
                                    <label [innerHTML]="item.demographicsId"></label>
                                    <label [innerHTML]="item.doNotCallFlg"></label>
                                    <label [innerHTML]="item.locationId"></label>
                                </div>
                                <div class="linkDiv">
                                    <a class="link-a" [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', item.demographicsId]">
                                        View Report
                                    </a>  
                                    <a style="cursor: pointer" (click)="openDialog(item);">Record Result</a>
                                </div>                            
                            </div>
                        </div>
                    </div>
                </div>
            
        </div>
        <div class="col-md-4">           
                <div *ngIf="this.outreachCallsData.callHistoryListCount > 0">
                    <div class="OutReachDetailInnerDiv">
                        <div style="float: left; width: 100%;">                        
                            <div class="administrativeActionDiv" *ngFor="let item of this.outreachCallsData.lstCallHistoryList;">
                                <div>
                                    <a style="cursor: pointer" [innerHTML]="retentionService.safeHtml(item.patientNameRiskDate)"></a>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.annotationDescPrName)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.homePhone)"></label>
                                </div>
                                <div>
                                    <label [innerHTML]="retentionService.safeHtml(item.mobilePhone)"></label>
                                </div>
                                <div class="hiddenDiv">
                                    <label [innerHTML]="item.annotateId"></label>
                                    <label [innerHTML]="item.annotateOptionId"></label>
                                    <label [innerHTML]="item.consecutive"></label>
                                    <label [innerHTML]="item.demographicsId"></label>
                                    <label [innerHTML]="item.doNotCallFlg"></label>
                                    <label [innerHTML]="item.locationId"></label>
                                </div>
                                <div class="linkDiv">
                                    <a class="link-a" [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', item.demographicsId]">
                                        View Report
                                    </a> 
                                </div>                            
                            </div>
                        </div>
                    </div>
                </div>          
        </div>
</div>
