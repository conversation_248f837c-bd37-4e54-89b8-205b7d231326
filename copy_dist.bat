@echo off
SET source=C:\path\to\source\
SET destination=C:\path\to\destination\
SET exclude_copy=index.html
SET keep_files=index.html web.config

cd %destination%
for %%F in (*) do (
    echo %%F | findstr /v /i "%keep_files%" >nul && del "%%F"
)
for /d %%D in (*) do (
    echo %%D | findstr /v /i "%keep_files%" >nul && rd /s /q "%%D"
)

cd %source%
for /d %%D in (*) do (
    xcopy /Y /E "%%D" "%destination%\%%D\"
)
for %%F in (*) do (
    echo %%F | findstr /v /i "%exclude_copy%" >nul && xcopy /Y "%%F" %destination%
)

echo Done.