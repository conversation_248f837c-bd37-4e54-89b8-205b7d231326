import { Injectable } from '@angular/core';
import { ValidatorFn, Validators } from '@angular/forms';
import { RuleDefinitionField, RuleDefinitionModel } from '../models/rule-definition.model';

@Injectable({
  providedIn: 'root'
})
export class JsonSchemaService {
  
  constructor() { }

  /**
   * Determine the field type based on the schema type
   * @param type The schema type
   * @returns The field type for the UI
   */
  determineFieldType(type: string): string {
    // Handle array types (e.g., "List<FormField>")
    if (type.startsWith('List<') || type.startsWith('Array<')) {
      return 'array';
    }

    // Handle primitive types
    switch (type.toLowerCase()) {
      case 'string':
        return 'string';
      case 'number':
      case 'int':
      case 'integer':
      case 'float':
      case 'double':
        return 'number';
      case 'boolean':
      case 'bool':
        return 'boolean';
      case 'date':
      case 'datetime':
        return 'date';
      default:
        // If it's capitalized, it's likely a custom object type
        if (type.charAt(0) === type.charAt(0).toUpperCase()) {
          return 'object';
        }
        return 'string'; // Default to string for unknown types
    }
  }

  /**
   * Get validators for a field based on its type
   * @param type The field type
   * @returns Array of validators
   */
  getValidators(type: string): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    switch (this.determineFieldType(type)) {
      case 'number':
        validators.push(Validators.pattern(/^-?\d*(\.\d+)?$/));
        break;
      case 'boolean':
        // No specific validators for boolean
        break;
      case 'date':
        // Date validation would be handled by the date picker component
        break;
      default:
        // No specific validators for other types
        break;
    }

    return validators;
  }

  /**
   * Create an empty object based on a template
   * @param template The template object
   * @returns A new object with the same structure but empty values
   */
  createEmptyObjectLike(template: Record<string, unknown>): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in template) {
      if (template.hasOwnProperty(key)) {
        const value = template[key];
        
        if (Array.isArray(value)) {
          result[key] = [];
        } else if (typeof value === 'object' && value !== null) {
          result[key] = this.createEmptyObjectLike(value as Record<string, unknown>);
        } else {
          result[key] = this.getDefaultValue(typeof value as string);
        }
      }
    }

    return result;
  }

  /**
   * Get default value for a field type
   * @param type The field type
   * @returns The default value
   */
  getDefaultValue(type: string): unknown {
    switch (this.determineFieldType(type)) {
      case 'string':
        return '';
      case 'number':
        return 0;
      case 'boolean':
        return false;
      case 'date':
        return null;
      case 'array':
        return [];
      case 'object':
        return {};
      default:
        return null;
    }
  }

  /**
   * Format a camelCase or PascalCase string to a readable label
   * @param name The field name
   * @returns Formatted label
   */
  formatLabel(name: string): string {
    if (!name) return '';
    
    // Insert a space before each capital letter and uppercase the first letter
    const formatted = name.replace(/([A-Z])/g, ' $1').trim();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  }

  /**
   * Check if a value is an object (but not null or array)
   */
  isObject(value: unknown): boolean {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }

  /**
   * Check if a value is an array
   */
  isArray(value: unknown): boolean {
    return Array.isArray(value);
  }

  /**
   * Get the type of a value as a string
   */
  getValueType(value: unknown): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }

  /**
   * Check if an object is empty
   */
  isEmptyObject(obj: unknown): boolean {
    if (!obj || typeof obj !== 'object') return true;
    return Object.keys(obj).length === 0;
  }
}
