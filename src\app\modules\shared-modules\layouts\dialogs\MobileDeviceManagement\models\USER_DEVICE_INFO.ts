 export interface USER_DEVICE_INFO {
     DEVICE_NAME: string;
    DEVICE_TYPE: string;
    DEVICE_ID: string;
    CREATED_DT: string;
    APP_VERSION: string;
}

export interface UserDevices {
    id: string;
    deviceName: string;
    deviceType: string;
    deviceId: string;
    isActive: boolean;
    appVersion: string;
    lastUpdate: string;
}

export interface DevicePinVM {
    pin: number | null;
    isActive: boolean;
}

export enum DeviceType {
    Android = 1,
    iOS = 2,
    Both = 3
} 

export enum MessageMethod {
    Email = 0,
    SMS = 1,
    Notification = 2
} 