import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, catchError, map, Observable, of, share, Subject, switchMap, throwError } from 'rxjs';
import { ISite } from 'src/app/modules/shared-modules/layouts/models/site-model';
import { ApiRoutes, ApiTypes } from '../ep-api-handler/api-option-enums';
import { ApiHandler } from '../ep-api-handler/ep-api-handler';
import { IChorusUserInfo } from '../ep-api-handler/models/chorus-user-Info.model';
import { IChorusResponseToken } from '../ep-api-handler/models/login-response.model';
import { IChorusAccessViewModel, RoleTypes } from './models/user-security-model';
import { MatDialog } from '@angular/material/dialog';
import { EpividianCommon } from 'src/app/modules/utility/EpividianCommon';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})

//User Context stores all user information for the current tabbed session/instance
export class UserContext {
  static lastRefreshTime: Date|null = null;
  static lastAttemptedRefreshTime: Date|null = null;
  static _windowLastActiveTime: Date = new Date();
  private _chorusUserInfo = new BehaviorSubject<IChorusUserInfo>({} as IChorusUserInfo);
  private _currentSite = new Subject<number>;
  private _userSession = new BehaviorSubject<IChorusResponseToken>({} as IChorusResponseToken);
  private _userSecurity = new BehaviorSubject<IChorusAccessViewModel[]>([]);
  private _deviceCode = new BehaviorSubject<string>('');
  private _hasOutReach = new BehaviorSubject<boolean>(false);
  private _isAuthenticated = new BehaviorSubject<boolean>(false);
  private _sessionTimeout = new Subject<number>;
  private _sessionTimeoutMessageVisible = new BehaviorSubject<boolean>(false);
  private _siteList = new BehaviorSubject<ISite[]>([]);
  private _userName = new BehaviorSubject<string>("");
  public siteId: number = 0;
  private currentToken: string = '';
  private userInfo: Observable<IChorusUserInfo> | null = null;

  constructor(public apihandler: ApiHandler, private router: Router, public dialog: MatDialog, private epividianCommon: EpividianCommon) {
    //Checks if Session Object exists in browser and loads token into api servers.
    /*
    let url = this.router.url; // Gets the current URL
    let path = "/";
    if (Object.keys(this.router.parseUrl(url).root.children).length > 0 && this.router.parseUrl(url).root.children.hasOwnProperty('primary'))
    {
    path = this.router.parseUrl(url).root.children['primary'].segments.map(segment => segment.path).join('/'); // Gets the path from the URL
    }
    */

    //Sets the Session Timeout from the environment variable or the session object.
    this.epividianCommon.LoadSessionTimeout();

    this.getCurrentSite().subscribe(s => {
      var storedSite = sessionStorage.getItem('currentSite');
      this.siteId = s;
      if (storedSite != null) {
          // Attempt to convert to a number
          const numericValue = Number(storedSite);

          if (!isNaN(numericValue)) {
              // Conversion successful
              this.siteId = numericValue;
          }
      }
    });

    this.getUserSession().subscribe(s => {
      if (s.access_token != null) {
        this.apihandler.setbearToken(s.access_token);
       // this.InitializeAuthorizedSession(s.issued);
        this.currentToken = s.access_token;
      }
    });


    //userContext shouldn't be used in login
    /*
    if (path != "Auth/ChangePassword" && path != "Auth/ResetPassword" && path != "TermsConditions"
           && path != "Auth/VerifyTfa" && path != "Auth/RegisterTf" && path != "ForgotPassword") {
      this.LoadUserObjFromLocalStorage();
    }
    */

  }

  // Methods to manage the BehaviorSubjects and get their current values
  public getChorusUserInfo(): Observable<IChorusUserInfo> {
    return this._chorusUserInfo.asObservable();
  }

  public getCurrentSite(): Observable<number> {
    return this._currentSite.asObservable();
  }

  public getUserSession(): Observable<IChorusResponseToken> {
    return this._userSession.asObservable();
  }

  public getUserSecurity(): Observable<IChorusAccessViewModel[]> {
    return this._userSecurity.asObservable();
  }

  public getHasOutReach(): Observable<boolean> {
    return this._hasOutReach.asObservable();
  }

  public getSiteList(): Observable<ISite[]> {
    return this._siteList.asObservable();
  }

  public isChorusUserInfoEmpty(): boolean {
    const userInfo = this._chorusUserInfo.getValue(); // get the current value of _chorusUserInfo
    return Object.keys(userInfo).length === 0 && userInfo.constructor === Object;
  }

  public isSiteListEmpty(): boolean {
    const siteList = this._siteList.getValue(); // get the current value of _chorusUserInfo
    return Object.keys(siteList).length === 0 && siteList.constructor === Array;
  }

  public GetCurrentSiteValue(): number {
    var storedSite = sessionStorage.getItem('currentSite');

    if (storedSite != null) {
        // Attempt to convert to a number
        const numericValue = Number(storedSite);
        if (!isNaN(numericValue)) {
            // Conversion successful
            return numericValue;
        }
    }
    return this.siteId;

  }

  public getContextToken(): string {
    return this.currentToken;
  }

  public DisplayName() {
    if (this.isChorusUserInfoEmpty()==false) {
      const fnameLetter = this._chorusUserInfo.value.firstNm.charAt(0).toUpperCase();
      const lnameLetter = this._chorusUserInfo.value.lastNm.charAt(0).toUpperCase();
      return fnameLetter + " " + lnameLetter;
    } else {
      return "";
    }

  }

  //used to set the User Info Object
  public SetUserInfo(userInfo: IChorusUserInfo) {
    this._chorusUserInfo.next(userInfo);
  }

  // --- References Tied to this need to moved over to the EpividianCommon Service ---
  // --- * Notes the issues around _userSession BehaviorSubject * ---
  //Saves the current user token in the browser.. To Get User object from Browser use userContext Service
  public SaveTokenToLocalStorage(userToken: IChorusResponseToken) {
    this.epividianCommon.SaveTokenToLocalStorage(userToken);
    this._userSession.next(userToken);
  }

  //Chorus User Info Gets all the generial user information first & last names
  //and options or settings that the user may have, eula, etc.
  public GetUserInfoSub(forceRefresh: boolean=false): Observable<IChorusUserInfo> {

    let userInfo: Observable<IChorusUserInfo>;
    // Check if the required conditions are met
    if ((this.DisplayName().trim() !== "" && this._userSession.value.issued !== undefined) && !forceRefresh) {
      // If conditions are met, check if chorusUserInfo is not empty
      userInfo = this.isChorusUserInfoEmpty()
        ? this.fetchChorusUserInfo() // If empty, fetch new user info
        : of(this._chorusUserInfo.value); // If not empty, return the current value
    } else {
      // If conditions are not met, always fetch new user info
      userInfo = this.fetchChorusUserInfo();
    }
    this.userInfo = userInfo.pipe(shareReplay(1));
    return this.userInfo;
  }

  private fetchChorusUserInfo(): Observable<IChorusUserInfo> {
    return this.apihandler.Get<IChorusUserInfo>(ApiTypes.V2, ApiRoutes.ChorusUserInfo).pipe(
      switchMap((res: IChorusUserInfo) => {
        this._chorusUserInfo.next(res);
        return of(res); // Emit the fetched user info
      }),
      catchError(error => {

        return throwError(() => {
          new Error('Error fetching Chorus user info')
          this.ClearSession();
          this.router.navigate(['/']);
        });
      }),
      share() // Share the subscription if there are multiple subscribers
    );
  }

  //Current Site Subscription
  public SetCurrentSite(site: number) {
    sessionStorage.setItem('currentSite',site.toString());
    this._currentSite.next(site);
  }

  //Adds Site Record to Users Site List.
  public SetSiteList(siteList: ISite[]): void {
      this._siteList.next(siteList);
  }

  //Gets the User Name from the Context object
  public GetUserName() {
    if (this._userName.value == "")
    {
      let name = localStorage.getItem('userName');
      if (name) {
        this._userName.next(name);
      }
    }

    return this._userName.value;
  }

  //Sets the username in localstorage
  public SetUsername(user: string): void {
    localStorage.setItem('userName', user);
    this._userName.next(user);
  }

  // --- References tied to this need to moved over to the EpividianCommon Service ---
  // --- * Notes the issues around _userSession BehaviorSubject * ---
  //Gets the Session Object from the local browsers and sets the token for the api to use.
  public LoadUserObjFromLocalStorage() {
    let name = localStorage.getItem('userName');
    if (name) {
      this._userName.next(name);
    }

    let sessionObj = localStorage.getItem('Session');
    if (sessionObj) {
      this._sessionTimeout.next(this.epividianCommon.LoadSessionTimeout(this._userSession.value));
      let session = JSON.parse(sessionObj)
      if (this.epividianCommon.isSessionValid(session)) {
        this._userSession.next(session);
      }
      else{
        /*
        if (!this.epividianCommon.isRefreshTokenValid(session))
        {
          this.ClearSession();
        }
        */
      }
    }
  }

  //Sets the Device Code outside of user session object will probably move this outside of the user context into a device context.
  public SetUserDeviceCode(code: string): void {
    localStorage.setItem('DeviceCode', code);
    this._deviceCode.next(code);;
  }

  public GetUserDeviceCode(): string {
    let deviceCode = localStorage.getItem('DeviceCode');
    if (deviceCode) {
      this._deviceCode.next(deviceCode);
    }
    else
    {
      this._deviceCode.next('');
    }
    return this._deviceCode.value;
  }

  //Returns an Observable List of Sites for Users Site Selector menu
  public getSites(): Observable<ISite[]> {
    let tmpSiteList: ISite[] = [] as ISite[];
    if (this.isSiteListEmpty() == true)
    {
      return this.apihandler.Get<Map<number,string>>(ApiTypes.V2, ApiRoutes.getUsersSites).pipe(map((s: Map<number, string>) =>{
        for(var key in s){
          tmpSiteList.push({site: parseInt(key),name:s[key]})
        }
        return tmpSiteList;
      }));
    } else {
      return this.getSiteList();
    }
  }

  //handle the Chorus Security Access Api call
  public LoadUsersChorusAccess(): Observable<IChorusAccessViewModel[]> {
    //this.InitializeAuthorizedSession();

    /*
      As we need to be able to create the server cookie that contains the users CS_USER value we must make this
      API call using withCredentils, therefore all optional parameters must be defined here.
     */
    return this.apihandler.Get<IChorusAccessViewModel[]>(
      ApiTypes.V2,             // baseUrl
      ApiRoutes.getUserAccess, // apiUri
      false,                   // returnError
      false,                   // observeResponse
      undefined,               // requestType
      undefined,               // httpParams
      true                     // withCredentials
    );

  }

  //Update the Chorus Security Access Subject for any components subscribed to this service.
  public SetUserSecurity(userSec: IChorusAccessViewModel[]) {
    this._userSecurity.next(userSec);
  }

  //Clears Users Session Objects and Local Storage.
  public ClearSession() {
    this.epividianCommon.ClearSession();
    this._isAuthenticated.next(false);
    this.apihandler.setbearToken("");
    this._userSession.next({} as IChorusResponseToken);
    this._userSecurity.next([]);
    this._sessionTimeoutMessageVisible.next(false);
  }

  public SetOutReach(hasOR: boolean) {
    this._hasOutReach.next(hasOR);
  }

  /**
   * Checks if a user has access to a specific feature
   * @param featureAccess The feature to check access for
   * @param siteId Optional site ID. If not provided, uses the current site
   * @returns Observable<boolean> True if the user has access, false otherwise
   */
  public checkFeatureAccess(featureAccess: string, siteId?: number|null): Observable<boolean> {
    let apiRoute: string;

    // Choose the appropriate endpoint based on whether siteId is provided
    if (siteId !== null && (siteId !== undefined || this.GetCurrentSiteValue() !== 0)) {
      const currentSiteId = siteId || this.GetCurrentSiteValue();
      // Use the endpoint with site ID
      apiRoute = ApiRoutes.FeatureAccessWithSite
        .replace('{{siteId}}', currentSiteId.toString())
        .replace('{{featureAccess}}', featureAccess);
    } else {
      // Use the endpoint without site ID
      apiRoute = ApiRoutes.FeatureAccess
        .replace('{{featureAccess}}', featureAccess);
    }

    return this.apihandler.Get<boolean>(ApiTypes.V2, apiRoute).pipe(
      catchError(error => {
        // If there's an error (like 401 Unauthorized), return false instead of throwing
        //console.log(`Feature access check for ${featureAccess} failed:`, error);
        return of(false);
      })
    );
  }

  /**
   * Checks if a user has a specific role
   *
   * @param roleType The role type to check for
   * @param siteId Optional site ID. If not provided, uses the current site
   * @param checkAllSites If true, checks for the role across all sites
   * @returns Observable<boolean> True if the user has the role, false otherwise
   */
  public hasRole(roleType: RoleTypes, siteId?: number, checkAllSites: boolean = false): Observable<boolean> {
    return this.getUserSecurity().pipe(
      map((userAccess: IChorusAccessViewModel[]) => {
        // If no user access data is available, return false
        if (!userAccess || userAccess.length === 0) {
          console.log('No user access data available');
          return false;
        }

        // If checkAllSites is true, check for the role across all sites
        if (checkAllSites) {
          // Check if the user has the role in any site
          for (const access of userAccess) {
            if (access.userRoles && access.userRoles.length > 0) {
              const hasRole = access.userRoles.some(r => r.role === roleType);
              if (hasRole) {
                //console.log(`User has ${RoleTypes[roleType]} role on site ${access.siteId}`);
                return true;
              }
            }
          }
          //console.log(`User does not have ${RoleTypes[roleType]} role on any site`);
          return false;
        }

        // Otherwise, check on the specific site
        const currentSiteId = siteId || this.GetCurrentSiteValue();

        // Find the access entry that matches the current siteId
        const siteAccess = userAccess.find(access => access.siteId === currentSiteId);

        if (!siteAccess || !siteAccess.userRoles || siteAccess.userRoles.length === 0) {
          //console.log(`No valid site access found for site ID ${currentSiteId}`);
          return false;
        }

        // Check if the user has the specified role
        const hasRole = siteAccess.userRoles.some(r => r.role === roleType);
        //console.log(`Role check for ${RoleTypes[roleType]} on site ${currentSiteId}: ${hasRole}`);

        return hasRole;
      }),
      catchError(error => {
        //console.log('Role check failed:', error);
        return of(false);
      })
    );
  }

}
