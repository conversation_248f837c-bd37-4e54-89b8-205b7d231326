.searchCriteriaDiv {
  position: relative;
  margin: 2px;
  float: left;
  padding-top: 5px;
  padding-bottom: 10px;
  width: 98%;
  z-index: 1;
}

.groupTitles{
  color:#0071bc;
  font-weight: 700;
}

.elementDiv {
  margin-bottom: 2px;
  margin-left: 5px;
  display: table-cell;
  color: #0071bc;
  float: left;
  font-weight: 600;
}

.textDiv{
  color: #0071bc;
}

.inlineBox {
  display:inline-block;
}


/* Overwrite css which gives top padding with 5px */
.col-sm-10 {
    padding: 0 0px 5px 0;
    width: 70%
}

.row-heading {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    display: flex;
    flex-wrap: wrap;
    padding: 0 20 0 0;
}

.form-select {
    width: 90%;
    padding-bottom: 2px;
}

.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    height: 25px;
    padding: 1px 10px;
    text-align: center;
    width: 300px;
    font-weight: 500;
    font-size: smaller;
    text-decoration: none;
}

.CMbtn:hover {
    background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F));
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}


.textInput {
  border-radius: 4px;
  background: #FFFFFF;
  height: 28px;
  margin-top: 4px;
  border: 1px solid gainsboro;
}

.textInput:focus {
  outline: none;
  box-shadow: 0 0 0px 4px #B6D1F7;
 // background-color: lightblue;
}

input::placeholder {
  color: gainsboro;
  font-style: italic;
  font-size: 14px;
}



.alertContainer {
  position: relative;
  float: inherit;
  margin-bottom: 20px;
}

.alert {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1;
  background-color: #f00;
  color: #fff;
  padding: 5px;
  float: inherit;
  font-size: 11px;
}

input:focus + .alert {
  display: block;
}
