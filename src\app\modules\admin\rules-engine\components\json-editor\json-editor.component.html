<div class="json-editor-container">
  <!-- Toolbar with view toggle and action buttons -->
  <app-json-toolbar
    [viewMode]="viewMode"
    [hasError]="!!jsonError"
    (viewModeChange)="onViewModeChange($event)"
    (applyChanges)="applyChanges()"
    (formatJson)="formatJson()">
  </app-json-toolbar>

  <!-- Object View -->
  <div *ngIf="viewMode === 'object'">
    <app-object-view
      [jsonData]="parsedJsonData"
      (jsonDataChange)="onObjectDataChange($event)">
    </app-object-view>
  </div>

  <!-- Raw JSON View -->
  <div *ngIf="viewMode === 'json'">
    <app-raw-json-view
      [jsonData]="formattedJsonData"
      (jsonDataChange)="onJsonDataChange($event)"
      (jsonErrorChange)="onJsonErrorChange($event)">
    </app-raw-json-view>
  </div>
</div>
