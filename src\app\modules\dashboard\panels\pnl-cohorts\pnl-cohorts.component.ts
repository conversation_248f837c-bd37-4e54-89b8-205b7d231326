import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-cohorts',
  templateUrl: './pnl-cohorts.component.html',
  styleUrls: ['./pnl-cohorts.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PnlCohortsComponent implements OnInit {

  @Input() rptpanCohorts: FormControl;
  cohortsData: IData[] = [] as IData[]
  rptbtnColor: string;
  noSearchPan: boolean = false;
  isRunDisabled: boolean = true;
  rawdata : IReportParamData[] = [] as IReportParamData[]
  filteredData: IReportParamData[] = [] as IReportParamData[]

  constructor(
    public panelService:PanelService
  ) {
    this.rptpanCohorts = new FormControl;
    this.rptbtnColor = this.rptbtnColor= btnColor.btnSecondColor;

    this.panelService.GetPanelData(ReportPanelTypes.pnlCohorts).subscribe(s =>
      {
        this.rawdata = s;
        this.comboFilter("All");
      });
  }

  ngOnInit(): void {
    this.panelService.HidePanelSub.subscribe(s => {
      this.noSearchPan = s;
    })
  }

  readyToRun(): void{
    this.setButtonColor();
  }

  public hideSearchPanel()
  {
    this.panelService.HidePanel();
  }


  checkTextLength(text: string): void {
    this.isRunDisabled = text.length === 0;
    this.setButtonColor();
  }

  setButtonColor()
  {
    if (this.isRunDisabled)
    {
      this.rptbtnColor = btnColor.btnSecondColor;
    }
    else
    {
      this.rptbtnColor= btnColor.btnPrimaryColor;
    }
  }

  comboFilter(searchString :string)
  {

   this.filteredData = this.panelService.ComboAutoComplete(searchString, 0, this.rawdata, true);
   this.cohortsData = this.filteredData[0].data.map(value=>value);
   
  }


}
