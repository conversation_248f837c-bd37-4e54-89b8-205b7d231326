.provider-view{
  padding: 5px;
  float: left; 
  width: 100%; 
  height: 98%;
  overflow: auto;
}

.scrollable-table {
  max-width: 90%;
}

.providerloc {
  width: 153px; 
  height: 80px;
}

.providerBtn {
  height: 29px; 
  margin-top: 29px !important; 
  background-color: #2779aa; 
  color: white; 
  line-height: 25px; 
  text-align: center;
  font-weight: 500;
  font-size: smaller;
}

.ColorLocationRCProviderlevel {
background-color: #dcdcdc !important;
font-weight:500 !important;
cursor:pointer;            
}

.ColorLocationRC {
background-color: #dcdcdc !important;
font-weight:500 !important;
cursor:pointer;
width: 130px !important;
}

.CursorPointerRC
{
cursor:pointer;
}

.table-header-rotated th.row-header{
width: auto;
}

.table-header-rotated td{
width: 40px;
border-top: 1px solid #dddddd;
border-left: 1px solid #dddddd;
border-right: 1px solid #dddddd;
vertical-align: middle;
text-align: center;

}

.table-header-rotated th.rotate-45{
height: 90px;/*80 to 90*/ 
width: 45px;
left: 95px;
min-width: 45px;
max-width: 48px;
position: relative;
vertical-align: bottom;
padding: 0;
font-size: 11px;
line-height: 1;  
}

.table-header-rotated th.rotate-45 > div {
  position: relative;
  top: 0px;
  left: 0px;
  height: 100%;
  transform: skew(-45deg);
  -webkit-transform: skew(-45deg);
  -moz-transform: skew(-45deg);
  -o-transform: skew(-45deg);
  overflow: hidden;
  border-top: 1px solid #dddddd;
  background-color: #2779aa;
  color: white;
  -webkit-print-color-adjust: exact !important;
  -moz-color-adjust: exact;
  transform-origin: 0 0;
}

.table-header-rotated th.rotate-45 span {
-ms-transform:skew(45deg,0deg) rotate(315deg);
-moz-transform:skew(45deg,0deg) rotate(315deg);
-webkit-transform:skew(45deg,0deg) rotate(315deg);
-o-transform:skew(45deg,0deg) rotate(315deg);
transform:skew(45deg,0deg) rotate(315deg);
position: absolute;
bottom: 30px; /* 40 cos(45) = 28 with an additional 2px margin*/
left: -25px; /*Because it looked good, but there is probably a mathematical link here as well*/
display: inline-block;
/*// width: 100%;*/
width: 95px; /* 80 / cos(45) - 40 cos (45) = 85 where 80 is the height of the cell, 40 the width of the cell and 45 the transform angle*/ /*85 to 90*//*90 to 95 16818*/
text-align: left;
font-weight:500;
/*// white-space: nowrap;*/ /*whether to display in one line or not*/
}



.table-header-rotated th.rotate-45-90w{
height: 100px; /*80 to 100*/
width: 74px;
min-width: 45px;
max-width: 74px;
position: relative;
vertical-align: bottom;
padding: 0;
font-size: 12px;
line-height: 1;  
}

.table-header-rotated th.rotate-45-90w > div{
position: relative;
top: 0px;
left: 40px; /* 80 * tan(45) / 2 = 40 where 80 is the height on the cell and 45 is the transform angle*/
height: 100%;
-ms-transform:skew(-45deg,0deg);
-moz-transform:skew(-45deg,0deg);
-webkit-transform:skew(-45deg,0deg);
-o-transform:skew(-45deg,0deg);
transform:skew(-45deg,0deg);
overflow: hidden;
/*border-left: 1px solid #dddddd;
border-right: 1px solid #dddddd;*/
border-top: 1px solid #dddddd;
background-color: #2779aa;
color:white;

-webkit-print-color-adjust: exact !important;
 -moz-color-adjust: exact;
}

.table td.sortingRow {    
background-color: #2779aa;
}

.table-header-rotated th.rotate-45-90w span{
-ms-transform:skew(45deg,0deg) rotate(315deg);
-moz-transform:skew(45deg,0deg) rotate(315deg);
-webkit-transform:skew(45deg,0deg) rotate(315deg);
-o-transform:skew(45deg,0deg) rotate(315deg);
transform:skew(45deg,0deg) rotate(315deg);
position: absolute;
bottom: 30px; /* 40 cos(45) = 28 with an additional 2px margin*/
left: -25px; /*Because it looked good, but there is probably a mathematical link here as well*/
display: inline-block;
/*// width: 100%;*/
width: 87px; /* 80 / cos(45) - 40 cos (45) = 85 where 80 is the height of the cell, 40 the width of the cell and 45 the transform angle*/
text-align: left;
font-weight:500;
padding-top:25px;
/*// white-space: nowrap;*/ /*whether to display in one line or not*/
}


.table-title {
  /* float: left; */
  padding: .3em .3em .3em .3em;
  position: relative;
  font-size: 13px;
  border-left: 0 none;
  border-right: 0 none;
  border-top: 0 none;
  text-align: center;
  font-weight:500;
  border: 1px solid #aed0ea;
  background: #deedf7; 
  color: #222;
  display: block !important;
}

.ui-jqgrid-view {
  position: relative;
  float: left;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  left: 0;
  top: 0;
  padding: 0;
  font-size: 11px;
}

.ui-state-default, .ui-jqgrid-hdiv {
  background: none repeat scroll 0 0 #2779aa !important;
  color: white !important;
  margin-left: -1px !important;
  margin-top: -1px !important;
border-top-left-radius: 8px;
border-top-right-radius: 5px;
}

.locationHeader{
  background-color: gray; 
  width:50px;
  border-top: 2px solid black;
  border-left: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
}
.locationName{
  font-weight: 800 !important;
  font-size: 0.7rem !important;
}

.locationCell{
  background: rgb(192,192,192) !important;
  font-weight: 700;
}

.firstEmptyCell{
  height:0;
  width:54px; 
  background-color: #2779aa;
}

.emptyCell{
  height:0;
  width:45px; 
  background-color: #2779aa;
}

.cellData {
  font-size: 11px;
  cursor: pointer;
}

.cellDataName {
  text-align:center;
  font-size: 11px;
  font-weight: bold;
  background-color: #dcdcdc;
  cursor:pointer;    
  padding-left: 5px;
}

.cellDataName a {
  cursor:pointer;
  text-decoration: none;  
  color: #222;  
}

.cellDataName a:hover {
  color:#2779aa;
}

.cell-wrapper {
  font-size: small;
  font-weight: 500;
}

.active {
  visibility: collapse;
}

.deactive {
  visibility:visible;
}

.locationGroup{
  display: flex;
  justify-content: left;
  align-items: center;
  width: 100% !important;
}

.tableArrow{
  margin-left:10px;
}

.thalign{
  padding-top: 0px;
    padding-left: 5px;
    padding-right: 2px;
    padding-bottom: 2px;
}
