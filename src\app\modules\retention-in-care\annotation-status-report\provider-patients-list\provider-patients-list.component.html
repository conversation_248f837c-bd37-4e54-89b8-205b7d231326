<div class="provider-view">
    <div class="topButtonRow">
        <a class="CMbtn" routerLink="/OutreachAndRetention/AnnotationStatusReport">
            Parent
        </a>
        <div id="dvError" style="float: left; margin-left: 30px; display: none; width: 60%">
            <a href="#" id="btnClose" class="errorepopupclose">
                <img id="imgMsg" src="../../../../assets/images/close.png" alt="#"></a>
            <p id="errorMsg" class="erroemsg errormsgdiv" style="margin: 1px;">Please enter notes...</p>
        </div>
        <img src="../../../../assets/images/printimg5.png" alt="#" title="PrintPDFView" class="imgIcons"
            Style="right: 54px;" onClick="printProviderPatientListView()" />
        <img src="../../../../assets/images/ZExcel.png" alt="#" title="Excel" class="imgIcons" Style="right: -27px;"
            (click)="providerPatientListExportExcel()" />
        <img src="../../../../assets/images/ZHTML.png" alt="#" title="HTML" class="imgIcons" Style="right: -110px;"
            onclick="providerPatientListHtml()" />
    </div>
    <div id="printProviderPatientListView">
        <div class="ui-jqgrid-view" style="width: 85%; border: 2px;">
            <div>
                <span class="table-title">
                    Annotation Status Report for: {{selectedRetentionProvider.provideR_NM}}
                    <a role="link" class="ui-corner-all ui-jqgrid-titlebar-close" title="Toggle Expand Collapse Grid">
                        <span class="fa fa-chevron-circle-up"></span>
                    </a>
                </span>
            </div>
            <div class="ui-state-default ui-corner-top ui-jqgrid-hdiv" style="width: 100%; position: relative;"
                id="printproviderExcel">
                <div class="ui-jqgrid-hbox" style="overflow: auto;height: 80vh;">
                    <table class="ui-jqgrid-htable" id="tableProviderData"
                        style="width:1px; border: 5px; color: black;">
                        <thead>
                            <tr class="ui-jqgrid-labels ui-sortable">
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 152px;">
                                    <div class="ui-jqgrid-sortable">
                                        Patient Name
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 48px;">
                                    <div class="ui-jqgrid-sortable">
                                        MRN
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 76px;">
                                    <div class="ui-jqgrid-sortable">
                                        Alert Date
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 96px;">
                                    <div class="ui-jqgrid-sortable">
                                        Last Attended <br>Appointment <br>Date
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 76px;">
                                    <div class="ui-jqgrid-sortable">
                                        Status Date
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 120px;">
                                    <div class="ui-jqgrid-sortable">
                                        Status
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 176px;">
                                    <div class="ui-jqgrid-sortable">
                                        Notes
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 176px;">
                                    <div class="ui-jqgrid-sortable">
                                        Change Status To
                                    </div>
                                </th>
                                <th class="ui-state-default ui-th-column ui-th-ltr" style="width: 80px;">
                                    <div class="ui-jqgrid-sortable">
                                        Edit
                                    </div>
                                </th>
                            </tr>
                            <tr class="ui-jqgrid-labels ui-sortable searchCriteriaTR">
                                <td class="searchButton">
                                    <input type="text" class="ui-widget-content" name="searchPatientName"
                                        [(ngModel)]="searchPatientName" (ngModelChange)="searchByPatientName()">
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td class="searchButton">
                                        <mat-select mat-form-field [(ngModel)]="filterStatus" multiple class="ui-widget-content dropdownMenu" name="filterStatus">
                                            <mat-option #allSelected (click)="toggleAll()" [value]="'-1'">All</mat-option>
                                            <mat-option (click)="singleSelection()" *ngFor="let status of statusSelectList | keyvalue" [value]="status.key">{{status.value}}</mat-option>
                                        </mat-select>  
                                </td>
                                <td class="searchButton">
                                    <input type="text" class="ui-widget-content" name="searchByNotes"
                                        [(ngModel)]="searchByNotes" (ngModelChange)="searchPatientByNotes()">
                                </td>
                                <td></td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="ui-widget-content jqgrow ui-row-ltr ui-state-highlight"
                                style="background: rgb(245, 245, 245);"
                                *ngFor="let patientList of providerPatientsList;">
                                <td class="ColorLocationRCProviderlevel" title="{{patientList.patienT_NAME}}">
                                    <a class="ColorLocationRCProviderlevel"
                                        [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', patientList.demographiC_ID]">
                                        {{patientList.patienT_NAME}}
                                    </a>
                                </td>
                                <td class="providerLevelData" title="{{patientList.mrn}}">
                                    {{patientList.mrn}}
                                </td>
                                <td class="providerLevelData" title="{{patientList.alerT_DATE}}">
                                    {{patientList.alerT_DATE | date:'dd-MMM-yyyy'}}
                                </td>
                                <td class="providerLevelData" title="{{patientList.mosT_RECENT_APPOINTMENT}}">
                                    {{patientList.mosT_RECENT_APPOINTMENT | date:'dd-MMM-yyyy'}}
                                </td>
                                <td class="providerLevelData" title="{{patientList.statuS_DATE}}">
                                    {{patientList.statuS_DATE | date:'dd-MMM-yyyy'}}
                                </td>
                                <td class="providerLevelData" title="{{patientList.ship_via}}">
                                    {{patientList.ship_via}}
                                </td>
                                <td class="searchButton">
                                    <div *ngIf="patientList.isEdit">
                                        <input type="text" name="notes" [(ngModel)]="patientList.notes"
                                            class="ui-widget-content" />
                                    </div>
                                    <div *ngIf="!patientList.isEdit">
                                        {{ patientList.notes }}
                                    </div>
                                </td>
                                <td class="searchButton">
                                    <div *ngIf="patientList.isEdit">
                                        <mat-select mat-form-field [(ngModel)]="patientList.changeStatusTo" class="ui-widget-content dropdownMenu" name="ship_via">
                                            <mat-option *ngFor="let status of getStatusList(patientList) | keyvalue" [value]="status.key">{{status.value}}</mat-option>
                                        </mat-select>
                                    </div>
                                </td>
                                <td style="text-align:center;">
                                    <div id="actionButtons" style="float:left; padding:5px; width:100%;">
                                        <a *ngIf="patientList.isEdit"
                                            style="height:auto; width:auto; cursor:pointer; margin:0;" alt="Save"
                                            (click)="savePatientDetails(patientList)">
                                            <img src="../../../../assets/images/save.png" height="16px" width="16px"
                                                alt="Save" title="Save">
                                        </a>
                                        <a *ngIf="!patientList.isEdit"
                                            style="height:auto; width:auto; cursor:pointer; margin:0;" alt="Edit"
                                            (click)="editPatientDetails(patientList)">
                                            <img src="../../../../assets/images/edit.png" height="16px" width="16px"
                                                alt="Edit" title="Edit">
                                        </a>
                                        <a style="height:auto; width:auto; cursor:pointer; margin-left:10px;"
                                            id="lnkCancelPortaljqg45" (click)="cancelPatientDetails(patientList)">
                                            <img src="../../../../assets/images/cancel.png" height="16px" width="16px"
                                                alt="Cancel" title="Cancel">
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- additional div for excel exporting as css is not applies -->
    <div id="printproviderExcel" style="display:none;height:20px">
        <div class="pdiv1" style="text-align:center;background:#deedf7;color: #222; ">
            <span id="PnameExcel"></span>
        </div>
        <p id='showPrintDataExcel'></p>
    </div>
    <div id="printprovider" style="display:none;height:20px">
        <div class="pdiv" style="text-align:center;background:#deedf7;color: #222; ">
            <span id="Pname"></span>
        </div>
        <p id='showPrintData'></p>
    </div>
</div>