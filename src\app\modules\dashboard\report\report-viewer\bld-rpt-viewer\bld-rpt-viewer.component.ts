import {
  Component,
  HostListener, 
  OnD<PERSON>roy
} from '@angular/core';
import { environment } from 'src/environments/environment';
import { ApiRoutes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ReportPanelTypes } from '../../../panels/report-panel-enums';
import { IReportParameter } from '../../models/report-params-model';
import { PanelService } from '../../../panels/PanelService';
import { Subscription } from 'rxjs';
import { EpividianCommon } from 'src/app/modules/utility/EpividianCommon';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
  selector: 'app-bld-rpt-viewer',
  templateUrl: './bld-rpt-viewer.component.html',
  styleUrls: ['./bld-rpt-viewer.component.css']
})

export class BldRptViewerComponent implements OnDestroy  {
  title = 'Report Title';
  public serviceAuthorizationToken: string = '';
  public toolbarSettings: any;
  public exportSettings: any;
  public serviceUrl: string = '';
  public reportPath: string = '';
  public serverUrl: string = "";
  public processingMode: string = 'Remote';
  public parameters: any;
  public routerState: any;
  public reportInfo: any;
  public panelType: ReportPanelTypes = 0;
  public isReady: boolean = false;
  private hasViewChecked = false;
  private currentJWt: string = '';
  site: string = '';

  public pageSubscriptions: Subscription = new Subscription;

  constructor(
    private userContext: UserContext,
    public panelService: PanelService,
    public epividianCommon: EpividianCommon,
    private auditService: AuditService) {

      this.currentJWt  = this.epividianCommon.LoadValidSessionObj()?.access_token || '';
      
      this.site = this.userContext.GetCurrentSiteValue().toString();
      if (this.site != '0') {
        this.viewerSetup();
      }

      this.pageSubscriptions.add(
        userContext.getCurrentSite().subscribe(site => {
          this.site = site.toString();
          this.viewerSetup();
      }));
  }  
  
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  @HostListener('window:beforeunload', ['$event']) 
      unloadHandler(event: Event) {        
          var reportviewerObj = $("#reportviewer").data("boldReportViewer");
          if (reportviewerObj && 'destroy' in reportviewerObj) {
            reportviewerObj.destroy();
        }
      }

  viewerSetup() {
    this.currentJWt  = this.epividianCommon.LoadValidSessionObj()?.access_token || '';
    this.serviceUrl = `${environment.apiV2}${ApiRoutes.reportViewer.replace(
      '{{siteid}}',
      this.site
    )}`;

    this.processingMode = 'Remote';
    this.serviceAuthorizationToken = `bearer ${this.currentJWt}`;
  }

  public InitBoldReportViewer(data, params: IReportParameter[])
  {
    this.reportInfo  = data.reportInfo;
    this.reportPath = data.reportPath;
    this.panelType = data.panelType;

    let toolbarItems;
    if (environment.showBoldReportParameters) {
      toolbarItems = ej.ReportViewer.ToolbarItems.All;
    } else {
      toolbarItems =
        ej.ReportViewer.ToolbarItems.All &
        ~ej.ReportViewer.ToolbarItems.Parameters;
    }

    //This hides the parmeter for the viewer.
    this.toolbarSettings = {
      // showToolbar: true,
      items: toolbarItems,      
    };

    this.exportSettings = {
      exportOptions:
        ej.ReportViewer.ExportOptions.All & ~ej.ReportViewer.ExportOptions.Html,
    };

    this.title = this.reportInfo.reportName;
    this.serviceUrl = `${environment.apiV2}${ApiRoutes.reportViewer.replace(
      '{{siteid}}',
      this.site
    )}`;

    this.auditService.setPageAudit(Page.Report, this.reportInfo.reportName);
    this.serverUrl = environment.reportingUrl;
    this.serviceAuthorizationToken = `bearer ${this.currentJWt}`;
    this.processingMode = 'Remote';
    this.isReady =true;
    this.parameters = params;
  }

  //This is needed for FireFox only.. something in bootstrap doesn't resize properly on load without trigger this event.
  ngAfterViewChecked() {
    this.forceResize();
  }

  public buildParameters(
    paramName: string,
    paramLbl: any,
    paramVal: any = paramLbl
  ): any  {
    return (this.parameters =
      {
        name: paramName,
        labels: paramLbl,
        values: paramVal,
        nullable: false,
      });

  }

  public onReportLoaded() {
  }

  public OnViewReportClick(event) {
  }

  onAjaxRequest(event)
  {
    this.currentJWt = this.epividianCommon.LoadValidSessionObj()?.access_token || '';
    this.serviceAuthorizationToken = 'Bearer ' + this.currentJWt;
    event.headers.push({ Key: 'Authorization', Value: this.serviceAuthorizationToken });

    let pageAudit = this.auditService.getPageAudit();
    event.headers.push({ Key: 'AuditRef', Value: pageAudit.guid || "" });
    event.headers.push({ Key: 'AuditPage', Value: pageAudit.page || "" });
    event.headers.push({ Key: 'AuditSiteId', Value: pageAudit.siteId || "" });
    event.headers.push({ Key: 'AuditParams', Value: JSON.stringify(pageAudit.parameters || "") });
  }

  public forceResize(forceResize: boolean = false)
  {
    setTimeout(() => {
      if (!this.hasViewChecked || forceResize) {

        window.dispatchEvent(new Event('resize'));
        this.hasViewChecked = true;
       
      }
   }, 1000);
  }

}
