import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PatientMeasure } from 'src/app/modules/annotation/annotation-models';
import { RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result, State } from '../../retention-models';
import { RetentionService } from '../../retention.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { MatOption } from '@angular/material/core';
import { MatSelect } from '@angular/material/select';
import { Subscription } from 'rxjs';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

@Component({
  selector: 'provider-patients-list',
  templateUrl: './provider-patients-list.component.html',
  styleUrls: ['./provider-patients-list.component.scss']
})

export class ProviderPatientsListComponent implements On<PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  siteId: string = "0";
  searchPatientName: any;
  searchByNotes: any;
  providerId: any = 0;
  locationId: any = 0;
  columnName: any = "";
  filterStatus: string[] = [];
  statusSelectList: {[key: number]:string} = {13:'New Risk Detected', 14:'Agreed to Visit', 15:'Scheduled Visit', 17:'Left Voicemail', 19:'Refused Visit', 20:'Moved', 21:'Transferred', 22:'Deceased', 23:'Incarcerated', 24:'Staff Declined to Contact', 25:'Custom Notation', 26:'No Phone Number', 27:'No Voicemail', 28:'Bad Number/No Service', 29:'Cannot be reached', 30:'Phone Number Corrected', 31:'Remind Later', 32:'Requested Appt Change', 33:'Confirmed Visit', 34:'Already Received Injection'};
  providerPatientsList: State[] = [];
  selectedRetentionProvider: RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result = new RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result();

  private pageSubscriptions: Subscription = new Subscription;

  @ViewChild('allSelected') private allSelected: MatOption = {} as MatOption;

  constructor(
    private _Activatedroute: ActivatedRoute,
    private retentionInCare: RetentionService,
    private userContext: UserContext,
    private ref: ChangeDetectorRef
  ) { }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {
  
    this.selectedRetentionProvider = this.retentionInCare.selectedRetentionProvider;
    this.providerId = this._Activatedroute.snapshot.paramMap.get("providerId");
    this.locationId = this._Activatedroute.snapshot.paramMap.get("locationId");
    this.columnName = this._Activatedroute.snapshot.queryParamMap.get("columnName");

    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    if (this.siteId != '0') {
      this.loadProviderPatientsList(this.siteId, true);
    }

    //Bind the site context to the page.
    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(siteId => {
        this.siteId = siteId.toString();
        this.loadProviderPatientsList(this.siteId, false);
      })
    );

    //this.loadProviderPatientsList(this.siteId);
    this.getDefaultStatus();
    this.toggleAll();
  }


  loadProviderPatientsList(siteId: string, intialLoad: boolean) {
    /* Patient list by Providers API call to show Patient List
          Fetching all Patients under selected provider by provider_id and location_id */
          this.retentionInCare.GetProviderViewData(siteId, this.providerId, this.locationId, { columnName: this.columnName }).subscribe(result => {
            if (result) {
              if (intialLoad)
              {
                 this.getDefaultStatus();
              }
              this.providerPatientsList = result;
              this.onStatusFilterChange();
            }
          });             
  }

  getDefaultStatus(){
    var statusId: string = '';
    if (this.columnName != null)
    {
      for(var pair in this.statusSelectList)
      {
        if(this.statusSelectList[pair].toLowerCase() == this.columnName.replaceAll('_',' '))
        {
          statusId = pair;
        }
        if(this.columnName == "new_alerts")
        {
          statusId = '13';
        }
        if(this.columnName == "wrong_number")
        {
          statusId = '28';
        }
      }
    }
    if( statusId != '')
    {
    this.filterStatus.push(statusId);
    }
    else
    {
      this.filterStatus = ['-1'];
      for(var key in this.statusSelectList)
        {
            this.filterStatus.push(key);
        }
    }
  }

  getStatusList(item: State): { [key: number]: string } {
    const commonStatus = {
        [item.annotatE_OPTION_ID]: item.ship_via,
        14: 'Agreed to Visit',
        15: 'Scheduled Visit',
        17: 'Left Voicemail',
        19: 'Refused Visit',
        20: 'Moved',
        21: 'Transferred',
        22: 'Deceased',
        23: 'Incarcerated',
        24: 'Staff Declined to Contact',
        25: 'Custom Notation',
        27: 'No Voicemail',
        28: 'Bad Number/No Service'
    };

    item.changeStatusTo = item.changeStatusTo || item.annotatE_OPTION_ID.toString();

    switch (item.primarY_MEASURE_ID) {
        case 79:
            return {
                ...commonStatus,
                34: 'Already Received Injection'
            };
        case 68:
            return {
                32: 'Requested Appt Change',
                33: 'Confirmed Visit',
                ...commonStatus
            };
        default:
            return commonStatus;
    }
  }

  // Function is used to search patient's by patient name
  searchByPatientName() {
    if (this.searchPatientName == '') {
      // If search string is null/empty, show all the patients list
      this.ngOnInit();
    } else {
      // Shows the patients list according to search criteria
      this.providerPatientsList = this.providerPatientsList.filter((res) => {
        return res.patienT_NAME
          .toLocaleLowerCase()
          .match(this.searchPatientName.toLocaleLowerCase());
      });
    }
  }

  // Function is used to search patient's by notes
  searchPatientByNotes() {
    if (this.searchByNotes == '') {
      // If search string is null/empty, show all the patients list
      this.ngOnInit();
    } else {
      // Shows the patients list according to search criteria
      this.providerPatientsList = this.providerPatientsList.filter((res) => {
        return res.notes == null ? "" :  res.notes
          .toLowerCase()
          .includes(this.searchByNotes.toLowerCase());
      });
    }
  }

  onStatusFilterChange()
  {
    var tempList: State[] = [];
    var filteredList: State[] = [];

    if (this.filterStatus.length > 0 && !this.allSelected.selected)
    {

      this.retentionInCare.GetProviderViewData(this.siteId, this.providerId, this.locationId, { columnName: '' }).subscribe(result => {
        if (result) {
          filteredList = result.filter((res) => {

            return this.filterStatus.includes(res.annotatE_OPTION_ID.toString());
          });
         
          this.providerPatientsList = filteredList.map(value=>value);
        }
      });

  
    }
    else
    {
      this.retentionInCare.GetProviderViewData(this.siteId, this.providerId, this.locationId, { columnName: '' }).subscribe(result => {
        if (result) {
          this.providerPatientsList = result;
        }
      }); 
    }
  }

  savePatientDetails(item: State) {
    let payload = new PatientMeasure();

    payload.annotateId = item.annotatE_ID;
    payload.annotateOptionId = Number(item.changeStatusTo)||0;
    payload.annotateDescription = this.statusSelectList[item.changeStatusTo];
    payload.annotateFreetext = item.notes;

    this.retentionInCare.SaveProviderAnnotationStatus(this.siteId, payload).subscribe(res => {
      if (res.ok==true) {
        item.isEdit = false;
        this.onStatusFilterChange();
      }
    });
  }

  editPatientDetails(item: State) {
    item.isEdit = true;
    this.ref.detectChanges();
  }

  cancelPatientDetails(item: State) {
    item.isEdit = false;
  }

  singleSelection()
  {
    if(this.allSelected.selected)
    {
      this.allSelected.deselect();
      this.filterStatus = this.filterStatus.filter(x => x != '-1')
    }
    this.onStatusFilterChange();
  }

  toggleAll()
  {
    if(this.allSelected.selected)
    {
      this.filterStatus = ['-1']
      for(var key in this.statusSelectList)
      {
          this.filterStatus.push(key);
      }
    }
    else
    {
      this.filterStatus = [];
    }
    this.onStatusFilterChange();
  }

  providerPatientListExportExcel() {
    // Select the table and clone it to manipulate without affecting the UI bindings
    const tableElement = document.getElementById('tableProviderData') as HTMLTableElement;
    const clonedTable = tableElement.cloneNode(true) as HTMLTableElement;
  
    // Remove the filter row for status filter
    const filterRow = clonedTable.querySelector('.searchCriteriaTR');
    if (filterRow) {
      filterRow.remove();
    }
  
    // Get the provider name for file naming
    const providerName = this.selectedRetentionProvider.provideR_NM || 'Patient';
  
    // Convert the cleaned cloned table to a workbook and export as Excel
    const workbook = XLSX.utils.table_to_book(clonedTable, { sheet: 'Sheet1' });
    const workbookOutput = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([workbookOutput], { type: 'application/octet-stream' });
  
    // Save the file with the providers's name appended
    saveAs(blob, `${providerName}_Annotation_Status_Report.xlsx`);
  }
  
}
