<div class="popupbg">
<div class="popupbgcolor">
<form [formGroup]="formChangePassword">
<h2 mat-dialog-title style="color:white;">Change your Password</h2>
<mat-dialog-content style="color: rgb(198, 185, 243)">Enter Current Password</mat-dialog-content>
<mat-form-field>
<div class="inputBox"><input matInput [type]="showpass ? 'password' : 'text'" [formControl]="formControls.currentPasswordFC"></div>
</mat-form-field>
<br>
<mat-form-field><div class="inputBox"><input matInput [formControl]="formControls.newPassword1FC" [type]="showpass ? 'password' : 'text'" placeholder="New Password"></div></mat-form-field>
<div></div>
<mat-form-field><div class="inputBox"><input matInput [type]="showpass ? 'password' : 'text'" [formControl]="formControls.newPassword2FC" placeholder="Confirm Password"></div></mat-form-field>
<mat-dialog-content>
  <div style="margin-top:10px;">
    <div style="font-weight: bold; color:white">Your Password Must:</div>
    <ul style="margin:0; color: white;">
      <li>be at least 8 characters long</li>
      <li class="passwordAlphaNumeric">contain at least one lowercase letter, one uppercase letter, one number, and one symbol like (%, *, $, or !). Must not contain spaces.</li>
      <li class="passwordPrevious">not be identical to your previous password</li>
    </ul>
  </div>
  <div style="margin-top:10px;">
    <div style="color:white; font-weight: bold;">For maximum security:</div>
    <ul style="margin:0;" style="color: white;">
      <li>choose long password that you don't use elsewhere</li>
      <li>avoid easy-to-guess personal information like pet names</li>
      <li>use a password manager rather than writing it down</li>
    </ul>
  </div>
</mat-dialog-content>
<mat-dialog-actions>
  <button  style="background-color: gray; color: white;" *ngIf="!this.formChangePassword.valid" mat-button mat-dialog-close>{{cancelClose}}</button>
  <button  style="background-color: #00518F; color: white;" *ngIf="this.formChangePassword.valid" mat-button mat-dialog-close>{{cancelClose}}</button>
  <!-- The mat-dialog-close directive optionally accepts a value as a result for the dialog. -->
  <button mat-button *ngIf="!this.formChangePassword.valid" disabled>Update</button>
  <button mat-button style="background-color: gray; color: white;" *ngIf="this.formChangePassword.valid" (click)=changeUserPassword()>Update</button>
</mat-dialog-actions>
</form>
</div>
</div>



