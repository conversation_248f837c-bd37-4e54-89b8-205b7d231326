import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl,  Validators,
  ValidatorFn,  AbstractControl, FormGroup } from '@angular/forms';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor } from '../report-panel-enums';

@Component({
  selector: 'pnl-from-to-date',
  templateUrl: './pnl-from-to-date.component.html',
  styleUrls: ['./pnl-from-to-date.component.scss']
})
export class PnlFromToDateComponent implements OnInit {

  @Input() rptpanFrmDate: FormControl;
  @Input() rptpanToDate: FormControl;
  allowReport: boolean = false;
  rptbtnColor: string;
  noSearchPan: boolean = false;
  fg: FormGroup;
  
  constructor(
    public panelService:PanelService,
    private formBuilder: FormBuilder
  ) {
    this.rptpanFrmDate = new FormControl;
    this.rptpanToDate = new FormControl;
    this.fg = new FormGroup(
      {
        from: this.rptpanFrmDate,
        to:  this.rptpanToDate
      },
      [Validators.required, this.dateRangeValidator]
    );
    this.rptbtnColor = this.rptbtnColor= btnColor.btnPrimaryColor;
    this.enableReportButton();
  }

  ngOnInit(): void {
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    
    const from = this.fg &&  this.fg.get("from")
    const to = this.fg && this.fg.get("to");
    if (from && to) {
      invalid = new Date(from.value).valueOf() > new Date(to.value).valueOf();
     
    }
   
    return invalid ? { invalidRange: { from, to } } : null;
  };

  readyToRun(): void{
    this.enableReportButton();    
  }

  runReport(): void {
    if (this.allowReport)
    {
      this.panelService.InitBoldReport();
    }
  
    
  }

  enableReportButton() : void
  {
    this.allowReport = this.rptpanFrmDate.value != null && this.rptpanToDate.value != null && !this.fg.invalid;
  
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}



