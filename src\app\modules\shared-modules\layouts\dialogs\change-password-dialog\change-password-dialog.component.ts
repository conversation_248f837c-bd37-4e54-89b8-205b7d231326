import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LoginService } from 'src/app/modules/login/services/login/login.service';
import { DialogResponseComponent } from '../dialog-response/dialog-response.component';


@Component({
  selector: 'app-change-password-dialog',
  templateUrl: './change-password-dialog.component.html',
  styleUrls: ['./change-password-dialog.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChangePasswordDialogComponent implements OnInit {

  dialogRef: MatDialogRef<ChangePasswordDialogComponent,any> = {} as MatDialogRef<ChangePasswordDialogComponent, any>
  openDialog: MatDialogRef<DialogResponseComponent,any> = {} as MatDialogRef<DialogResponseComponent, any>
  updateResult: any;
  formChangePassword = new FormGroup({
    currentPasswordFC: new FormControl('', [Validators.required]),
    newPassword1FC:  new FormControl('', [Validators.required]),
    newPassword2FC: new FormControl('', [Validators.required])
  });
  public formControls;
  showpass: boolean = true;
  cancelClose: string = "Cancel";
  constructor(private _loginservice: LoginService, public dialog: MatDialog) {
    this.formControls = this.formChangePassword.controls;
  }

  ngOnInit(): void {
  }


  closeDialog(isValid: boolean)
  {
    if (isValid)
    {

    }
  }

  modalDialogOk() {
    this.openDialog.close();
  }


  public changeUserPassword()
  {
    var b = this.formControls.currentPasswordFC;
    this._loginservice.changePassword(this.formControls.currentPasswordFC.value,
                                      this.formControls.newPassword1FC.value,
                                      this.formControls.newPassword2FC.value)
         .subscribe(res => {
          if (res.succeeded){
            this.cancelClose="Close";
            this.openResponseDialog("Password Update Successful");
            this.openDialog.afterClosed().subscribe(() => {
            this.dialog.closeAll();
            })
          } else {
            this.openResponseDialog(res.error[0]);
          }
        });
  }


  public openResponseDialog(data: any) {
    this.openDialog = this.dialog.open(DialogResponseComponent, {
      data: data
    });
  }

}
