
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-multi-reporting-year',
  templateUrl: './pnl-multi-reporting-year.component.html',
  styleUrls: ['./pnl-multi-reporting-year.component.scss'],
})
export class PnlMultiReportingYearComponent implements OnInit {
  rptbtnColor: string;
  defaultYear: string;
  @Input() rptpanMultiReportingYear: FormControl;
  multiReportYearData: IReportParamData[] = [] as IReportParamData[];
  selectData: IData[] = [] as IData[];
  noSearchPan: boolean = false;
  isRunDisabled : boolean = true;

  constructor(public panelService: PanelService) {
    this.rptbtnColor = this.rptbtnColor = btnColor.btnSecondColor;
    this.rptpanMultiReportingYear = new FormControl();
    this.defaultYear='';
  }

  ngOnInit(): void {
    this.panelService
      .GetPanelData(ReportPanelTypes.pnlMultiReportingYear)
      .subscribe((s) => {
        this.multiReportYearData = s;
        this.selectData = s[0].data.sort((a, b) => +b.key - +a.key);
       
       
      });
  }

  readyToRun(tmp : Array<any>): void {
  

    if (tmp == null || tmp.length == 0)
    {
      this.rptbtnColor = btnColor.btnSecondColor;
      this.isRunDisabled = true;
    }
    else {
    this.rptbtnColor = btnColor.btnPrimaryColor;
    this.isRunDisabled = false;
    }
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
