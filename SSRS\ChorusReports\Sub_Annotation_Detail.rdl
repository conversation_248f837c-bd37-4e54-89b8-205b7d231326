<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.7.34202.233</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.7.34202.233</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-08-30T13:37:02.0676781Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_Sub_Annotation_Detail">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@MEASURE_ID">
            <Value>=Parameters!MEASURE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@PATIENT_ID">
            <Value>=Parameters!PATIENT_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>;WITH Sub_Annotation_Detail
AS
(
SELECT CREATED_DT,MODIFIED_USER,RESPONSE FROM [DM].[ANNOTATE_MEASURE_AUDIT]
WHERE MEASURE_ID = @MEASURE_ID AND PATIENT_ID = @PATIENT_ID AND RESPONSE IS NOT NULL
UNION 
SELECT CREATED_DT,MODIFIED_USER,RESPONSE FROM [DM].[ANNOTATE_MEASURE]
WHERE MEASURE_ID = @MEASURE_ID AND PATIENT_ID = @PATIENT_ID AND RESPONSE IS NOT NULL
)
SELECT CAST(CREATED_DT AS DATE) AS [DATE], MODIFIED_USER,RESPONSE FROM Sub_Annotation_Detail
ORDER BY CREATED_DT</CommandText>
      </Query>
      <Fields>
        <Field Name="DATE">
          <DataField>DATE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="MODIFIED_USER">
          <DataField>MODIFIED_USER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RESPONSE">
          <DataField>RESPONSE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.79in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.58528in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.44517in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.26958in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DATE">
                          <CanGrow>true</CanGrow>
                          <CanShrink>true</CanShrink>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DATE.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DATE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>=IIf(RowNumber(Nothing) Mod 2 = 0,"SILVER","WHITE")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle2">
                          <ReportItems>
                            <Textbox Name="MODIFIED_USER">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!MODIFIED_USER.Value + ""</Value>
                                      <Style>
                                        <FontFamily>Calibri</FontFamily>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>MODIFIED_USER</rd:DefaultName>
                              <Height>0.25in</Height>
                              <Width>1.54361in</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>4pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle1">
                          <ReportItems>
                            <Textbox Name="Textbox2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!RESPONSE.Value + ""</Value>
                                      <Style>
                                        <FontFamily>Calibri</FontFamily>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox2</rd:DefaultName>
                              <Height>0.25in</Height>
                              <Width>2.44517in</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>4pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <KeepTogether>true</KeepTogether>
            <DataSetName>DS_Sub_Annotation_Detail</DataSetName>
            <Height>0.26958in</Height>
            <Width>4.82045in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.26958in</Height>
        <Style />
      </Body>
      <Width>4.82045in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style>
          <BackgroundColor>= IIf(Parameters!RowNumber.Value Mod 2 = 0,"Lavender","#d4d4e5")</BackgroundColor>
        </Style>
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="MEASURE_ID">
      <DataType>String</DataType>
      <Prompt>MEASURE ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="PATIENT_ID">
      <DataType>String</DataType>
      <Prompt>PATIENT ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="RowNumber">
      <DataType>Integer</DataType>
      <Prompt>RowNumber</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="Height">
      <DataType>Float</DataType>
      <DefaultValue>
        <Values>
          <Value>2</Value>
        </Values>
      </DefaultValue>
      <Prompt>Height</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>MEASURE_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>PATIENT_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>RowNumber</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>Height</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b8bc3655-200b-4843-8d2b-1706ffbafd49</rd:ReportID>
</Report>
