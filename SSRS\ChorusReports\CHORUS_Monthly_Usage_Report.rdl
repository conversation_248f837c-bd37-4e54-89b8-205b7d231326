<?xml version="1.0" encoding="utf-8"?>
<Report MustUnderstand="df" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:df="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition/defaultfontfamily">
  <df:DefaultFontFamily>Segoe UI</df:DefaultFontFamily>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsChorus">
      <DataSourceReference>dsChorus</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>70a5d355-82c7-4e4b-b08f-7dd7ed68d7d1</rd:DataSourceID>
    </DataSource>
    <DataSource Name="dsAuthProvider">
      <DataSourceReference>dsAuthProvider</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>d1136830-397b-4ade-ac08-07d44a4b4e62</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="AllUsers">
      <Query>
        <DataSourceName>dsChorus</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@START_DT">
            <Value>=Parameters!START_DT.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@STOP_DT">
            <Value>=Parameters!STOP_DT.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>DECLARE @siteDb SYSNAME;
DECLARE @sql NVARCHAR(MAX) = N'';

-- Step 1: Pre-create temp table
CREATE TABLE #ProviderData (
    DB_NAME SYSNAME,
    SITE_ID INT,
    FIRST_NM NVARCHAR(255),
    LAST_NM NVARCHAR(255),
    NPI_ID NVARCHAR(255)
);


DECLARE @databases TABLE (
    Site_ID INT,
    DbName SYSNAME,
    Site_NM NVARCHAR(255)
);

INSERT INTO @databases (Site_ID, DbName, Site_NM)
SELECT Site_ID, 'SITE' + CAST(Site_ID AS VARCHAR), SITE_NM
FROM ADMIN.SITE
WHERE Site_ID NOT IN (0, 9901, 9902);

-- Step 2: Build dynamic SQL to insert into temp table from existing SITE databases
DECLARE db_cursor CURSOR FOR
SELECT Site_ID, DbName FROM @databases;

OPEN db_cursor;
FETCH NEXT FROM db_cursor INTO @siteDb, @siteDb;

WHILE @@FETCH_STATUS = 0
BEGIN
    BEGIN TRY
        IF EXISTS (
            SELECT 1 
            FROM sys.databases 
            WHERE name = @siteDb AND state = 0
        )
        BEGIN
            DECLARE @checkTableSQL NVARCHAR(MAX);
            SET @checkTableSQL = 'IF EXISTS (SELECT 1 FROM [' + @siteDb + '].INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ''CLEAN'' AND TABLE_NAME = ''PROVIDER'')
            BEGIN
                INSERT INTO #ProviderData
                SELECT ''' + @siteDb + ''' AS DB_NAME, SITE_ID, FIRST_NM, LAST_NM, NPI_ID 
                FROM [' + @siteDb + '].CLEAN.PROVIDER 
                WHERE NPI_ID IS NOT NULL;
            END';
            EXEC sp_executesql @checkTableSQL;
        END
    END TRY
    BEGIN CATCH
        PRINT 'Skipping database ' + @siteDb + ' due to error.';
    END CATCH
    FETCH NEXT FROM db_cursor INTO @siteDb, @siteDb;
END

CLOSE db_cursor;
DEALLOCATE db_cursor;

-- Step 3: Use #ProviderData in main query

WITH SITE_NAMES AS (
    SELECT Site_ID,
           CASE Site_ID
                WHEN 117 THEN '117 (AHF)'
                WHEN 107 THEN '107 (WFH)'
                WHEN 109 THEN '109 (Sinai)'
                WHEN 110 THEN '110 (Mills)'
                WHEN 116 THEN '116 (FIGHT)'
                WHEN 123 THEN '123 (Amity)'
                WHEN 124 THEN '124 (Rosedale)'
                WHEN 125 THEN '125 (Neighborhood)'
                WHEN 126 THEN '126 (CAN)'
                WHEN 127 THEN '127 (Fairgrove)'
                WHEN 131 THEN '131 (Ingham)'
                ELSE CAST(Site_ID AS VARCHAR) + ' (' + Site_NM + ')'
           END AS SITE_NAME
    FROM @databases
),
LOGINS AS (
-- App Logins
SELECT DISTINCT User_NM AS USERNAME,
       COUNT(Login_ID) AS LOGIN_COUNT,
       CAST((Login_DT - DAY(LOGIN_DT) + 1) AS DATE) AS LoginMonthFirstDay,
       SN.SITE_NAME,
       SG.SITE_ID,
       IIF(LEN(WEB_URL) &lt;= 3, 'Mobile App', 'Web Portal') AS TYPE,
       PD.NPI_ID
FROM [CHORUS].[CHORUS].[LOGIN_LOG] LL
LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USER_NM
LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
LEFT JOIN #ProviderData PD ON (SG.SITE_ID = PD.SITE_ID AND UD.FIRST_NM = PD.FIRST_NM AND UD.LAST_NM = PD.LAST_NM)
LEFT JOIN SITE_NAMES SN ON SG.SITE_ID = SN.SITE_ID
WHERE LDC.CODE_ID IN (1,11,16,19,21)
  AND DEFAULT_SITE = '1'
  AND EMAIL_TXT NOT LIKE '%@epividian.com'
  AND LOGIN_DT BETWEEN @START_DT AND @STOP_DT
GROUP BY CAST((Login_DT - DAY(LOGIN_DT) + 1) AS DATE), SG.SITE_ID, User_NM, IIF(LEN(WEB_URL) &lt;= 3, 'Mobile App', 'Web Portal'), PD.NPI_ID, SN.SITE_NAME

UNION

-- Web Portal Logins
SELECT DISTINCT USERNAME,
       COUNT(Login_ID) AS LOGIN_COUNT,
       CAST((Login_DT - DAY(LOGIN_DT) + 1) AS DATE) AS LoginMonthFirstDay,
       SN.SITE_NAME,
       SG.SITE_ID,
       'Web Portal' AS TYPE,
       PD.NPI_ID
FROM [Authprovider].[dbo].[LOGIN_LOG] LL
LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USERNAME
LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
LEFT JOIN #ProviderData PD ON (SG.SITE_ID = PD.SITE_ID AND UD.FIRST_NM = PD.FIRST_NM AND UD.LAST_NM = PD.LAST_NM)
LEFT JOIN SITE_NAMES SN ON SG.SITE_ID = SN.SITE_ID
WHERE LDC.CODE_ID IN (1,11,16,19,21)
  AND DEFAULT_SITE = '1'
  AND USERNAME NOT LIKE '%@epividian.com'
  AND LOGIN_DT BETWEEN @START_DT AND @STOP_DT
GROUP BY CAST((Login_DT - DAY(LOGIN_DT) + 1) AS DATE), SG.SITE_ID, USERNAME, PD.NPI_ID, SN.SITE_NAME
)

SELECT SITE_ID, SITE_NAME, LoginMonthFirstDay,
(SELECT COUNT(DISTINCT USERNAME) FROM LOGINS WHERE NPI_ID IS NOT NULL AND TYPE = 'Mobile App' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_PROVIDERS_APP,
(SELECT COUNT(DISTINCT USERNAME) FROM LOGINS WHERE NPI_ID IS NOT NULL AND TYPE = 'Web Portal' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_PROVIDERS_WEB,
(SELECT COUNT(DISTINCT USERNAME) FROM LOGINS WHERE TYPE = 'Mobile App' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_USERS_APP,
(SELECT COUNT(DISTINCT USERNAME) FROM LOGINS WHERE TYPE = 'Web Portal' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_USERS_WEB,
(SELECT SUM(LOGIN_COUNT) FROM LOGINS WHERE TYPE = 'Mobile App' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_LOGIN_APP,
(SELECT SUM(LOGIN_COUNT) FROM LOGINS WHERE TYPE = 'Web Portal' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay) AS TOTAL_LOGIN_WEB
FROM (SELECT DISTINCT SITE_ID, SITE_NAME, LoginMonthFirstDay FROM LOGINS) A
ORDER BY A.SITE_ID, A.LoginMonthFirstDay;

-- Cleanup temp table
DROP TABLE IF EXISTS #ProviderData;
</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="SITE_ID">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SITE_NAME">
          <DataField>SITE_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LoginMonthFirstDay">
          <DataField>LoginMonthFirstDay</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="TOTAL_PROVIDERS_APP">
          <DataField>TOTAL_PROVIDERS_APP</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_PROVIDERS_WEB">
          <DataField>TOTAL_PROVIDERS_WEB</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_USERS_APP">
          <DataField>TOTAL_USERS_APP</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_USERS_WEB">
          <DataField>TOTAL_USERS_WEB</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_LOGIN_APP">
          <DataField>TOTAL_LOGIN_APP</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_LOGIN_WEB">
          <DataField>TOTAL_LOGIN_WEB</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="FirstTimeUser">
      <Query>
        <DataSourceName>dsChorus</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@FIRST_LOGIN_MONTH">
            <Value>=Parameters!FIRST_LOGIN_MONTH.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>WITH LOGINS AS (
-- App Logins
SELECT DISTINCT User_NM AS USERNAME,
	    COUNT(Login_ID) AS LOGIN_COUNT
		,LOGIN_DT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,CASE WHEN sg.SITE_ID = 117 THEN '117 (AHF)'
	  WHEN sg.SITE_ID = 107 THEN '107 (WFH)'
	  WHEN sg.SITE_ID = 109 THEN '109 (Sinai)'
	  WHEN sg.SITE_ID = 110 THEN '110 (Mills)'
	  WHEN sg.SITE_ID = 116 THEN '116 (FIGHT)'
	  WHEN sg.SITE_ID = 123 THEN '123 (Amity)'
	  WHEN sg.SITE_ID = 124 THEN '124 (Rosedale)'
	  WHEN sg.SITE_ID = 125 THEN '125 (Neighborhood)'
	  WHEN sg.SITE_ID = 126 THEN '126 (CAN)'
	  WHEN sg.SITE_ID = 127 THEN '127 (Fairgrove)'
	  WHEN sg.SITE_ID = 131 THEN '131 (Ingham)'
	  END AS SITE_NAME,
	  SG.SITE_ID
	  ,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal') AS TYPE
	  ,ASSOCIATION_NM
  FROM [CHORUS].[CHORUS].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USER_NM
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
  LEFT OUTER JOIN (
  
  SELECT
		PA.SITE_ID,
        PA.USER_ID,
		AT.ASSOCIATION_NM
    FROM CHORUS.[ADMIN].SITE_PROVIDER_ASC PA
    LEFT JOIN CHORUS.ADMIN.ASSOCIATION_TYPE AS AT ON PA.ASSOCIATION_TYPE_ID = AT.ASSOCIATION_TYPE_ID
	WHERE ASSOCIATION_NM LIKE 'SELF'
    GROUP BY PA.USER_ID, ASSOCIATION_NM, site_id
							) S ON (SG.SITE_ID = S.SITE_ID AND ud.USER_ID = s.USER_ID)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND EMAIL_TXT not like '%@epividian.com'
	 --and LOGIN_DT BETWEEN @START_DT AND @STOP_DT
  GROUP BY LOGIN_DT, CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USER_NM,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal'), ASSOCIATION_NM
 UNION
  -- Web Portal Logins
SELECT DISTINCT Username,	
	    COUNT(Login_ID) AS LOGIN_COUNT
		, LOGIN_DT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,CASE WHEN sg.SITE_ID = 117 THEN '117 (AHF)'
	  WHEN sg.SITE_ID = 107 THEN '107 (WFH)'
	  WHEN sg.SITE_ID = 109 THEN '109 (Sinai)'
	  WHEN sg.SITE_ID = 110 THEN '110 (Mills)'
	  WHEN sg.SITE_ID = 116 THEN '116 (FIGHT)'
	  WHEN sg.SITE_ID = 123 THEN '123 (Amity)'
	  WHEN sg.SITE_ID = 124 THEN '124 (Rosedale)'
	  WHEN sg.SITE_ID = 125 THEN '125 (Neighborhood)'
	  WHEN sg.SITE_ID = 126 THEN '126 (CAN)'
	  WHEN sg.SITE_ID = 127 THEN '127 (Fairgrove)'
	  WHEN sg.SITE_ID = 131 THEN '131 (Ingham)'
	  END AS SITE_NAME,
	  SG.SITE_ID
	  ,'Web Portal' AS TYPE
	  ,ASSOCIATION_NM
  FROM [Authprovider].[dbo].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USERNAME
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
  LEFT OUTER JOIN (
  SELECT
		PA.SITE_ID,
        PA.USER_ID,
		AT.ASSOCIATION_NM
    FROM CHORUS.[ADMIN].SITE_PROVIDER_ASC PA
    LEFT JOIN CHORUS.ADMIN.ASSOCIATION_TYPE AS AT ON PA.ASSOCIATION_TYPE_ID = AT.ASSOCIATION_TYPE_ID
	WHERE ASSOCIATION_NM LIKE 'SELF'
    GROUP BY PA.USER_ID, ASSOCIATION_NM, site_id


							) S ON (SG.SITE_ID = S.SITE_ID AND ud.USER_ID = s.USER_ID)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND USERNAME not like '%@epividian.com'
	 --and LOGIN_DT BETWEEN @START_DT AND @STOP_DT
  GROUP BY LOGIN_DT, CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USERNAME, ASSOCIATION_NM
  )
  ,
FIRSTTIMEUSER AS (

SELECT USERNAME, 
LOGIN_COUNT, 
LoginMonthFirstDay, 
SITE_NAME,
SITE_ID, 
TYPE, 
ASSOCIATION_NM
FROM 
(
SELECT USERNAME, 
LOGIN_COUNT, 
LoginMonthFirstDay, 
LOGIN_DT,
SITE_NAME,
SITE_ID, 
TYPE,
ASSOCIATION_NM,
ROW_NUMBER() OVER (PARTITION BY USERNAME ORDER BY LOGIN_DT ASC) ROWNUM
FROM LOGINS
) L
WHERE L.ROWNUM = 1
AND Login_DT &gt;= @FIRST_LOGIN_MONTH

)

select SITE_ID, SITE_NAME, LoginMonthFirstDay,
(SELECT COUNT(DISTINCT USERNAME) FROM FIRSTTIMEUSER WHERE TYPE = 'Mobile App' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay
) AS TOTAL_FIRST_USERS_APP,
(SELECT COUNT(DISTINCT USERNAME) FROM FIRSTTIMEUSER WHERE TYPE = 'Web Portal' AND SITE_ID = A.SITE_ID AND LoginMonthFirstDay = A.LoginMonthFirstDay
) AS TOTAL_FIRST_USERS_WEB
FROM (SELECT DISTINCT SITE_ID, SITE_NAME, LoginMonthFirstDay FROM FIRSTTIMEUSER) A
ORDER BY A.SITE_ID, A.LoginMonthFirstDay</CommandText>
      </Query>
      <Fields>
        <Field Name="LoginMonthFirstDay">
          <DataField>LoginMonthFirstDay</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SITE_NAME">
          <DataField>SITE_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOTAL_FIRST_USERS_APP">
          <DataField>TOTAL_FIRST_USERS_APP</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SITE_ID">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_FIRST_USERS_WEB">
          <DataField>TOTAL_FIRST_USERS_WEB</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>0.00448cm</Top>
            <Left>0.02292in</Left>
            <Height>1.64251cm</Height>
            <Width>5.312cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="HIVFlowsheet">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CHORUS Monthly Usage Report </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#0097c4</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <Left>2.12814in</Left>
            <Height>0.66221in</Height>
            <Width>5.27435in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>1pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Through: </Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Today()</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <Format>yyyy-MM-dd</Format>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>0.4262in</Top>
            <Left>7.47194in</Left>
            <Height>0.25in</Height>
            <Width>2.09778in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox47">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Epividian® CHORUS</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>™</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> Report</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Top>0.00176in</Top>
            <Left>7.47194in</Left>
            <Height>0.38267in</Height>
            <Width>2.09778in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.6071in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.18769in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.85145in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.89468in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.00588in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Log Ins</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox27</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.60417in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Site ID</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Providers App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Providers Portal</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Users App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Users Portal</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Login App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Login Portal</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox28</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.07in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle5">
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                          </Style>
                        </Rectangle>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox21">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox21</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox25">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox25</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox29">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox29</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SITE_ID">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!SITE_NAME.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAST_30_ACT_IND">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAST_30_ACT_IND</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_PROVIDERS_APP">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_PROVIDERS_APP.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Mobile App"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="Value"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_PROVIDERS_WEB">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_PROVIDERS_WEB.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Web Portal"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="Value"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_USERS_APP">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_USERS_APP.Value</Value>
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Mobile App"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="None"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_USERS_WEB">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_USERS_WEB.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Web Portal"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="None"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_LOGIN_APP">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_LOGIN_APP.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Mobile App"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="None"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_LOGIN_WEB">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_LOGIN_WEB.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Web Portal"</Value>
                                    </Parameter>
                                    <Parameter Name="NPI_ID">
                                      <Value>="None"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <FixedRowHeaders>true</FixedRowHeaders>
            <DataSetName>AllUsers</DataSetName>
            <Top>0.70398in</Top>
            <Left>0.02292in</Left>
            <Height>1.17417in</Height>
            <Width>9.5468in</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Chart Name="Chart1">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart1_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart1_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_PROVIDERS_APP">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_PROVIDERS_APP.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Providers App 
</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>4.88269in</Top>
            <Left>0.17585in</Left>
            <Height>2.99454in</Height>
            <Width>3.64187in</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart2">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart2_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart2_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_PROVIDERS_WEB">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_PROVIDERS_WEB.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Providers Portal</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>4.88269in</Top>
            <Left>4.01764in</Left>
            <Height>2.99454in</Height>
            <Width>3.55208in</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart3">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart3_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart3_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_USERS_APP">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_USERS_APP.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>MM/dd/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Users App</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>4.88269in</Top>
            <Left>7.90305in</Left>
            <Height>2.99454in</Height>
            <Width>3.64583in</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart4">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart4_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart4_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_USERS_WEB">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_USERS_WEB.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Users Portal</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>4.88269in</Top>
            <Left>11.97792in</Left>
            <Height>2.99454in</Height>
            <Width>3.69792in</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart5">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart5_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart5_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_LOGIN_APP">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_LOGIN_APP.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Login App</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>8.03in</Top>
            <Left>0.17584in</Left>
            <Height>3.10417in</Height>
            <Width>3.64188in</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart6">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart6_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart6_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_LOGIN_WEB">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_LOGIN_WEB.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <AllowLabelRotation>None</AllowLabelRotation>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>Total Login Portal</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>AllUsers</DataSetName>
            <Top>8.03in</Top>
            <Left>4.01763in</Left>
            <Height>3.10417in</Height>
            <Width>3.55208in</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart7">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart7_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart7_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_FIRST_USERS_APP">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <X>=Count(Fields!LoginMonthFirstDay.Value)</X>
                        <Y>=Sum(Fields!TOTAL_FIRST_USERS_APP.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>First Time User App</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>FirstTimeUser</DataSetName>
            <Top>8.03in</Top>
            <Left>7.90305in</Left>
            <Height>3.10417in</Height>
            <Width>3.64583in</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Chart Name="Chart8">
            <ChartCategoryHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart8_CategoryGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LoginMonthFirstDay.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!LoginMonthFirstDay.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartCategoryHierarchy>
            <ChartSeriesHierarchy>
              <ChartMembers>
                <ChartMember>
                  <Group Name="Chart8_SeriesGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!SITE_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SITE_ID.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <Label>=Fields!SITE_ID.Value</Label>
                </ChartMember>
              </ChartMembers>
            </ChartSeriesHierarchy>
            <ChartData>
              <ChartSeriesCollection>
                <ChartSeries Name="TOTAL_FIRST_USERS_WEB">
                  <ChartDataPoints>
                    <ChartDataPoint>
                      <ChartDataPointValues>
                        <Y>=Sum(Fields!TOTAL_FIRST_USERS_WEB.Value)</Y>
                      </ChartDataPointValues>
                      <ChartDataLabel>
                        <Style />
                      </ChartDataLabel>
                      <Style />
                      <ChartMarker>
                        <Type>Auto</Type>
                        <Style />
                      </ChartMarker>
                      <DataElementOutput>Output</DataElementOutput>
                    </ChartDataPoint>
                  </ChartDataPoints>
                  <Type>Line</Type>
                  <Style />
                  <ChartEmptyPoints>
                    <Style />
                    <ChartMarker>
                      <Style />
                    </ChartMarker>
                    <ChartDataLabel>
                      <Style />
                    </ChartDataLabel>
                  </ChartEmptyPoints>
                  <ValueAxisName>Primary</ValueAxisName>
                  <CategoryAxisName>Primary</CategoryAxisName>
                  <ChartSmartLabel>
                    <CalloutLineColor>Black</CalloutLineColor>
                    <MinMovingDistance>0pt</MinMovingDistance>
                  </ChartSmartLabel>
                </ChartSeries>
              </ChartSeriesCollection>
            </ChartData>
            <ChartAreas>
              <ChartArea Name="Default">
                <ChartCategoryAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Format>M/d/yyyy</Format>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                      <IntervalType>Months</IntervalType>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Scalar>true</Scalar>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Enabled>False</Enabled>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartCategoryAxes>
                <ChartValueAxes>
                  <ChartAxis Name="Primary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                  <ChartAxis Name="Secondary">
                    <Style>
                      <Border>
                        <Color>Gainsboro</Color>
                        <Style>None</Style>
                      </Border>
                      <FontSize>8pt</FontSize>
                      <Color>#5c5c5c</Color>
                    </Style>
                    <ChartAxisTitle>
                      <Caption />
                      <Style>
                        <FontSize>9pt</FontSize>
                        <Color>#5c5c5c</Color>
                      </Style>
                    </ChartAxisTitle>
                    <ChartMajorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                    </ChartMajorGridLines>
                    <ChartMinorGridLines>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>Dotted</Style>
                        </Border>
                      </Style>
                    </ChartMinorGridLines>
                    <ChartMajorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </ChartMajorTickMarks>
                    <ChartMinorTickMarks>
                      <Style>
                        <Border>
                          <Color>Gainsboro</Color>
                        </Border>
                      </Style>
                      <Length>0.5</Length>
                    </ChartMinorTickMarks>
                    <CrossAt>NaN</CrossAt>
                    <Location>Opposite</Location>
                    <Minimum>NaN</Minimum>
                    <Maximum>NaN</Maximum>
                    <ChartAxisScaleBreak>
                      <Style />
                    </ChartAxisScaleBreak>
                  </ChartAxis>
                </ChartValueAxes>
                <Style>
                  <BackgroundColor>#00ffffff</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </ChartArea>
            </ChartAreas>
            <ChartLegends>
              <ChartLegend Name="Default">
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>8pt</FontSize>
                </Style>
                <Position>TopLeft</Position>
                <ChartLegendTitle>
                  <Caption />
                  <Style>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </ChartLegendTitle>
                <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                <ColumnSeparatorColor>Black</ColumnSeparatorColor>
              </ChartLegend>
            </ChartLegends>
            <ChartTitles>
              <ChartTitle Name="Default">
                <Caption>First Time User Portal
</Caption>
                <Style>
                  <BackgroundGradientType>None</BackgroundGradientType>
                  <FontSize>14pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextAlign>Left</TextAlign>
                  <VerticalAlign>Top</VerticalAlign>
                </Style>
              </ChartTitle>
            </ChartTitles>
            <Palette>Pacific</Palette>
            <ChartBorderSkin>
              <Style>
                <BackgroundColor>Gray</BackgroundColor>
                <BackgroundGradientType>None</BackgroundGradientType>
                <Color>White</Color>
              </Style>
            </ChartBorderSkin>
            <ChartNoDataMessage Name="NoDataMessage">
              <Caption>No Data Available</Caption>
              <Style>
                <BackgroundGradientType>None</BackgroundGradientType>
                <FontWeight>Bold</FontWeight>
                <TextAlign>General</TextAlign>
                <VerticalAlign>Top</VerticalAlign>
              </Style>
            </ChartNoDataMessage>
            <DataSetName>FirstTimeUser</DataSetName>
            <Top>8.03in</Top>
            <Left>11.97792in</Left>
            <Height>3.10417in</Height>
            <Width>3.69792in</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Color>LightGrey</Color>
                <Style>None</Style>
              </Border>
              <BackgroundColor>White</BackgroundColor>
              <BackgroundGradientType>None</BackgroundGradientType>
            </Style>
          </Chart>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.6071in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.18769in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.85145in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.89468in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.00588in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>First Time Log Ins</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox31">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox32">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox27</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0c090a</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.60417in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Site ID</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total First Time Users App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox34">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total First Time Users Web</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox35">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox36">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox28</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.07in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle6">
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                          </Style>
                        </Rectangle>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox21</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox39">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox25</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox29</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SITE_NAME">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!SITE_NAME.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAST_30_ACT_IND2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAST_30_ACT_IND</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_FIRST_USERS_APP">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_FIRST_USERS_APP.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail_FirstTimeUsers</ReportName>
                                  <Parameters>
                                    <Parameter Name="START_DT">
                                      <Value>=Parameters!START_DT.Value</Value>
                                    </Parameter>
                                    <Parameter Name="STOP_DT">
                                      <Value>=Parameters!STOP_DT.Value</Value>
                                    </Parameter>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Mobile App"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOTAL_FIRST_USERS_WEB">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TOTAL_FIRST_USERS_WEB.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>CHORUS_Monthly_Usage_Report_Detail_FirstTimeUsers</ReportName>
                                  <Parameters>
                                    <Parameter Name="START_DT">
                                      <Value>=Parameters!START_DT.Value</Value>
                                    </Parameter>
                                    <Parameter Name="STOP_DT">
                                      <Value>=Parameters!STOP_DT.Value</Value>
                                    </Parameter>
                                    <Parameter Name="LOGINMONTHFIRSTDAY">
                                      <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                    </Parameter>
                                    <Parameter Name="SITE_ID">
                                      <Value>=Fields!SITE_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="TYPE">
                                      <Value>="Web Portal"</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAST_LOG_90_IND2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAST_LOG_90_IND</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>User_Activity_Detail_Report</ReportName>
                                  <Parameters>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Today()</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox30</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details2" />
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <FixedRowHeaders>true</FixedRowHeaders>
            <DataSetName>FirstTimeUser</DataSetName>
            <Top>2.57259in</Top>
            <Left>0.02291in</Left>
            <Height>1.17417in</Height>
            <Width>9.5468in</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>11.13417in</Height>
        <Style />
      </Body>
      <Width>15.67584in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="START_DT">
      <DataType>DateTime</DataType>
      <Prompt>START DT</Prompt>
    </ReportParameter>
    <ReportParameter Name="STOP_DT">
      <DataType>DateTime</DataType>
      <Prompt>STOP DT</Prompt>
    </ReportParameter>
    <ReportParameter Name="FIRST_LOGIN_MONTH">
      <DataType>DateTime</DataType>
      <Prompt>FIRST LOGIN MONTH</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>START_DT</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>STOP_DT</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>FIRST_LOGIN_MONTH</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>cc29af76-f0cf-4d9d-9f5f-e6e707db3405</rd:ReportID>
</Report>