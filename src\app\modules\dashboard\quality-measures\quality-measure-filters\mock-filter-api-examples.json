{"description": "Mock API responses for database-driven filter configurations", "examples": {"qualityMeasuresReport": {"reportId": 1, "reportName": "Quality Measures Report", "filters": [{"filterKey": "measureGroup", "displayName": "Measure Group", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": true, "defaultValue": "targeted", "optionsSource": null, "sortOrder": 1, "placeholderText": "Select a measure group", "helpText": "Choose the quality measure group to filter reports"}, {"filterKey": "year", "displayName": "Report Year", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": true, "defaultValue": "CURRENT_YEAR", "optionsSource": "/api/lookups/years", "sortOrder": 2, "placeholderText": "Select a year"}, {"filterKey": "patientLocation", "displayName": "Patient Location", "controlType": "multiselect", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "ALL", "optionsSource": "/api/lookups/locations", "sortOrder": 3, "placeholderText": "Select locations", "helpText": "Filter by patient location(s)"}, {"filterKey": "quarter", "displayName": "Quarter", "controlType": "dropdown", "isEnabled": false, "isReadOnly": false, "isRequired": false, "defaultValue": null, "optionsSource": null, "sortOrder": 4}, {"filterKey": "hivStatus", "displayName": "HIV Status", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "-1", "optionsSource": null, "sortOrder": 5, "placeholderText": "Select HIV status", "helpText": "Filter by HIV diagnosis status", "embeddedOptions": [{"value": "-1", "label": "All"}, {"value": "1", "label": "Positive"}, {"value": "0", "label": "Negative"}]}]}, "hivSpecificReport": {"reportId": 2, "reportName": "HIV Specific Report", "filters": [{"filterKey": "year", "displayName": "Report Year", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": true, "defaultValue": "2024", "optionsSource": "/api/lookups/years", "sortOrder": 1, "placeholderText": "Select a year"}, {"filterKey": "hivStatus", "displayName": "HIV Status", "controlType": "dropdown", "isEnabled": true, "isReadOnly": true, "isRequired": true, "defaultValue": "1", "optionsSource": null, "sortOrder": 2, "placeholderText": "HIV Status (Fixed)", "helpText": "This report only shows HIV positive patients", "embeddedOptions": [{"value": "1", "label": "Positive"}]}, {"filterKey": "patientLocation", "displayName": "Patient Location", "controlType": "multiselect", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "ALL", "optionsSource": "/api/lookups/locations", "sortOrder": 3, "placeholderText": "Select locations"}, {"filterKey": "provider", "displayName": "Provider", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "any", "optionsSource": "/api/lookups/providers", "sortOrder": 4, "placeholderText": "Select a provider", "dependsOn": ["patientLocation"]}]}, "customKpiReport": {"reportId": 3, "reportName": "Custom KPI Report", "filters": [{"filterKey": "date<PERSON><PERSON><PERSON>", "displayName": "Date Range", "controlType": "date", "isEnabled": true, "isReadOnly": false, "isRequired": true, "defaultValue": "LAST_30_DAYS", "optionsSource": null, "sortOrder": 1, "placeholderText": "Select date range", "validationRules": {"min": "2020-01-01", "max": "CURRENT_DATE"}}, {"filterKey": "kpiThreshold", "displayName": "KPI Threshold", "controlType": "number", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "80", "optionsSource": null, "sortOrder": 2, "placeholderText": "Enter threshold percentage", "validationRules": {"min": 0, "max": 100}, "helpText": "Set the minimum percentage threshold for KPI achievement"}, {"filterKey": "includeInactive", "displayName": "Include Inactive Patients", "controlType": "checkbox", "isEnabled": true, "isReadOnly": false, "isRequired": false, "defaultValue": "false", "optionsSource": null, "sortOrder": 3, "helpText": "Check to include inactive patients in the report"}]}, "disabledFiltersExample": {"reportId": 4, "reportName": "Simple Report with Minimal Filters", "filters": [{"filterKey": "year", "displayName": "Report Year", "controlType": "dropdown", "isEnabled": true, "isReadOnly": false, "isRequired": true, "defaultValue": "2024", "optionsSource": "/api/lookups/years", "sortOrder": 1, "placeholderText": "Select a year"}, {"filterKey": "patientLocation", "displayName": "Patient Location", "controlType": "multiselect", "isEnabled": false, "isReadOnly": false, "isRequired": false, "defaultValue": null, "optionsSource": null, "sortOrder": 2}, {"filterKey": "provider", "displayName": "Provider", "controlType": "dropdown", "isEnabled": false, "isReadOnly": false, "isRequired": false, "defaultValue": null, "optionsSource": null, "sortOrder": 3}]}}, "apiEndpoints": {"getReportFilters": {"url": "/api/reports/{reportId}/filters", "method": "GET", "description": "Get filter configurations for a specific report", "parameters": {"reportId": "The ID of the report to get filters for"}, "response": "Array of FilterConfiguration objects"}, "getFilterOptions": {"url": "/api/lookups/{optionsSource}", "method": "GET", "description": "Get options for filters that use API-sourced options", "parameters": {"optionsSource": "The options source identifier (e.g., 'years', 'locations', 'providers')"}, "response": "Array of FilterOption objects"}}, "notes": ["The filterKey must match the property names in the QualityMeasureFilters interface", "When isEnabled is false, the filter should not be rendered in the UI", "When isReadOnly is true, the filter should be displayed but not editable", "When optionsSource is provided, make an API call to get the options", "When optionsSource is null, use embeddedOptions if provided", "The sortOrder determines the display order of filters", "dependsOn array indicates which filters this filter depends on for its options", "validationRules provide client-side validation constraints", "helpText provides additional context for users"]}