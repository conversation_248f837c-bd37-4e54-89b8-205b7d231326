﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.6.33829.357</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.6.33829.357</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2023-08-30T10:59:28.5518897Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="FlowsheetResults">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@FromDate">
            <Value>=Parameters!FromDate.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ToDate">
            <Value>=Parameters!ToDate.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT
      d.patient_search_display,
      d.patient_report_display,
	  d.first_nm,
	  d.last_nm,
	  d.BIRTH_DT,
	  CAST(DATEDIFF(MINUTE, d.BIRTH_DT, GETDATE()) / (365.23076923074 * 24 * 60) AS INT) AGE,
      CASE oc.OBXConceptID
         WHEN 22230 THEN 'Pap interpretation'
         WHEN 4000026 THEN 'Gyn procedure results - Colpo'
         WHEN 4000014 THEN 'Colonoscopy results'
         WHEN 4000065 THEN 'HIV Rapid Test'
      END AS FLOWSHEET_LABEL,
      rbm.base_id LAB_RESULT_ID
      ,dbm.base_id DEMOGRAPHICS_ID
      ,x.PatientID
	  ,IIF(o.obrid is null, 'No','Yes') ORDER_EXISTS
      ,o.[PanelDescription]   LAB_PANEL_TXT
      ,ISNULL(o.[ReportStatus],'Final')       LAB_STAT_DESC
      ,x.TestDescription      LAB_RSLT_TITL_TXT
      ,ISNULL(x.resultValue,x.localresultvalue)  AS       LAB_RSLT_TXT
      ,x.resultUnits AS       LAB_RSLT_UNIT
      ,x.referenceRange AS    NRML_RNG_TXT
      ,SUBSTRING(x.ResultFlag,1,10)  AS        ABNRML_CD
      ,SUBSTRING(x.ResultStatus,1,1) AS      ABNRML_HL7_CD
      ,CAST(x.CollectionDate AS DATE) AS    COLL_DT
      ,CAST(x.ResultDate AS DATE) AS           RSLT_DT
      ,SUBSTRING(cs.description,1,20)        MED_DICT_SRCE_NM
      ,ocs.codeid             MED_DICT_SRCE_CD
      ,ord.dateordered
  FROM [EHR].[OBX] x
     LEFT OUTER JOIN (SELECT MAX(OBRID) AS OBRID, OBXID FROM [EHR].[OBROBX] GROUP BY OBXID) ox ON(ox.OBXID = x.OBXID)
     INNER JOIN [DM].[BASE_ID_MAPPING] rbm ON(rbm.ehr_id = CAST(x.obxid AS VARCHAR(500)) AND rbm.mapping_domain = 'RESULT_ID')
     INNER JOIN [DM].[BASE_ID_MAPPING] dbm ON(dbm.ehr_id = CAST(x.patientid AS VARCHAR(500)) AND dbm.mapping_domain = 'DEMOGRAPHICS_ID')
     INNER JOIN CLEAN.DEMOGRAPHICS D ON(D.DEMOGRAPHICS_ID = dbm.base_id)
     LEFT OUTER JOIN [EHR].[OBR] o ON(o.OBRID = ox.OBRID)
     LEFT OUTER JOIN EHR.OrdersTracking ord ON(ord.orderid = o.orderid)
     LEFT OUTER JOIN [EHR].[OBXConcept] oc ON(x.OBXid = oc.OBXId)
     LEFT OUTER JOIN [EHR].[OBXCodingSystem] ocs ON(oc.OBXConceptID = ocs.OBXConceptID)
     LEFT OUTER JOIN [EHR].[CODINGSYSTEM] cs on(cs.codingSystemID = ocs.CodingSystemId)
  WHERE ISNULL(x.resultValue,x.localresultvalue) IS NOT NULL
  AND oc.OBXConceptID IN(22230,4000026,4000014,4000065)
  AND CAST(x.ResultDate AS DATE) BETWEEN @FromDate AND @ToDate</CommandText>
      </Query>
      <Fields>
        <Field Name="patient_search_display">
          <DataField>patient_search_display</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="patient_report_display">
          <DataField>patient_report_display</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="first_nm">
          <DataField>first_nm</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FLOWSHEET_LABEL">
          <DataField>FLOWSHEET_LABEL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="last_nm">
          <DataField>last_nm</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BIRTH_DT">
          <DataField>BIRTH_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="AGE">
          <DataField>AGE</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_RESULT_ID">
          <DataField>LAB_RESULT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DEMOGRAPHICS_ID">
          <DataField>DEMOGRAPHICS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PatientID">
          <DataField>PatientID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ORDER_EXISTS">
          <DataField>ORDER_EXISTS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_PANEL_TXT">
          <DataField>LAB_PANEL_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_STAT_DESC">
          <DataField>LAB_STAT_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_RSLT_TITL_TXT">
          <DataField>LAB_RSLT_TITL_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_RSLT_TXT">
          <DataField>LAB_RSLT_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_RSLT_UNIT">
          <DataField>LAB_RSLT_UNIT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NRML_RNG_TXT">
          <DataField>NRML_RNG_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ABNRML_CD">
          <DataField>ABNRML_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ABNRML_HL7_CD">
          <DataField>ABNRML_HL7_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COLL_DT">
          <DataField>COLL_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="RSLT_DT">
          <DataField>RSLT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="MED_DICT_SRCE_NM">
          <DataField>MED_DICT_SRCE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MED_DICT_SRCE_CD">
          <DataField>MED_DICT_SRCE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="dateordered">
          <DataField>dateordered</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.35417in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.5625in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.60417in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.51042in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.39583in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.36458in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.44792in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Patient ID</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Patient Name</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox3</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date of Birth</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Age</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Flowsheet Label</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Collection Date</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Result Date</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Order Exists</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Lab Panel Title</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Lab Result Title</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Lab Result</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PatientID">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PatientID.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PatientID</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="patient_search_display">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!patient_search_display.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>patient_search_display</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="BIRTH_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=FormatDateTime(Fields!BIRTH_DT.Value, DateFormat.ShortDate)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>BIRTH_DT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="AGE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!AGE.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>AGE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="FLOWSHEET_LABEL">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FLOWSHEET_LABEL.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>FLOWSHEET_LABEL</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="COLL_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=FormatDateTime(Fields!COLL_DT.Value,DateFormat.ShortDate)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>COLL_DT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="RSLT_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=FormatDateTime(Fields!RSLT_DT.Value, DateFormat.ShortDate)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>RSLT_DT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ORDER_EXISTS">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ORDER_EXISTS.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ORDER_EXISTS</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAB_PANEL_TXT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LAB_PANEL_TXT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAB_PANEL_TXT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAB_RSLT_TITL_TXT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LAB_RSLT_TITL_TXT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAB_RSLT_TITL_TXT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LAB_RSLT_TXT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LAB_RSLT_TXT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LAB_RSLT_TXT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <RepeatRowHeaders>true</RepeatRowHeaders>
            <DataSetName>FlowsheetResults</DataSetName>
            <PageName>Results</PageName>
            <SortExpressions>
              <SortExpression>
                <Value>=Fields!FLOWSHEET_LABEL.Value</Value>
              </SortExpression>
              <SortExpression>
                <Value>=Fields!last_nm.Value</Value>
              </SortExpression>
              <SortExpression>
                <Value>=Fields!first_nm.Value</Value>
              </SortExpression>
              <SortExpression>
                <Value>=Fields!RSLT_DT.Value</Value>
              </SortExpression>
            </SortExpressions>
            <Top>0.55083in</Top>
            <Height>0.5in</Height>
            <Width>12.23959in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox17">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="Flowsheet Results with Result Dates Between " + Parameters!FromDate.Value + " And " + Parameters!ToDate.Value + vbcrlf + 
"Report Run Date: " + Globals!ExecutionTime</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox17</rd:DefaultName>
            <Height>0.48139in</Height>
            <Width>10.79167in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox18">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Flowsheet Results Report</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <MarkupType>HTML</MarkupType>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>This report displays the results for selected tests that were entered through the Flowsheets and, therefore, do not have associated orders.  The results with orders may also be displayed on the report if they were recorded in the EHR under the same Concept ID. </Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>The "Order Exists" column in the output was added to indicate if there is an associated order for the reported result.  Filtering the results in Excel where [Order Exists] equals 'No' will return only the results entered via the Flowsheets.</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>The following result types are returned from the Flowsheets under the stated Flowsheel Label with the associated Concept ID in parentheses:</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Pap interpretation (22230)</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <ListStyle>Bulleted</ListStyle>
                <ListLevel>1</ListLevel>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Gyn procedure results - Colpo (4000026)</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <ListStyle>Bulleted</ListStyle>
                <ListLevel>1</ListLevel>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Colonoscopy results (4000014)</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <ListStyle>Bulleted</ListStyle>
                <ListLevel>1</ListLevel>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>HIV Rapid Test (4000065)</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <ListStyle>Bulleted</ListStyle>
                <ListLevel>1</ListLevel>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <MarkupType>HTML</MarkupType>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>The results are sorted in ascending order by the following fields: Flowsheet Label, Patient Last Name, Patient First Name, and Result Date.</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox18</rd:DefaultName>
            <Top>1.4675in</Top>
            <Height>3.59375in</Height>
            <Width>9.42709in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle2">
            <PageBreak>
              <BreakLocation>Start</BreakLocation>
            </PageBreak>
            <PageName>Report Description</PageName>
            <KeepTogether>true</KeepTogether>
            <Top>1.17583in</Top>
            <Height>0.125in</Height>
            <Width>10.79167in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>5.30083in</Height>
        <Style />
      </Body>
      <Width>12.67709in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="FromDate">
      <DataType>DateTime</DataType>
      <Prompt>From Date</Prompt>
    </ReportParameter>
    <ReportParameter Name="ToDate">
      <DataType>DateTime</DataType>
      <Prompt>To Date</Prompt>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>FromDate</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>ToDate</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>92a9823c-be72-4149-8290-937148045bee</rd:ReportID>
</Report>