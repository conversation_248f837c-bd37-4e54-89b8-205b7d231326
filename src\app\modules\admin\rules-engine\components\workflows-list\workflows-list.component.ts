import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Workflow } from '../../../models/rule.model';
import { RulesEngineService } from '../../../services/rules-engine.service';
import { LayoutService } from '../../../../shared-modules/layouts/services/layout/layout.service';
import { SqlScriptGeneratorService } from '../../../services/sql-script-generator.service';

@Component({
  selector: 'app-workflows-list',
  templateUrl: './workflows-list.component.html',
  styleUrls: ['./workflows-list.component.scss']
})
export class WorkflowsListComponent implements OnInit {
  @Input() siteId: string = '';
  @Input() workflows: Workflow[] = [];

  @Output() workflowSelected = new EventEmitter<Workflow>();
  @Output() workflowDeleted = new EventEmitter<Workflow>();
  @Output() refreshWorkflows = new EventEmitter<void>();
  @Output() createWorkflow = new EventEmitter<void>();
  @Output() executeWorkflow = new EventEmitter<Workflow>();

  displayedColumns: string[] = ['name', 'description', 'createdDate', 'modifiedDate', 'actions'];

  constructor(
    private rulesService: RulesEngineService,
    private layoutService: LayoutService,
    private sqlScriptGenerator: SqlScriptGeneratorService
  ) { }

  ngOnInit(): void {
  }

  onViewWorkflow(workflow: Workflow): void {
    this.workflowSelected.emit(workflow);
  }

  onDeleteWorkflow(workflow: Workflow): void {
    if (confirm(`Are you sure you want to delete the workflow "${workflow.name}"?`)) {
      this.layoutService.showSpinner();
      this.rulesService.deleteWorkflow(workflow.workflowID)
        .subscribe({
          next: () => {
            this.layoutService.hideSpinner();
            this.workflowDeleted.emit(workflow);
          },
          error: (error) => {
            this.layoutService.hideSpinner();
            console.error('Error deleting workflow:', error);
            alert('Failed to delete workflow. Please try again later.');
          }
        });
    }
  }

  onRefreshWorkflows(): void {
    this.refreshWorkflows.emit();
  }

  onCreateWorkflow(): void {
    this.createWorkflow.emit();
  }

  onExecuteWorkflow(workflow: Workflow): void {
    this.executeWorkflow.emit(workflow);
  }

  /**
   * Generate and download SQL script for a workflow
   * @param workflow The workflow to generate SQL script for
   */
  onDownloadSqlScript(workflow: Workflow): void {
    this.layoutService.showSpinner();

    this.sqlScriptGenerator.generateWorkflowSqlScript(workflow).subscribe({
      next: (sqlScript) => {
        this.layoutService.hideSpinner();
        this.downloadSqlFile(sqlScript, `workflow_${workflow.workflowID}_${workflow.name.replace(/\s+/g, '_')}.sql`);
      },
      error: (error) => {
        this.layoutService.hideSpinner();
        console.error('Error generating SQL script:', error);
        alert('Failed to generate SQL script. Please try again later.');
      }
    });
  }

  /**
   * Download a text file with the given content
   * @param content The content of the file
   * @param filename The name of the file
   */
  private downloadSqlFile(content: string, filename: string): void {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');

    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Cleanup
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}
