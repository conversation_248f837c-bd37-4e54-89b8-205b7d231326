const fs = require('fs');
const path = require('path');
const { env } = require('process');

function getFormattedDate() {
  const now = new Date();
  const year = now.getUTCFullYear().toString().slice(-2);
  const month = (now.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = now.getUTCDate().toString().padStart(2, '0');
  const hour = now.getUTCHours().toString().padStart(2, '0');
  const minutes = now.getUTCMinutes().toString().padStart(2, '0');
  const seconds = now.getUTCSeconds().toString().padStart(2, '0');

  return `${year}${month}${day}.${hour}${minutes}${seconds}`;
}

function replaceAppVersion(fileContent, newVersion) {
  // Regular expression to match the appVersion line
  const versionRegex = /appVersion: '(\d+\.\d+\.\d+\.\d+)'/;

  // Replacement string with the new version
  const replacement = `appVersion: '${newVersion}'`;

  // Replace the old version with the new version in the file content
  const updatedContent = fileContent.replace(versionRegex, replacement);

  return updatedContent;
}

function getVersions() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

    // Extract Angular version
    const angularVersion = packageJson.dependencies['@angular/core'].split('.')[0].replace(/^\D+/g, '') || packageJson.devDependencies['@angular/core'].split('.')[0].replace(/^\D+/g, '') || 'Not Found';

    // Extract package version
    const packageVersion = packageJson.version.split('.')[0].replace(/^\D+/g, '') || 'Not Found';

    return {
      angularVersion,
      packageVersion
    };
  } catch (error) {
    console.error('Error reading versions:', error);
    return { angularVersion: 'Error', packageVersion: 'Error' };
  }
}

const environmentsDir = path.join(__dirname, 'src\\app\\shared-services');
console.log(`Reading environment files from ${environmentsDir}`);
var envFileContent =  "appversion.ts";
// Function to update version in a file
const updateVersionInFile = (filePath) => {
  let envFileContent = fs.readFileSync(filePath, 'utf8');

  const versions = getVersions();
  const newVersion =  `${versions.packageVersion}.${versions.angularVersion}.${getFormattedDate()}`;
/*
  const newVersion = new Date().toISOString()
      .replace(/\..+/, '') // delete the dot and everything after
      .replace(/T/, '.') // replace T with .
      .replace(/:/g, '') // remove colons
      .replace(/-/g, '.'); // replace dashes with dots
*/

  envFileContent = replaceAppVersion(envFileContent, newVersion);
  fs.writeFileSync(filePath, envFileContent);
  console.log(`Updated version in ${path.basename(filePath)} to ${newVersion}`);
};

var randomSeconds = Math.random() * 10 + 5;
// Function to process each file
const processFile = (file) => {
  const filePath = path.join(environmentsDir, file);

  // Generate a random number between 5 and 10
  randomSeconds = Math.random() * 10 + 5;

  // Wait for the randomly generated number of seconds
  setTimeout(() => {
    
      // Update the version in the file
      updateVersionInFile(filePath);
      //console.log(`Completed processing ${file} after waiting for ${randomSeconds.toFixed(2)} seconds.`);
      // Add any additional file processing logic here
  }, randomSeconds * 1000);
};

file = "appversion.ts";
processFile(file);
