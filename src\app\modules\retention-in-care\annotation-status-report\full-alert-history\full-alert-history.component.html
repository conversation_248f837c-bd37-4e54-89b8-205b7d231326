<div id="PatientMeasureDetail" class="OutreachDeatilDiv">
    <div style="width: 99%; float: left; padding: 5px 0 2px 0; border-bottom: 1px solid #a1a1a1;">
        <input class="CMbtn" type="button" (click)="goBack()" value="Back"/>
        <img id="Img2" src="../../../../assets/images/printimg5.png" alt="#" title="PDF" Style="background-color: transparent; width:50px; height:30px; cursor: pointer; float:right; padding-right:20px; position: relative;left: -67px;" onClick="printDetailView()" />                    
        
        <img id="Img6" src="../../../../assets/images/ZHTML.png" alt="#" title="HTML" Style="background-color: transparent; cursor: pointer; width:50px; height:30px; float: right; padding-right: 20px; position: relative;left: 20px;" onClick="downloadInnerHtml()" />                    
    </div>
    <div class="OutReachDetailInnerDiv" style="float: left; width: 99%;">
        <div style="float: left; width: 100%; margin: 10px 10px 0 10px;">
            <div>
                <label>Patient with Active Outreach Alerts:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.patienT_NM)"></label>
                <label>Provider:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.fulL_NM)"></label>
            </div>
            <div>
                <label>DOB:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.birtH_DT)"></label>
                <label>HIV Diagnosis Date:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.hiV_DIAG_DT)"></label>
            </div>
        </div>
        <div style="float: left; width: 100%; overflow-y: auto; margin: 10px 10px 0 10px;">
            <div class="historyDiv">
                <label class="table-header">Full Alert History</label>
                <table class="table table-striped table-bordered" style="width: auto;">
                    <thead>
                        <tr>
                            <th style="border-bottom: 2px solid #ddd; width: 200px;">Date</th>
                            <th style="border-bottom: 2px solid #ddd; width: 200px;">Status</th>
                            <th style="border-bottom: 2px solid #ddd; width: 400px;">Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let history of patientFullAlertHistory;">
                            <td>{{retentionInCare.safeHtml(history.valiD_FROM_DT)}}</td>
                            <td>{{retentionInCare.safeHtml(history.annotatE_DESCRIPTION)}}</td>
                            <td>{{retentionInCare.safeHtml(history.annotatE_FREETEXT)}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="OutreachDetailInnerNoRecordsDiv" style="display: none; float: left; margin-top: 15px; text-align: center; width: 100%;">
        <label class="RetentionGen">No records found for this patient.</label>
    </div>
</div>