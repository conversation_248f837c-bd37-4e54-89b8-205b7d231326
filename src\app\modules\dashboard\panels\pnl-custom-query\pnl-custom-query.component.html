<div class="searchCriteriaDiv">

    <div class="col">
      <form [formGroup]="customQuerySearchForm" (ngSubmit)="readyToRun()">
        <div class="row">
            <div class="col">
              <br>
                    <!-- Demographic Attributes -->
                    <div class="row">
                        <div class="groupTitles">Demographic Attributes:</div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Sex</label>
                        <div class="col-sm-3 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientSex" formControlName="CustomQueryDDLPatientSex">
                                <option [ngValue]="defaultCode">All</option>
                                <option *ngFor="let opt of CustQueryOptions.sex" [value]="opt.code">{{opt.description}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Ethnicity</label>
                        <div class="col-sm-5 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientEthnicity"
                                formControlName="CustomQueryDDLPatientEthnicity">
                                <option [ngValue]="defaultCode">All</option>
                                <option *ngFor="let opt of CustQueryOptions.ethnicity" [value]="opt.code">{{opt.description}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Race</label>
                        <div class="col-sm-8 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientRace"
                                formControlName="CustomQueryDDLPatientRace">
                                <option [ngValue]="defaultCode">All</option>
                                <option *ngFor="let opt of CustQueryOptions.race" [value]="opt.code">{{opt.description}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Age</label>
                        <div class="col-sm-3 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientAge" (change)="toggleExtraFields($event.target, 'AgeFields')"
                                formControlName="CustomQueryDDLPatientAge">
                                <option *ngFor="let option of ageSelectOptions">{{option}}</option>
                            </select>
                        </div>
                          <div class="inlineBox" *ngIf="extraFormFields['AgeFields']>=1">
                              <input type="text" class="textInput" style="width:80px;" [placeholder]="extraFormFields['AgeFields']==1 ? 'Max Age' : 'Min Age'"   name="AgeFields1" [formControl]="AgeFields1">
                              <div class="alertContainer">
                                <mat-error class="alert" *ngIf="AgeFields1.invalid && AgeFields1.value!==''">{{reqIntValidationMsg}}</mat-error>
                              </div>
                          </div>
                          <div class="inlineBox" *ngIf="extraFormFields['AgeFields']>=2">
                            &nbsp;And&nbsp;
                          </div>
                          <div class="inlineBox" *ngIf="extraFormFields['AgeFields']>=2">
                            <input type="text" class="textInput" style="width:80px;" placeholder="Max Age"  name="AgeFields2" [formControl]="AgeFields2">
                            <div class="alertContainer">
                              <mat-error class="alert" *ngIf="AgeFields2.invalid && AgeFields2.value!==''">{{reqIntValidationMsg}}</mat-error>
                            </div>
                          </div>
                        </div>
                    </div>
                    <br>
                    <!-- Disease Status -->
                    <div class="row">
                        <div class="groupTitles">Disease Status:</div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">HIV Status</label>
                        <div class="col-sm-3 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLHivStatus"
                                formControlName="CustomQueryDDLHivStatus">
                                <option *ngFor="let option of HIVStatusSelectOptions" [value]="option.value">{{option.text}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">HCV Status</label>
                        <div class="col-sm-4 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLHCVStatus"
                                formControlName="CustomQueryDDLHCVStatus">
                                <option *ngFor="let option of HCVStatusSelectOptions" [value]="option.value">{{option.text}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <br>
                    <!-- Lab Results -->
                    <div class="row">
                        <div class="groupTitles">Lab Results:</div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Last HIV Viral Load</label>
                        <div class="col-sm-4 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLLastHIVViralLoad" (change)="toggleExtraFields($event.target,'ViralLoadFields')"
                                formControlName="CustomQueryDDLLastHIVViralLoad">
                                <option *ngFor="let option of LastHIVViralLoadSelectOptions">{{option}}</option>
                            </select>
                        </div>
                        <div class="inlineBox" *ngIf="extraFormFields['ViralLoadFields']>=1">
                          <input type="text" class="textInput" style="width:105px;" [placeholder]="extraFormFields['ViralLoadFields']==1 ? 'Max Viral Load' : 'Min Viral Load'"  name="ViralLoadFields1" [formControl]="ViralLoadFields1">
                          <div class="alertContainer">
                            <mat-error class="alert" *ngIf="ViralLoadFields1.invalid">{{reqIntValidationMsg}}</mat-error>
                          </div>
                        </div>
                        <div class="inlineBox" *ngIf="extraFormFields['ViralLoadFields']>=2">
                          &nbsp;And&nbsp;
                        </div>
                          <div class="inlineBox" *ngIf="extraFormFields['ViralLoadFields']>=2">
                            <input type="text" class="textInput" style="width:105px;" placeholder="Max Viral Load"  name="ViralLoadFields2" [formControl]="ViralLoadFields2">
                            <div class="alertContainer">
                              <mat-error class="alert" *ngIf="ViralLoadFields2.invalid">{{reqIntValidationMsg}}</mat-error>
                            </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Last CD4 Count</label>
                        <div class="col-sm-4 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLCDCount" (change)="toggleExtraFields($event.target, 'CD4Fields')"
                                formControlName="CustomQueryDDLCDCount">
                                <option *ngFor="let option of LastCD4CountSelectOptions" click="UpdatePatientSelectionCriteria()">{{option}}</option>
                            </select>
                        </div>
                        <div class="inlineBox" *ngIf="extraFormFields['CD4Fields']>=1">
                          <input type="text" class="textInput" style="width:110px;" [placeholder]="extraFormFields['CD4Fields']==1 ? 'Max CD4 Count' : 'Min CD4 Count'"  name="CD4Fields1" [formControl]="CD4Fields1">
                          <div class="alertContainer">
                            <mat-error class="alert" *ngIf="CD4Fields1.invalid">{{reqIntValidationMsg}}</mat-error>
                          </div>
                        </div>
                        <div class="inlineBox" *ngIf="extraFormFields['CD4Fields']>=2">
                          &nbsp;And&nbsp;
                        </div>
                          <div class="inlineBox" *ngIf="extraFormFields['CD4Fields']>=2">
                          <input type="text" class="textInput" style="width:110px;" placeholder="Max CD4 Count"  name="CD4Fields2" [formControl]="CD4Fields2">
                          <div class="alertContainer">
                            <mat-error class="alert" *ngIf="CD4Fields2.invalid">{{reqIntValidationMsg}}</mat-error>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Nadir CD4 Count</label>
                        <div class="inlineBox" style="width:160px; padding-right: 0;">
                            <select class="form-select form-select-sm" name="CustomQueryDDLNadirCD4Count" (change)="toggleExtraFields($event.target,'NadirCD4Fields')"
                                formControlName="CustomQueryDDLNadirCD4Count">
                                <option *ngFor="let option of NadirCD4CountSelectOptions">{{option}}</option>
                            </select>
                        </div>
                        <div class="inlineBox" style="padding-left: 0;" *ngIf="extraFormFields['NadirCD4Fields']>=1">
                          <input type="text" class="textInput" style="width:120px;" [placeholder]="extraFormFields['NadirCD4Fields']==1 ? 'Max Nadir Count' : 'Min Nadir Count'"  name="NadirCD4Fields1" [formControl]="NadirCD4Fields1">
                          <div class="alertContainer">
                            <mat-error class="alert" *ngIf="NadirCD4Fields1.invalid">{{reqIntValidationMsg}}</mat-error>
                          </div>
                        </div>
                        <div class="inlineBox" *ngIf="extraFormFields['NadirCD4Fields']>=2">
                          &nbsp;And&nbsp;
                        </div>
                          <div class="inlineBox" *ngIf="extraFormFields['NadirCD4Fields']>=2">
                          <input type="text" class="textInput" style="width:120px;" placeholder="Max Nadir Count"  name="NadirCD4Fields2" [formControl]="NadirCD4Fields2">
                          <div class="alertContainer">
                            <mat-error class="alert" *ngIf="NadirCD4Fields2.invalid">{{reqIntValidationMsg}}</mat-error>
                          </div>
                        </div>
                      </div>
                    </div>
                    <br>
                    <!-- Other -->
                    <div class="row">
                        <div class="groupTitles">Other:</div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Patient Status</label>
                        <div class="col-sm-3 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientStatus"
                                formControlName="CustomQueryDDLPatientStatus">
                                <option [ngValue]="defaultCode">All</option>
                                <option *ngFor="let opt of CustQueryOptions.patientStatus" [value]="opt.code">{{opt.description}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Last Active Visit</label>
                        <div class="col-sm-5 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLLastActiveVisit"
                                formControlName="CustomQueryDDLLastActiveVisit">
                                <option *ngFor="let option of LastActiveVisitSelectOptions" [value]="option.value">{{option.text}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                        <label class="col-sm-3 col-form-label-sm elementDiv">Patient Location</label>
                        <div class="col-sm-8 inlineBox">
                            <select class="form-select form-select-sm" name="CustomQueryDDLPatientLocation"
                                formControlName="CustomQueryDDLPatientLocation">
                                <option [ngValue]="defaultCode">All</option>
                                <option *ngFor="let opt of CustQueryOptions.patientLocation" [value]="opt.code">{{opt.description}}</option>
                            </select>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div>
                      <label class="col-sm-3 col-form-label-sm elementDiv">Primary Provider</label>
                      <div class="col-sm-8 inlineBox">
                          <select class="form-select form-select-sm" name="CustomQueryDDLPrimaryProvider"
                              formControlName="CustomQueryDDLPrimaryProvider">
                              <option [ngValue]="defaultCode">All</option>
                              <option *ngFor="let opt of CustQueryOptions.patientProvider" [value]="opt.code">{{opt.description}}</option>
                          </select>
                      </div>
                    </div>
                  </div>

            </div>
            <!-- Second section started here -->
            <div class="col">
              <br>
                <div class="row">
                    <!-- <label class="col-sm-3 col-form-label-sm">Medications:</label> -->
                    <div class="groupTitles">Medications:</div>
                    <div class="textDiv">
                        Use the controls below to filter the patients by current or historical medications.
                        You can select an overall exposure status of ART and HCV medications or, for more precise
                        medication filtering,
                        click the Regimen Builder button.
                    </div>
                </div>
                <br>
                <div class="row">
                  <label class="col-sm-3 col-form-label-sm elementDiv">ART Medication:</label>
                  <div class="col-sm-5">
                    <div *ngIf="panelService.isDefinedArt()"> Defined within created regimens </div>
                      <select *ngIf="!panelService.isDefinedArt()" class="form-select form-select-sm" name="CustomQueryDDLARTMedication" formControlName="CustomQueryDDLARTMedication">
                          <option *ngFor="let option of ARTMedicationSelectOptions"
                          [selected]="option.value == defaultCode"
                          [value]="option.value">{{option.text}}</option>
                      </select>
                  </div>
              </div>
              <div class="row">
                <label class="col-sm-3 col-form-label-sm elementDiv">HCV Medication:</label>
                <div class="col-sm-6">
                    <div *ngIf="panelService.isDefinedHCV()"> Defined within created regimens </div>
                    <select *ngIf="!panelService.isDefinedHCV()" class="form-select form-select-sm" name="CustomQueryDDLHCVMedication" formControlName="CustomQueryDDLHCVMedication">
                        <option *ngFor="let option of HCVMedicationSelectOptions"
                        [value]="option.value">{{option.text}}</option>
                    </select>
                </div>
            </div>
            <div class="row">
              <label class="col-sm-3 col-form-label-sm elementDiv">Regimens:</label>
              <div class="col-sm-5">
                  <a class="CMbtn"
                      (click)="saveAndRouteToRegimenBuilder()">
                      Regimen Builder
                  </a>
              </div>
          </div>
          <div class="row">
            <div style="margin-top: 20px; overflow-y: auto;height: 200px; width: 98%">
              <!-- Summary Section for Regimen Builder -->
                <div *ngFor="let rs of regimenSummary; index as rsIndex">
                  <div *ngIf="rsIndex==0">Include patients</div>
                  <div><div *ngIf="rsIndex>0" style="text-decoration-line: underline; display: inline-block;">{{panelService.getTextForChain(rs.chainMethod)}}</div>
                    &nbsp;Who {{panelService.getTextForRegOptions(rs)}}</div>
                  <div *ngFor="let regMedList of rs.regimenList">
                    <div>- An {{panelService.getTextForMedGroupId(regMedList.group.code)}} {{panelService.getTextForMedGroupOptions(regMedList.selectedOption)}}
                      <div *ngFor="let medList of regMedList.medicationName; index as mnIndex" style="color: blue; display: inline-block;">
                        <div style="display: inline-block;" *ngIf="mnIndex>0">,</div>
                        {{medList.brndNM}}
                      </div>
                    </div>
                  </div>
                </div>
            </div>
          </div>
          <div class="row">
            <div style="margin-top: 20px; display: flex; align-content: center; width: 100%; gap: 20px;">
              <div style="flex: 1"></div>
              <div style="flex: 0 0 auto;">
                <button type="button" id="clear" (click)="clearForm()"  [class]="btnClearAllStyle">Clear All</button>
              </div>
              <div style="flex: 0 0 auto;">
                <button type="button" (click)="RunReport()" id="reportViewer_Control_viewReportClick"  aria-describedby="reportViewer_Control_viewReportClick" [class]="btnClearAllStyle">Run Report</button>
              </div>
              <div style="flex: 1"></div>
            </div>
          </div>
        </div>
        </div>
      </form>
    </div>
</div>
