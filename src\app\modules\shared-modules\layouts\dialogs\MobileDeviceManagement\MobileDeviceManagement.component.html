<div style="padding: 25px;">
    <div style="float: right;margin-top: -25px; margin-right:-23px; cursor: pointer; z-index: 999999;"><mat-icon
            (click)="close()">close</mat-icon></div>
    <div style="height:650px;">
        <div id="HeaderLableDiv">
            <div class="SStopstrip">
                <div class="SStopstripleft">
                    <span ID="lblHeaderDeviceInfo">
                        Mobile Devices Authorized to Access Warmup via CHORUS App
                    </span>
                </div>
            </div>
        </div>
        <div class="SStablegridbg">
            <div class="SStablewrap">
                <table class="tablehead" style="display: block;height: 30.5vh;overflow-y: auto;">
                    <thead>
                        <th style="width: 10%;text-align: center;">
                            <img src="../../../../assets/images/alert.png" alt="#" />
                        </th>
                        <th style="width: 20%">DEVICE NAME</th>
                        <th style="width: 20%">DEVICE DESCRIPTION</th>
                        <th style="width: 12%">APP VERSION</th>
                        <th style="width: 17%">DEVICE ID</th>
                        <th style="width: 17%">LAST UPDATED DATE</th>
                        <th style="width: 6%">Delete</th>
                    </thead>
                    <tbody>
                        <tr *ngFor="let info of UserDeviceInfo">
                            <td style="width: 10%; text-align: center;"><a class="DeviceFlgRed"
                                    (click)="RevokeDeviceList(info)">{{RevokeOrReActivateDevice(info)}}</a></td>
                            <td style="width: 20%">{{info.deviceName}}</td>
                            <td style="width: 20%">{{info.deviceType}}</td>
                            <td style="width: 12%">{{info.appVersion}}</td>
                            <td style="width: 17%">{{info.deviceId}}</td>
                            <td style="width: 17%">{{info.lastUpdate}}</td>
                            <td style="width: 6%">
                                <img src="../../../../assets/images/Cross_24.png" (click)="DeleteDevice(info);"
                                    style="cursor: pointer; margin-left: 10px;" title="Delete the current record" />
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>
        </div>

        <div id="footerDiv" class="DeviceFooterDiv">

            <div id="DownloadLinkDiv" style="float: left;width: 50%;border: 1px solid #d3d5da;margin-bottom: 25px;">
                <div class="SSFooterDeviceStrip">
                    <div class="SStopstripleft">
                        <span ID="lblHeaderDownloadLink" CssClass="lblHeaderDownloadLink">
                            Download the CHORUS Mobile App
                        </span>
                    </div>
                </div>

                <div>
                    <div id="HeaderDiv">
                        <div>
                            <div class="DeviceListHeader">To download the mobile app, select your mobile device's
                                operating
                                system (OS):</div>
                        </div>
                        <div id="CustomQueryPatientAgeInfoDiv" class="DeviceOptions">
                            <select id="DDlDeviceList" [(ngModel)]="selectedDeviceType">
                                <option [value]="DeviceType.Android" >Android</option>
                                <option [value]="DeviceType.iOS">iOS</option>
                                <option [value]="DeviceType.Both">Both</option>
                            </select>
                        </div>
                    </div>

                    <div>

                        <div class="DeviceListHeader" style="width: 100%;">Get app download link by email:</div>
                        <div id="EmailTxtDiv" class="DeviceOptions">
                            <input type="text" ID="txtEmail" [(ngModel)]="userEmail" Style="float: left; height: 22px; margin-right: 7px;">
                            <ng-container *ngIf="showEmailMessage">
                                <div class="SentMessage">Email sent successfully.</div>
                            </ng-container>
                            <input type="button" id="btnSendEmailLink" value="Send email" class="CMbtn"
                                style="height: 21px; width: 100px; color: white; float: right;"
                                (click)="SendAppLink(MessageMethod.Email);" />
                        </div>

                    </div>

                    <div id="MobilelinkDiv" style="width: 100%; float: left;">
                        <div style="">
                            <div class="DeviceListHeader">Get app download link by text message:</div>
                        </div>
                        <div id="MobileTxtDiv" class="DeviceOptions">
                            <input type="text" ID="txtMobileNo" [(ngModel)]="sendtext" Style="float: left; height: 22px; margin-right: 7px;"
                                MaxLength="15">
                            <ng-container *ngIf="showSMSMessage">
                                    <div class="SentMessage">Text message sent successfully.</div>
                            </ng-container>
                            <input type="button" id="btnSendMessageLink" value="Send text" class="CMbtn"
                                style="height: 21px; width: 100px; color: white; float: right;"
                                (click)="SendAppLink(MessageMethod.SMS);" />
                        </div>
                    </div>

                    <div id="lblTextMessage" class="DeviceListHeader"
                        style="height: 14px; width: 60%; float: left; visibility: hidden; padding-left: 216px; width: auto;">
                    </div>

                    <div style="float: left; width: 100%;">
                        <div style="float: left; width:40%; padding-bottom: 10px;">
                            <a ID="lnkPlayStore" Target="_blank" [href]="environment.PlayStore">
                                <img src="../../../../assets/images/PlayStore.png" style="width: 100%; height: auto;"
                                    alt="Android Play Store" class="DownloadStoreImg" />
                            </a>
                        </div>
                        <div style="float: right; width:40%">
                            <a ID="lnkiStore" Target="_blank" [href]="environment.AppStore">
                                <img src="../../../../assets/images/AppStore.png"
                                    style="width: 100%; height: auto; padding-right: 25px;" alt="App Store"
                                    class="DownloadStoreImg" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div id="ManagePinDiv" style="float: right; width: 49%; border: 1px solid #d3d5da; margin-bottom: 25px; height:327px;">
                <div class="SSTopDiv">
                    <div class="SSFooterDeviceStrip">
                        <div class="SStopstripleft">

                            <span ID="Label1">
                                Manage Mobile Device PIN
                            </span>
                        </div>

                    </div>
                </div>
                <div  style="background-color: #fff; word-wrap: break-word;">
                    <div *ngIf="showPinMgmt==false">
                        <div>
                            <div class="DeviceListHeader" style="width: 100%;">To protect your patients' privacy, this
                                app
                                requires you to reauthenticate each time you navigate back to it, even if you left it
                                open. You
                                can use a PIN that you create to speed this process up if you have not closed or signed
                                out of
                                the app. </div>
                        </div>

                        <div *ngIf="isMessageVisible" class="MessageDiv" style="padding-top: 100px; padding-left: 100px;">PIN successfully deactivated</div>

                        <div id="PinNotExistDiv" class="DeviceListHeader"
                            style="padding-top: 60px; width: 100%; color: #444444" visible="false">
                            <span ID="lblPinNotExist">To activate the mobile device PIN feature, please login to CHORUS
                                Mobile app.
                            </span>
                        </div>

                        <div id="lblPINErroMsg" class="DeviceListHeader" style="float: left; color: red; width: auto">
                        </div>
                    </div>
                    <div *ngIf="showPinMgmt==true">
                        <p class="DeviceListHeader" style="width: 100%;">To protect your patients' privacy, this app requires you to reauthenticate each time you navigate back to it, even if you left it open. You can use a PIN that you create to speed this process up if you have not closed or signed out of the app.</p>
                        <div class="pin-details">
                            <div style="float:left">
                            <ul class="PinFont">
                                <li>PIN may not be a single repeated digit: 555555</li>
                                <li>PIN may not be 2 sets of 3 consecutive digits: 987987</li>
                                <li>PIN may not be 6 consecutive digits: 345678</li>
                                <li>The same PIN is used for every device.</li>
                            </ul>
                            </div>
                            <div style="float: right; padding-left: 50px;">
                                <div style="border: right; box-sizing: 1;"></div>
                                <div>
                                    <label for="new-pin" style="padding-right: 26px;">New Pin:</label>
                                    <input type="password" [(ngModel)]="pin" (input)="ValidatePin()">
                                </div>
                                <div style="margin-top: 15px;">
                                    <label for="confirm-pin" style="padding-right: 2px;">Confirm Pin:</label>
                                    <input type="password" [(ngModel)]="pinConfirm" id="pinConfirm" (input)="ValidatePin()">
                                </div>
                            </div>
                        </div>
                       
                        <div>
                            <table width="100%" style="margin-top: 25px;">
                                <tr>
                                    <td width="20%"></td>
                                    <td><button  [ngClass]="isValid" (click)="ValidatePin(true)">Change PIN</button></td>
                                    <td></td>
                                    <td><button  class="CMbtn" (click)="DeactivatePin()">Deactivate PIN</button></td>
                                    <td width="20%"></td>
                                </tr>
                            </table>
                            <div *ngIf="isMessageVisible" class="MessageDiv">PIN successfully updated. Please note that you will use this PIN on all mobile devices where you access the CHORUS App.</div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>