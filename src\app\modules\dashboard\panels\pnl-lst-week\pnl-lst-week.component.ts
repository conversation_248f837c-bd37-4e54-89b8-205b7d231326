import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-lst-week',
  templateUrl: './pnl-lst-week.component.html',
  styleUrls: ['./pnl-lst-week.component.scss']
})
export class PnlLstWeekComponent implements OnInit {
  @Input() rptpanLstWeek: FormControl;
  lstWeekParam: IReportParamData[] = [] as IReportParamData[];
  lstWeekData: IData[] = [] as IData[];
  noSearchPan: boolean = false;

  rptbtnColor: string;

  constructor(
    public panelService:PanelService
  ) {
    this.rptpanLstWeek = new FormControl();
    this.rptbtnColor = this.rptbtnColor= btnColor.btnSecondColor;
  }

  ngOnInit(): void {
    this.panelService.GetPanelData(ReportPanelTypes.pnlLstWeek).subscribe(s =>
      {
        this.lstWeekParam=s;
        this.lstWeekData = this.lstWeekParam[0].data;
      });
  }

  readyToRun(): void{
      this.rptbtnColor= btnColor.btnPrimaryColor;
  }


  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
