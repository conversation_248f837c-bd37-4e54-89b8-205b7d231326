@media screen, print {
    /* Print-specific styles for Huddle PDF generation */

    .huddle-print-container {
        font-family: 'Helvetica', Arial, sans-serif;
        font-size: 12px;
        color: black;
        background: white;
        margin: 0;
        padding: 20px;
    }

    .huddle-print-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #0071BC;
        padding-bottom: 15px;
    }

    .huddle-print-title {
        font-size: 18px;
        font-weight: bold;
        color: #0071BC;
        margin-bottom: 10px;
    }

    .huddle-print-subtitle {
        font-size: 12px;
        color: #666666;
        font-style: italic;
        margin-bottom: 5px;
    }

    .huddle-print-appointment-count {
        font-size: 14px;
        font-weight: bold;
        color: #0071BC;
        margin: 15px 0;
    }

    .huddle-print-date-location {
        font-size: 12px;
        color: #333333;
        margin-bottom: 20px;
    }

    .huddle-print-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 40px; /* Add space for footer */
        font-size: 10px;
    }

    .huddle-print-table th {
        background-color: #0071BC;
        color: white;
        font-weight: bold;
        padding: 8px 4px;
        text-align: left;
        border: 1px solid #0071BC;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .huddle-print-table td {
        padding: 6px 4px;
        border: 1px solid #cccccc;
        vertical-align: top;
        word-wrap: break-word;
    }

    .huddle-print-table tr:nth-child(even) {
        background-color: #f9f9f9;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .huddle-print-footer {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        width: calc(100% - 40px);
        margin: 0 20px;
        font-size: 10px;
        color: #666666;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: white;
        z-index: 1000;
    }

    .huddle-print-page-number {
        text-align: left;
    }

    .huddle-print-branding {
        text-align: center;
        font-weight: bold;
    }

    .huddle-print-date {
        text-align: right;
    }

    /* Ensure proper page breaks */
    .huddle-print-page-break {
        page-break-before: always;
    }

    .huddle-print-no-break {
        page-break-inside: avoid;
    }

    /* Column widths for better layout */
    .huddle-print-table .col-time {
        width: 8%;
    }

    .huddle-print-table .col-type {
        width: 10%;
    }

    .huddle-print-table .col-status {
        width: 8%;
    }

    .huddle-print-table .col-provider {
        width: 12%;
    }

    .huddle-print-table .col-patient {
        width: 15%;
    }

    .huddle-print-table .col-mrn {
        width: 10%;
    }

    .huddle-print-table .col-gaps {
        width: 8%;
        text-align: center;
    }

    .huddle-print-table .col-details {
        width: 29%;
    }

    /* Status color indicators */
    .status-completed {
        color: #009CCC;
        font-weight: bold;
    }

    .status-cancelled {
        color: #CC293D;
        font-weight: bold;
    }

    .status-no-show {
        color: #F2CB1D;
        font-weight: bold;
    }

    /* Print optimization */
    @page {
        size: letter;
        margin: 0.5in;
    }

    /* Hide elements that shouldn't print */
    .no-print {
        display: none !important;
    }
}
