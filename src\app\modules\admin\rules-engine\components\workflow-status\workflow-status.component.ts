import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { WorkflowProcess, WorkflowStatus, ExecutorTransaction, Rule } from '../../../models/rule.model';

@Component({
  selector: 'app-workflow-status',
  templateUrl: './workflow-status.component.html',
  styleUrls: ['./workflow-status.component.scss']
})
export class WorkflowStatusComponent implements OnInit {
  @Input() workflowStatus: WorkflowStatus | null = null;
  @Input() selectedProcess: WorkflowProcess | null = null;
  @Input() loadingWorkflowStatus: boolean = false;
  @Input() rules: Rule[] = [];

  @Output() refreshStatus = new EventEmitter<void>();

  constructor() { }

  ngOnInit(): void {
  }

  onRefreshStatus(): void {
    this.refreshStatus.emit();
  }

  getStatusFromWorkflowStatus(status: WorkflowStatus | null): string {
    if (!status) return 'Unknown';

    // Check if using new format
    if (status.Status) {
      // Determine status based on executor transactions
      const transactions = this.getExecutorTransactions(status);
      if (transactions.length === 0) return 'Unknown';

      // Look for the Session transaction (RuleId = null)
      const sessionTransaction = transactions.find(t => t.RuleId === null);
      if (sessionTransaction) {
        // If session has started but not ended, it's running
        if (sessionTransaction.StartExecution && !sessionTransaction.EndExecution) {
          // Check if it's been running for more than an hour (potentially aborted)
          const startTime = new Date(sessionTransaction.StartExecution).getTime();
          const currentTime = new Date().getTime();
          const hourInMs = 60 * 60 * 1000;

          if (currentTime - startTime > hourInMs) {
            return 'Aborted';
          }
          return 'Running';
        }

        // If session has ended, check if any transactions failed
        if (sessionTransaction.EndExecution) {
          const hasFailedTransactions = transactions.some(t => t.Success === false);
          if (hasFailedTransactions) return 'Failed';
          return 'Completed';
        }
      }

      // Fallback to checking all transactions if no session transaction found
      const hasRunningTransactions = transactions.some(t => !t.EndExecution);
      if (hasRunningTransactions) return 'Running';

      const hasFailedTransactions = transactions.some(t => t.Success === false);
      if (hasFailedTransactions) return 'Failed';

      return 'Completed';
    }

    // Legacy format
    return status.status || 'Unknown';
  }

  getStartTimeFromWorkflowStatus(status: WorkflowStatus | null): Date | null {
    if (!status) return null;

    // Check if using new format
    if (status.Status?.startTime) {
      return new Date(status.Status.startTime);
    }

    // Legacy format
    return status.startTime || null;
  }

  getEndTimeFromWorkflowStatus(status: WorkflowStatus | null): Date | null {
    if (!status) return null;

    // Check if using new format
    if (status.Status) {
      if (status.Status.endTime) {
        return new Date(status.Status.endTime);
      }

      // If no explicit end time, check if all transactions are complete
      const transactions = this.getExecutorTransactions(status);
      if (transactions.length > 0) {
        const allComplete = transactions.every(t => t.EndExecution);
        if (allComplete) {
          // Use the latest end time from transactions
          const lastEndTime = transactions
            .filter(t => t.EndExecution)
            .map(t => new Date(t.EndExecution as string).getTime())
            .reduce((max, time) => Math.max(max, time), 0);

          return lastEndTime > 0 ? new Date(lastEndTime) : null;
        }
      }

      return null;
    }

    // Legacy format
    return status.endTime || null;
  }

  getExecutorTransactions(status: WorkflowStatus | null): ExecutorTransaction[] {
    if (!status) return [];

    // Check if using new format
    if (status.Status?.executorTransactions) {
      return status.Status.executorTransactions;
    }

    return [];
  }

  getRuleNameById(ruleId: number | null, status: WorkflowStatus): string {
    if (!ruleId) return 'Unknown Rule';

    // Check if using new format
    if (status.Status?.RuleDetails) {
      const rule = status.Status.RuleDetails.find(r => r.RuleID === ruleId);
      if (rule) return rule.Name;
    }

    // Try to find in rules array
    const rule = this.rules.find(r => r.ruleID === ruleId);
    if (rule) return rule.name;

    return `Rule ${ruleId}`;
  }

  formatExecutionException(exception: any): string {
    if (!exception) return 'Unknown error';

    if (typeof exception === 'string') {
      return exception;
    }

    if (typeof exception === 'object') {
      try {
        return JSON.stringify(exception, null, 2);
      } catch (e) {
        return 'Error parsing exception';
      }
    }

    return String(exception);
  }

  formatJsonString(jsonString: string): string {
    if (!jsonString) return '';

    try {
      const parsed = JSON.parse(jsonString);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      // If it's not valid JSON, return as is
      return jsonString;
    }
  }

  calculateStatusDuration(status: WorkflowStatus): string {
    if (!status) return 'N/A';

    const startTime = this.getStartTimeFromWorkflowStatus(status);
    if (!startTime) return 'N/A';

    const endTime = this.getEndTimeFromWorkflowStatus(status);
    const end = endTime ? endTime.getTime() : new Date().getTime();

    const durationMs = end - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);

    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes} min ${remainingSeconds} sec`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const remainingMinutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hr ${remainingMinutes} min`;
    }
  }

  formatGuid(guid?: string): string {
    if (!guid) return '';

    // Split the GUID by hyphens and get the last part
    const parts = guid.split('-');
    if (parts.length > 0) {
      return parts[parts.length - 1];
    }

    // If the GUID doesn't have hyphens, return the last 8-12 characters
    return guid.length > 8 ? guid.substring(guid.length - 8) : guid;
  }
}
