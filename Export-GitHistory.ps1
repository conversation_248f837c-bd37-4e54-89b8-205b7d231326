<#
.SYNOPSIS
  Exports Git commit history and (optionally) inline diffs for a specific directory from a given date until now,
  and always lists the files that changed in each commit.

.DESCRIPTION
  - Uses `git log --since` with `--name-status` to list changed files, plus `-p` if diffs are requested.
  - Writes the output to the console; optionally, saves it to a file.

.PARAMETER RepositoryPath
  The local filesystem path to the root of the Git repository.

.PARAMETER TargetDirectory
  The (sub)directory within the repository whose commit history you want to retrieve.

.PARAMETER SinceDate
  The date from which you want to retrieve commit history (up to the present).

.PARAMETER WithDiffs
  If specified, also include line-by-line diffs in the output.

.EXAMPLE
  .\Export-GitHistoryWithDiffs.ps1 -RepositoryPath "C:\EpividianRepos\Chorus_Portal" -TargetDirectory "SSRS\ChorusReports" -SinceDate "2024-01-01" -WithDiffs
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [string]$RepositoryPath,

    [Parameter(Mandatory=$true)]
    [string]$TargetDirectory,

    [Parameter(Mandatory=$true)]
    [DateTime]$SinceDate,

    [Parameter(Mandatory=$false)]
    [switch]$WithDiffs = $false
)

Write-Host "Changing directory to $RepositoryPath"
Set-Location -Path $RepositoryPath

# Format the date to the format Git expects (yyyy-MM-dd)
$formattedDate = $SinceDate.ToString("yyyy-MM-dd")

Write-Host "Retrieving commit history for directory '$TargetDirectory' since $formattedDate (up to now)..."

if ($WithDiffs -contains $true) {
    Write-Host "Retrieving commit history WITH diffs..."
  #   --name-status will show changed files; -p adds line-by-line patches
    $logOutput = git log --since=$formattedDate `
                         -p `
                         --pretty=format:"====================================================================================================%ncommit %H%nAuthor: %an <%ae>%nDate:   %ad%n%n%s%n" `
                         -- $TargetDirectory
}
else {
    Write-Host "Retrieving commit history WITHOUT diffs (files changed only)..."
    # --name-status will show changed files but no diff
    $logOutput = git log --since=$formattedDate `
                         --name-status `
                         --pretty=format:"====================================================================================================%ncommit %H%nAuthor: %an <%ae>%nDate:   %ad%n%n%s%n" `
                         -- $TargetDirectory
}

Write-Host "`n===== GIT LOG OUTPUT =====`n"
Write-Host $logOutput

# (Optional) Uncomment or adjust below if you want to store the output in a file
$outputFile = Join-Path $RepositoryPath "${TargetDirectory}_From_${formattedDate}_To_Now.txt"
$logOutput | Out-File $outputFile -Encoding UTF8
Write-Host "History saved to: $outputFile"
