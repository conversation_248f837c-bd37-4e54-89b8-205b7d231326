import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BehaviorSubject, Subject } from 'rxjs';

@Component({
  selector: 'app-dialog-response',
  templateUrl: './dialog-response.component.html',
  styleUrls: ['./dialog-response.component.css']
})
export class DialogResponseComponent implements OnInit {
  public content: any;
  public data: any;
  public title: any;
  public dialogCloseText: string = "Ok";
  public dialogActionText = new BehaviorSubject<string>("");
  public showWarningSymbol: boolean = false;
  public dialogTypeAction = new BehaviorSubject<boolean>(false);

  constructor(public dialogRef: MatDialogRef<DialogResponseComponent>,
              @Optional() @Inject(MAT_DIALOG_DATA) public results: any) {

                if (this.isString(results)) {
                  this.dialogTypeAction.next(false);
                  this.content = this.results;
                  this.showWarningSymbol = true;

                } else {
                  this.dialogTypeAction.next(true);
                  this.title = this.results.title
                  this.content = this.results.content;

                  if (this.results.dialogType == "YesNo")
                  {
                    this.dialogCloseText = "Cancel";
                    this.dialogActionText.next("Confirm");
                  } else
                  {
                    this.dialogCloseText = "Ok";
                  }

                  if (this.results.MessageLevel = 0)
                  {
                    this.showWarningSymbol = false;
                  } 
                  else if (this.results.MessageLevel = 1)
                  {
                    this.showWarningSymbol = true;
                  }
                  
                }
              }

  ngOnInit(): void {

  }


  closeDialog(closeData: string) {
    this.dialogRef.close(closeData);
  }

  isString(value: any): boolean{
    return typeof value === 'string' && value.trim() !== '';
  }

}
