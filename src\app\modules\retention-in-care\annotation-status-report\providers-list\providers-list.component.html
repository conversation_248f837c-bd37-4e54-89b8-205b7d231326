<div class="provider-view">
    <div style="width: 100%; float: left">
        <div class="scrollable-table WordSection1" style="float: left" id="providerListView">
            <table class="table table-striped table-header-rotated" id="providerListViewTable">
                <thead>
                    <tr>
                        <th>
                            <div id="actionButtons" class="providerloc">
                                <div>
                                    <img
                                        src="../../../../assets/images/printimg5.png"
                                        (click)="printPDF()"
                                        alt="#"
                                        title="PDF"
                                        style="background-color: transparent; cursor: pointer; width:45px; height:30px; padding-right: 20px;"/>
                                    <img
                                        src="../../../../assets/images/ZExcel.png"
                                        (click)="printCSV()"
                                        alt="#"
                                        title="Excel"
                                        style="background-color: transparent; cursor: pointer; width:45px; height:30px; padding-right: 20px;" />
                                    <img
                                        src="../../../../assets/images/ZHTML.png"
                                        onClick="providerListdownloadInnerHtml()"
                                        alt="#"
                                        title="HTML"
                                        style="background-color: transparent; cursor: pointer; width:45px; height:30px; padding-right: 20px;" />
                                </div>
                                <div class="providerBtn"><span class="thalign">Provider by Location</span></div>
                            </div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Total Patients</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">New Alerts / Reattempt Call</span></div>
                        </th>
                        <th class="rotate-45"  RCShowAnnotation="true">
                            <div><span class="thalign">Agreed to Visit / ReqtApptChange</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Scheduled Visit/ Confirmed Visit</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Already Received-Injection</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">No Voicemail /Remind Later</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Left Voicemail</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Bad Number / No Service</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Refused Visit</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Moved</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Transferred</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Deceased</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Incarcerated</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Staff Declined to Contact</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Phone Number Corrected</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">Cannot Be Reached</span></div>
                        </th>
                        <th class="rotate-45">
                            <div><span class="thalign">Custom</span></div>
                        </th>
                        <th class="rotate-45" OthersAnnotation="true" style="display:none;">
                            <div><span class="thalign">Others</span></div>
                        </th>
                        <th class="rotate-45" RCShowAnnotation="true">
                            <div><span class="thalign">No Phone Number</span></div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="ui-widget-content jqgfirstrow ui-row-ltr" role="row" style="height:auto">
                        <td role="gridcell" class="firstEmptyCell"></td>
                        <td role="gridcell" class="emptyCell" ></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                        <td role="gridcell" class="emptyCell"></td>
                    </tr>
                    <ng-container *ngFor="let providerLocation of retentionTableGroup; let i = index">
                        <ng-container *ngFor="let retentionProvider of providerLocation.providerList;">
                            <ng-container *ngIf="retentionProvider.provideR_ID === 0; else elseBlock">
                                <tr class="ui-widget-content jqgrow ui-row-ltr group-row locationHeader">
                                    <td style="display:none; width: 50px;" title="">{{ retentionProvider.provideR_ID }}</td>
                                    <td (click)="providerLocation.expanded = !providerLocation.expanded" class="cellDataName locationGroup locationCell locationName"   style="text-align:left" title="{{ retentionProvider.locatioN_NM }}">
                                        {{ retentionProvider.locatioN_NM }}
                                        <span class="tableArrow">
                                            <ng-container *ngIf="providerLocation.expanded">
                                              <mat-icon>keyboard_arrow_down</mat-icon>
                                            </ng-container>
                                            <ng-container *ngIf="!providerLocation.expanded">
                                              <mat-icon>keyboard_arrow_right</mat-icon>
                                            </ng-container>
                                        </span>
                                    </td>
                                    <td class="cellData locationCell" title="{{ retentionProvider.retentioN_PARTICIPATION_TOTAL_PATIENTS }}">{{ retentionProvider.retentioN_PARTICIPATION_TOTAL_PATIENTS }}</td>
                                    <td class="cellData locationCell" title="{{ retentionProvider.neW_ALERTS }}">{{ retentionProvider.neW_ALERTS }}</td>
                                    <td class="cellData locationCell" title="{{ retentionProvider.agreeD_TO_VISIT }}">{{ retentionProvider.agreeD_TO_VISIT }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.scheduleD_VISIT }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.alreadY_RECEIVED_INJECTION }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.nO_VOICEMAIL }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.lefT_VOICEMAIL }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.wronG_NUMBER }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.refuseD_VISIT }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.moved }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.transferred }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.deceased }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.incarcerated }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.stafF_DECLINED_TO_CONTACT }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.phonE_NUMBER_CORRECTED }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.cannoT_BE_REACHED }}</td>
                                    <td class="cellData locationCell" >{{ retentionProvider.custom }}</td>
                                    <td class="cellData locationCell" style="display:none;" title="{{ retentionProvider.otheR_ANNOTATION_PATIENTS }}">
                                        {{ retentionProvider.otheR_ANNOTATION_PATIENTS }}
                                    </td>
                                    <td class="cellData locationCell" >{{ retentionProvider.nO_PHONE_NUMBER }}</td>
                                    <td style="display:none;" title="{{ retentionProvider.locatioN_NM }}">
                                        {{ retentionProvider.locatioN_NM }}
                                    </td>
                                    <td style="display:none;" title="{{ retentionProvider.locatioN_ID }}">
                                        {{ retentionProvider.locatioN_ID }}
                                    </td>
                                </tr>
                            </ng-container>


                            <ng-template #elseBlock>
                                <ng-container *ngIf="providerLocation.expanded;"> 
                                    <tr  [ngClass]="{ 'active': isActive, 'deactive': isHighlighted }"  >                          
                                        <td style="display:none;width: 50px;" title="">{{ retentionProvider.provideR_ID }}</td>
                                        <td  style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'Total_Patient',retentionProvider.provideR_NM)" class="cellDataName" title="{{ retentionProvider.provideR_NM }}">
                                        {{ retentionProvider.provideR_NM }}
                                        </td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'Total_Patient',retentionProvider.retentioN_PARTICIPATION_TOTAL_PATIENTS)" class="cellData" title="{{ retentionProvider.retentioN_PARTICIPATION_TOTAL_PATIENTS }}">{{ retentionProvider.retentioN_PARTICIPATION_TOTAL_PATIENTS }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'neW_ALERTS',retentionProvider.neW_ALERTS)" class="cellData" title="{{ retentionProvider.neW_ALERTS }}">{{ retentionProvider.neW_ALERTS }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'agreeD_TO_VISIT',retentionProvider.agreeD_TO_VISIT)" class="cellData" title="{{ retentionProvider.agreeD_TO_VISIT }}">{{ retentionProvider.agreeD_TO_VISIT }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'scheduleD_VISIT',retentionProvider.scheduleD_VISIT)" class="cellData" >{{ retentionProvider.scheduleD_VISIT }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'alreadY_RECEIVED_INJECTION',retentionProvider.alreadY_RECEIVED_INJECTION)" class="cellData" >{{ retentionProvider.alreadY_RECEIVED_INJECTION }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'nO_VOICEMAIL',retentionProvider.nO_VOICEMAIL)" class="cellData" >{{ retentionProvider.nO_VOICEMAIL }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'lefT_VOICEMAIL',retentionProvider.lefT_VOICEMAIL)" class="cellData" >{{ retentionProvider.lefT_VOICEMAIL }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'wronG_NUMBER',retentionProvider.wronG_NUMBER)" class="cellData" >{{ retentionProvider.wronG_NUMBER }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'refuseD_VISIT',retentionProvider.refuseD_VISIT)" class="cellData" >{{ retentionProvider.refuseD_VISIT }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'moved',retentionProvider.moved)" class="cellData" >{{ retentionProvider.moved }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'transferred',retentionProvider.transferred)" class="cellData" >{{ retentionProvider.transferred }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'deceased',retentionProvider.deceased)" class="cellData" >{{ retentionProvider.deceased }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'incarcerated',retentionProvider.incarcerated)" class="cellData" >{{ retentionProvider.incarcerated }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'stafF_DECLINED_TO_CONTACT',retentionProvider.stafF_DECLINED_TO_CONTACT)" class="cellData" >{{ retentionProvider.stafF_DECLINED_TO_CONTACT }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'phonE_NUMBER_CORRECTED',retentionProvider.phonE_NUMBER_CORRECTED)" class="cellData" >{{ retentionProvider.phonE_NUMBER_CORRECTED }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'cannoT_BE_REACHED',retentionProvider.cannoT_BE_REACHED)" class="cellData" >{{ retentionProvider.cannoT_BE_REACHED }}</td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'custom',retentionProvider.custom)" class="cellData" >{{ retentionProvider.custom }}</td>
                                        <td style="cursor: pointer;" class="cellData" style="display:none;" title="{{ retentionProvider.otheR_ANNOTATION_PATIENTS }}">
                                            {{ retentionProvider.otheR_ANNOTATION_PATIENTS }}
                                        </td>
                                        <td style="cursor: pointer;" (click)="LoadPatientsByProviderData(retentionProvider,'nO_PHONE_NUMBER',retentionProvider.nO_PHONE_NUMBER)" class="cellData" >{{ retentionProvider.nO_PHONE_NUMBER }}</td>
                                        <td style="display:none;" title="{{ retentionProvider.locatioN_NM }}">
                                            {{ retentionProvider.locatioN_NM }}
                                        </td>
                                        <td style="display:none;" title="{{ retentionProvider.locatioN_ID }}">
                                            {{ retentionProvider.locatioN_ID }}
                                        </td>
                                    </tr>                      
                                </ng-container>
                            </ng-template>
                        </ng-container>
                    </ng-container>                
                </tbody>
            </table>
        </div>
        <div class="WordSection1" id="retentiontable"style="display:none; width:20px;"></div>
    </div>
</div>
