<div class="workflow-status-container">
  <div class="loading-indicator" *ngIf="loadingWorkflowStatus">
    <mat-spinner diameter="30"></mat-spinner>
    <p>Loading workflow status...</p>
  </div>

  <div *ngIf="!loadingWorkflowStatus && workflowStatus">
    <div class="status-header">
      <div class="info-row">
        <span class="label">Process ID:</span>
        <span class="value">{{formatGuid(selectedProcess?.workflowProcessId || selectedProcess?.workFlowProcessId)}} <span class="guid-full">({{selectedProcess?.workflowProcessId || selectedProcess?.workFlowProcessId}})</span></span>
        <button type="button" mat-icon-button [cdkCopyToClipboard]="selectedProcess?.workflowProcessId || selectedProcess?.workFlowProcessId || ''" matTooltip="Copy Full ID">
          <mat-icon>content_copy</mat-icon>
        </button>
      </div>
      <div class="info-row">
        <span class="label">Workflow:</span>
        <span class="value">{{selectedProcess?.workflowName}}</span>
      </div>
      <div class="info-row">
        <span class="label">Status:</span>
        <span class="status-badge" [ngClass]="{
          'success': getStatusFromWorkflowStatus(workflowStatus) === 'Completed',
          'error': getStatusFromWorkflowStatus(workflowStatus) === 'Failed',
          'running': getStatusFromWorkflowStatus(workflowStatus) === 'Running'
        }">
          {{getStatusFromWorkflowStatus(workflowStatus)}}
        </span>
      </div>
    </div>

    <div class="status-details">
      <div class="info-row">
        <span class="label">Start Time:</span>
        <span class="value">{{getStartTimeFromWorkflowStatus(workflowStatus) | date:'medium'}}</span>
      </div>
      <div class="info-row" *ngIf="getEndTimeFromWorkflowStatus(workflowStatus)">
        <span class="label">End Time:</span>
        <span class="value">{{getEndTimeFromWorkflowStatus(workflowStatus) | date:'medium'}}</span>
      </div>
      <div class="info-row">
        <span class="label">Duration:</span>
        <span class="value">{{calculateStatusDuration(workflowStatus)}}</span>
      </div>
    </div>

    <h3>Rule Execution Results</h3>

    <div *ngIf="getExecutorTransactions(workflowStatus).length === 0" class="no-results-message">
      <p>No rule execution results available</p>
    </div>

    <mat-accordion *ngIf="getExecutorTransactions(workflowStatus).length > 0">
      <mat-expansion-panel *ngFor="let transaction of getExecutorTransactions(workflowStatus); let i = index">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="rule-header">
              <span class="rule-order">{{i + 1}}.</span>
              <span class="rule-name" [ngClass]="{'session-transaction': transaction.RuleId === null}">
                {{transaction.RuleId === null ? 'Session' : getRuleNameById(transaction.RuleId, workflowStatus)}}
              </span>
              <mat-icon *ngIf="transaction.Success !== null" [ngClass]="{'success': transaction.Success, 'error': transaction.Success === false}">
                {{transaction.Success ? 'check_circle' : 'error'}}
              </mat-icon>
              <mat-icon *ngIf="transaction.Success === null && !transaction.EndExecution" class="running">
                hourglass_empty
              </mat-icon>
            </div>
          </mat-panel-title>
          <mat-panel-description>
            <span>{{transaction.StartExecution | date:'medium'}}</span>
          </mat-panel-description>
        </mat-expansion-panel-header>

        <div *ngIf="transaction.Success === false && transaction.ExecutionException" class="rule-error">
          <p><strong>Error:</strong> {{formatExecutionException(transaction.ExecutionException)}}</p>
        </div>

        <div *ngIf="transaction.RuleInputJson" class="rule-data">
          <h4>Rule Input</h4>
          <pre>{{formatJsonString(transaction.RuleInputJson)}}</pre>
        </div>

        <div *ngIf="transaction.ExecutionResult" class="rule-data">
          <h4>Rule Result</h4>
          <pre>{{formatJsonString(transaction.ExecutionResult)}}</pre>
        </div>

        <div class="rule-actions" *ngIf="transaction.ExecutionResult">
          <button type="button" mat-button [cdkCopyToClipboard]="formatJsonString(transaction.ExecutionResult || '')" matTooltip="Copy Result">
            <mat-icon>content_copy</mat-icon> Copy Result
          </button>
        </div>
      </mat-expansion-panel>
    </mat-accordion>
  </div>

  <div *ngIf="!loadingWorkflowStatus && !workflowStatus" class="no-status-message">
    <mat-icon>error_outline</mat-icon>
    <p>No status information available for this workflow process</p>
  </div>
</div>

<div class="dialog-actions">
  <button type="button" mat-button mat-dialog-close>Close</button>
  <button type="button" mat-raised-button color="primary" (click)="onRefreshStatus()" *ngIf="getStatusFromWorkflowStatus(workflowStatus) === 'Running'">
    <mat-icon>refresh</mat-icon> Refresh
  </button>
</div>
