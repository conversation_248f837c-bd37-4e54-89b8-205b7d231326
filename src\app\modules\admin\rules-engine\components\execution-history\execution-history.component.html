<div class="execution-history-container">
  <div class="filter-controls">
    <mat-form-field appearance="outline">
      <mat-label>Filter by Workflow</mat-label>
      <mat-select [value]="selectedWorkflowId" (selectionChange)="onWorkflowFilterChange($event.value)">
        <mat-option [value]="null">All Workflows</mat-option>
        <mat-option *ngFor="let workflow of workflows" [value]="workflow.workflowID">
          {{workflow.name}}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <div class="action-buttons">
      <button type="button" mat-icon-button (click)="onRefreshHistory()" matTooltip="Refresh History">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <div class="history-table-container">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z2">
      <!-- Workflow Name Column -->
      <ng-container matColumnDef="workflowName">
        <th mat-header-cell *matHeaderCellDef>Workflow</th>
        <td mat-cell *matCellDef="let process">
          {{process.workflowName || getWorkflowName(process.workflowId || process.workFlowId)}}
        </td>
      </ng-container>

      <!-- Process ID Column -->
      <ng-container matColumnDef="processId">
        <th mat-header-cell *matHeaderCellDef>Process ID</th>
        <td mat-cell *matCellDef="let process">
          <div class="process-id-container">
            <span class="guid-display">{{formatGuid(process.workflowProcessId || process.workFlowProcessId)}}</span>
            <button type="button" mat-icon-button [cdkCopyToClipboard]="process.workflowProcessId || process.workFlowProcessId || ''" matTooltip="Copy Full ID" class="copy-button">
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </td>
      </ng-container>

      <!-- Start Time Column -->
      <ng-container matColumnDef="startTime">
        <th mat-header-cell *matHeaderCellDef>Start Time</th>
        <td mat-cell *matCellDef="let process">
          {{(process.firstExecutionTime || process.startTime) | date:'medium'}}
        </td>
      </ng-container>

      <!-- Duration Column -->
      <ng-container matColumnDef="duration">
        <th mat-header-cell *matHeaderCellDef>Duration</th>
        <td mat-cell *matCellDef="let process">
          {{calculateDuration(process)}}
        </td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let process">
          <span class="status-badge" [ngClass]="{
            'success': process.success,
            'error': process.success === false,
            'running': process.lastExecutionTime === undefined
          }">
            {{getStatusText(process)}}
          </span>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Details</th>
        <td mat-cell *matCellDef="let process">
          <button type="button" mat-icon-button (click)="onViewWorkflowStatus(process)" matTooltip="View Details">
            <mat-icon>visibility</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="executionHistory.length === 0" class="no-data-message">
      <mat-icon>info</mat-icon>
      <p>No execution history found. Execute a workflow to see results here.</p>
    </div>

    <mat-paginator [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50]" showFirstLastButtons></mat-paginator>
  </div>
</div>
