import { IReportList } from "src/app/shared-services/ep-api-handler/models/report-list.model";
import { environment } from "src/environments/environment";
import { DeviceDetectorService, DeviceInfo } from 'ngx-device-detector';

class UserLoginObject{

  deviceInfo: DeviceInfo;
  deviceId: string;
  USER_NM: string;
  WEB_URL: string;
  CODE_ID: number;
  USER_AST: string;
  Manufacturer: string;
  AppVersion: string;
  PushToken: string;


  //this.userString = this.userNameEmail + '^{"CODE_ID":0,"USER_NM":"'+this.userNameEmail+'","WEB_URL":"http://localhost:4200","USER_AST":"Manufacturer: Apple|~| GlobalClass: 14.3|~| Model: '+window.navigator.userAgent.toString()+'|~| Device Name: iPhone 11|~| VersionNumber: 14.3|~| Idiom: Phone|~| Platform: iOS|~| Current Id: 52FED91B-EB0A-4BA9-B0BB-31C5D4ABA80A|~| Device Id: 52FED91BEB0A4BA9B0BB31C5D4ABA80A|~| AppVersion: 7.0.0|~| Hardware: x86_64|~| iOS|~| Push Token:1","IP_ADD":"'+ this.ip +'","LOGIN_DT":"'+ this.date +'"}';
  constructor(private userName: string, private deviceService: DeviceDetectorService)
  {
    this.USER_NM = userName;
    this.WEB_URL = environment.apiURL;
    this.AppVersion = environment.version;
    this.Manufacturer = this.buildManufacturer();
    this.deviceInfo = this.deviceService.getDeviceInfo();
    this.PushToken = "none";
    this.deviceId = new DeviceUUID().get();


  }

  buildManufacturer(): string
  {
    const userString = `${this.USER_NM}^{"USER_NM":"${this.USER_NM}","WEB_URL":"${this.WEB_URL}","USER_AST":"Manufacturer: ${this.deviceInfo.os}|~| GlobalClass: ${this.deviceInfo.os_version}|~| Model:${this.getDeviceType}|~| Device Name: ${this.deviceInfo.device}|~| VersionNumber: ${this.deviceInfo.browser_version}|~| Idiom: ${this.getDeviceType(this.deviceInfo)}|~| Platform: ${this.deviceInfo.os}|~| Current Id: 52FED91B-EB0A-4BA9-B0BB-31C5D4ABA80A|~| Device Id: 52FED91BEB0A4BA9B0BB31C5D4ABA80A|~| AppVersion: ${this.AppVersion}|~| Hardware: ${this.deviceInfo.userAgent}|~| ${this.deviceInfo.os}|~| Push Token:${this.PushToken}"
    //,"IP_ADD":"${currentIP}","LOGIN_DT":"${currentDate}"}`;
    return "";
  }

  getDeviceType(deviceInfo: DeviceInfo): string
  {
    if (this.deviceService.isDesktop()) {
      return "Desktop";
    } else if (this.deviceService.isMobile()) {
      return "Phone";
    } else if (this.deviceService.isTablet()) {
      return "Tablet";
    } else {
      return "Unknown DeviceType";
    }
  }




  //Manufacturer: Apple|~| GlobalClass: 14.3|~| Model: '+window.navigator.userAgent.toString()+'|~| Device Name: iPhone 11|~| VersionNumber: 14.3|~| Idiom: Phone|~| Platform: iOS|~| Current Id: 52FED91B-EB0A-4BA9-B0BB-31C5D4ABA80A|~| Device Id: 52FED91BEB0A4BA9B0BB31C5D4ABA80A|~| AppVersion: 7.0.0|~| Hardware: x86_64|~| iOS|~| Push Token:1"


}


interface USER_AST
{
  Manufacturer: string;
  GlobalClass: number;
  Model: string;
  Device_Name: string;
  VersionNumber: number;
  Idiom: string;
  Platform: string;
  Current_Id: string;
  Device_Id: string;
  AppVersion: number;
  Hardware: string; x86_64|~| iOS|~| Push Token
  Push_Token:
}
