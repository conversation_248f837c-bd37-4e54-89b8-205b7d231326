import { Component, OnInit, AfterViewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { IframeJwtService } from 'src/app/shared-services/iframe-communication/iframe-jwt.service';

@Component({
  selector: 'app-pdf-filler-manager',
  templateUrl: './pdf-filler-manager.component.html',
  styleUrls: ['./pdf-filler-manager.component.scss']
})
export class PdfFillerManagerComponent implements OnInit, AfterViewInit, OnDestroy {
  pdfFillerManagerUrl: SafeResourceUrl;
  private iframeId = 'pdfFillerManagerFrame';
  siteId: string = '';
  private loadTimeoutId: number | null = null;
  private messageListener: ((event: MessageEvent) => void) | null = null;
  isLoading = true;
  loadError = false;
  errorMessage = '';

  constructor(
    private userContext: UserContext,
    private sanitizer: DomSanitizer,
    private layoutService: LayoutService,
    private iframeJwtService: IframeJwtService
  ) {
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    const tmpUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);

    // Construct the URL for the PDF Filler Management page
    // We're using the same base URL as the PDF Filler but with the /pdf-filler-management path
    const pdfFillerManagementPath = '/pdf-filler-management/';

    // Use the URL without token in the src attribute for better security
    this.pdfFillerManagerUrl = this.sanitizer.bypassSecurityTrustResourceUrl(tmpUrl + pdfFillerManagementPath + this.siteId);
  }

  ngOnInit(): void {
    // Set up a listener for iframe messages
    const pdfFillerUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);

    // Store the listener so we can remove it later
    this.messageListener = (event: MessageEvent) => {
      // Security check - only accept messages from the PDF Filler origin
      // Use startsWith to be more lenient with the origin check (handles http vs https, subdomains, etc.)
      if (!event.origin.startsWith(pdfFillerUrl.split('/').slice(0, 3).join('/'))) {
        console.log('Message origin mismatch:', event.origin, 'expected:', pdfFillerUrl);
        return;
      }

      // Handle messages from the iframe
      if (event.data) {
        console.log('Received message from iframe:', event.data.action, 'Full message:', JSON.stringify(event.data));

        if (event.data.action === 'IFRAME_READY') {
          console.log('PDF Filler Manager iframe is ready to receive JWT token');
          this.sendJwtToIframe();
        }

        // Handle loading complete message - accept multiple possible message types
        if (['PdfLoaded', 'IFRAME_LOADED', 'CONTENT_LOADED', 'PDF_MANAGER_READY', 'ManagerLoaded', 'APP_LOADED'].includes(event.data.action)) {
          console.log('PDF Filler Manager content loaded');
          this.clearLoadTimeout();
          this.layoutService.hideSpinner();
          this.isLoading = false;
        }

        // Handle error messages from iframe
        if (event.data.action === 'ERROR') {
          console.error('Error from PDF Filler Manager:', event.data.message);
          this.loadError = true;
          this.errorMessage = event.data.message || 'An error occurred loading the PDF Filler Manager';
          this.layoutService.hideSpinner();
          this.isLoading = false;
        }
      }
    };

    window.addEventListener('message', this.messageListener);
  }

  ngAfterViewInit(): void {
    // After the view is initialized, set up the iframe with JWT token
    setTimeout(() => {
      this.sendJwtToIframe();
    }, 500);
  }

  /**
   * Public method called by the iframe load event
   */
  public onIframeLoad(): void {
    console.log('PDF Filler Manager iframe loaded');
    //this.layoutService.showSpinner();
    this.sendJwtToIframe();

    // Set a timeout to hide the spinner after 10 seconds if no 'PdfLoaded' message is received
    this.clearLoadTimeout();
    this.loadTimeoutId = window.setTimeout(() => {
      console.log('PDF Filler Manager load timeout - hiding spinner');
      this.layoutService.hideSpinner();
      this.isLoading = false;

      // Check if the iframe exists
      const iframe = document.getElementById(this.iframeId) as HTMLIFrameElement;
      if (!iframe) {
        // If the iframe doesn't exist, show an error
        this.loadError = true;
        this.errorMessage = 'Failed to load PDF Filler Manager. Please try again later.';
        console.error('PDF Filler Manager iframe not found');
        return;
      }

      // Don't try to access iframe content as it will likely fail due to cross-origin restrictions
      // Instead, assume the iframe loaded successfully if it exists
      console.log('PDF Filler Manager iframe appears to be loaded');

      // Send a test message to the iframe to see if it responds
      try {
        iframe.contentWindow?.postMessage({ action: 'PING' }, '*');
        console.log('Sent PING message to iframe');
      } catch (error) {
        console.warn('Error sending PING to iframe:', error);
        // Don't set error state here, as the iframe might still be working
      }
    }, 10000);
  }

  /**
   * Clear any existing load timeout
   */
  private clearLoadTimeout(): void {
    if (this.loadTimeoutId) {
      clearTimeout(this.loadTimeoutId);
      this.loadTimeoutId = null;
    }
  }

  /**
   * Send the JWT token to the iframe using the IframeJwtService
   */
  private sendJwtToIframe(): void {
    // Get the iframe by ID
    const iframe = document.getElementById(this.iframeId) as HTMLIFrameElement;
    if (iframe?.contentWindow) {
      // Send the JWT token to the iframe
      this.iframeJwtService.sendJwtToIframe(this.iframeId);

      // Set up token refresh handling
      this.iframeJwtService.setupTokenRefreshHandling([this.iframeId]);
    } else {
      console.error('PDF Filler Manager iframe not found or not ready');
    }
  }

  /**
   * Clean up resources when the component is destroyed
   */
  ngOnDestroy(): void {
    // Clear any pending timeout
    this.clearLoadTimeout();

    // Remove the message event listener
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener);
    }
  }

  /**
   * Reload the page to try loading the iframe again
   */
  reload(): void {
    window.location.reload();
  }

  /**
   * Manually simulate a 'ManagerLoaded' message from the iframe
   * This is useful for testing when the iframe is having trouble sending the message
   */
  simulateManagerLoaded(): void {
    console.log('Manually simulating ManagerLoaded message');
    this.clearLoadTimeout();
    this.layoutService.hideSpinner();
    this.isLoading = false;
    this.loadError = false;
  }


}

