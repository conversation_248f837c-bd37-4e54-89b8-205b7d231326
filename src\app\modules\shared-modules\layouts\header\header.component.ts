import { Component, Injectable, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { MatDialog } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { LayoutService } from '../services/layout/layout.service';
@Injectable({
  providedIn: 'root',
})
@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})

//This is the authenticated Header Component for main layout
export class HeaderComponent implements OnInit, OnDestroy {
  lblDataLoadMsg: string = '';
  imgDataLoad: string = '';
  siteId: number = 0;

  private pageSubscriptions: Subscription = new Subscription;
  public isCtrlShiftPressed: boolean = false;

  constructor(
    public userContext: UserContext,
    private layoutService: LayoutService
  ) {  }

  ngOnInit() {
    // Check for stored data load status first
    const storedStatus = this.layoutService.getDataLoadStatus();
    if (storedStatus.message) {
      this.lblDataLoadMsg = storedStatus.message;
      this.imgDataLoad = storedStatus.icon;
    }

    // Subscribe to data load status changes from the layout service
    this.pageSubscriptions.add(
      this.layoutService.dataLoadStatus.subscribe(status => {
        this.lblDataLoadMsg = status.message;
        this.imgDataLoad = status.icon;
      })
    );
  }

  onKeyDown(event: KeyboardEvent): void {
    // Check if Ctrl + Shift is pressed
    if (event.ctrlKey && event.shiftKey) {
      this.isCtrlShiftPressed = true;
    }
  }

  onRightClick(event: MouseEvent): void {
    if (this.isCtrlShiftPressed) {
      event.preventDefault(); // Prevent the default context menu from appearing
      this.copyToClipboard(this.userContext.apihandler.LoadValidSessionObj());
      this.isCtrlShiftPressed = false; // Reset the flag
    }
  }

  async copyToClipboard(text: string): Promise<void> {
    try {
      // Use the Clipboard API to copy the text
      await navigator.clipboard.writeText(text);
      console.log('Copied to clipboard:', text);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  }

  public openHelpWindow() {
    this.openCenteredWindow("HelpScreen/"+ this.siteId.toString(), 1280, 720);
  }

  private openCenteredWindow(url: string, w: number, h: number) {
    // Calculate the position for the new window to be centered
    const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
    const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

    const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    const left = ((width - w) / 2) + dualScreenLeft;
    const top = ((height - h) / 2) + dualScreenTop;

    // Open the window
    const newWindow = window.open(url, '_blank', `resizable=yes, width=${w}, height=${h}, top=${top}, left=${left}`);

    // Focus on the new window if it opened successfully
    if (newWindow && newWindow.focus) {
        newWindow.focus();
    }
  }

  ngOnDestroy(): void {
    // Complete the destroyed$ subject to clean up subscriptions
    this.pageSubscriptions.unsubscribe();

  }
}
