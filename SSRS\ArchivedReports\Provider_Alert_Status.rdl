﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.64375in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.63672in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.62631in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.8125in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.82292in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.67708in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox24">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox24</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <BackgroundColor>#1f4e78</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>8</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
                <TablixCell />
                <TablixCell />
                <TablixCell />
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.9in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox20">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Total Patient Count</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox20</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox15">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>New / Unread</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox15</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Read</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox18">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Successfully rescheduled patient appointment</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox18</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox14">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Erroneous</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox14</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox11">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Patient has moved</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox11</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox8">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Patient has transferred to another practice</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox8</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Other</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>Gainsboro</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox73">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!TOTAL_PATIENT_COUNT.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox73</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox2">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!NEW_UNREAD.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox2</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox3">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!READ.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox3</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox4">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!REACHED_PATIENT.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox4</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox5">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!ERRONEOUS.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox5</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox7">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!PATIENT_MOVED.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox7</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox9">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!PATIENT_TRANSFERRED.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Sum(Fields!OTHER.Value)</SortExpression>
                        <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox12</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                        </RightBorder>
                        <BackgroundColor>#545454</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.36458in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox23">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!TOTAL_PATIENT_COUNT.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox23</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(SUM(Fields!TOTAL_PATIENT_COUNT.Value)) +" Total Patient" + IIF(SUM(Fields!TOTAL_PATIENT_COUNT.Value) &gt; 1, "s","")</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="NEW_UNREAD">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!NEW_UNREAD.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Red</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>NEW_UNREAD</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=99 'New/Unread'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!NEW_UNREAD.Value)) + " New/Unread Patient" + IIF(sum(Fields!NEW_UNREAD.Value) &gt; 1, "s","")</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="READ">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!READ.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>READ</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=98 'Read'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!READ.Value)) +" Read Patient" + IIF(sum(Fields!READ.Value) &gt; 1, "s","")</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="REACHED_PATIENT">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!REACHED_PATIENT.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>REACHED_PATIENT</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=9 'Successfully rescheduled patient appointment'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!REACHED_PATIENT.Value)) +" Successfully Rescheduled Patient"+ IIF(sum(Fields!REACHED_PATIENT.Value) &gt; 1, "s","")+" Appointment"</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ERRONEOUS">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!ERRONEOUS.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ERRONEOUS</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=8 'Erroneous'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!ERRONEOUS.Value)) +" Erroneous Patient" + IIF(sum(Fields!ERRONEOUS.Value) &gt; 1,"s","")</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PATIENT_MOVED">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!PATIENT_MOVED.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PATIENT_MOVED</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=10 'Patient has moved'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!PATIENT_MOVED.Value)) +" Patient"+ IIF(SUM(Fields!PATIENT_MOVED.Value) &gt; 1, "s","")+ " has moved"</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PATIENT_TRANSFERRED">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!PATIENT_TRANSFERRED.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PATIENT_TRANSFERRED</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=11 'Patient has transferred to another practice'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!PATIENT_TRANSFERRED.Value)) +" Patient"+IIF(sum(Fields!PATIENT_TRANSFERRED.Value) &gt; 1, "s","")+" has transferred to another practice"</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="OTHER">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=SUM(Fields!OTHER.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>Underline</TextDecoration>
                                <Color>Blue</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>OTHER</rd:DefaultName>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Drillthrough>
                              <ReportName>Provider_High_Risk_LTF_Summary</ReportName>
                              <Parameters>
                                <Parameter Name="USER_ID">
                                  <Value>=Parameters!USER_ID.Value</Value>
                                </Parameter>
                                <Parameter Name="IS_LOCATION">
                                  <Value>=IIF(inscope("PROVIDER_NM") , FALSE, IIF(Parameters!IS_LOCATION.Value, TRUE, FALSE))</Value>
                                </Parameter>
                                <Parameter Name="ID">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PRIMARY_PROVIDER_ID.Value,
			inscope("LOCATION_NM"), IIF(Parameters!IS_LOCATION.Value = FALSE, Fields!PRIMARY_PROVIDER_ID.Value,Fields!LOCATION_ID.Value),
			inscope("ALL"),Fields!LOCATION_ID.Value,0
			,0
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_OPTION">
                                  <Value>=12 'Other'</Value>
                                </Parameter>
                                <Parameter Name="LFU_ALERTS_FOR">
                                  <Value>=SWITCH(
			inscope("PROVIDER_NM"), Fields!PROVIDER_NM.Value,
			inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,
			inscope("ALL"),"All Locations"
		)</Value>
                                </Parameter>
                                <Parameter Name="ANNOTATE_DESC">
                                  <Value>=CStr(sum(Fields!OTHER.Value)) +" Other Patient"+ IIF(sum(Fields!OTHER.Value) &gt; 1, "s", "")</Value>
                                </Parameter>
                              </Parameters>
                            </Drillthrough>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <Border>
                          <Color>Silver</Color>
                          <Style>Solid</Style>
                          <Width>1.1pt</Width>
                        </Border>
                        <BackgroundColor>#d4d4e5</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>4.58222in</Size>
                <CellContents>
                  <Textbox Name="Textbox16">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value />
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>Gainsboro</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox16</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>Gray</Color>
                        <Style>Solid</Style>
                      </Border>
                      <RightBorder>
                        <Style>None</Style>
                      </RightBorder>
                      <BackgroundColor>#1f4e78</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>2.56842in</Size>
                <CellContents>
                  <Textbox Name="Textbox25">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Location</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>Gainsboro</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>Gray</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#0b6c9f</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.0138in</Size>
                    <CellContents>
                      <Textbox Name="Textbox21">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Provider</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>Gainsboro</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox21</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#0b6c9f</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
              </TablixMembers>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.44429in</Size>
                <CellContents>
                  <Textbox Name="Textbox70">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value />
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox70</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <TopBorder>
                        <Color>Gray</Color>
                        <Width>1pt</Width>
                      </TopBorder>
                      <BottomBorder>
                        <Color>Gray</Color>
                        <Width>1pt</Width>
                      </BottomBorder>
                      <LeftBorder>
                        <Color>Gray</Color>
                        <Width>1pt</Width>
                      </LeftBorder>
                      <RightBorder>
                        <Style>None</Style>
                      </RightBorder>
                      <BackgroundColor>#545454</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.12413in</Size>
                    <CellContents>
                      <Textbox Name="Textbox1">
                        <CanGrow>true</CanGrow>
                        <UserSort>
                          <SortExpression>=Fields!LOCATION_NM.Value</SortExpression>
                          <SortExpressionScope>LOCATION_NM</SortExpressionScope>
                        </UserSort>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Color>Gray</Color>
                          </TopBorder>
                          <BottomBorder>
                            <Color>Gray</Color>
                          </BottomBorder>
                          <LeftBorder>
                            <Style>None</Style>
                          </LeftBorder>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#545454</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.0138in</Size>
                        <CellContents>
                          <Textbox Name="Textbox72">
                            <CanGrow>true</CanGrow>
                            <UserSort>
                              <SortExpression>=Fields!PROVIDER_NM.Value</SortExpression>
                              <SortExpressionScope>PROVIDER_NM</SortExpressionScope>
                            </UserSort>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Left</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox72</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <TopBorder>
                                <Color>Gray</Color>
                                <Width>1pt</Width>
                              </TopBorder>
                              <BottomBorder>
                                <Color>Gray</Color>
                                <Width>1pt</Width>
                              </BottomBorder>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>#545454</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <FixedData>true</FixedData>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <Group Name="All">
                <GroupExpressions>
                  <GroupExpression>All</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>All</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>0.44429in</Size>
                <CellContents>
                  <Textbox Name="Group1">
                    <CanGrow>true</CanGrow>
                    <ToggleImage>
                      <InitialState>true</InitialState>
                    </ToggleImage>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>All</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Group1</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <RightBorder>
                        <Style>None</Style>
                      </RightBorder>
                      <BackgroundColor>Lavender</BackgroundColor>
                      <VerticalAlign>Top</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <Group Name="LOCATION_NM">
                    <GroupExpressions>
                      <GroupExpression>=Fields!LOCATION_NM.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!LOCATION_NM.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>2.12413in</Size>
                    <CellContents>
                      <Textbox Name="LOCATION_NM">
                        <CanGrow>true</CanGrow>
                        <ToggleImage>
                          <InitialState>=iif(CountDistinct(Fields!LOCATION_NM.Value,"LOCATION_NM") = 1,True,False)</InitialState>
                        </ToggleImage>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Fields!LOCATION_NM.Value</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>LOCATION_NM</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                            <Width>1.1pt</Width>
                          </Border>
                          <TopBorder>
                            <Color>Silver</Color>
                            <Width>1pt</Width>
                          </TopBorder>
                          <BottomBorder>
                            <Color>Silver</Color>
                            <Width>1pt</Width>
                          </BottomBorder>
                          <LeftBorder>
                            <Style>None</Style>
                          </LeftBorder>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>Lavender</BackgroundColor>
                          <VerticalAlign>Top</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="PROVIDER_NM">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PROVIDER_NM.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!PROVIDER_NM.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>2.0138in</Size>
                        <CellContents>
                          <Textbox Name="PROVIDER_NM">
                            <CanGrow>true</CanGrow>
                            <ToggleImage>
                              <InitialState>=iif(CountDistinct(Fields!PROVIDER_NM.Value,"PROVIDER_NM") = 1,True,False)</InitialState>
                            </ToggleImage>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!PROVIDER_NM.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>PROVIDER_NM</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <TopBorder>
                                <Color>Silver</Color>
                              </TopBorder>
                              <BottomBorder>
                                <Color>Silver</Color>
                              </BottomBorder>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>Lavender</BackgroundColor>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="Details" />
                        </TablixMember>
                      </TablixMembers>
                      <Visibility>
                        <Hidden>=iif(CountDistinct(Fields!PROVIDER_NM.Value,"PROVIDER_NM")=1,False,True)</Hidden>
                        <ToggleItem>LOCATION_NM</ToggleItem>
                      </Visibility>
                    </TablixMember>
                  </TablixMembers>
                  <Visibility>
                    <Hidden>=iif(CountDistinct(Fields!LOCATION_NM.Value,"LOCATION_NM")=1,False,True)</Hidden>
                    <ToggleItem>Group1</ToggleItem>
                  </Visibility>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DS_ALERT_MEASURE</DataSetName>
        <Top>0.68924in</Top>
        <Left>0.03045in</Left>
        <Height>1.76458in</Height>
        <Width>10.8015in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox10">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox10</rd:DefaultName>
        <Top>0.00441cm</Top>
        <Height>1.49687cm</Height>
        <Width>4.51084cm</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox13">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>LFU Alert Summary Report</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>16pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox13</rd:DefaultName>
        <Top>0cm</Top>
        <Left>4.61667cm</Left>
        <Height>1.48166cm</Height>
        <Width>17.0139cm</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <VerticalAlign>Middle</VerticalAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox47">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Epividian® CHORUS</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>™</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> Report</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Color>DimGray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Format>dd-MMM-yyyy</Format>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> (data)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=Now()</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Format>dd-MMM-yyyy</Format>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> (run)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontWeight>Normal</FontWeight>
                  <Color>Gray</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox46</rd:DefaultName>
        <Top>0.00441cm</Top>
        <Left>8.55764in</Left>
        <Height>0.58333in</Height>
        <Width>2.27431in</Width>
        <ZIndex>3</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Line Name="Line1">
        <Top>0.65278in</Top>
        <Left>0.03045in</Left>
        <Height>0in</Height>
        <Width>10.8015in</Width>
        <ZIndex>4</ZIndex>
        <Style>
          <Border>
            <Color>Purple</Color>
            <Style>Solid</Style>
            <Width>2pt</Width>
          </Border>
        </Style>
      </Line>
    </ReportItems>
    <Height>2.50382in</Height>
    <Style />
  </Body>
  <Width>10.87361in</Width>
  <Page>
    <PageHeight>8.5in</PageHeight>
    <PageWidth>12in</PageWidth>
    <LeftMargin>0.5in</LeftMargin>
    <RightMargin>0.5in</RightMargin>
    <TopMargin>0.5in</TopMargin>
    <BottomMargin>0.5in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_ALERT_MEASURE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@IS_LOCATION">
            <Value>=Parameters!IS_LOCATION.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ID">
            <Value>=Parameters!ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT LOCATION_ID, LOCATION_NM, PRIMARY_PROVIDER_ID, PROVIDER_NM, TOTAL_PATIENT_COUNT
, NEW_UNREAD, [READ], REACHED_PATIENT, ERRONEOUS, PATIENT_MOVED, PATIENT_TRANSFERRED, OTHER
FROM [REPORT].[GET_CRS_LFU_SUMMARY_DTL](@USER_ID, @IS_LOCATION, @ID)
ORDER BY LOCATION_NM, PROVIDER_NM</CommandText>
      </Query>
      <Fields>
        <Field Name="LOCATION_ID">
          <DataField>LOCATION_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LOCATION_NM">
          <DataField>LOCATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PROVIDER_NM">
          <DataField>PROVIDER_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER_ID">
          <DataField>PRIMARY_PROVIDER_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_PATIENT_COUNT">
          <DataField>TOTAL_PATIENT_COUNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="NEW_UNREAD">
          <DataField>NEW_UNREAD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="READ">
          <DataField>READ</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="REACHED_PATIENT">
          <DataField>REACHED_PATIENT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ERRONEOUS">
          <DataField>ERRONEOUS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PATIENT_MOVED">
          <DataField>PATIENT_MOVED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PATIENT_TRANSFERRED">
          <DataField>PATIENT_TRANSFERRED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="OTHER">
          <DataField>OTHER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
          DECLARE @SITE_ID INT
          SET                @SITE_ID =
          (SELECT        REPLACE(DB_NAME(), 'SITE', ''))
          SELECT        SITE_NM, CITY_TXT, STATE_TXT
          FROM            [CHORUS].[ADMIN].[SITE]
          WHERE        (STATUS_CD = 'A') AND (SITE_ID = @SITE_ID)</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>B7B36036-F48F-40AF-94BB-8EF0129A318D</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="drillthroughProviderCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughProviderCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughLocationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughLocationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughMeasureCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughMeasureCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="IS_LOCATION">
      <DataType>Boolean</DataType>
      <Nullable>true</Nullable>
      <Prompt>IS LOCATION</Prompt>
    </ReportParameter>
    <ReportParameter Name="ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>faef4e45-6f86-4d5f-ae3d-9230d18844b3</rd:ReportID>
</Report>