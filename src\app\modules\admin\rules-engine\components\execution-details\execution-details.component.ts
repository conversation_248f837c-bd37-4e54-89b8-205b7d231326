import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { WorkflowProcess, RuleExecutionResult } from '../../../models/rule.model';

@Component({
  selector: 'app-execution-details',
  templateUrl: './execution-details.component.html',
  styleUrls: ['./execution-details.component.scss']
})
export class ExecutionDetailsComponent implements OnInit {
  @Input() selectedProcess: WorkflowProcess | null = null;
  @Input() processRuleResults: RuleExecutionResult[] = [];
  @Input() loadingProcessDetails: boolean = false;

  displayedColumns: string[] = ['ruleName', 'executionTime', 'status'];

  constructor() { }

  ngOnInit(): void {
  }

  calculateDuration(result: RuleExecutionResult): string {
    if (!result.executionTime) return 'N/A';

    // For now, we'll just return a placeholder since we don't have start/end times
    // in the RuleExecutionResult interface
    return 'N/A';

    // When the API provides start and end times, we can use this code:
    /*
    const start = new Date(result.executionTime).getTime();
    const end = new Date().getTime(); // We don't have end time in the model

    const durationMs = end - start;
    const seconds = Math.floor(durationMs / 1000);

    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes} min ${remainingSeconds} sec`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const remainingMinutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hr ${remainingMinutes} min`;
    }
    */
  }

  formatGuid(guid?: string): string {
    if (!guid) return '';

    // Split the GUID by hyphens and get the last part
    const parts = guid.split('-');
    if (parts.length > 0) {
      return parts[parts.length - 1];
    }

    // If the GUID doesn't have hyphens, return the last 8-12 characters
    return guid.length > 8 ? guid.substring(guid.length - 8) : guid;
  }
}
