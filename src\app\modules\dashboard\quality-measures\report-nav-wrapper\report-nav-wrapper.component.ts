import { Component, OnInit, AfterViewInit, <PERSON><PERSON><PERSON>roy, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutService } from '../../../shared-modules/layouts/services/layout/layout.service';
import { ActivatedRoute } from '@angular/router';
import { LayoutModule } from '../../../shared-modules/layouts/layout.module';

@Component({
  selector: 'app-report-nav-wrapper',
  templateUrl: './report-nav-wrapper.component.html',
  styleUrls: ['./report-nav-wrapper.component.css'],
  imports: [
    CommonModule,
    LayoutModule
  ],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ReportNavWrapperComponent implements OnInit, AfterViewInit, OnDestroy {
  private subscriptions: any[] = [];

  constructor(
    public layoutService: LayoutService,
    private route: ActivatedRoute
  ) {
    console.log('ReportNavWrapper: Constructor called');

    // Try to get the site ID from the URL directly
    const urlParts = window.location.pathname.split('/');
    const qualityMeasuresIndex = urlParts.indexOf('QualityMeasures');

    if (qualityMeasuresIndex !== -1 && qualityMeasuresIndex + 1 < urlParts.length) {
      const siteId = urlParts[qualityMeasuresIndex + 1];
      if (siteId && !isNaN(Number(siteId))) {
        console.log('ReportNavWrapper Constructor: Found site ID in URL:', siteId);
        this.layoutService.setNavandReload(Number(siteId));
      }
    }
  }

  ngOnInit(): void {
    // Get the site ID from the route parameters and initialize the report navigation
    const subscription = this.route.params.subscribe(params => {
      const siteId = params['siteId'];
      if (siteId) {
        console.log('ReportNavWrapper: Setting site ID for report navigation:', siteId);
        // Initialize the report navigation with the site ID
        this.layoutService.setNavandReload(Number(siteId));
      } else {
        console.warn('ReportNavWrapper: No site ID found in route parameters');

        // Fallback: Try to extract siteId from URL
        const urlParts = window.location.pathname.split('/');
        const qualityMeasuresIndex = urlParts.indexOf('QualityMeasures');

        if (qualityMeasuresIndex !== -1 && qualityMeasuresIndex + 1 < urlParts.length) {
          const potentialSiteId = urlParts[qualityMeasuresIndex + 1];

          if (potentialSiteId && !isNaN(Number(potentialSiteId))) {
            console.log('ReportNavWrapper: Found site ID in URL:', potentialSiteId);
            this.layoutService.setNavandReload(Number(potentialSiteId));
          } else {
            console.error('ReportNavWrapper: Could not determine site ID from URL');
          }
        }
      }
    });

    // Store the subscription for cleanup
    this.subscriptions.push(subscription);
  }

  ngAfterViewInit(): void {
    // Force initialization of the report navigation
    console.log('ReportNavWrapper: ngAfterViewInit called');

    // Delay the initialization to ensure the DOM is fully rendered
    setTimeout(() => {
      // Try to get the site ID from the URL directly
      const urlParts = window.location.pathname.split('/');
      const qualityMeasuresIndex = urlParts.indexOf('QualityMeasures');

      if (qualityMeasuresIndex !== -1 && qualityMeasuresIndex + 1 < urlParts.length) {
        const siteId = urlParts[qualityMeasuresIndex + 1];
        if (siteId && !isNaN(Number(siteId))) {
          console.log('ReportNavWrapper AfterViewInit: Found site ID in URL:', siteId);
          this.layoutService.setNavandReload(Number(siteId));
        }
      }
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
  }
}
