<div class="rules-container">
  <div class="filter-controls">
    <mat-form-field appearance="outline">
      <mat-label>Filter by Rule Type</mat-label>
      <mat-select [value]="selectedRuleType" (selectionChange)="onRuleTypeChange($event.value)">
        <mat-option [value]="null">All Rule Types</mat-option>
        <mat-option *ngFor="let type of ruleTypes" [value]="type.id">
          {{type.name}}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <div class="action-buttons">
      <button type="button" mat-raised-button color="primary" (click)="onCreateRule()" class="btn-primary">
        <mat-icon>add</mat-icon> Create Rule
      </button>
      <button type="button" mat-icon-button (click)="onRefreshRules()" matTooltip="Refresh Rules">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <div class="rules-table-container">
    <table mat-table [dataSource]="rules" class="mat-elevation-z2">
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let rule">{{rule.name}}</td>
      </ng-container>

      <!-- Type Column -->
      <ng-container matColumnDef="type">
        <th mat-header-cell *matHeaderCellDef>Type</th>
        <td mat-cell *matCellDef="let rule">{{rule.type}}</td>
      </ng-container>

      <!-- Version Column -->
      <ng-container matColumnDef="version">
        <th mat-header-cell *matHeaderCellDef>Version</th>
        <td mat-cell *matCellDef="let rule">{{rule.version}}</td>
      </ng-container>

      <!-- Created Date Column -->
      <ng-container matColumnDef="createdDate">
        <th mat-header-cell *matHeaderCellDef>Created</th>
        <td mat-cell *matCellDef="let rule">{{rule.createdDate | date:'short'}}</td>
      </ng-container>

      <!-- Modified Date Column -->
      <ng-container matColumnDef="modifiedDate">
        <th mat-header-cell *matHeaderCellDef>Modified</th>
        <td mat-cell *matCellDef="let rule">{{rule.modifiedDate | date:'short'}}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let rule">
          <button type="button" mat-icon-button (click)="onViewRule(rule)" matTooltip="View Rule">
            <mat-icon>visibility</mat-icon>
          </button>
          <button type="button" mat-icon-button (click)="onDeleteRule(rule)" matTooltip="Delete Rule">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="rules.length === 0" class="no-data-message">
      <mat-icon>info</mat-icon>
      <p>No rules found. Select a different rule type or create a new rule.</p>
    </div>
  </div>
</div>
