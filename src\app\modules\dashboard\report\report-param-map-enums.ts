export enum ReportPanelFieldMaps {
  rptpanTxtSearch="DEMOGRAPHICS_ID",
  rptpanCohorts="COHORT_ID",
  rptpanRetentionIsLocation="IS_LOCATION",
  rptpanCohortYears="REPORTING_YEAR",
  rptpanFrmDate="FromDate",
  rptpanToDate="ToDate",
  rptpanMultiReportingYear="REPORTING_YEAR",
  rptpanReportingYear="REPORTING_YEAR",
  rptpanLocation="LOCATION_ID",
  rptpanPatientSelectionCriteria="PatientSelectionCriteria",
  rptpanCondition="CONDITION_ID",
  rptpanProvider="PROVIDER_ID",
  rptpanStartDt="START_DT",
  rptpanStopDt="STOP_DT",
  rptpanFirstLoginMonth="FIRST_LOGIN_MONTH"
}


export enum ReportPanelIdMaps {
 rptpanCohorts="cohortsData"
}
