import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models/report-list.model';

@Component({
  selector: 'app-report-redirect',
  templateUrl: './report-redirect.component.html',
  styleUrls: ['./report-redirect.component.css']
})
export class ReportRedirectComponent implements OnInit {
  reportinfo: string = "";
  newUrl: string = "";
  
  constructor(public router: Router) {
    let routerState: any;
    let tmpState = this.router.getCurrentNavigation()?.extras.state;
    if (tmpState!=undefined || tmpState!=null)
    {
      routerState = tmpState;
    }
    else
    {
      routerState = history.state;
    }
     if (routerState) {
    
        this.reportinfo = routerState.reportInfo;
        //this.reportPath = `/Chorus_Portal/${this.reportinfo.reportFileName.replace(".rdl","")}`;
        this.newUrl = this.router.url.replace("ReportRedirect/","Report/");

     }
    }

  ngOnInit(): void {
    this.router.navigateByUrl(this.newUrl, {
      state: {
        reportInfo: this.reportinfo
      }
    });
  }

}
