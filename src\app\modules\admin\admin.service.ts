import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  constructor() { }

  // This service will contain admin-specific functionality
  // For example, checking if a user has admin role, fetching admin-specific data, etc.
  
  hasAdminRole(): boolean {
    // This is a placeholder. In a real implementation, this would check the user's roles
    // from the authentication service or user context
    return true;
  }
}
