import { Injectable } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';

export enum Page {
    Login="Login",
    TfaSetup="TfaSetup",
    TfaVerify="TfaVerify",
    TermsAgreement="TermsAgreement",
    Schedule="Schedule",
    Documents="Documents",
    FileDownload="FileDownload",
    OutreachCalls="OutreachCalls",
    OutreachAdminActions="OutreachAdminActions",
    OutreachAnnotationStatus="OutreachAnnotationStatus",
    OutreachList="OutreachList",
    Report="Report",
    CustomQuery="CustomQuery",
    MobileManagement="MobileManagement",
    ChangePassword="ChangePassword",
    ForgotPassword="ForgotPassword",
    EHIExport="EHI-Export",
    QRDAExport="QRDA-Export",
    Unknown="Unknown"
  }


// src/app/models/config.model.ts
export interface PageAudit {
    page: Page|string;
    guid: string;
    siteId: string;
    parameters: { [key: string]: any }
  }

@Injectable({
  providedIn: 'root'
})
export class AuditService {
  public pageAudit: PageAudit = {
    page: '',
    guid: '',
    siteId: '0',
    parameters: {}, // Initialize as an empty object
  };

  constructor() { }

  public getPageAudit(): PageAudit {
    try {
      this.pageAudit = JSON.parse(this.readFromStorage('pageAudit'));
    } catch (error) {
      this.pageAudit.page = Page.Unknown;
    }

    // Report back the current site for this tab.
    this.pageAudit.siteId = sessionStorage.getItem('currentSite') || '';

    return this.pageAudit;
  }

  public setPageAudit(page: Page, extraInfo: string = ""): void {

    if (extraInfo != "") {
      extraInfo = " | " + extraInfo;
    }

    let tmpPage = page + extraInfo;

     // Reset whenever the page changes
    if (this.pageAudit.page != tmpPage) {
        this.pageAudit.page = page + extraInfo
        this.pageAudit.guid = this.generateGuid();
        this.pageAudit.parameters = {}; // Reset whenever the page changes
        this.saveCurrentAudit();
      }
  }

  public resetParamAudits(): void {
    this.pageAudit.parameters = {};
    this.saveCurrentAudit();
  }

  public addParamAudit(param: string, value: any, save: boolean = false): void {
    this.pageAudit.parameters[param] = value.toString();

    if (save){
      this.saveCurrentAudit();
    }
  }

  public saveCurrentAudit(): void {
    this.upsertToStorage('pageAudit', JSON.stringify(this.pageAudit) );
  }

  public generateGuid(): string {
    return uuidv4();
 }

 public upsertToStorage(name: string, valueToSave: any): void
  {
    sessionStorage.setItem(name, valueToSave);
  }

  public readFromStorage(name: string): string
  {
    return  sessionStorage.getItem(name) || '';
  }


}
