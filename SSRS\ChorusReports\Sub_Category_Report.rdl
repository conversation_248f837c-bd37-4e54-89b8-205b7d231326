﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.66667in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox3">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>MEASURE NAME</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox3</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MEASURE_NM">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MEASURE_NM.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MEASURE_NM</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>=Parameters!COLOR.Value</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Double</Style>
                        </LeftBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Visibility>
                <Hidden>true</Hidden>
              </Visibility>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>Sub_Category_Detail</DataSetName>
        <Height>0.5in</Height>
        <Width>3.66667in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.5in</Height>
    <Style />
  </Body>
  <Width>3.66667in</Width>
  <Page>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>1in</RightMargin>
    <TopMargin>1in</TopMargin>
    <BottomMargin>1in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="Sub_Category_Detail">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@CONCEPT_ID">
            <Value>=Parameters!CONCEPT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@COLOR">
            <Value>=Parameters!COLOR.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT DISTINCT O.SOURCE_CD + '_' + O.NAME + ' - ' + O.[DESCRIPTION] AS MEASURE_NM
FROM [MDM].[CONCEPT] C
LEFT JOIN [MDM].[OBJECT_CONCEPT_ASC] OC ON OC.CONCEPT_ID = C.CONCEPT_ID
LEFT JOIN [MDM].[OBJECT] O ON O.[OBJECT_ID] = OC.[OBJECT_ID]
LEFT JOIN [MDM].[CONCEPT_CATEGORY_ASC] CC ON (CC.CONCEPT_ID = C.CONCEPT_ID) AND (CC.CONCEPT_ID = OC.CONCEPT_ID)
WHERE C.CONCEPT_ID = @CONCEPT_ID</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="MEASURE_NM">
          <DataField>MEASURE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="CONCEPT_ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>CONCEPT ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="COLOR">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>354efd0b-5204-45bb-8132-8da4c199e4bd</rd:ReportID>
</Report>