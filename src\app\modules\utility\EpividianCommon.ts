import { Injectable } from "@angular/core";
import moment from "moment";
import { Observable, catchError, map, of } from "rxjs";
import { ApiRoutes, ApiTypes } from "src/app/shared-services/ep-api-handler/api-option-enums";
import { ApiHandler } from "src/app/shared-services/ep-api-handler/ep-api-handler";
import { IChorusResponseToken } from "src/app/shared-services/ep-api-handler/models/login-response.model";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root',
  })
// ObjectUtils.ts
export class EpividianCommon {
    
  constructor(private apihandler: ApiHandler) { }


  //Loads the session timeout from the environment variable or the session object.
  public LoadSessionTimeout(jwtSession: IChorusResponseToken | any = 0, offset: number = 2): number
  {
    let  sessionTimeout =  jwtSession?.expires_in || 20;
    if (environment.hasOwnProperty("sessionTimeout") && environment["sessionTimeout"] > 0)
    {
        sessionTimeout = environment["sessionTimeout"];
    }

    return sessionTimeout - offset;
  }


  public GetSessionObjFromLocalStorage(): IChorusResponseToken | null {
     // Retrieve the JSON string (encoded or plain) from local storage
     const jsonString = localStorage.getItem("Session");
    
     if (!jsonString) {
         // If nothing is retrieved, return null indicating no token is stored
         return null;
     }
 
     // Decode the JSON string if it was encoded before storing
     let decodedJsonString = jsonString;
     if (environment.encodeBrowserObjects) {
         decodedJsonString = atob(jsonString);
     }
 
     // Parse the JSON string back to the userToken object
     try {
         const userToken: IChorusResponseToken = JSON.parse(decodedJsonString);
         return userToken;
     } catch (error) {
         console.error('Error parsing user token from local storage:', error);
         return null;
     }
  }

  // Loads and validates the session object from local storage
  public LoadValidSessionObj(): IChorusResponseToken | null {
    let sessionObj = localStorage.getItem('Session');
    if (sessionObj) {
      let session = JSON.parse(sessionObj)
      let sessionInfoParsed = this.isSessionValid(session)
      if (sessionInfoParsed.isValid) {
        return session;
      }
    }
    return null;
  }

  // Similar to LoadValidSessionObj but checks for the validity of the refresh token
  public LoadValidRefreshTokenSessionObj(): IChorusResponseToken | null {
    let sessionObj = localStorage.getItem('Session');
     // Checks if the session object exists in local storage
    if (sessionObj) {
      let session = JSON.parse(sessionObj)
      let sessionInfoParsed = this.isRefreshTokenValid(session)
      if (sessionInfoParsed.isValid) {
        return session;
      }
      else{
        this.ClearSession();
      }
    }
    return null;
  }

  // Validates if the current session is still valid based on expiry time
  public isSessionValid(session: any): { isValid: boolean; minutesLeft: number } {
    const currentTime = new Date().getTime();
    const sessionExpiresTime = this.convertUtcStringToDatePlusMin(
      session.expires
    ).getTime();
  
    const isValid = currentTime <= sessionExpiresTime;
    const minutesLeft = isValid
      ? Math.floor((sessionExpiresTime - currentTime) / (1000 * 60))
      : 0;
  
    return { isValid, minutesLeft };
  }

  // Validates if the refresh token within the session is still valid
  public isRefreshTokenValid(session: any,offsetTimeInMin: number = -60): { isValid: boolean; minutesLeft: number } {
    const currentTime = new Date().getTime();
    //session.expires.setMinutes(session.expires.getMinutes() + 1);
    const refreshExpiresTime = this.convertUtcStringToDatePlusMin(
      session.issued,
      offsetTimeInMin
    ).getTime();
  
    const isValid = currentTime <= refreshExpiresTime;
    const minutesLeft = isValid
      ? Math.floor((refreshExpiresTime - currentTime) / (1000 * 60))
      : 0;
  
    return { isValid, minutesLeft };
  }

  // Converts a UTC date string to a Date object, adjusting for a given minute offset
  public convertUtcStringToDatePlusMin(expiresUtcString: string, min: number = 0, offset: number = 0): Date {
    
    let offsetToExpiration = this.percentToNearestWholeNumber(min, 15)
    if (offset > 0) 
    {
      offsetToExpiration += offset;
    }

    const parsedExpiresDate = moment.utc(expiresUtcString, 'M/D/YYYY h:mm:ss A');
    const convertedExpiresDate = parsedExpiresDate.toDate();
    convertedExpiresDate.setMinutes(convertedExpiresDate.getMinutes() - offsetToExpiration);

    return convertedExpiresDate;
  }


  // Converts a given integer to its nearest whole number percentage value
  public percentToNearestWholeNumber(integer: number, percent: number): number {
    // Ensure percent is within the valid range (0-100)
    if (percent < 0 || percent > 100) {
      throw new Error('Percentage must be between 0 and 100.');
    }
  
    // Calculate the actual value represented by the percentage
    const actualValue = (percent / 100) * integer;
  
    // Round the actual value to the nearest whole number
    const roundedValue = Math.round(actualValue);
  
    return roundedValue;
  }


    //Clears Users Session Objects and Local Storage.
    public ClearSession() {
        localStorage.removeItem("userName");
        localStorage.removeItem("Session");
        localStorage.removeItem("lastRefreshCheck");
        localStorage.removeItem("lastActiveTime");
    }


  // Saves the current user token in the browser.. To Get User object from Browser use userContext Service
  public SaveTokenToLocalStorage(userToken: IChorusResponseToken) {
    //depending on app configuration setting to base 64 encode browser objects.
    let jsonString = JSON.stringify(userToken);
    if (environment.encodeBrowserObjects) {
      jsonString = btoa(JSON.stringify(userToken))
    }
    localStorage.setItem("Session", jsonString);
  }

    // Saves data to local storage
    public SaveLocalStoreObject(name: string, value: any) {
      //depending on app configuration setting to base 64 encode browser objects.
      try 
      {
        let jsonString = JSON.stringify(value);
        if (environment.encodeBrowserObjects) {
          jsonString = btoa(JSON.stringify(value))
        }
        localStorage.setItem(name, jsonString);
      } catch (error) {
        console.error('Error saving to localStorage', error);
      }
    }

    // Reads data from local storage
    readFromStorage(name: string, isLocalStorage: boolean = true ): any | null {
      try {
        let savedData:string|null = null;
        if (isLocalStorage) {
          savedData = localStorage.getItem(name);
        } else
        {
          savedData = sessionStorage.getItem(name);
        }
        if (savedData) {
          // Decode the serialized value if necessary
          if (environment.encodeBrowserObjects) {
            savedData = atob(savedData);
          }
    
          // Parse if the saved data is a JSON string
          if (this.isJsonString(savedData)) {
            savedData = JSON.parse(savedData);
          }
          return savedData; // Return the deserialized object
        }
      } catch (error) {
        console.error('Error reading from localStorage', error);
      }
      return null; // Return null if not found or on error
    }

    upsertToStorage(name: string, value: any, isLocalStorage: boolean = true, isDateMustForward: boolean = false): void {
      setTimeout(() => {
        try {
          let valueToSave: string;
          
          if (isDateMustForward)
          {
            value = new Date(value);
            var strDateFromLocal = this.readFromStorage(name, isLocalStorage);
            if (strDateFromLocal)
            {
              let dateFromLocal = new Date(strDateFromLocal);
              if (dateFromLocal > value)
              {
                return;
              }
            }
          }
          
          // Serialize the value if it's an object
          if (typeof value === 'object') {
            valueToSave = JSON.stringify(value);
          } else {
            valueToSave = value.toString();
          }
      
          // Encode the serialized value if necessary
          if (environment.encodeBrowserObjects) {
            valueToSave = btoa(valueToSave);
          }
      
          if (isLocalStorage) {
            localStorage.setItem(name, valueToSave);
          } else
          {
            sessionStorage.setItem(name, valueToSave);
          }
          
        } catch (error) {
          console.error(`Error writing to storage location IsLocal ${isLocalStorage}`, error);
        }
      }, 100);
    }
    
    // Deletes data from local storage
    deleteFromLocalStorage(name: string): void {
      try {
        localStorage.removeItem(name);
      } catch (error) {
        console.error('Error deleting from localStorage', error);
      }
    }
       


  // Helper function to check if a string is a valid JSON
  private isJsonString(str: string): boolean {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }


  // Refreshes the user JWT token and handles the response
  public refreshUserJWTToken(userSession: IChorusResponseToken): Observable<IChorusResponseToken | null> {
    let refreshTokenPath = ApiRoutes.RefreshToken.replace("{{refreshToken}}", userSession.refreshToken);
    
    return this.apihandler.Post(
        ApiTypes.AuthProvider,
        refreshTokenPath,
        "",
        false,
        true
    ).pipe(
        map(res => {
            if (res.access_token != null) {
                this.apihandler.setbearToken(res.access_token);
                this.SaveTokenToLocalStorage(res);
                return res;
            }
            return null;
        }),
        catchError(error => {
            // handle the error here if you want, e.g., log it
            console.error('An error occurred:', error);
            return of(null);
        })
    );
  }

  public getLastSegmentOfUrl(urlString: string): string | null {
    try {
     //   const url = new URL(urlString);
      //  const pathname = url.pathname;
        // Split the pathname into segments and filter out any empty segments
        const segments = urlString.split('/').filter(segment => segment.length > 0);
        // Return the last segment or null if there are no segments
        return segments.length > 0 ? segments[segments.length - 1] : null;
    } catch (e) {
        console.error('Invalid URL:', e);
        return null; // Return null for invalid URLs
    }
  }

  public JSONtryParse(jsonString: string): any {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        return jsonString;
    }
  }

}

