export interface Rule {
    RuleID: number;
    Name: string;
    Description: string;
    Type: string;
    Version: string;
    ConditionExpression: string;
    Action: string;
    JsonData: string;
    CreatedDate: Date;
    ModifiedDate: Date;
}

export interface FormModel {
    FormName: string;
    Fields: FormField[];
    Layout: FormLayout;
    DataUrl: string;
}

export interface FormField {
    FieldName: string;
    FieldType: string;
    Label: string;
    DataUrl: string;
    SelectedValue: string; // Adjust type based on actual usage
    isEditable: boolean;
    isRequired: boolean;
    LayoutOption: string;
    ValidationCondition: string;
    ValidationText: string;
    Attributes: { [key: string]: string };
}

export interface FormLayout {
    LayoutType: string; // e.g., Grid, Column, Row
    LayoutAttributes: { [key: string]: string }; // Styling attributes
}

export interface ControlStyle {
  properties: { [key: string]: string };
}

export interface ControlDimensions {
  height: number;
  width: number;
}


export interface WorkFlowProcess {
    workFlowProcessId: string;
    workFlowId: number;
    workFlowName: string;
    workFlowTextCode: string;
    startTime: Date;
    endTime?: Date | null;
    rulesInProcess: number[];
    RuleDetails: Rule[];
    executorTransactions: ExecutorTransaction[];
    workFlowRuleDependencies: WorkFlowRuleDependency[];
  }
  
  export interface WorkFlowRuleDependency {
    DependencyRuleId?: number | null;
    RuleId: number;
    Status?: WorkFlowStatus | null;
    SequenceOrder: number;
  }
  
  export enum WorkFlowStatus {
    Waiting_Ready = 0,
    Waiting_NotReady = 1,
    Executing = 2,
    Done = 3
  }

  export interface ExecutorTransaction {
    ExecutionLogId: number;
    RuleId?: number | null;
    WorkFlowProcessId?: string | null;
    WorkFlowId?: number | null;
    RuleInputJson: string;
    ExecutionResult: string;
    ExecutionException: string;
    StartExecution?: Date | null;
    EndExecution?: Date | null;
    Success?: boolean | null;
  }

  export interface TableData {
    [key: string]: any;
  }

  //#region Tree Methods
  export interface JsonNode {
    name: string;
    value?: any;
    children?: JsonNode[];
  }
  
  export interface FlatNode {
    expandable: boolean;
    name: string;
    level: number;
    value?: any;
  }

  export interface PdfFillerRule {
    RulesToProcess: number[];
    PdfFillerId: number;
  }

  export interface FormStatus {
    formId: string;
    formStatus: string;
  }