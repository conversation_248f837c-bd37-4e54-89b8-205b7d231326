import { Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-boolean-field',
  templateUrl: './boolean-field.component.html',
  styleUrls: ['./boolean-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => BooleanFieldComponent),
      multi: true
    }
  ]
})
export class BooleanFieldComponent implements ControlValueAccessor {
  @Input() label: string = '';
  value: boolean = false;
  disabled: boolean = false;

  // Function to call when the value changes
  onChange: any = () => {};
  
  // Function to call when the input is touched
  onTouched: any = () => {};

  // Write value to the component
  writeValue(value: any): void {
    if (value === null || value === undefined) {
      this.value = false;
    } else {
      this.value = Boolean(value);
    }
  }

  // Register onChange callback
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  // Register onTouched callback
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  // Set disabled state
  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Update value and call onChange
  updateValue(value: boolean): void {
    if (this.disabled) return;
    
    this.value = value;
    this.onChange(this.value);
    this.onTouched();
  }
}
