﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="dsPatientDemographic">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>REPORT.GET_PATIENT_DEMOGRAPHICS</CommandText>
      </Query>
      <Fields>
        <Field Name="PATIENT_HEADER">
          <DataField>PATIENT_HEADER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DATE_RANGE">
          <DataField>DATE_RANGE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsLabFlowsheet">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>REPORT.GET_VISIT_RESULTS</CommandText>
      </Query>
      <Fields>
        <Field Name="VISIT_DATE">
          <DataField>VISIT_DATE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LOCATION_NM">
          <DataField>LOCATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FULL_NM">
          <DataField>FULL_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="VISIT_TYPE_DESC">
          <DataField>VISIT_TYPE_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.08887in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.6097in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.95346in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.52637in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.36458in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Visit Date</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#d9e2f3</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Location Name</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#d9e2f3</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Provider Name</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#d9e2f3</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Visit Type</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#d9e2f3</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.26042in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VISIT_DATE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VISIT_DATE.Value</Value>
                                  <Style>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VISIT_DATE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) = 1, "#d9f3ea", IIF(RowNumber(Nothing) Mod 2 = 0,  "White","#d9f3ea"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LOCATION_NM">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LOCATION_NM.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LOCATION_NM</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) = 1, "#d9f3ea", IIF(RowNumber(Nothing) Mod 2 = 0,  "White","#d9f3ea"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="FULL_NM">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FULL_NM.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>FULL_NM</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) = 1, "#d9f3ea", IIF(RowNumber(Nothing) Mod 2 = 0,  "White","#d9f3ea"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VISIT_TYPE_DESC">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VISIT_TYPE_DESC.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VISIT_TYPE_DESC</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) = 1, "#d9f3ea", IIF(RowNumber(Nothing) Mod 2 = 0,  "White","#d9f3ea"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                </TablixMember>
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <KeepTogether>true</KeepTogether>
            <DataSetName>dsLabFlowsheet</DataSetName>
            <Height>0.625in</Height>
            <Width>8.1784in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>5.57169in</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>8.1784in</Width>
      <Page>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>0.15in</LeftMargin>
        <RightMargin>0.15in</RightMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="DEMOGRAPHICS_ID">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>31</Value>
        </Values>
      </DefaultValue>
      <Prompt>DEMOGRAPHICS_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>88D180EC-184E-4F88-8794-1A4F0BC0748C</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>3</NumberOfColumns>
      <NumberOfRows>1</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>DEMOGRAPHICS_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="Logo">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAANcAAAA7CAYAAADo+x9MAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAhdEVYdENyZWF0aW9uIFRpbWUAMjAxNDowMzoxMyAxMzo1Nzo1MznMPb4AACeQSURBVHhe7X0HnFTV3fYzs222V7Z3dl16WUGqgBVErLFgiBp9NRrTvsS8MdFoMCYmkejPYMpniwVbUDSggChIB+kdFrb33ne2zezM+/zPnbu9AbuIsA+cnbnnnnvqv557zhmDncAQhjCEAYfR8TmEIQxhgGGw2awXveYyOD576whJcyb3+3pOoJcv+CbqMFj5XqwwGJzU55DmGsIQBglDzDWEIQwShphrCEMYJAwx1zmAmpC12flfC0fzqrS4IVzQGJrQIMQxF/TWEWfj9AtD7cuuxHNrj6q4z44Xo+KFW2Fy0RxfvXzBYNXhm8j3YoU+odGBuWyUru+k16CpxY5QD2eEu7vA180AdycjXI0StHQtJJYWpm2wG9BgtaDOCtQ22VFtaUFNcwvujPeBi9HAQrT05zv0ag40AaYX1+JvX53ClrRiHCmoZozW6cJTlS/cBndXZ17ZWssXDHQdBGf6nI4zzfdiRbfMJQzzdmo1TtVa8G9+ljZa2IEGmjDyAP9ISuEYRtjljjJteF/uMV7xHm8X35WAAFcnRkn3n//Qaymt6Ql9EVJ39yvNzThZVI09WVV4dMV+UAYpuFLwDDHXhYtumUuHmDHbS8y4Yk0+bOxBJ16/ODUYV4R6wqgYRnuEvIj6FqDIbMHeigYsPVZBBjOidNElquPJfSrdQEP5K9SaVdSU3s4GOFOzng1UXYnOtbXZbKhraqH5ZqS20croSWC09UpHSLz05/ylm/FFSpGKk7x6ZC6mleZV1FsQQOvBQEbUa9hTGTp6un+mz+k4k3ylHZ3vf1uE7dmi1/dcEjl9mCeCTaKdhBgM
uD/RD6P83TDCz5VBPt0wimFSoBvmx3hi8YQgLJ0aBj83o6MTB7EjyVj7KuqR9GE6fvJ1qSNy4HEorxpjFq/G3a/v7JmK+gHR/hOj/aTXtdAr7Pj3tgxEPvYxlu/N4dW3kyCl1lYy2Jai+rPqu28zuhf5JABnSsxITzde2JlIu+4CiWKQ+/LMjFAPBLsJ19p4zTBYYHGPbC9BOZXuq6cqcaC8EXZRowMFMm9jsxXzlm5CXm0TPjqUi00pJSz2zMvwNrmqp/vKoYQm+U+X70UzEz764QFYWwaxHwcRIhRSqi1YsC4PjaKKZdAuMvRqT5lotZwOOQW5GfDDkQGOq8GDmByTg8j4dhsiPVwQ6+XSt0I4Tbg6O+HKS4JhZFmBHiYkhfmwL3rtrgGBn7szEoO1sqbEBXYv1L4VsCOnzgozx6jQ3HIRslYfzNVqxqie6bt7vF2ccHeij+Nq8GBkrZ+fHILfjB2GNddGwF+05UByF21hI4n6rfunY/H80Vj749kI83NnD5yFFulQvZ5FlquzESsfmYlfX5WIN+6b2p9uP09hQHmjBU4kscIGfSrn4sLgi+LBAM02VxcDnk4OxEhfV0fkwMPZaMfjC8YgOcZf1OU50VxClNGB3vjjrRPg7ebiiPv2QWRdeZMNSRyf3HphrtOxgS4M9I9aVL/03DnyfkyCCPb+rDwQ/6g/6XoEB05mLZ0MMt0vM26DAyNVpDY7Sq/SKGVp8WeEDs3tJSPeUuXwj4Hly2TI4EJ7rTLgoAAsaWzB2AA3lJK5eHnRYUBEsfTbnrJ6pNQ0diKi7pFT38y/A9DbzEJIY/BxlsLgDCBMdS6KtClLd+DLEmYqbyBz+bkiT2mui4+7elz+JF0xa00OthbXw4MuTcWiJLg6afP3rbC18I+RCsuO/9lWiFtivHFDtFerhJf3O0ry0ocxsLfzaXsvPVqOZenVyF94CVPYUdNsw0fZtdhT2ggL0w/3csGtsd64xMeV+fBpqZ1D
BLQn8DqLDf88WYVPMmvw9Y3DVVxFXQNyKusR6O5Gs5HWPp+TOpgbmuHvZYK/p2ZC2uwtyK9oQAPzcHOmRuI/K6msurEJI0J9YXJxVu03N7XgrR0ZeHlrKvY9OR/OEuloW3uoevF/VpkZKw/lIaWwGpaWFoT5e+PyhCBcMSIUf/3yOJ5cpS1/klcbnd9zaX2lskEO67Zk3Qm0WFvwz3smM14rVEunvipkldbg4wP5OFVihrXFilD6hbMTgnHVyFBor+XkD/NnP6oq8o8QvcSeKKrFnz8/CqvFjncenM6bBqSX1uI/e7KRUVYHL3cXzBo+DAvGR3Lc+ZD81wfWAblqV50OsDO/Ozfk4ruX+OGTjBq8NSfccefCR68vkQXScTpzObNTS+5KgF8nH8COFlTTrt5YaMZ9ZK4110RjWrCpdRBkQOutFryRWovPcs3YXFgP0VnhJifk3JmA18kcj+8tQQ151Ga3oUUIgPdlLH+U5I/nJg9zDGxbfinVjfj3qVq8nVaFMhJ/uLsTchcmqfv7s0rwwLI9OJQvS400YvV0c8ZNYyPwyJxETE0YptIJkX20Nxevbk3HxlNFbIW014C7L4vF87dPQGpJHf69PR0f7s9FbbNVMVvlC99RS7pUpp1QRU382PIDcCFDL5oSg6hAT1SardieWoy/bzoJMwk4PsgDm9LKVXoXUnd3zCXT/a+z3I8P5qGJXHTXxEgse3BGa5FaOgO1jRWLyaifHSnE7ZOi4OXmhIN51fhwXy7qLVZMiwnEhw9fjjBfk/YcH5Q251c14BO26bOjRfjqVKHK65ZxEXjvwWl4etURLFl/UtWmpUVjSFmVM2KYN5Y/NBOjwny7tF0uuyUeQsZqDunn9ctD8fCOYqyfG9Vt312I6PUlsg6RcjIyQnxJK9JxyUeprSF+eSqC30tD8PtpuG1jAYnQBh8Siy5lBaJ1hEiCGD8p0JUDJ7CTWe344Y5CHKpswlfXR6NqUSJK77oEn14d
galBJpXupZQq3L+NhN9h9GzIrW2BD3m8kZpBUU278pJJVNsfuxYz4slEZAS7wYg/3Twebz8wvZWxBEI4t0+Oxhc/n4M7+Sm+zU/JfG/ePxWB3ibkUgP5mVxgJmNppO/wStqKUhACKqlpwpV/3YArRwbjpe9eimksJ9LfA2MjffDQFQnY++R1uGFMGDanlShC7NCcdhDizxWNwXKbqEUlb7sQuON+G2x4cuVRLN14Cht+MQePXzcKP71qBP59zxTseuxqBHm6YEdOBR54+2um7Fhik6UF/h6umBztSwaVuU+70tgPvrkbzrRKjj91HSqfvwVpz9yAv9w8Fl6uLjhO7Th36SYU1TQ4fGVHZn1A6l1ntSGalkglheDFiF6ZSx8XWUMYwUEb62fSgr8JEwNNuDzYA5P5qSQ64WTsOFUtaw4DXFzVQt5nLg3GnDBKUrsRufUtSA5yx0tTgzGa0pWKjIxpwHURXtg0Pxq3R3vL0/ggsw7vZlRpmSkYcU2kB54YH4T5EdqUf/uxlu/uLgYsvetSGEUy8P8Jmmg9Qfy1KD8v+FLy/3bBWEcs8J3JUXju9omYFB2g2u5YNdkFshbzrle2YlJMAG6n1uvCCKyQGzXviwsnYkq0MLf0T8c+0iEMf/fM4fjHXZMQ4kHp0SUzDSlFNXj+yxMI9/OgmWtiEXpCO0aG++IHlycyxo4vUgpRUt3ULh8D4oZ54btT4/A0Bc7wYZ4q7osTJbh3ehwWLxiN4cHe1ILOiKOW/eU1I/Hpj2bBlQKqkNbC7z89ovJSa0z7AREMwomy4Fs+u2/1hY3emcsB8RG2XR+LFdQsKlylhY/5ffsNMfh6QTQ82Il0EYi2zlfmIUvg+KjvwRw4uR9Mbnog0V8i1T31qb4baAYa8fqsUAz3pn/EgVxyuELzM4TI1eCS1PlMoHBkJ6jymOGESF9cNSKYMTYs25OLKjOJrBuaEL1wOL8KD5EgAyk8dMgsoRQWFeCptHeXR1kX
iXtnZwY2ppdRcySpjmwjdA2qrqyPhDnUbFKe/OuuMjIrKTLKmWX7e7kzppsKE8fyqmBlutyqejQ0WdtKVM8bMCHCjxdGMj5QVNuo3RMwoepj9qnUJ9hLVt8AU+MClE8oWl7ykKD1IzAzIQSLLotRffDhgVyOr7SbF/1AE7WW0I1oZC8KvGqaxhcbhCb6hCTqbaXA+AA3ahRP+kC9zwo5cQCliz1kEqGX5VGyxeXX4wIUUx2vsiCt1sLYToMjBNADJOXPrkziwBpR29SMN7ZndEuq5eZm7Mkq19I64tpDWzQr6HpXHHYxzYK9TBhNjSHt7rlGBni6ijCQfLorqf+4JTkay+6ZhoWX0pxuaFbWQXuI36dYn/2jmYXdgAQvdRIEKKHSNZ2805McbqS/Klfl9VZk0mztp+JCjcUGPzqXwqjh7s4oZl2/jbDVWfDEujoYV9Xhil0WFPaz/YLemUv6XzSG+tdzrpLivkRfaiYXfu+jdI6OPvg9QQbw5lhvbf8Yk52oEgncvqqqYkoqaoTSEUIWc0eHY1SwmI4G/HNLBiV5O2aWRxhe25qOu+hzhdHU1d5ndUSHGK1IBWlvUZUZh2hyhvm6KyZUj3fNohVaD2qhj4S9wona4LtTY/DavVMQTt9OsbSjG2RSRL6rUrrpl1awsjqTiJAQdG6+Mv8YlxjiQ19LbsqECMehL+5y3C6lnxXkRmHKeoR5uiJfTcd/22BHWrYFeSZpvwHRjVbsNGt3+oO+NVcvTKBD+ntBlCdG+fexGFFl1Xd+ksSfkj7BWzPVzFRc3WWrmTld8xN7Xyy7n1yZoK4zymvw2aEC9V0gedU3W/Dy5lT8gr7F6UJKTCmuUQQtxfejRYMH1kHak19Rh1UH8tWrgIGskUx0qBLY0PomsSD6h9LGFgS6u7Am9Gs9nFFo/jZ6XQYkxLggslF6mPB1wTRxVfuJXplLCS9Hvr0NmJLcpGYDzb42U6oHtObXMxTT8DPUQ0wcA9sk
V90PTvfZabGLpsQhiCaJNPNvX6UoKWq3Sz52vLUjS/ka0YEeKu3poq5B/B1qsOp6pTF0DTDYUAqJQV5dbEstwSPv7sWMP6/DH1efQEV9I+KDvXifdTn7YXCAKSUvfmjLv/pqp9w3orTBhhD6xfJMKJksXxYOqH7SUnWB3KMfIH0pq32UVXIOoMohSSi6kDbKNb/r5Ru9XPDHuV6w3eiJtyY4I6yv5rdD35rrG4Q43mLejKTZdiaQd1wPzKD2YodsTS/D/uxKdpwBzVY7Xlyfgv+dO1L684zg5a5NuJTUNCK1uE6LPEcEkVNRi2tf2ISrX/wK8UGe2PDo1fjn9ybh+zOGY2yY7Bs7d8zeE8rpfwc6JrAivIzIM8tsV8/9I3fEFF16rBL3bNY2lZ4bGFBtteJPhypx4/o83LOlEKtzG1ifsx/LXplLxkeKkIJOpyiRQJ/l1Kqp6g4Pynd2oPLfeslQ0zBAUb0Fib4mxNE8PJM1hFLOw3MS4UpNKKbiSxtPyQiqVQjJMQEYEebTra/VH4wI9WZTmC819Rs70lnfFtVfPUE13RHOFJJHk8WGuX/bgo1ppXj+1vH4JQWEuxypwHpIW6Q5qmt7K0ivTH+hMuwvZOxaUNbYTLNQrBn6XB4uHEttyVvP3a2oAssza3CIvl1d92sbBgyKokmf5fQNp63KRUZdIx5O8sfV4R741d4i/P5gmUODOh44A/SuuVTGp5/72oJ6PLS92HHVFfKStLfRFXOrlM7jyRorHhnhywE5HUpoDwMi/N3xnYlR/G5XKxjyK+vxwpfH8djcUVqSMwIJxtcdyVF+ZDAj/rElFellZpEbPcJMSS4DdbbKbdWhXKSWmtXKlXuplU9P7HWFev5Mu7cHyHCV0CyUVy6iQUNNzihqEOEjBfVcX1nJIqJXFhycqh7c2UVNMNrx7KEyzI/2xKszIjA/yhP3DPfB5vmxeONkNU7Ie8KzgPKQOgehAFE6ahEEL+U0qNy6ZkYz
sjVIOi2tmk0iv5yobMaiTfm44ctc3JPgq6bvpT/1fEPo2MozBfUGvJNWqTSc5Knf14PYv6+drMJoH2c8SGkiU8Ja0O/bMdrPRVWjgL5PUYOF37UNeR0C/zjxz+v3XoYoHxMabS2YueRLyF6tcdFkWkc6Ha3POcLoUC2NrGLQlgQ57vGLdNy/Fk1Wq03MzS2Y+uf1OJBTqXwhsdmlctIOqWM1pfiHe3JaM2622lBaU897MoNm5DNyQ4M8H06BIDHZ5fX8S/nHPByPslyOjcGm8lBT4waNgLXytM2WogMk7daTJYyX+zJ/quchY2LHuEitX3eklqnn2u53DJeEeKl3YtL2rWnFSoCowHuC9mlV4B8pQ1ZlBNAslGtvV6NaC6qVI//apW8NdqTXWdTa0nkRPlibV9drvfobFI0xbCsy46c7ivFjPewsxk92lGJNbj0uV2fDMD2DvOMcRqHw3ORQMl6FFt9Nvr0FHd1qLmlUKQniiJoCFxjw6K4StT5wd1mjWrb0NT/X5pvx6qlq/HxPESZ9loXx/83AfzJrkRzojicnBDqe7Q5WPLqnDNtKhHjaV0eD5PF2Wo16SW2idOkMeUKz4TVk13b/kliHbEB8eLYsFKa/UtWA/712FPPo+hK6M3IqzCpfi7UFBa190YbkaH88c9M4piExNTZh1vPr8Ze1KUzbpPqwkZL4y+NF+OHbe3Hj+DDHUwQbsPpIgUqjhIuMajvkV8i5EwbkyWSJaPl298dHBmo158i9KGsBeV+eF3aqaWjEc58fpznmqrTEF8cKGc9CunYxThZXKa1bwXHOLHX4jN3ASsIURpbuPZxboT77g4omG5mrjby8abpWNfc+HX+ANJUcZMLoAFccr2jurtpnhK3FDbjjqyLE+jhhQqBbhxDuaUQZhXNnFDPOXz9L8AxhsHc6t7CBg/WvEzV441Ql1SI1grzsbU3BIeSAaAPGpjNoHSBDy28klLmRnnhzVgiC3Fw1tdgOv9pT
iiVHy9WSmlH+Ljha0aTSz6HkkCMCShrt+JwSy5eN+sf0EITLMqB2YPWUFvlXSjUWHyhFFaWhSNHh1HB3xvlhlJ8rFsZ7s9yOBcsMVHldE+J/+ynCfdxxdPH1rVq1O9RTE7204SSeXntEMYhgDLXYd5IjSdz+uHlipIrT8Y+NqXj8k4OooWOsL7vycXNRTLlochyW3D4B7+3OxCP/OaA9wIa4uxgxJsQXtyRH4VfzkviMEwposv5hzVG8uj3DoaftmJcUjNkjwtSRA5PitCMUHl9xEEs2pCgGmhITiNkJQaimBpcVG7+6bqRa73jTv7aittmC6bwfQq295I5kxAV4IqPcjN+tPIR39+bK6KtSQjxNWDg5BmPC/XHfzHitZGrQgzkVrM9xrDycJ4aJwjWsx8xLQnHf9DhEiobt1In6htKpn2Zg07wYtRxNsPCrQvzu0gCM9NVWhnSGlPejnaW4jeM3jQx2+eoc7L4xtgsN9RdqfyEhjLVwYz7emROGK8L008vasJ4uzCM7CrFlfjTNWM2vz6evf/maXLw7OwwzQjrOJmu5qiHuGfqq+M7MVUeCWJZWixhvZ0RRRXsZndVZGlIpMT9qbRxEMnpFk4UcbyNDWEhUUNtSpgebaC+zMkLx/C/mV3vozOXlRIn5vUQco4TfkF9P36pZMUC8twvmR3phvL8mebsyiQEWmnbvsX7+JgODs1r1Icty5EBSK+3Ym2N84NSpXGWecdBf3ZyGaGrVeWNk1YEwV2vTO6CmoRkf781GkI8n/MjgsiuggX1T3dAEZ9ZhwQR5vg1idhRRe368LweHC6tpQtoxfJgXbmK6EaFyHgalcnYF1p/Q/NAF48KRwPtulOasmmJy0WKiQXZklGOYtxt83TWzt4FtqqRJHunnjsviA1lrMf8M2JBSgrVHCpW2igryxo1jQzA+mtYCnxH+ziqrx8oDOfBwc8LcUZGIIcFKm7NKa5FZ3oBAL1c4cRzouqn6NVGrlFAA
XTsqQmMuslMBtXweTVNnan6TLMpmRRs57sU1jZgS76/WNnYeI525Jn6Sjv03x7Uyx8+/LsW8aHfMDffSIjpBxn/W6lysuiYSfhSuk1dlYdsCMqdU8AwgtKwxVgHevSKcAlyWlGl03B4tHIAlRyrw0rEqLIjxVH3+WW4dHhsbgJ+MCmytvw7pK0GvteqOuQYbOnN5OtNEIHOp/VFDuCAgxCwzb0uPV+KmaG/8YHsR9t4U00rMzx+toHXihHsTZalYG9TMMP81WQ2YsSYL+26KU/F3bijAU8kB9K01zdHbcrn20DXWluJG3KU0Vmi3GqszUqub8VVBgxJ0V0e4U9Dre/9sYmjg1ZM1eDmlkj6+hRaSB56YEIArQ90oyLtxL/qz5WQIQ+g3SJQ/3FkELxcn3EaijpXXJ61yHghzd0JhQ9etJ8JcmbUt2EMNOY5mpo4pwW7YVtSoGK93NdEVm4vrlSn4HjWWMFZ/kODjigdH+KoQ124Rtxg3Tx8ow9tp1fjnjDAcvzUePx3FdFsKsTa/wZGqe5xTzfXEvjI8e6RUraCv+l6S2jTYHUT6NHb044dwnmN1bi3eT6/D8itC8Xm+mQTqjBFkFt2s2lTUgA8zapQv3R4rMmvxEzKlHPG9jMxwucPHyaEpfBX9HtklMD/KA0suG0ZO1N6btUG+awWIKSzYRB9q0SaHKRjS1Sc8XRQ2tiD5k0wcuTUWgTSNdVdnQ2Ed/t+OUhy8laYvrzsU802Yhb/YVYIXj8v0phHFC4d3u21EUGC24vaNeey6s+uYIZw7+NDUl4ksfcmTInwygoy1IK3Ggl/vLsFHV3f0V69fl4vnpgTDRKKV8yd1f1kEbKa5CSHurvjZzhJkk9nUQmAHFWtsJd8d5Ov4WJNnRgwZe3RA9xMnp4vSBiv8qY0/vFomsaQ9WvmyQCL4/VPIuG04vF1dGN+Ojc4lc8mvooj6l2MDvi6TF3N2vEwJ9kCin9YnFDviQDvJalt1dwjf
NvQlBmstdlz7eTZ23BCrRZAeWjjul67MwoGb+54VzKxphraaU8PLJyuwPKMOy2aHI0hWrfdXQ+nE1c/kxyub8dzhCuy8IVqZiLJ+VjKpaLIi6aMM5C1MUHsQO9T/XDLXnpIGPLq7VL3X0u1wmYGbHepBaeWKx8b7I46fusodYq5vH/qiVdm8OZmMtO8WB3PxOtNsoVYqxqprwh1E2zNEOLeHTG88sa8U6/Lr8PncaMdG3H5Ar2g/icxqtyH5v1l4bFwg7or3VUwkZcsxFfIK8tWZ2vvLb8wsrLXIKgobvGk6yO5UqYecjWFh7aysoDi73b130qd1ZSXBNwG9YzqPR6dqdoFGB/JHUrZN+csrAQ0S11cu5xbaJJujzvxwGBEDBumTyauy1fS6m1GzZD7JMatz/p9JDjrt/tB6FHh8b6l6N7puXrQyG7u8GlDOmJwVYsfyrBqmlYULwHWRHrg9zkcJ+fZj1Bnykv5EdTNuXl+ABD8XJHi7YXtxPYJcDFhOU9HPtRvX5lwyl6Ip1QaZ+2FDVInauydNk2kdcqEwl0hpOaYsPshL+RB6uwrlXEfCiR0S7HtmW10GCyKFS+sa1OsROWagg5kzAJC+E//qlctD1Y8qym6Hpw6Uq1USt8R4dWGKvqCPhfhmTzKfNXm1WHdtFH20jhpMmFoE+R2b8tQ5ig8k+av4l1OqEObphA9mR6i29lS8elXA0NgCfE4tKYvJR/qb1MSLUGfnd7kKDuYaYPnUPUTjS+VJZqpA9Z2RWqM04mvfOPHR3tqegbe3p+PHy75W5weqNXunCRkACbILWRxQEWL6oPQG0TDbTpVi0Stb8cm+XFW2bFe59/XteHNLqiNVz8itMGPBSxvRYJXlPprUlNUTv3h/rwpbHUesnQ70tvQFeRm7J7Mcnx7KV4TVXzQ0W/Gjd/Zgd2YlR6nrg9IH8sJV+vFfm1JRUtvEuG6m1h2hCxgZ5uGEgnrHjzLwz0FqrQmBJu36NCHP
SBDh9eylQWrxwdwvclDSYFXaRhtsimV2wvuZVahotOHLeVG4O8FHhQ3zIlBotuKDTNn02jNtCX3KekMPFyNujfXBI6MC1fS+WFrdMlY7nBPmOl3klpvVRsC7p8fivpkJqDDL+4QzGQINwsi//GCv+i66sy/IQtfpiYE4WVSH68bKYZZGTIzyQ35lE64b33G2qztEB3oi1F87XWlnahlW7M5SqzEmxwWoMKhg8z4/lt/juSE9wd3NSVvh0V7KtcOznx5DlWwJpxWRSI3s4SLS+fTIJ8zTRc0EC8QqyWN+Me3eKZ0NnpkYiOsjvHH157l4+lA5Fh8sUeEP/L70aA1+kOSnGEKH/ADhD5J8sY4m5dnQVm9wWvy7pxY7vp83sFCPP7nyCCZG++Oy4YHwoRnx2uYMbDxVgrWUyGuPFaGZkvaZ1cfU2rXhw7yx6WQJlu/JwYGcCkyKC8TfvjyFQ9kV+CPTRPqZ8Nf1KTBRAo2J8FNb/tceK0BqUS18TC54YsVBZFPbrGO+MxOHUdPKATpGvLLlJL4/PUH9cqVMAb+7KxO3T4yGiYzyActafbAANY0WdU7hh3uz8f83pSOKJkOwjwfe3JmJO5Kj8Mc1x5BZXsc6eCCrQhMSKYU1WHkwH2nFNbiMdZX2frA7G2/uSEctJa9s8pT8Xt6cjqtHhGJLegneY37/2ZuryLnM3KSWPq05mMf6BivpqvNEFZ8vq27E+hNFuHViFOtqRFZ5PR5bcQB1NIueZX1kfaWsvP/iaCGW7cpSaxrHRfphS2opwvxcseZwIVYfymM/BuH3/z2M4uoGvL4jjVrAgBC2bdmuDEyICoCvyRmrDxfg/d052JtRhvhhXrQ2srCSYzQnif0o/1rp1o60aqv6zexJw9xRTGtkLX2u+0jgA+F/ill5VbgnXGnX1jVTE6k+0co/VdOMaSHuGMOx0YsSrX6oqgmZtVbcTLN0IOrQCsfkzHmpuQI9XbHsf6bh0eX7cPdrO9Fo
aaGP4opKEtXiW8apBaXyG1Z/unU8XvoqTU2K7M8qVxsHP/g6S8khX3cjTpbU4O+LJmFGYghCvE14YHai+lV9IbwRYb74y9pjiCBj7MuvwqyEENw/I5Yk0NbJNruTIqpXtmihoKoeNvbYin3ZOEkGGR3li6dIfHuyKshUnuqA0WW7chxPaydmTR8ehFmXhCA5NoA5S+52EqafOlvxHTKU+Gd/WHWExBiMJbclY86IEKzan4tFU+KVNjmQV4mJ4f54b082nrx+DMaQCf5CgREd5IltGaU4xPvttfHHTBcf4oPLYoPw8YFcxhgRE+iBo2zj3DGh+OW8kepgHlnom1VWh99cN5JMnKY9rGDEd6fEYnNaqTrJ149jcefUWMQEeOGe6XEUZB7UYFYl3D4gU7nRXPrtDWPw/ZnD8QmZXTxpWdTbbOlsahnUsQ358ju/xOHyBoyjSTjQuD/BF0+OD8JT44ep8MS4APxslC9eoY8li751yNHpr6TUYG6UrIV1RA4wzkvmqqxvUivAt//mWmoN4H0SrKhxPw83SiijWjDqTY3j6+6q/ClnJztCfd0pQbPVT4VKbxnpVI4KD0CErE2TGRTHrJEcMZ0U6oPRYT7Y9cQ8PmsggTghKdyX0txTLWSVdAKZQXpwZqLariJB8pKsNtEfG08NmBwdgC2/ugajwrxxILcSu7LKWJ82P0SbXhb53RFurrKz2qDOYxc/5ovjRdQIbkxnV+fZT0sMwts7qFGEmZUQNiCWplgoNXAOtZD0w4hgT3zw0CxMkINLmae4DeKrppbWobLJQrM2GO+xPzQY4OziTC3tCn8PEzWlTVkDcvbMJ/vlQBsdwvxGBPmYEMH+XEfNNoHWgzbtpLeCKZQmB1ZR+8bQBJbJiQAvF9wxOQbbqWV/+M4uNMtmQDUWOmxqIXg+TUHJTfytifIDhgMMpa0o1GTsJAi9LIz3Q6DJqEzGZWnVKly1NhcRZPaFsbJnr/MIDQzOS+ban1ON
jSeK1UbHS2l+BHOwRcKroeIfcVJFQsrYSaist+J5mn03T4xQzKWdUsQUrYNr4GDbGG+lxvLHZ4fz4OZsxOE8bX+S2hOlJewAWd3emgVBS0Yx8/hIHyWlJY9D+RX4D01EmRCYxLrW0ySRrSbyoHqUbahualabLSWvdlVSq+ml3Ghqg79vSkUV27E7vRy/XnEYV4wYRoZzp5aQ9XWE48HhwV74OrOE2hzIpuapqZf7Wu33ZpYpU3oeTck7J7EvWI+TRdqJxXaH1JZayb/Uohp8vL8A88ZFqJ+obaB1wOowaNt4Fk2Lw5/WHMWckSFMLZtOQZPVAotMDDGIiTiOmvuFL0+w/5ux5WQpPmOfvH3/DIwI9cMJ+qvtO1V8rDBaE7LwVep7oKwZEwIH77fV2iA/NUUBPTsCD4/wxxf59So8MtIf784JV5NqHSo6gDgvfa4gLzcs352F9LJGBFLa3TA+Ctvpb8nWbPlxgWL6FGJby+EwQjJyKKeJ0nRvZoWStE1WO0qYRjb5jQz1VkzgRsm98Xgh7pgSraT8u/RhxN9pJMfU0hcRaSrHPQtk9mhXWpkSPeZGK0ZSM+3OKFf5ynaU702PR3a5WUl9MeOSY/yw5lARImmqldU2wo++iDCmHPMtWzPExxvmZVLn/olvYCBx+lPrygSBJ02ve6bFYh19qO1pJbhhQgTL88V79O9mJwxDeqlZEbfwhjBbTJAHxrC9ciycnPt+Kc0/Zkkma8aK/fkwsa1j2Qfltc30B1vUhk8Ptr2Rvo43tVUOGVIYYwbzlq0jOeW11IreSjuV1TWrbTvyfJjs3GYbZgyn78T8fWkprKIvFUmNVsp0wi73zIhHSn6t8h+vGRWiNONKmqKJtAxkW49obulXDXa1kuHvJ6rwUJIfnj1Shl+PCeowyTAYkLpLkFlF+a0w+QUdCfJdhLd+f0Dh8LnO6drC/kI0kxoTR6vVey6Jko5QylbTCnJXk9p6evnTFu+4w6Bd6dcy
4PJyUa7lhAL5lEf1Y+HUuw31jK7YRU86clD3+I0PqNKkrqozJU9H3rySrKQMuRKG0mK1/AWSh3C5mDEqTWu7tGvVFD05M5aytfZLKkkrfcIaOqqovarQ4lValZN8Eu3qqEPaqAjLbmQfaKWq3FVaA14j895yaYzaWyYQrabqyc/WPnM8I3+1Z+VD7ki7HC9SpZ6EKo/1m/DfLGy8PhLz1+Vi+4K2LSkXFM7le67ThXS4vEMQApWgJCCpSB21pq7lnhC4nkZPr6fR3tTr6WRDZftrdZ/phZlEeqk4ycgBLZ1ella+9hy/SznsNT0v7dcntTqIdJRrWSOpl6F91/JT6R1BTBW5p31v3y4xwSSftrSSpyqX37U0GsMyuhVa/tpnW/21Z9rXUQ9a2ZKvozz1PPPnv1PFddhJ87TD+fmOenXos/ZlSP7MS6+bMFWHl/+Mk6O3ZXP5jpIGjFHnhPChCxjnpeYawjcHXWuLAiOrkAlV9FlDJ7I7NhQgQraj+Lmq90zCiBcczmfNNYRvDrrWE802UIwlEBaSINPxq/PqMD5AVmZc2JpriLmGcE4xwtcVxY0W9cs4oiEvZAyZhUM4p6hubsGh8ibMCju/Fi4PKPTJnCHmGsK5hJrVtBs7TMZccBjyuYbwTUDNSl4kVDfEXEMYwiBhiLmGMIRBAfB/iMqObdc1peAAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="ID30x30">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAAgACADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAACAkAAgMK/8QAJhAAAgMBAAEEAgIDAQAAAAAAAwQCBQYBBwgREhQAExYhFSMxQv/EABwBAAICAgMAAAAAAAAAAAAAAAgJAAIBAwQGB//EACkRAAICAwABBAICAQUAAAAAAAMEAgUBBgcRABITFAghFTEWIiMkMkH/2gAMAwEAAhEDEQA/AO7DyF5CDjRK16Cq9tqbZd5msrGXw1lclXVguGtdNprU3JjpctSjkOVhYyGUpTFXRRXYcYhCPlPTung0MKdZWprXW5XS1i3UVDdiGoqq+qqQ4Nc7dt10fExa/p1AKY52lqQRjFOZaurlmnmhjj3LUtSJsRDtNHKhRoFVA66FUjrjLjpPjRpaVEeYysryylGeFE4zGOAxlZZKFcMpZXft/VUrx9gP8i3W6YgSQyFyt9PxHiFv7l+wdCGoRsNtarR5LoIP394Fg3efaABbkQx/Fc9C/M1PFkwvjaejdGaGWQzG0zZScR56p/qlkotaBSV1n0G6UjiWVx2WzbEu0fP/AC1llIwBH0XWs8KPlQRP4jV9XDKGJDhe1Ud/2Y36j7J2pLBpTWkDSzjBZKVVYQQ/2ApTZkSXq+H9VS0rAC/NHt8KyQsRinsL6flnDN97KP6xX3+TQrtvTLyjzgZWNFdsTBL3baVZhI0OX53+Zic7NZbG1dD5y2Q0RhnvOyT7Xzp3OZxwIOyYt62q6FQLSj4BO11zYWiLS8uuJtjkYeK7Pwo+FClz
T6ztAYQzImNeqo6Fs4MeM5nOr+k05rNkXGfJIqWlaKJcf7ATBliEvTD/AB/vxbIDST6YafUVIUjW1SF4NkiwjZC6aq0ectQ8iK7y12KJCVdoMYp/MTCTi6zqpg8aNzLpgd8Wcr7JEFHuNICvYu6RewBbVzVdahyem2nVbkGIB2HTthDApaa5EIM/eFlB9ZSwTOCIi7ZqhNdKBlVglhR2BGRoPlVIk0JpMmBvU9wgTMiVl5WklCDyM5kj7SBZXKZY4yZXX6qNzZcV0UAnnA+73ehy5ywl3k1sR4kOpSK0I+9/uKtttHdBfvxh2P7zhVGT5CFGPVZ/mX0W1+ltAwMkGx0bo+0agcw5SjNTnvEmEdfU1oUs/uCV1v7+zbLZRHLH2mV0xF8hBCGS+4XrCfz1EiCjIWr6vUXY4SjjMTbLvwmLI1pPH9SOhra9TVKSljPxCKecPEyZzgCfxa3oqvU/J6no/wD0q7qxmrmOnNMh8PuKbFftnPvzaw/liFmqKlLL/pQUm3p6m5r+E7L63HG1wfERew4zP8M+i2hE9PyywQrPPOh0PP8A5iTl8jvO+0jt0w0BZ+fJltd6DQ0t7WRLmX04vOLL+wJ8w9Cf3XV04nu/iFGAtm1mx2X2Rjj2A2fQ5JHJYwj/AFAtnrVg/XtZhjHzSXAUnunD3esPVThrD62g4BchGcNt9FrSCHDspNYXyywpcQv4c9uSmtS7ZK8obHsOS+qRlQxvYLECc4/5l86tMqbPhdUxW+d9D2ndyhFCUpO867Wwlej2UUc49xFKDoNdsWt2uRYz9IriJz+F2hlxs4Vs6fzVOSmhAOz6zUa/Cc5eMA2fQhMV8qqefPiJrHWmqy0UxLOPngFgY/JBSh6Af8Wp6Kz1Pyep6P8A9K2HfGvlxMLkE3t9rS7qQSD78k8H4rHZsqXZY+3yCG+29xW1Nb0nx45BFtoHzCDsus0/DPnlkJXUAsrFE90LoFB0WYCjliaPN+Njt2ktgNDx
iYAbL0K8qqWqyX24eHXOOL/IBfM8if3TZ1ZlvJiLGa+s63ZavEkJY9rG07zJIJ62Ev6ISq1mvdfcxDOcryZAEvsITEfTC/IHj4GzCo8k4On1NQF4FTblSDZIsI2YeBtc7o6lj2BeZa6HAY7SrLMRPkIDaTKji4jcaB03mK++gRsa94VFuNGvYr0l4avBbV7Ndbgwvc6ttNKz7V9h0/YAwEK4pzECT3BXdr20n1gnwI+p7YXXSMKsrzsaOwIqV+vgyRJkTSRMlRt6d8XktZeVs5Tmk8OM4+0hV2QnWNMfpfG59KShHjsczO3xLJCEkT+F1S/lTDtz/v8A2UwRW1Jt6Nc0+xJ1K5q3OJxnISzDQhe8Vh9E/C5IliyzHUeh8+bIQky/4DSq9l546TznGSUAA3evdC19Y5JRJmvvad76MJSEo06EWMxLXWO8MQVELN1rOzBjGMYf5I+XRdnBH/yNkQiFnrNkUccZj9mueX+eWMTMIM5/u2G9KaonwMcy+02rYiQmKe5qlvF2EUJ/5JcpytrzcX4Az50n0KqtRE7HnANtKiJL8vzr8L0w2KzWNP37oLoiQIGfRKZTj3OUS+cewt9Xzuti6Hsq65I5L/G01XXBsIRiu64mI0/ONn7ucipQ5u9b1oE4ZjOOsPG3jaGIZ/7QrmYoVmsVRCRziGWnnGprSzki4TzhHPpg+AwC+MA4445G51FzFKN3dxSBXL/XrgfXq6OjrFvcFJmKQHZhp6cEycDwh2WTtOtMskZ5zPma2hLPvPPwvtwvoIQ2DYI161Ut9WqX+rT67rtOr5W1/UdfXzNejo15liDBWG22G7Btpook7XthdjKsuutmtpK6TOa2syyVwvzOF+Z60s3jeC2V3ZFxElhYljDJfYIIRAWAEMP/2Q==</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportServerUrl>http://ord-devsql01/ReportServer</rd:ReportServerUrl>
  <rd:ReportID>5ff3f37b-a144-487c-9b67-f176ddb97e2e</rd:ReportID>
</Report>