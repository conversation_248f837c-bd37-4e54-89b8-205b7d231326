/**
 * Provider Group Label Constants
 * 
 * Centralized mapping for provider group labels to their user-friendly display names.
 * This ensures consistency across the application and makes it easy to maintain
 * label mappings in one place.
 */

export const PROVIDER_GROUP_LABELS = {
  'AssociatesProvider': 'My Associated Providers',
  'AppointmentProvider': 'Providers With Appointments'
} as const;

/**
 * Type for provider group keys to ensure type safety
 */
export type ProviderGroupKey = keyof typeof PROVIDER_GROUP_LABELS;

/**
 * Helper function to get the display label for a provider group
 * @param groupKey The provider group key from the API
 * @returns The user-friendly display label, or the original key if no mapping exists
 */
export function getProviderGroupDisplayLabel(groupKey: string): string {
  return PROVIDER_GROUP_LABELS[groupKey as ProviderGroupKey] || groupKey;
}
