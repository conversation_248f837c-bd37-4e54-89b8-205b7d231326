.custom_input {
    width: auto;
    font-weight: 500;
    font-size: 14px;
    text-align: left;
    color: #004b75;
    margin-bottom: 5px;
    line-height: 10px;
    height: 15px;
    text-align: left;
    margin-top: 5px;
    padding-right: 5px;
}

.CMbtnDisabled {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    height: 30px;
    border: none;
    color: #FFFFFF;
    cursor: pointer;
    text-decoration: none;
    float: left;
    font-size: 14px;
    padding: 0 10px;
    text-align: center;
    width: 100px;
}

.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    height: 30px;
    border: none;
    color: #FFFFFF;
    cursor: pointer;
    text-decoration: none;
    float: left;
    font-size: 14px;
    padding: 0 10px;
    text-align: center;
    width: 100px;
}

.CMbtn:hover {
    background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}

.PALcontent {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #D6D7DA;
    float: left;
    padding: 15px 20px;
    border-radius: 5px;
}

.PALcontent h1 {
    font-size: 16px;
    font-weight: bold;
}

.div-container {
    float: right;
    width: 100%;
}

.btnContainer {
    float: initial;
    display: flex;
    justify-content: flex-end;
}

.btnContainer button{
    margin-left: 10px;
}

.dropdownMenu{
    width: 220px;
    font-size: smaller;
}

::ng-deep mat-dialog-container.mat-dialog-container {
    border: 12px solid rgba(0,0,0,0.85);
    margin: 0px;
    border-radius: 15px;
}

.btn-secondary {
    color: #fff;
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
  }