import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { JsonSchemaService } from '../../../../services/json-schema.service';

@Component({
  selector: 'app-object-view',
  templateUrl: './object-view.component.html',
  styleUrls: ['./object-view.component.scss']
})
export class ObjectViewComponent implements OnChanges {
  @Input() jsonData: Record<string, unknown> | null = null;
  @Output() jsonDataChange = new EventEmitter<Record<string, unknown>>();

  rootKeys: string[] = [];
  expandedPanels: { [key: string]: boolean } = {};

  constructor(public jsonSchemaService: JsonSchemaService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['jsonData']) {
      console.log('ObjectViewComponent.ngOnChanges: jsonData=', this.jsonData);
      this.updateRootKeys();
    }
  }

  /**
   * Update the root keys from the JSON data
   */
  private updateRootKeys(): void {
    console.log('ObjectViewComponent.updateRootKeys: jsonData=', this.jsonData);

    if (this.jsonData && !this.jsonSchemaService.isEmptyObject(this.jsonData)) {
      this.rootKeys = Object.keys(this.jsonData);
      console.log('ObjectViewComponent.updateRootKeys: rootKeys=', this.rootKeys);

      // Set the first panel to be expanded by default
      if (this.rootKeys.length > 0) {
        this.expandedPanels[this.rootKeys[0]] = true;
      }
    } else {
      this.rootKeys = [];
    }
  }

  /**
   * Handle changes to a node
   * @param event The change event
   */
  onNodeChange(event: { path: string, value: any }): void {
    if (!this.jsonData) return;

    // Update the JSON data at the specified path
    this.updateJsonAtPath(this.jsonData, event.path, event.value);

    // Emit the updated JSON data
    this.jsonDataChange.emit(this.jsonData);
  }

  /**
   * Update a value at a specific path in the JSON data
   * @param obj The object to update
   * @param path The path to the property
   * @param value The new value
   */
  private updateJsonAtPath(obj: any, path: string, value: any): void {
    const parts = path.split('.');
    const key = parts[0];

    if (parts.length === 1) {
      // Direct property
      obj[key] = value;
    } else {
      // Nested property
      const remainingPath = parts.slice(1).join('.');
      if (!obj[key]) {
        // Create the object if it doesn't exist
        obj[key] = {};
      }
      this.updateJsonAtPath(obj[key], remainingPath, value);
    }
  }
}
