
<div class="reportNavWrapper">
  <div class="itemsContainer">
    <ng-container *ngFor="let section of menuSections">
      <div class="section-container">
        <div class="section-header">
          <h4 class="section-title">{{section.categoryNm}}</h4>
        </div>
        <div class="section-content">
          <a *ngFor="let navItem of section.reportsForSection"
             class="report-item"
             [ngClass]="{activeReport: navItem.reportName === currentReport}"
             (click)="NavSelect(navItem)"
             title="{{navItem.reportName}}">
            <div class="reportText">{{navItem.reportName}}</div>
            <div class="chevron-icon">></div>
          </a>
        </div>
      </div>
    </ng-container>
  </div>
</div>
