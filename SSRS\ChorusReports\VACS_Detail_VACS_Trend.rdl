﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.8.34330.188</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.8.34330.188</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-02-12T15:27:19.8845500Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="VACS_Index_Details">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@PATIENT_ID">
            <Value>=Parameters!PATIENT_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT  PATIENT_ID, VACS_INDEX, COLLECTION_DT
FROM [REPORT].[VACS_INDEX_DTL] V
WHERE PATIENT_ID = @PATIENT_ID
AND  V.CURRENT_FLG = 1
AND VIEW_FLG &lt;=10</CommandText>
      </Query>
      <Fields>
        <Field Name="PATIENT_ID">
          <DataField>PATIENT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VACS_INDEX">
          <DataField>VACS_INDEX</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="COLLECTION_DT">
          <DataField>COLLECTION_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.69725in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.4in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Chart Name="Sparkline2">
                          <ChartCategoryHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Group Name="ChartGroup2" />
                                <SortExpressions>
                                  <SortExpression>
                                    <Value>=Fields!COLLECTION_DT.Value</Value>
                                  </SortExpression>
                                </SortExpressions>
                                <Label />
                              </ChartMember>
                            </ChartMembers>
                          </ChartCategoryHierarchy>
                          <ChartSeriesHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Label>VACS INDEX</Label>
                              </ChartMember>
                            </ChartMembers>
                          </ChartSeriesHierarchy>
                          <ChartData>
                            <ChartSeriesCollection>
                              <ChartSeries Name="VACS_INDEX">
                                <ChartDataPoints>
                                  <ChartDataPoint>
                                    <ChartDataPointValues>
                                      <Y>=Fields!VACS_INDEX.Value</Y>
                                    </ChartDataPointValues>
                                    <ChartDataLabel>
                                      <Style />
                                      <UseValueAsLabel>true</UseValueAsLabel>
                                    </ChartDataLabel>
                                    <ToolTip>=Microsoft.VisualBasic.Strings.FormatDateTime(First(Fields!COLLECTION_DT.Value), Microsoft.VisualBasic.DateFormat.ShortDate) + "(" + Microsoft.VisualBasic.Strings.FormatNumber(First(Fields!VACS_INDEX.Value), 0) + ")"</ToolTip>
                                    <Style>
                                      <Color>#0b6c9f</Color>
                                    </Style>
                                    <ChartMarker>
                                      <Type>Circle</Type>
                                      <Size>=SWITCH(CDbl(Fields!VACS_INDEX.Value) &gt;= 70, "6pt",
        CDbl(Fields!VACS_INDEX.Value) &gt;= 50, "5pt",
		CDbl(Fields!VACS_INDEX.Value) &gt;= 20, "4pt",
		1=1, "2pt"
)</Size>
                                      <Style>
                                        <Border>
                                          <Color>#0b6c9f</Color>
                                          <Width>0.5pt</Width>
                                        </Border>
                                        <Color>=SWITCH(CDbl(Fields!VACS_INDEX.Value) &gt;= 70, "Red",
        CDbl(Fields!VACS_INDEX.Value) &gt;= 50, "Orange",
		CDbl(Fields!VACS_INDEX.Value) &gt;= 20, "Yellow",
		1=1, "Black"
)</Color>
                                      </Style>
                                    </ChartMarker>
                                    <DataElementOutput>Output</DataElementOutput>
                                  </ChartDataPoint>
                                </ChartDataPoints>
                                <Type>Line</Type>
                                <Subtype>Smooth</Subtype>
                                <Style />
                                <ChartEmptyPoints>
                                  <Style />
                                  <ChartMarker>
                                    <Style />
                                  </ChartMarker>
                                  <ChartDataLabel>
                                    <Style />
                                  </ChartDataLabel>
                                </ChartEmptyPoints>
                                <ValueAxisName>Primary</ValueAxisName>
                                <CategoryAxisName>Primary</CategoryAxisName>
                                <ChartSmartLabel>
                                  <CalloutLineColor>Black</CalloutLineColor>
                                  <MinMovingDistance>0pt</MinMovingDistance>
                                </ChartSmartLabel>
                              </ChartSeries>
                            </ChartSeriesCollection>
                          </ChartData>
                          <ChartAreas>
                            <ChartArea Name="Default">
                              <ChartCategoryAxes>
                                <ChartAxis Name="Primary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <Margin>False</Margin>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <Margin>False</Margin>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartCategoryAxes>
                              <ChartValueAxes>
                                <ChartAxis Name="Primary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>=Min(Fields!VACS_INDEX.Value) - 5</Minimum>
                                  <Maximum>=Max(Fields!VACS_INDEX.Value) + 5</Maximum>
                                  <IncludeZero>false</IncludeZero>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartValueAxes>
                              <Style>
                                <BackgroundColor>#00ffffff</BackgroundColor>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                            </ChartArea>
                          </ChartAreas>
                          <Palette>BrightPastel</Palette>
                          <ChartBorderSkin>
                            <Style>
                              <BackgroundColor>Gray</BackgroundColor>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <Color>White</Color>
                            </Style>
                          </ChartBorderSkin>
                          <ChartNoDataMessage Name="NoDataMessage">
                            <Caption>No Data Available</Caption>
                            <Style>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <TextAlign>General</TextAlign>
                              <VerticalAlign>Top</VerticalAlign>
                            </Style>
                          </ChartNoDataMessage>
                          <rd:DesignerMode>Sparkline</rd:DesignerMode>
                          <DataSetName>VACS_Index_Details</DataSetName>
                          <Filters>
                            <Filter>
                              <FilterExpression>=Microsoft.VisualBasic.Information.IsNothing(Fields!VACS_INDEX.Value)</FilterExpression>
                              <Operator>NotEqual</Operator>
                              <FilterValues>
                                <FilterValue>=TRUE</FilterValue>
                              </FilterValues>
                            </Filter>
                          </Filters>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundGradientType>None</BackgroundGradientType>
                          </Style>
                        </Chart>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details">
                    <GroupExpressions>
                      <GroupExpression>=Fields!PATIENT_ID.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!VACS_INDEX.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>VACS_Index_Details</DataSetName>
            <Height>0.4in</Height>
            <Width>1.69725in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.4in</Height>
        <Style />
      </Body>
      <Width>1.69725in</Width>
      <Page>
        <PageHeight>0.5in</PageHeight>
        <PageWidth>2in</PageWidth>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>88D180EC-184E-4F88-8794-1A4F0BC0748C</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="extractDt">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>extract Dt</Prompt>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>provider Cd</Prompt>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>location Cd</Prompt>
    </ReportParameter>
    <ReportParameter Name="PATIENT_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>1044604</Value>
        </Values>
      </DefaultValue>
      <Prompt>PATIENT ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>3</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>extractDt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>PATIENT_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportServerUrl>http://ord-devsql01/ReportServer</rd:ReportServerUrl>
  <rd:ReportID>616a6f18-c565-4581-971c-acfc121a2366</rd:ReportID>
</Report>