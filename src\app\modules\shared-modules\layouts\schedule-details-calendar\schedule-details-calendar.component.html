<div class="wrapper">
    <div class="title">Schedule Details</div>
    <div class="topBar">
        <div class="buttonWrapper"> 
            <button class="btn btn-light customBtn" [ngClass]="{'active': selectedDropModifier === 'Location'}" (click)="updateDropSections('Location')">Location</button>
            <button class="btn btn-light customBtn" [ngClass]="{'active': selectedDropModifier === 'Provider'}" (click)="updateDropSections('Provider')">Provider</button>
        </div>
        <div class="selectWrapper" *ngIf="isSiteDirector && selectedDropModifier === 'Location'; else dropDown">
            <input type="text" class="selector" #search matInput [formControl]="locationSearchControl" [matAutocomplete]="auto"
                (keyup)="searchLocation(search.value)" placeholder="Search by location name"/>

            <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onLocationSelected($event.option.value)" [displayWith]="displayFn">
                <mat-option class="options" *ngFor="let site of searchLocations" [value]="site">
                {{ site.name }}
                </mat-option>
            </mat-autocomplete>
        </div>
        <ng-template #dropDown>
            <div class="selectWrapper">
                <select class="selector" [(ngModel)]="selectedDropValue" (change)="optionSelected(selectedDropValue)" [compareWith]="compareOptions">
                    <ng-container class="selectDrop" *ngFor="let section of sections">
                        <optgroup class="labelSection" [label]="section.label" *ngIf="section.options.length > 0">
                            <option class="options" *ngFor="let option of section.options" [ngValue]="option">
                                {{ option.value }}
                            </option>
                        </optgroup>
                    </ng-container>
                </select>
            </div>
        </ng-template>
    </div>
    <hr class="divider">
    <div class="calendarWrapper">
        <div class="dayButtonWrapper"> 
            <button class="btn btn-light customBtn" [ngClass]="{'active': isSameDate(selectedDate, yesterdayDate)}" (click)="setDate('Yesterday', calendar)">Yesterday</button>
            <button class="btn btn-light customBtn" [ngClass]="{'active': isSameDate(selectedDate, currentDate)}" (click)="setDate('Today', calendar)">Today</button>
            <button class="btn btn-light customBtn" [ngClass]="{'active': isSameDate(selectedDate, tomorrowDate)}" (click)="setDate('Tomorrow', calendar)">Tomorrow</button>
        </div>
        <div>
            <mat-calendar #calendar class="calendar" [(selected)]="selectedDate" (click)="emitSelectedDate()"></mat-calendar>
        </div>
    </div>
</div>
