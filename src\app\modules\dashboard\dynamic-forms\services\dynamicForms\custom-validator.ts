import {AbstractControl, FormGroup, ValidationErrors, ValidatorFn, Validators} from '@angular/forms';
import { FormField, FormModel } from '../../../models/dynamic-form-model';
import { InvokeFunctionExpr } from '@angular/compiler';

export function customValidator(formTemplates: FormModel, field: FormField, form: FormGroup, prefix: string = ''): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        let results: any[] = [];
        let conditions: any[] = [];
        if (field.ValidationCondition || field.isRequired) {
            // Check if there are multiple validations
            if (field.ValidationCondition.includes("|")) {
                conditions = field.ValidationCondition.split("| ").map(cond => prefix + cond.trim());
            } else {
                conditions.push(prefix + field.ValidationCondition.trim());
            }
        }

            // Loop through each condition and evaluate
            conditions.forEach(condition => {
              const fieldValue = form.get(field.FieldName)?.value;
              
              // Check if the field has a value
              if (fieldValue != null && fieldValue !== "") {
                // Validation for date type fields
                if (field.FieldType == "date") {
                  const variables: { [key: string]: string } = {};
                  variables[field.FieldName] = fieldValue;
                  results.push(parseConditions(condition, variables));
                }
                // Validation for regex pattern matching
                else if (field.ValidationCondition.includes("RegEx")) {
                  results.push(CheckRegEx(fieldValue, field.ValidationCondition));
                }
                // Validation for dependency requirements
                // Example of how this field should be formated is: 
                // DependentObject=[dependent field name], [parent value that would make dependent field required], ..., [parent value that would make dependent field required]
                else if (field.ValidationCondition.includes("DependentObject")) {
                  updateDepenency(fieldValue, field, form, formTemplates, prefix);
                }
                // Validation for checkbox type fields
                else if (field.FieldType == "checkbox") {
                  if (fieldValue.includes(true) || !field.isRequired) {
                    results.push(null);
                  } else {
                    results.push("false");
                  }
                }
                // Validation for radio button type fields
                else if (field.FieldType == "radio") {
                  // For radio buttons, simply check if the field has a selected value (non-null)
                  if (fieldValue != null || !field.isRequired) {
                    results.push(null);
                  } else {
                    results.push("false");
                  }
                }
                // Handle custom conditions when no specific type is applied
                else if (field.ValidationCondition === "") {
                  results.push(null);
                }
                // Evaluate custom conditions for non-date, non-checkbox/radio fields
                else {
                  condition = getFormfieldValue(formTemplates, condition, form, prefix);
                  //condition = condition.replaceAll(field.FieldName, fieldValue);
                  if (eval(condition)) {
                    results.push(null);
                  } else {
                    results.push("false");
                  }
                }
              }
              // Handle case when the field has no value
              else {
                if (field.isRequired) {
                  results.push("false");
                } else {
                  results.push(null);
                }
              }
            });

            // Check results array and make sure none of the conditions threw false
            if (results.includes("false")){
                if (field.ValidationText != null && field.ValidationText != "") {
                    return {customValidator:{message: field.ValidationText}};
                }
                else if (field.ValidationCondition){
                    return {customValidator:{message: generateValidationMessage(field.ValidationCondition)}};
                }
                else{
                    return {customValidator:{message: "Required"}};
                }
            }
            return null;
    }
}

function getFormfieldValue(formTemplates: FormModel, condition: string, form: FormGroup, prefix: string = ''): any {

  // Define a regex to match conditions (e.g., DOB < 1999-01-01)
  //const conditionRegex = /(\w+(?:__\w+)*)\s*(<=|>=|==|<|>)\s*([^\sANDOR]+)/gi;
  const conditionRegex = /(\w+(?:__\w+)*)\s*(<=|>=|==|<|>)\s*([^\s]+)/gi;

  // Split the expression by logical operators (AND/OR), keeping them as tokens
  const tokens = condition.split(/(\s+AND\s+|\s+OR\s+)/i).map(token => token.trim());

  // Array to hold parsed conditions and logical operators
  const parsedConditions: Array<Condition | string> = [];

  // Iterate through tokens and parse conditions or operators
  tokens.forEach(token => {
    if (token.toUpperCase() === 'AND' || token.toUpperCase() === 'OR') {
      // Push logical operators directly
      parsedConditions.push(token.toUpperCase());
    } else {
      // Match and extract components of the condition
      const match = conditionRegex.exec(token);
      if (match) {
        const [_, component, operator, value] = match;
        parsedConditions.push({ component, operator, value });
      }
    }
  });

  parsedConditions.forEach((parsedConditions: any) => {
    let part1Value = form.get(parsedConditions.component)?.value ?? "''";
    let part2Value = form.get(parsedConditions.value)?.value;
    

    if (part2Value == undefined)
    {
      part2Value = parsedConditions.value;
    }


    let conditionBefore = condition;
    condition = condition.replaceAll(parsedConditions.component, part1Value);
    let conditionAfter = condition;
    if (conditionBefore == conditionAfter){
      if (!isNaN(part1Value)) {
        condition = condition.replaceAll(parsedConditions.component, part1Value);
      }
      else 
      {
      condition = condition.replaceAll(parsedConditions.component, "'"+ part1Value + "'");
      }
    }
    conditionBefore= condition;
    condition = condition.replaceAll(parsedConditions.value, part2Value);
    conditionAfter = condition;
    if (conditionBefore == conditionAfter){
      if (!isNaN(part2Value)) {
        condition = condition.replaceAll(parsedConditions.value, part2Value);
      }
      else 
      {
      condition = condition.replaceAll(parsedConditions.value, "'"+ part2Value + "'");
      }
    }
  });

  return condition;
}

// #region Condition Message Generation
function generateValidationMessage(condition: string): string {
    // Regular expressions to identify different condition patterns
    const regexPattern = /RegEx=\/(.+)\//;
    const comparisonPattern = /^(.+)\s*([<>]=?|==?|!=)\s*(.+)$/;
    
    // Check if the condition is a regex pattern
    const regexMatch = condition.match(regexPattern);
    if (regexMatch) {
      return convertRegexToMessage(regexMatch[1]);
    }
  
    // Check if the condition is a comparison (e.g., Dosage > 0)
    const comparisonMatch = condition.match(comparisonPattern);
    if (comparisonMatch) {
      return convertComparisonToMessage(comparisonMatch[1].trim(), comparisonMatch[2], comparisonMatch[3].trim());
    }
  
    // If the condition type is unknown or unsupported, return the original condition
    return `Condition: ${condition}`;
  }
  
  // Converts a regex pattern to a human-readable message
  function convertRegexToMessage(pattern: string): string {
    // Handle common regex patterns with a switch-case or specific conditions
    switch (pattern) {
      case '^\\d{4}$':
        return 'Must be a 4-digit number.';
      case '^\\d{5}$':
            return 'Must be a 5-digit number.';
      case '^\\d{5}-\\d{3}-\\d{2}$':
        return 'Must follow the format: 5 digits, a dash, 3 digits, a dash, 2 digits.';
      default:
        return `Must match the pattern: /${pattern}/.`;
    }
  }
  
  // Converts a comparison condition to a human-readable message
  function convertComparisonToMessage(field: string, operator: string, value: string): string {
    const operatorsMap: { [key: string]: string } = {
      '>': 'greater than',
      '>=': 'greater than or equal to',
      '<': 'less than',
      '<=': 'less than or equal to',
      '==': 'equal to',
      '!=': 'not equal to',
    };
  
    const operatorMessage = operatorsMap[operator] || operator;
    return `Must be ${operatorMessage} ${value}.`;
  }
  
  // Capitalizes the first letter of a string
  function capitalizeFirstLetter(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  //#endregion

export function CheckRegEx(selectedValue: string, regEx: string){ 
    const pattern = new RegExp(regEx.split("=")[1].replaceAll('/', ''));
    const isValid = pattern.test(selectedValue);
    return isValid ? null : "false";
}

export function updateDepenency(selectedValue: string, field: FormField, form: FormGroup, formTemplates: FormModel, prefix: string){
  const dependentFieldName = field.ValidationCondition.split("=")[1].split(",")[0];
  var isRequired = false;
  // loop through each value (seperated by comma) and check if the selected value is equal to one of the values in the validation condition
  field.ValidationCondition.split(",").forEach(value => {
    if (selectedValue == value.trim())
    {
      isRequired = true;
    }
  });
  // find the child field and set the is required field
  var dependentField = formTemplates.Fields.find(field => field.FieldName == prefix + dependentFieldName)
  if (dependentField)
  {
    dependentField.isRequired = isRequired;
    form.get(prefix + dependentFieldName)?.updateValueAndValidity();
  }
}

interface Condition {
    component: string;
    operator: string;
    value: string;
  }
  
  export function parseConditions(expression: string, variables: { [key: string]: any }, prefix: string = ''): string | null {
    // Define a regex to match conditions (e.g., DOB < 1999-01-01)
    const conditionRegex = /(\w+(?:__\w+)*)\s*(<=|>=|==|=|<|>)\s*([^\sANDOR]+)/gi;
    // Split the expression by logical operators (AND/OR), keeping them as tokens
    const tokens = expression.split(/(\s+AND\s+|\s+OR\s+)/i).map(token => token.trim());
  
    // Array to hold parsed conditions and logical operators
    const parsedConditions: Array<Condition | string> = [];
  
    // Iterate through tokens and parse conditions or operators
    tokens.forEach(token => {
      if (token.toUpperCase() === 'AND' || token.toUpperCase() === 'OR') {
        // Push logical operators directly
        parsedConditions.push(token.toUpperCase());
      } else {
        // Match and extract components of the condition
        const match = conditionRegex.exec(token);
        if (match) {
          const [_, component, operator, value] = match;
          parsedConditions.push({ component, operator, value });
        }
      }
    });
  
    
  
    // Evaluate the parsed conditions with logical operators
    let result = evaluateCondition(parsedConditions[0] as Condition, variables);
  
    for (let i = 1; i < parsedConditions.length; i += 2) {
      const operator = parsedConditions[i] as string;
      const nextCondition = parsedConditions[i + 1] as Condition;
      const nextResult = evaluateCondition(nextCondition, variables);
  
      if (operator === 'AND') {
        result = result && nextResult;
      } else if (operator === 'OR') {
        result = result || nextResult;
      }
    }
  
    if (result) {
      return null;
    } 

    return "false";
  }

  // Function to evaluate a single condition
  function evaluateCondition(condition: Condition, variables: { [key: string]: any }): boolean {
    const { component, operator, value } = condition;
    const variableValue = variables[component];

    // Ensure the variable exists
    if (variableValue === undefined) {
      console.warn(`Variable ${component} not provided.`);
      return false;
    }

    // Convert values to numbers or dates if necessary for comparison
    const parsedValue = !isNaN(Date.parse(value)) ? new Date(value).getTime() : parseFloat(value);
    const parsedVariableValue = !isNaN(Date.parse(variableValue)) ? new Date(variableValue).getTime() : parseFloat(variableValue);

    // Evaluate based on the operator
    switch (operator) {
      case '=':
        return parsedVariableValue === parsedValue;
      case '<':
        return parsedVariableValue < parsedValue;
      case '>':
        return parsedVariableValue > parsedValue;
      case '<=':
        return parsedVariableValue <= parsedValue;
      case '>=':
        return parsedVariableValue >= parsedValue;
      default:
        return false;
    }
  }
  