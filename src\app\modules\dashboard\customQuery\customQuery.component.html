<!--A Placeholder for our Report Panels to pass selected parameters to the Bold Report Viewer-->
<div class="customQueryHeader">
  <div class="header-left">
    <h2 class="section-title">Reports</h2>
  </div>
  <div class="header-center">
    <h2 class="customQueryTitle">Custom Query for Patients</h2>
  </div>
  <div class="header-right">
    <div [hidden]="!hidePanel" style="display: float; float: right; ">
      <div style="display: inline-block">
        <div style="display: inline-block"><button class="btn btn-primary btn-sm" (click)="showPanel()">Edit Criteria</button></div>
      </div>
    </div>
  </div>
</div>
<div class="contentWrapper">
  <!-- Left Sidebar: Report Selection Panel -->
  <div class="left-sidebar">
    <div class="sidebar-panel reports-panel" [class.collapsed]="reportNavCollapsed">
      <div class="panel-inner">
        <div class="panel-header"
             (click)="toggleReportNav()"
             [attr.aria-label]="reportNavCollapsed ? 'Show reports' : 'Hide reports'"
             title="Toggle Reports">
          <h3>{{ getSelectReportTitle() }}</h3>
          <button
            type="button"
            mat-icon-button
            class="panel-toggle-btn"
            (click)="$event.stopPropagation(); toggleReportNav()"
            [attr.aria-label]="reportNavCollapsed ? 'Show reports' : 'Hide reports'"
            title="Toggle Reports">
            <mat-icon>{{ reportNavCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
          </button>
        </div>
        <div class="panel-content" [class.hidden]="reportNavCollapsed">
          <epividian-reportNav
            (currentReportChanged)="onCurrentReportChanged($event)"
            (reportSelected)="onReportSelected()">
          </epividian-reportNav>
        </div>
      </div>
    </div>
  </div>

  <!-- Right: Custom Query Panel and Results -->
  <div class="main-content-area">
    <div class="templateContainer" [hidden]="hidePanel" >
      <ng-template  #panelContainerRef></ng-template>
    </div>

    <!--This will need to moved into its own component for the epiavidian report Viwer -->
    <div [hidden]="!hidePanel" class="EpividianDataTable">
    <div class="ReportViewer">
      <div style="display: inline-table">
        <div style="display: inline-block;padding-right: 20px; vertical-align: top;" [innerHTML]="criteriaSummary.column1"></div>
        <div style="display: inline-block;padding-right: 20px; vertical-align: top;" [innerHTML]="criteriaSummary.column2"></div>
        <div style="display: inline-block;">
          <div style="vertical-align: top; overflow-y: auto; height: 100%; width: 400px;">
            <!-- Summary Section for Regimen Builder -->
              <div *ngFor="let rs of regimenSummary; index as rsIndex">
                <div *ngIf="rsIndex==0">Include patients</div>
                <div><div *ngIf="rsIndex>0" style="text-decoration-line: underline; display: inline-block;">{{panelService.getTextForChain(rs.chainMethod)}}</div>
                  &nbsp;Who {{panelService.getTextForRegOptions(rs)}}</div>
                <div *ngFor="let regMedList of rs.regimenList">
                  <div>- An {{panelService.getTextForMedGroupId(regMedList.group.code)}} {{panelService.getTextForMedGroupOptions(regMedList.selectedOption)}}
                    <div *ngFor="let medList of regMedList.medicationName; index as mnIndex" style="color: blue; display: inline-block;">
                      <div style="display: inline-block;" *ngIf="mnIndex>0">,</div>
                      {{medList.brndNM}}
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
      <div style="display: block">
        <div style="float: right; vertical-align: bottom;">
          <button mat-button [matMenuTriggerFor]="menu"><mat-icon>print</mat-icon></button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="printRpt()">PDF</button>
            <button mat-menu-item (click)="printCSV()">CSV</button>
          </mat-menu>
        </div>
      </div>
      <div style="display:block">
        <hr/>
      </div>
      <table mat-table [dataSource]="ds" id="rptViewer" #rptViewer matSort matSortActive="mrn" matSortDirection="asc" matSortDisableClear>

        <ng-container matColumnDef="mrn">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>MRN</th>
          <td mat-cell *matCellDef="let row">{{row.mrn}}</td>
        </ng-container>

        <ng-container matColumnDef="patientName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Patient Name</th>
          <td mat-cell *matCellDef="let row;" class="patientColumn" (click)="patientFlowsheetRedirect(row.demographicsId)">{{row.patientName}}</td>
        </ng-container>

        <ng-container matColumnDef="birthDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Birth Date</th>
          <td mat-cell class="noWrap" *matCellDef="let row">{{row.birthDate | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="age">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Age</th>
          <td mat-cell *matCellDef="let row">{{row.age}}</td>
        </ng-container>

        <ng-container matColumnDef="genderDesc">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Gender</th>
          <td mat-cell *matCellDef="let row">{{row.genderDesc}}</td>
        </ng-container>

        <ng-container matColumnDef="ethnicityDesc">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Ethnicity</th>
          <td mat-cell *matCellDef="let row">{{row.ethnicityDesc}}</td>
        </ng-container>

        <ng-container matColumnDef="raceDesc">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Race</th>
          <td mat-cell *matCellDef="let row">{{row.raceDesc}}</td>
        </ng-container>

        <ng-container matColumnDef="lastVisitDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Visit Date</th>
          <td mat-cell class="noWrap" *matCellDef="let row">{{row.lastVisitDate | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="hivDiagDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>HIV Diag Date</th>
          <td mat-cell class="noWrap" *matCellDef="let row">{{row.hivDiagDate | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="hivDiagFlg">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>HIV Status</th>
          <td mat-cell *matCellDef="let row">{{row.hivDiagFlg | PosNegStatus}}</td>
        </ng-container>

        <ng-container matColumnDef="hcvDiagFlg">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>HCV Status</th>
          <td mat-cell *matCellDef="let row">{{row.hcvDiagFlg | HCVStatus}}</td>
        </ng-container>

        <ng-container matColumnDef="lastVacsMortalityRatio">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Vacs Mortality Ratio</th>
          <td mat-cell *matCellDef="let row">{{row.lastVacsMortalityRatio}}</td>
        </ng-container>

        <ng-container matColumnDef="lastCD4CollectDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last CD4 Collection Date</th>
          <td mat-cell *matCellDef="let row">{{row.lastCD4CollectDate | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="lastCD4Count">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last CD4 Count</th>
          <td mat-cell *matCellDef="let row">{{row.lastCD4Count}}</td>
        </ng-container>

        <ng-container matColumnDef="nadirCD4Date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Nadir CD4 Collection Date</th>
          <td mat-cell *matCellDef="let row">{{row.nadirCD4Date | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="nadirCD4Count">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Nadir CD4 Count</th>
          <td mat-cell *matCellDef="let row">{{row.nadirCD4Count}}</td>
        </ng-container>

        <ng-container matColumnDef="lastViralLoadCollectDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Viral Load Collection Date</th>
          <td mat-cell *matCellDef="let row">{{row.lastViralLoadCollectDate | date:'yyyy-MM-dd'}}</td>
        </ng-container>

        <ng-container matColumnDef="lastViralLoadCount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Viral Load Count</th>
          <td mat-cell *matCellDef="let row">{{row.lastViralLoadCount}}</td>
        </ng-container>

        <ng-container matColumnDef="lastViralLoadNonDetInd">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Viral Load Non-Detect Indicator</th>
          <td mat-cell *matCellDef="let row">{{row.lastViralLoadNonDetInd}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

      </table>

      <mat-paginator [length]="dataCount" [pageSize]="25"
                    [pageSizeOptions]="[25,50,75,100]"></mat-paginator>

    </div>
  </div>
  </div>
</div>
