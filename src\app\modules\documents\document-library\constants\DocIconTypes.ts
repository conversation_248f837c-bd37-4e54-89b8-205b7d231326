export enum DocIconTypes {
  UnknownFile="insert_drive_file",
  Image="insert_photo",
  FolderClosed="folder",
  FolderOpen="folder_open",
  DescendingSort="arrow_drop_down",
  AscendingSort="arrow_drop_up"
}

export enum PopUpNames {
  Upload="Add a document",
  CreateFolder="Create a new folder",
  RenameFileOrDirectory="Rename {{fileordir}}",
  Delete="Delete {{fileordiur}}"
}

export const IconList = [
    { type: 'pdf', icon: 'fiv-sqo fiv-icon-pdf'},
    { type: 'docx', icon: 'fiv-sqo fiv-icon-docx' },
    { type: 'doc', icon: 'fiv-sqo fiv-icon-doc' },
    { type: 'zip', icon: 'fiv-sqo fiv-icon-zip' },
    { type: 'txt', icon: 'fiv-sqo fiv-icon-txt' },
    { type: 'sql', icon: 'fiv-sqo fiv-icon-sql' },
    { type: 'jpg', icon: 'fiv-sqo fiv-icon-jpg' },
    { type: 'png', icon: 'fiv-sqo fiv-icon-png' },
    { type: 'gif', icon: 'fiv-sqo fiv-icon-gif' },
    { type: 'bmp', icon: 'fiv-sqo fiv-icon-bmp' },
    { type: 'xls', icon: 'fiv-sqo fiv-icon-xls' },
    { type: 'xlsx', icon: 'fiv-sqo fiv-icon-xlsx' },
    { type: 'csv', icon: 'fiv-sqo fiv-icon-csv' },
    { type: 'ppt', icon: 'fiv-sqo fiv-icon-ppt' },
    { type: 'pptx', icon: 'fiv-sqo fiv-icon-pptx' },
    { type: 'mp3', icon: 'fiv-sqo fiv-icon-mp3' },
    { type: 'mp4', icon: 'fiv-sqo fiv-icon-mp4' },
    { type: 'avi', icon: 'fiv-sqo fiv-icon-avi' },
    { type: 'html', icon: 'fiv-sqo fiv-icon-html' },
    { type: 'svg', icon: 'fiv-sqo fiv-icon-svg' },
    { type: 'xml', icon: 'fiv-sqo fiv-icon-xml' },
    { type: 'exe', icon: 'fiv-sqo fiv-icon-exe' },
    { type: 'wav', icon: 'fiv-sqo fiv-icon-wav' },
    { type: 'folder', icon: 'fiv-sqo fiv-icon-folder'},
    { type: 'blank', icon: 'fiv-sqo fiv-icon-blank' },
    // Add more extensions and icons as needed
];



