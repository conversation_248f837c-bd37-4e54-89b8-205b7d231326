import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardComponent } from 'src/app/modules/dashboard/dashboard/dashboard.component';
import { ReportComponent } from './report/report.component';
import { DashboardRoutingModule } from 'src/app/modules/dashboard/dashboard.routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BoldReportViewerModule } from '@boldreports/angular-reporting-components';
import { PnlPatientSearchComponent } from './panels/pnl-patient-search/pnl-patient-search.component';
import { PnlCohortsComponent } from './panels/pnl-cohorts/pnl-cohorts.component';
import { PnlRetentionComponent } from './panels/pnl-retention/pnl-retention.component';
import { PnlCohortsReportingYearComponent } from './panels/pnl-cohorts-reporting-year/pnl-cohorts-reporting-year.component';
import { PnlReportingYearComponent } from './panels/pnl-reporting-year/pnl-reporting-year.component';
import { MasterReportComponent } from './panels/master-report/master-report.component';
import { PnlMultiReportingYearComponent } from './panels/pnl-multi-reporting-year/pnl-multi-reporting-year.component';
import { PnlLstWeekComponent } from './panels/pnl-lst-week/pnl-lst-week.component';
import { PnlCustomQueryComponent } from './panels/pnl-custom-query/pnl-custom-query.component';
import { PnlFromToDateComponent } from './panels/pnl-from-to-date/pnl-from-to-date.component';
import { PnlUsageDatesComponent } from './panels/pnl-usage-dates/pnl-usage-dates.component';
import { PnlDiseaseManagementComponent } from './panels/pnl-disease-management/pnl-disease-management.component';
import { PnlLocationComponent } from './panels/pnl-location/pnl-location.component';
import { PnlHcvLocationComponent } from './panels/pnl-hcv-location/pnl-hcv-location.component';
import { MobileDeviceManagementComponent } from '../shared-modules/layouts/dialogs/MobileDeviceManagement/MobileDeviceManagement.component';
import { MatSelectModule } from '@angular/material/select';
import {MatAutocompleteModule} from '@angular/material/autocomplete'
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { AnnotationComponent } from '../annotation/annotation.component';
import { RegimenBuilderComponent } from './panels/pnl-custom-query/regimen-builder/regimen-builder.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatTreeModule } from '@angular/material/tree';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatMenuModule} from '@angular/material/menu';
import { MatChipsModule } from '@angular/material/chips';
import { NgSelectModule } from '@ng-select/ng-select';
import { CustomQueryComponent } from './customQuery/customQuery.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { PosNegStatusPipe } from 'src/app/pipes/PosNegStatus.pipe';
import { HCVStatusPipe } from 'src/app/pipes/HCVStatus.pipe';
import { PnlQrdaExtractComponent } from './panels/pnl-qrda-extract/pnl-qrda-extract.component';
import { PnlUserIdComponent } from './panels/pnl-user-id/pnl-user-id.component';
import { LayoutModule } from '../shared-modules/layouts/layout.module';
import { PdfFillerComponent } from './pdf-filler/pdf-filler.component';
import { DynamicFormsComponent } from './dynamic-forms/dynamic-forms.component';
import {MatStepperModule} from '@angular/material/stepper';
import { HuddleComponent } from './huddle/huddle.component';
import { RulesEngineService } from '../admin/services/rules-engine.service';
import { QualityMeasuresComponent } from './quality-measures/quality-measures.component';
import { QualityMeasureFiltersComponent } from './quality-measures/quality-measure-filters/quality-measure-filters.component';
import { EHIExportComponent } from './ehi-export/ehi-export.component';
import { QrdaExportComponent } from './qrda-export/qrda-export.component';
import { BonusMeasuresComponent } from './bonus-measures/bonus-measures.component';
import { ReportDetailsComponent } from './report-details/report-details.component';

@NgModule({
  declarations:[DashboardComponent, ReportComponent, DynamicFormsComponent, PnlPatientSearchComponent,
    PnlFromToDateComponent,
    PnlUsageDatesComponent,
    PnlCohortsComponent,
    PnlCustomQueryComponent,
    PnlReportingYearComponent,
    PnlMultiReportingYearComponent,
    PnlLstWeekComponent,
    PnlRetentionComponent,
    PnlCohortsReportingYearComponent,
    PnlDiseaseManagementComponent,
    PnlQrdaExtractComponent,
    PnlUserIdComponent,
    PnlLocationComponent,
    PnlHcvLocationComponent,
    MasterReportComponent,
    RegimenBuilderComponent,
    MobileDeviceManagementComponent,
    AnnotationComponent,
    CustomQueryComponent,
    PosNegStatusPipe,
    HCVStatusPipe,
    PdfFillerComponent,
    HuddleComponent,
    QualityMeasuresComponent,
    QualityMeasureFiltersComponent,
    EHIExportComponent,
    QrdaExportComponent,
    BonusMeasuresComponent,
    ReportDetailsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    DashboardRoutingModule,
    BoldReportViewerModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatInputModule,
    DragDropModule,
    MatTreeModule,
    MatChipsModule,
    MatNativeDateModule,
    MatIconModule,
    MatCheckboxModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatExpansionModule,
    MatPaginatorModule,
    MatSortModule,
    FormsModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatDialogModule,
    MatInputModule,
    MatButtonModule,
    MatMenuModule,
    MatTableModule,
    BrowserAnimationsModule,
    MatDatepickerModule,
    LayoutModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatIconModule,
    MatStepperModule,
    NgSelectModule
  ],
  providers: [
    RulesEngineService
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})

export class DashboardModule {

  constructor(){
  }

}
