import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { IChorusLogin, IUserIP, IDeviceDetailsVM } from "src/app/models/api";

@Injectable({
    providedIn: 'root'
})

export class EpUserLoginServService {

    private userData:any = new BehaviorSubject(null);
    currentUserData = this.userData.asObservable();


    private dashboardData:any = new BehaviorSubject(null);
    currentDashboardData = this.dashboardData.asObservable();

    constructor(
        private httpClient: HttpClient) {}

    _isUserLogIn: boolean = false;
    set userLogIn(value: boolean) {
        this._isUserLogIn = value;
    }
    get userLogIn(): boolean {
        return this._isUserLogIn;
    }

    _tfaMethod: any = {};
    set tfaMethod(value: {}) {
        this._tfaMethod = value;
    }
    get tfaMethod(): any {
        return this._tfaMethod;
    }

    getUserData(data: any) {
        this.userData.next(data);
    }

    getDashboardData(data: any) {
        this.dashboardData.next(data);
    }

    getIPAddress(): Observable<IUserIP> {
        return this.httpClient.get<IUserIP>(
            "http://api.ipify.org/?format=json"
        );
    }

    epLogin(email: string, password:string, currentIP:string): any {

        const currentDate = new Date().toDateString();

        const userString = `${email}^{"CODE_ID":2,"USER_NM":"${email}","WEB_URL":"http://localhost:3017","USER_AST":"Manufacturer: Apple|~| GlobalClass: 14.3|~| Model:${window.navigator.userAgent.toString()}|~| Device Name: iPhone 11|~| VersionNumber: 14.3|~| Idiom: Phone|~| Platform: iOS|~| Current Id: 52FED91B-EB0A-4BA9-B0BB-31C5D4ABA80A|~| Device Id: 52FED91BEB0A4BA9B0BB31C5D4ABA80A|~| AppVersion: 7.0.0|~| Hardware: x86_64|~| iOS|~| Push Token:1","IP_ADD":"${currentIP}","LOGIN_DT":"${currentDate}"}`;

        const body: IChorusLogin = { grant_type: "password", username: encodeURIComponent(userString), password: encodeURIComponent(password)}

        return this.httpClient.post(
            'https://apidev.epividian.com/V3/ChorusLogin',
            JSON.stringify(body),
            { observe: 'response', headers: {'Content-Type': 'application/json'} }
            ).pipe(
                tap(res=> {
                    return res;
            })
        );
    }

    epForgotPassword(userName: String): any {
        const forgotPassModelJson = `{"Username":"${userName}","IsAccountAvailable":true,"IsLockedOut":false,"IsApproved":true,"IsTempPwdGenrate":true,"IsEmailSent":true,"IsUpdateTempPwd":false}`;

        return this.httpClient.get(
            "https://apidev.epividian.com/api/User/ForgotPassword?forgotPassModelJson="+ forgotPassModelJson.toString()
        );
    }

    epChangePassword(userName: string, currentPassword: string,  newPassword: string, MFAToken:string): any {
        const changePassModel = `{"Username":"${userName}","CurrentPassword":"${currentPassword}","NewPassword":"${newPassword}", "IsChangePassChanged":false, "IsTFAActive":false, "TOTPCode":null,"METHOD_ID":0,"IsVerifyTOTP":false, "MFAToken": "${MFAToken}" }`;

        return this.httpClient.get(
            "https://apidev.epividian.com/api/User/ChangePassword?changePassModelJson="+ encodeURIComponent(changePassModel)
        );
    }

    epGetTFA(userName: string): any {
        return this.httpClient.get(
            "https://apidev.epividian.com/V3/api/TFA/QRCode"+ "?username=" + userName
        );
    }

    epSendSMS(mobileNumber: number, userName: string): any {
        return this.httpClient.get(
            "https://apidev.epividian.com/V3/api/TFA/SendTFASMS?MobileNumber="+ encodeURIComponent(mobileNumber.toString())+"&username=" + encodeURIComponent(userName)
        );
    }

    epVerifyTFACode(code: string, mfaToken: string, mobile: string, methodCode: number, userName: string, userId: string): any {
        const currentDate = new Date().toISOString().slice(0, 10);

        const body: IDeviceDetailsVM = { USER_ID: userId, TOTPCode: code, METHOD_ID: methodCode, IS_TFA_LOGIN: true, IS_TWO_FACTOR: true, AppDeviceId: "", IPAddress:"", IS_AUTH_CODE_SET: true, MFA_TOKEN: mfaToken, MOB_NUM: mobile.toString(), TFADate: currentDate, LOGIN_DT: currentDate,  TFADays: 0, TFADeviceId:"", USER_AST:"", IP_ADD:"", Username: userName};

        return this.httpClient.post(
            'https://apidev.epividian.com/V3/api/TFA/Verify2FACode',
            JSON.stringify(body),
            { observe: 'response', headers: { 'Content-Type': 'application/json'} }
        );
    }

    epLoginWithMFAToken(code: string, mfaToken: string, mobile: string, methodCode: number, userName: string, userId: string): any {
        const currentDate = new Date().toISOString().slice(0, 10);

        const body: any = `${mfaToken}^{"Username":"${userName}","USER_ID":"${userId}","TOTPCode":${code},"METHOD_ID":${methodCode},"IS_TFA_LOGIN":true,"IS_TWO_FACTOR":true,"AppDeviceId":"","IPAddress":null,"IS_AUTH_CODE_SET":true,"MFA_TOKEN":"${mfaToken}","MOB_NUM":"${encodeURIComponent(mobile.toString())}","TFADate":null,"TFADays":31,"TFADeviceId":null,"USER_AST":null}^{"CODE_ID": 0,"USER_NM": "${userName}" ,"WEB_URL": null,"USER_AST": "Manufacturer: Apple|~| GlobalClass: 14.3|~| Model: iPhone 11 Simulator|~| Device Name: iPhone 11|~| VersionNumber: 14.3|~| Idiom: Phone|~| Platform: iOS|~| Current Id: 52FED91B-EB0A-4BA9-B0BB-31C5D4ABA80A|~| Device Id: 52FED91BEB0A4BA9B0BB31C5D4ABA80A|~| AppVersion: 7.0.0|~| Hardware: x86_64|~| iOS|~| Push Token: d7eAjMxjADY:APA91bEhNsLp_hWPpzmO4m-bT42lChRaIWtOKJTkCQQ9Ntt1lZA58c0SpUSh02azb-8oUh0SfFTQvql4RO5OXSpca5TkaO_OPfGW23iMblfBPTP53uP-p-swkrJ-jBghDJviYSuPWe-a","IP_ADD": null,"LOGIN_DT": "${currentDate}","CREATED_DT": "${currentDate}","NAT_IP": null}`;

        const bodyStr: any = { mfa_token: body, grant_type: "mfa_token"};

        return this.httpClient.post(
            'https://apidev.epividian.com/V3/ChorusLogin',
            JSON.stringify(bodyStr),
            { observe: 'response', headers: {'Content-Type': 'application/json'} });
    }
}
