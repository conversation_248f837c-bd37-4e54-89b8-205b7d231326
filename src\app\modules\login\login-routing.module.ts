import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { LoginComponent } from 'src/app/modules/login/login/login.component';
import { ForgotPasswordComponent } from 'src/app/modules/login/forgot-password/forgot-password.component'
import { ChangePasswordComponent } from 'src/app/modules/login/change-password/change-password.component';
import { LayoutWithoutNavComponent } from '../shared-modules/layouts/layout-without-nav/layout-without-nav.component';
import { RouterModule, Routes } from '@angular/router';
import { TermsConditionComponent } from './terms-condition/terms-condition.component';
import { TfaNewDevicLinkComponent } from './tfa-new-device-link/tfa-new-device-link..component';
import { TfaVerificationComponent } from './tfa-verification/tfa-verification.component';

const routes: Routes = [
  {
    path: 'Auth',
    component: LayoutWithoutNavComponent,
    children: [
      {path: "Login", component: LoginComponent, pathMatch:"full"},
      {path: "ForgotPassword", component: ForgotPasswordComponent, pathMatch:"full"}
    ]
  },
  {
    path: "TermsConditions", component: TermsConditionComponent, pathMatch:"full"
  },
  {
    path: "Auth/ChangePassword", component: ChangePasswordComponent, pathMatch:"full"
  },
  {
    path: "Auth/VerifyTfa", component: TfaVerificationComponent, pathMatch:"full"
  },
  {
    path: "Auth/RegisterTfa", component: TfaNewDevicLinkComponent, pathMatch:"full"
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LoginRoutingModule { }

