
@import 'bootstrap/scss/_functions';
@import 'bootstrap/scss/_variables';
@import 'bootstrap/scss/mixins/_breakpoints';

.cover-container {
    background-color: #0071bc;
    min-height: 100vh;
    overflow: hidden;
    position: relative;

    a:visited {
      color: whitesmoke;
    }
    @include media-breakpoint-up(md) {
        padding-left: 8rem;
    }

    .header-row-1  {
        background-image: url("../../../../assets/images/Bubbles.svg");
        background-position: right -90px;
        background-repeat: no-repeat;
        background-size: contain;
        @include media-breakpoint-down(md) {
            background-position: right top;
            transform: scale(160%) translate(35%, 6%);
        }
        .logo-container {
            background-image: url("../../../../assets/images/Logo.svg");
            background-position: top center;
            background-repeat: no-repeat;
            background-size: contain;
            padding-top: 32.5%;

            @include media-breakpoint-down(md) {
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                padding-top: 90%;
                transform: scale(70%) translate(-50%, -50%);
            }
        }
    }

    .header-row-2 {
        @include media-breakpoint-down(md) {
            margin-top: -35%;
        }
    }

    .footer-row {
        bottom: 0;
        margin-left: -0.75rem;
        position: absolute;
        width: 100%;
        .contact-support {
            color: #fff;
            font-family: "MuseoSans-300","sans-serif";
            text-underline-offset: 1px;
        }
    }

    ::ng-deep .form-control {
        border-radius: 0.7rem;
        font-family: "Museo300-Regular", "sans-serif";
        font-size: 18px;
        color: #999;
        &:focus {
            color: #000;
        }
    }

    ::ng-deep .btn-light {
        background-color: #fff;
        background-image: url("../../../../assets/images/right_arrow.png");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 10px 15px;
        border: 1px solid #ced4da;
        border-left: none;
        border-radius: 0.7rem;
    }

    ::ng-deep .spinner-container {
        position: relative;
        .spinner-wrapper {
            position: absolute;
            z-index: 99;
            .img-spinner {
                height: 6rem;
                width: 6rem;
            }
        }
    }
}

.eTFALoginInfo{
    width: 40%;
    height: 50%;
    display: table;
    position: relative;
    float: right;
    top: 44%;
    right: 8%;
}
.input-group{
    padding-top: 10px;
    width: 65%;
}
.form-control{
    border-radius: 11px !important;
}
.eTFAMessage{
    color: white;
    visibility: visible;
    font-family: "MuseoSans-500";
    font-size: 11pt;
    width: 100%;
    word-wrap: break-word;
    padding-bottom: 5px;
}
.resendCode{
    color: white;
    visibility: visible;
    font-family: "MuseoSans-500";
    font-size: 11pt;
    width: 100%;
    word-wrap: break-word;
    padding-bottom: 5px;
    cursor: pointer;
}
