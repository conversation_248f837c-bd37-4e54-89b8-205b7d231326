<div class="pdf-filler-manager-container">
  <div class="pdf-filler-manager-header">
    <h1>PDF Filler Manager</h1>
    <p>Manage PDF templates and field mappings for automated form filling.</p>
  </div>

  <div class="pdf-filler-manager-content">
    <!-- Loading indicator -->
    <div class="loading-indicator" *ngIf="isLoading">
      <div class="loading-spinner"></div>
      <p>Loading PDF Filler Manager...</p>
    </div>

    <!-- Error message -->
    <div class="error-message" *ngIf="loadError">
      <mat-icon class="error-icon">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button type="button" mat-raised-button color="primary" (click)="reload()">Reload</button>
      <button type="button" mat-raised-button color="accent">Continue Anyway</button>
    </div>

    <!-- PDF Filler Manager iframe -->
    <iframe
      id="pdfFillerManagerFrame"
      title="PDF Filler Management"
      class="pdf-filler-iframe"
      [src]="pdfFillerManagerUrl"
      (load)="onIframeLoad()">
    </iframe>
  </div>
</div>
