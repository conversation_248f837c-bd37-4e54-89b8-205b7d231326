import { Injectable } from '@angular/core';
import { Observable, forkJoin, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { Rule, Workflow, WorkflowRuleDetail } from '../models/rule.model';
import { RulesEngineService } from './rules-engine.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

@Injectable({
  providedIn: 'root'
})
export class SqlScriptGeneratorService {
  private siteId: string;

  constructor(
    private rulesService: RulesEngineService,
    private userContext: UserContext
  ) {
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
  }

  /**
   * Generate SQL insert scripts for a workflow, its rules, and dependencies
   * @param workflow The workflow to generate scripts for
   * @returns Observable of the generated SQL script as a string
   */
  generateWorkflowSqlScript(workflow: Workflow): Observable<string> {
    // Refresh the site ID in case the user has switched sites
    this.siteId = this.userContext.GetCurrentSiteValue().toString();

    console.log('Generating SQL script for workflow:', workflow);

    // Get all the data we need
    return this.rulesService.getWorkflowRuleDetails(workflow.workflowID).pipe(
      switchMap(workflowRules => {
        console.log('Workflow rules details:', workflowRules);

        // If there are no rules, just return the workflow script
        if (!workflowRules || workflowRules.length === 0) {
          console.log('No workflow rules found, returning only workflow script');
          return of(this.generateWorkflowInsertScript(workflow));
        }

        // Get all rule IDs
        const ruleIds = workflowRules.map(wr => wr.ruleID);
        console.log('Rule IDs to fetch:', ruleIds);

        // Check if we already have rule data in the workflowRules
        if (workflowRules[0].rule && Object.keys(workflowRules[0].rule).length > 0) {
          console.log('Rules already included in workflowRules, using those');
          // Extract rules from workflowRules
          const rules = workflowRules.map(wr => wr.rule);
          return of(this.generateCompleteScript(workflow, rules, workflowRules));
        }

        // Get all rules
        return forkJoin(
          ruleIds.map(ruleId => {
            console.log(`Fetching rule ${ruleId} with site ID ${this.siteId}`);
            return this.rulesService.getRuleById(this.siteId, ruleId).pipe(
              map(rule => {
                console.log(`Received rule ${ruleId}:`, rule);
                return rule;
              })
            );
          })
        ).pipe(
          map(rules => {
            console.log('All rules fetched:', rules);
            // Generate the complete script
            return this.generateCompleteScript(workflow, rules, workflowRules);
          })
        );
      })
    );
  }

  /**
   * Generate the complete SQL script for a workflow, its rules, and dependencies
   * @param workflow The workflow
   * @param rules The rules
   * @param workflowRules The workflow rule details
   * @returns The complete SQL script
   */
  private generateCompleteScript(
    workflow: Workflow,
    rules: Rule[],
    workflowRules: WorkflowRuleDetail[]
  ): string {
    let script = '';

    // Add workflow insert script
    script += this.generateWorkflowInsertScript(workflow);
    script += '\n\n';

    // Add rules insert script
    script += this.generateRulesInsertScript(rules);
    script += '\n\n';

    // Add workflow rules insert script
    script += this.generateWorkflowRulesInsertScript(workflowRules);
    script += '\n\n';

    // Add dependency rules insert script
    script += this.generateDependencyRulesInsertScript(workflow.workflowID, workflowRules);
    script += '\n';

    return script;
  }

  /**
   * Generate SQL insert script for a workflow
   * @param workflow The workflow to generate script for
   * @returns The SQL insert script for the workflow
   */
  private generateWorkflowInsertScript(workflow: Workflow): string {
    if (!workflow || !workflow.workflowID) {
      console.log('Invalid workflow data:', workflow);
      return '';
    }

    console.log('Generating workflow insert script for:', workflow);

    const createdDate = this.formatDateForSql(workflow.createdDate);
    const modifiedDate = this.formatDateForSql(workflow.modifiedDate);

    // Ensure all fields have values
    const workflowId = workflow.workflowID || 0;
    const name = this.escapeSqlString(workflow.name);
    const description = this.escapeSqlString(workflow.description);
    const type = this.escapeSqlString(workflow.type);

    return `SET IDENTITY_INSERT [FLOW].[Workflow] ON

INSERT [FLOW].[Workflow] ([WorkflowID], [Name], [Description], [Type], [CreatedDate], [ModifiedDate]) VALUES (${workflowId}, N'${name}', N'${description}', N'${type}', ${createdDate}, ${modifiedDate})
SET IDENTITY_INSERT [FLOW].[Workflow] OFF
GO`;
  }

  /**
   * Generate SQL insert script for rules
   * @param rules The rules to generate script for
   * @returns The SQL insert script for the rules
   */
  private generateRulesInsertScript(rules: Rule[]): string {
    if (!rules || rules.length === 0) {
      console.log('No rules to generate script for');
      return '';
    }

    console.log('Generating script for rules:', rules);

    // Filter out rules with missing or invalid data
    const validRules = rules.filter(rule => {
      const isValid = rule && rule.ruleID !== undefined && rule.ruleID !== null;
      if (!isValid) {
        console.log('Skipping invalid rule:', rule);
      }
      return isValid;
    });

    if (validRules.length === 0) {
      console.log('No valid rules to generate script for after filtering');
      return '';
    }

    let script = `SET IDENTITY_INSERT [FLOW].[Rule] ON \n\n`;

    for (const rule of validRules) {
      const createdDate = this.formatDateForSql(rule.createdDate);
      const modifiedDate = this.formatDateForSql(rule.modifiedDate);

      // Make sure jsonData is a string and properly escaped
      let jsonData = '';
      try {
        jsonData = this.escapeSqlString(
          typeof rule.jsonData === 'string'
            ? rule.jsonData
            : rule.jsonData ? JSON.stringify(rule.jsonData) : '{}'
        );
      } catch (error) {
        console.error('Error processing JSON data for rule', rule.ruleID, error);
        jsonData = '{}';
      }

      // Ensure all fields have values
      const ruleId = rule.ruleID || 0;
      const name = this.escapeSqlString(rule.name);
      const description = this.escapeSqlString(rule.description);
      const type = this.escapeSqlString(rule.type);
      const version = this.escapeSqlString(rule.version);
      const action = this.escapeSqlString(rule.action);

      script += `INSERT [FLOW].[Rule] ([RuleID], [Name], [Description], [Type], [Version], [Action], [JsonData], [CreatedDate], [ModifiedDate]) VALUES (${ruleId}, N'${name}', N'${description}', N'${type}', N'${version}', N'${action}', N'${jsonData}', ${createdDate}, ${modifiedDate})\n`;
    }

    script += `SET IDENTITY_INSERT [FLOW].[Rule] OFF\nGO`;
    return script;
  }

  /**
   * Generate SQL insert script for workflow rules
   * @param workflowRules The workflow rules to generate script for
   * @returns The SQL insert script for the workflow rules
   */
  private generateWorkflowRulesInsertScript(workflowRules: WorkflowRuleDetail[]): string {
    if (!workflowRules || workflowRules.length === 0) {
      console.log('No workflow rules to generate script for');
      return '';
    }

    console.log('Generating script for workflow rules:', workflowRules);

    // Filter out workflow rules with missing or invalid data
    const validWorkflowRules = workflowRules.filter(wr => {
      const isValid = wr &&
                     wr.workflowRuleID !== undefined && wr.workflowRuleID !== null &&
                     wr.workflowID !== undefined && wr.workflowID !== null &&
                     wr.ruleID !== undefined && wr.ruleID !== null;
      if (!isValid) {
        console.log('Skipping invalid workflow rule:', wr);
      }
      return isValid;
    });

    if (validWorkflowRules.length === 0) {
      console.log('No valid workflow rules to generate script for after filtering');
      return '';
    }

    let script = `SET IDENTITY_INSERT [FLOW].[WorkflowRules] ON \nGO\n`;

    for (const wr of validWorkflowRules) {
      const workflowRuleId = wr.workflowRuleID || 0;
      const workflowId = wr.workflowID || 0;
      const ruleId = wr.ruleID || 0;
      const orderNo = wr.orderNo || 0;

      script += `INSERT [FLOW].[WorkflowRules] ([WorkflowRuleID], [WorkflowID], [RuleID], [SequenceOrder]) VALUES (${workflowRuleId}, ${workflowId}, ${ruleId}, ${orderNo})\n`;
    }

    script += `SET IDENTITY_INSERT [FLOW].[WorkflowRules] OFF\nGO`;
    return script;
  }

  /**
   * Generate SQL insert script for dependency rules
   * @param workflowId The workflow ID
   * @param workflowRules The workflow rules with dependencies
   * @returns The SQL insert script for the dependency rules
   */
  private generateDependencyRulesInsertScript(workflowId: number, workflowRules: WorkflowRuleDetail[]): string {
    if (!workflowId || !workflowRules || workflowRules.length === 0) {
      console.log('No workflow ID or rules for dependency script');
      return '';
    }

    console.log('Generating dependency script for workflow ID:', workflowId);

    // Filter rules that have dependencies
    const rulesWithDependencies = workflowRules.filter(wr => {
      const hasDependencies = wr?.ruleID && wr?.dependencyRules && Array.isArray(wr?.dependencyRules) && wr?.dependencyRules.length > 0;
      if (!hasDependencies && wr?.dependencyRules) {
        console.log('Rule has invalid dependencies:', wr);
      }
      return hasDependencies;
    });

    if (rulesWithDependencies.length === 0) {
      console.log('No rules with valid dependencies found');
      return '';
    }

    console.log('Rules with dependencies:', rulesWithDependencies);

    let script = `\nSET IDENTITY_INSERT [FLOW].[DependencyRule] ON\nGO\n`;
    let dependencyId = 1; // Starting ID for dependencies

    for (const wr of rulesWithDependencies) {
      const ruleId = wr.ruleID || 0;

      for (const dependentRuleId of wr.dependencyRules) {
        if (dependentRuleId === undefined || dependentRuleId === null) {
          console.log('Skipping invalid dependent rule ID for rule', ruleId);
          continue;
        }

        script += `INSERT [FLOW].[DependencyRule] ([DependencyRuleID], [WorkflowID], [RuleID], [DependentRuleID], [SiteId]) VALUES (${dependencyId}, ${workflowId}, ${ruleId}, ${dependentRuleId}, NULL)\nGO\n`;
        dependencyId++;
      }
    }

    script += `SET IDENTITY_INSERT [FLOW].[DependencyRule] OFF\nGO`;
    return script;
  }

  /**
   * Format a date for SQL insert
   * @param date The date to format
   * @returns The formatted date string for SQL
   */
  private formatDateForSql(date: unknown): string {
    if (!date) {
      return 'NULL';
    }

    try {
      // Convert string date to Date object if needed
      const dateObj = typeof date === 'string' ? new Date(date) : date as Date;

      // Check if it's a valid date object
      if (dateObj instanceof Date && !Number.isNaN(dateObj.getTime())) {
        // Format: CAST(N'2025-01-14T08:33:27.947' AS DateTime)
        const isoString = dateObj.toISOString().replace('Z', '');
        return `CAST(N'${isoString}' AS DateTime)`;
      }

      // If it's a string, try to use it directly
      if (typeof date === 'string') {
        return `CAST(N'${date}' AS DateTime)`;
      }

      // If all else fails, return NULL
      return 'NULL';
    } catch (error) {
      console.error('Error formatting date for SQL:', error, date);
      return 'NULL';
    }
  }

  /**
   * Escape single quotes in SQL strings
   * @param str The string to escape
   * @returns The escaped string
   */
  private escapeSqlString(str: string | null | undefined): string {
    if (str === null || str === undefined) {
      return '';
    }

    // Convert to string if it's not already
    const strValue = String(str);

    // Replace single quotes with two single quotes
    return strValue.replace(/'/g, "''");
  }
}
