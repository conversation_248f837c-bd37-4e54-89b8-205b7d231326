<div class="eTncContainer">
    <div class="eTncBubbleContainer"></div>
    <div class="eTncLogoContainer"></div>
    <div class="eTermsCondition">
        <div class="eTermsConditionBlock">
            <div class="container term-text-container">  
                <div class="headerContent">Acceptable Use Policy</div>              
                <div class="content" [innerHTML]="content"></div>
                <div class="content">
                    <small><a href="../../../../assets/images/Acceptable_Use_Policy.pdf" download>{{linkText}}</a></small>
                </div>
                <div class="content">
                    <small>
                        <input type="checkbox" id="tncAcceptanceStatus" name="tncAcceptanceStatus" class="btn-sm" (change)="changeFn($event)">
                        &nbsp;<label for="tncAcceptanceStatus">{{acceptanceText}}</label>
                    </small>
                </div>
                <div class="actionButton">
                    <button 
                        [ngClass]="[disabledContinueBtn] ? 'btn btn-primary btn-sm' : 'btn btn-secondary btn-sm'"
                        [disabled]="disabledContinueBtn"
                        mat-button
                        (click)="onContinueClick(isSubmitTnC)"
                    >
                        Continue
                    </button>
                    &nbsp;
                    <button class="btn btn-primary btn-sm" mat-button (click)="onCancelClick()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>



