<div class="json-node json-node-level-{{ level }}">
  <!-- Node header with key and type -->
  <div *ngIf="nodeKey" class="node-header" (click)="toggleExpansion()">
    <mat-icon class="expand-icon">
      {{ isNodeExpanded ? 'expand_more' : 'chevron_right' }}
    </mat-icon>
    <span class="node-key">{{ jsonSchemaService.formatLabel(nodeKey) }}</span>
    <span class="node-type-badge">{{ nodeType }}</span>
  </div>

  <!-- Node content based on type -->
  <div class="node-content" [class.expanded]="isNodeExpanded">
    <!-- Object node -->
    <ng-container *ngIf="jsonSchemaService.isObject(node)">
      <div *ngIf="nodeKeys.length === 0" class="empty-node">
        Empty object
      </div>
      <div *ngFor="let key of nodeKeys" class="object-property">
        <app-json-node
          [node]="node[key]"
          [path]="getChildPath(key)"
          [level]="level + 1"
          [nodeKey]="key"
          [isExpanded]="level === 0"
          (nodeChange)="onChildNodeChange($event)">
        </app-json-node>
      </div>
    </ng-container>

    <!-- Array node -->
    <ng-container *ngIf="jsonSchemaService.isArray(node)">
      <div class="array-controls">
        <button type="button" mat-icon-button color="primary" (click)="addArrayItem()" matTooltip="Add item">
          <mat-icon>add</mat-icon>
        </button>
      </div>
      <div *ngIf="node.length === 0" class="empty-node">
        Empty array
      </div>

      <!-- Array items with lazy loading -->
      <div *ngFor="let key of nodeKeys; let i = index" class="array-item">
        <!-- Array item header with preview -->
        <div class="array-item-header" (click)="toggleArrayItem(i)">
          <div class="array-item-title">
            <mat-icon class="expand-icon">
              {{ isArrayItemExpanded(i) ? 'expand_more' : 'chevron_right' }}
            </mat-icon>
            <span class="array-index">{{ i }}</span>
            <span class="array-item-preview">{{ arrayItemPreviews[i] }}</span>
          </div>
          <button type="button" mat-icon-button color="warn" (click)="removeArrayItem(i); $event.stopPropagation()" matTooltip="Remove item">
            <mat-icon>delete</mat-icon>
          </button>
        </div>

        <!-- Array item content - only render when expanded -->
        <div class="array-item-content" [class.expanded]="isArrayItemExpanded(i)">
          <ng-container *ngIf="isArrayItemExpanded(i)">
            <app-json-node
              [node]="node[i]"
              [path]="getChildPath(i.toString())"
              [level]="level + 1"
              [nodeKey]="''"
              [isExpanded]="true"
              (nodeChange)="onChildNodeChange($event)">
            </app-json-node>
          </ng-container>
        </div>
      </div>
    </ng-container>

    <!-- Primitive node (string, number, boolean) -->
    <ng-container *ngIf="!jsonSchemaService.isObject(node) && !jsonSchemaService.isArray(node)">
      <div class="primitive-value">
        <!-- String input -->
        <mat-form-field *ngIf="nodeType === 'string'" appearance="outline" class="full-width">
          <mat-label>{{ nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'Value' }}</mat-label>
          <input matInput [formControl]="stringControl" (input)="onPrimitiveValueChange(stringControl.value)"
                 [placeholder]="'Enter ' + (nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'value')"
                 [attr.aria-label]="nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'Value'" />
        </mat-form-field>

        <!-- Number input -->
        <mat-form-field *ngIf="nodeType === 'number'" appearance="outline" class="full-width">
          <mat-label>{{ nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'Value' }}</mat-label>
          <input matInput type="number" [formControl]="numberControl" (input)="onPrimitiveValueChange(numberControl.value)"
                 [placeholder]="'Enter ' + (nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'value')"
                 [attr.aria-label]="nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'Value'" />
        </mat-form-field>

        <!-- Boolean input -->
        <mat-checkbox *ngIf="nodeType === 'boolean'"
                     [formControl]="booleanControl"
                     (change)="onPrimitiveValueChange(booleanControl.value)">
          {{ nodeKey ? jsonSchemaService.formatLabel(nodeKey) : (nodeValue ? 'True' : 'False') }}
        </mat-checkbox>

        <!-- Null value with option to convert to string -->
        <div *ngIf="nodeType === 'null'" class="null-value-container">
          <div class="null-header">
            <span class="null-value">null</span>
            <button type="button" mat-button color="primary" (click)="convertNullToEmptyString()">
              Convert to empty string
            </button>
          </div>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Convert to string</mat-label>
            <input matInput [formControl]="nullControl" (input)="onPrimitiveValueChange(nullControl.value)"
                   placeholder="Enter value to convert from null"
                   [attr.aria-label]="nodeKey ? jsonSchemaService.formatLabel(nodeKey) : 'Value'" />
            <mat-hint>Enter a value to convert this null to a string</mat-hint>
          </mat-form-field>
        </div>
      </div>
    </ng-container>
  </div>
</div>
