.pdf-filler-manager-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-filler-manager-header {
  margin-bottom: 30px;

  h1 {
    margin-bottom: 10px;
    color: #333;
    font-size: 28px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

.pdf-filler-manager-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.pdf-filler-iframe {
  width: 100%;
  height: 100%;
  min-height: 700px;
  border: none;
  flex: 1;
}

/* Loading indicator styles */
.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;

  p {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
  }
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #0071BC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error message styles */
.error-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 0, 0, 0.1);
  z-index: 10;
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #f44336;

  p {
    margin: 0 10px;
    font-size: 14px;
    color: #333;
    flex: 1;
  }

  .error-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
    color: #f44336;
    margin-right: 10px;
  }

  button {
    margin-left: 10px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
