<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.9564in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.56545in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.21875in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.21875in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.16962in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox4">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!patient_name.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Patient</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox4</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!Diagnosis.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Diagnosis</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox8">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!Status.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Status</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox8</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox10">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!Onset_Dt.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Onset Date</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox10</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!Biologics_treatment.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Biologics Treatment</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox12</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox14">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!charlson_1year_survival.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Charlson 1-Year Survival</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox14</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <UserSort>
                        <SortExpression>=Fields!Harvey_Bradshaw_index.Value</SortExpression>
                      </UserSort>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Harvey Bradshaw Index</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#0b6c9f</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="patient_name">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!patient_name.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>patient_name</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Diagnosis">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Diagnosis.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Diagnosis</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Status">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Status.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Status</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Onset_Dt">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=FormatDateTime(Fields!Onset_Dt.Value,DateFormat.ShortDate)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Onset_Dt</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Biologics_treatment">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Biologics_treatment.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Biologics_treatment</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="charlson_1year_survival">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=FormatNumber(Fields!charlson_1year_survival.Value,1)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>charlson_1year_survival</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Harvey_Bradshaw_index">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=FormatNumber(Fields!Harvey_Bradshaw_index.Value,0)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Harvey_Bradshaw_index</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>IBDPatients</DataSetName>
        <Top>0.06944in</Top>
        <Height>0.5in</Height>
        <Width>12.12898in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.84375in</Height>
    <Style />
  </Body>
  <Width>12.12898in</Width>
  <Page>
    <PageHeader>
      <Height>0.75in</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="Textbox15">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>IBD Patients On Biologics Treatment</Value>
                  <Style>
                    <FontStyle>Normal</FontStyle>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>14pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextDecoration>None</TextDecoration>
                    <Color>#000000</Color>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.3175cm</Top>
          <Left>11.48292cm</Left>
          <Height>0.80434cm</Height>
          <Width>10.74468cm</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox47">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Epividian® CHORUS</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <Color>DimGray</Color>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>™</Value>
                  <Style>
                    <FontStyle>Normal</FontStyle>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextDecoration>None</TextDecoration>
                    <Color>DimGray</Color>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> Report</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <Color>DimGray</Color>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontWeight>Normal</FontWeight>
                    <Format>dd-MMM-yyyy</Format>
                    <Color>Gray</Color>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> (data)</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontWeight>Normal</FontWeight>
                    <Color>Gray</Color>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Now()</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontWeight>Normal</FontWeight>
                    <Format>dd-MMM-yyyy</Format>
                    <Color>Gray</Color>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> (run)</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontWeight>Normal</FontWeight>
                    <Color>Gray</Color>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox46</rd:DefaultName>
          <Top>0.35279cm</Top>
          <Left>9.70742in</Left>
          <Height>0.58333in</Height>
          <Width>2.12989in</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox1">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>="Records Returned: " + cstr(CountRows("IBDPatients"))</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <Color>DimGray</Color>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox1</rd:DefaultName>
          <Top>0.41273cm</Top>
          <Left>0cm</Left>
          <Height>0.72497cm</Height>
          <Width>6.50268cm</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <InteractiveHeight>0in</InteractiveHeight>
    <InteractiveWidth>0in</InteractiveWidth>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>1in</RightMargin>
    <TopMargin>1in</TopMargin>
    <BottomMargin>1in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b69f057e-2da4-4b43-a72c-df9a50a702bf</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="IBDPatients">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT last_nm + ', ' + first_nm + ' (MRN: ' + CAST(D.DEMOGRAPHICS_ID * 13 as varchar) + ')' as patient_name,
       IIF(P.DICTIONARY_SRCE_CD = '564.1', 'IBD (inflammatory bowel disease)', P.title_txt) as 'Diagnosis', 
	   IIF(P.STATUS_DESC = 'RESOLVED', 'Inactive', ISNULL(P.status_desc, 'Active')) as Status, P.start_dt as Onset_Dt,
          
          CASE
            WHEN LEFT(d.demographics_id, 1) = 1 THEN 'Humira (adalimumab)'
              WHEN LEFT(d.demographics_id, 1) = 2 THEN 'Infliximab (Remicade)'
              WHEN LEFT(d.demographics_id, 1) = 3 THEN 'Certolizumab pegol (Cimzia)'
              WHEN LEFT(d.demographics_id, 1) = 4 THEN 'Simponi Aria (Golimumab)'
              WHEN LEFT(d.demographics_id, 1) = 5 THEN 'Natalizumab (Tysabri)'
              WHEN LEFT(d.demographics_id, 1) = 6 THEN 'Ustekinumab (Stelara)'
              ELSE '(None)'
          END as Biologics_treatment,
  -- Charlson Comorbidity 1-year Survival %
  100.0 - LEFT(d.demographics_id, 1) * 2 - V.MORTALITY_PCT  as charlson_1year_survival,
  9 - LEFT(d.demographics_id, 1) as Harvey_Bradshaw_index

  FROM ANALYSIS.DEMOGRAPHICS D
  INNER JOIN ANALYSIS.PROBLEM_CLSF P ON (P.DEMOGRAPHICS_ID = D.DEMOGRAPHICS_ID AND
                                                 P.DICTIONARY_SRCE_CD IN ( '564.1', '555.9','556.9') AND
                                                                                  P.start_dt = (SELECT MIN(P2.start_dt) 
                                                                                                   FROM ANALYSIS.PROBLEM_CLSF P2
                                                                                                              WHERE P2.DEMOGRAPHICS_ID = P.DEMOGRAPHICS_ID
                                                                                                                AND P2.DICTIONARY_SRCE_CD IN ( '564.1', '555.9','556.9')))
  INNER JOIN ANALYSIS.VACS_INDEX V ON (V.DEMOGRAPHICS_ID = D.DEMOGRAPHICS_ID AND
                                               V.COLLECTION_DT = (SELECT max(V2.collection_dt)
                                                                                                   FROM ANALYSIS.VACS_INDEX V2
                                                                                                                 WHERE V2.DEMOGRAPHICS_ID = V.DEMOGRAPHICS_ID))
order by last_nm, first_nm
</CommandText>
      </Query>
      <Fields>
        <Field Name="patient_name">
          <DataField>patient_name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Diagnosis">
          <DataField>Diagnosis</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Status">
          <DataField>Status</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Onset_Dt">
          <DataField>Onset_Dt</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="Biologics_treatment">
          <DataField>Biologics_treatment</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="charlson_1year_survival">
          <DataField>charlson_1year_survival</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Harvey_Bradshaw_index">
          <DataField>Harvey_Bradshaw_index</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>bab6896f-741f-46cc-80f6-834d2e37ea60</rd:ReportID>
</Report>