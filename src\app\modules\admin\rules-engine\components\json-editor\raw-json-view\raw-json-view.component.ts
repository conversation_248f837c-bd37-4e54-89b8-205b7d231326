import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-raw-json-view',
  templateUrl: './raw-json-view.component.html',
  styleUrls: ['./raw-json-view.component.scss']
})
export class RawJsonViewComponent implements OnChanges {
  @Input() jsonData: string = '{}';
  @Output() jsonDataChange = new EventEmitter<string>();
  @Output() jsonErrorChange = new EventEmitter<string | null>();
  @Output() formatRequest = new EventEmitter<void>();

  rawJsonValue: string = '';
  jsonError: string | null = null;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['jsonData']) {
      this.rawJsonValue = this.jsonData;
      this.validateJson();
    }
  }

  /**
   * Handle changes to the raw JSON input
   * @param value The new JSON string
   */
  onRawJsonChange(value: string): void {
    this.rawJsonValue = value;
    this.validateJson();
    this.jsonDataChange.emit(this.rawJsonValue);
  }

  /**
   * Validate the current JSON value
   */
  private validateJson(): void {
    try {
      // Try to parse the JSON to validate it
      JSON.parse(this.rawJsonValue);
      this.jsonError = null;
      this.jsonErrorChange.emit(null);
    } catch (e) {
      this.jsonError = 'Invalid JSON format';
      this.jsonErrorChange.emit('Invalid JSON format');
    }
  }

  /**
   * Request to format the JSON
   */
  onFormatRequest(): void {
    this.formatRequest.emit();
  }
}
