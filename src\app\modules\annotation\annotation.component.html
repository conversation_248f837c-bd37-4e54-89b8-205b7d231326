<div class="PALcontent bodyDiv">
    <h1>
        <span id="lblMeasureHeader">Measure details for {{annotationDetails.demographicProviderName}}</span>
    </h1>
    <div class="DQAuppercontent" style="height: 372px; margin-bottom: 8px; margin-top: 5px; padding: 5px 5px 5px 10px;">
        <div id="divAlertDesc">
            <p>
                <span id="lblMeasureTitle"
                    style="color: rgb(78, 78, 83); font-weight: bold; font-size: 14px; margin-bottom: 10px">
                    {{annotationDetails.annotate.headerName}}</span>
            </p>
            <span id="lblMeasureDesc" style="color: rgb(70, 70, 70); font-size: 13px; font-weight: lighter">
                {{annotationDetails.annotate.detailedDescription}}</span>
        </div>
        <div class="alertDetailsSeperator"></div>
        <div class="DQAlowercontent">
            <div id="pnlMeasureDetails">

                <!-- bind Radiobuttons -->
                <div *ngIf="controlTypes.RadioButton==annotationDetails.annotate?.controlTypeId">
                    <div class="divDynamicControl" *ngFor="let item of annotationDetails.optionsByMeasure">

                        <span IsOptional="False">
                            <input id={{item?.annotateOptionId}} type="radio" name="rdbGroup"
                                value={{item?.annotateOptionId}} [checked]="item?.checked"
                                (click)="optionClick(item?.annotateOptionId)"
                                (change)="changeGender($event)">
                            <label for={{item?.annotateOptionId}}>{{item?.optionDescription}} </label>
                        </span>

                    </div>
                </div>

                <!-- bind checkboxs -->
                <div *ngIf="controlTypes.CheckBox==annotationDetails.annotate?.controlTypeId">
                    <div class="divDynamicControl" *ngFor="let item of annotationDetails.optionsByMeasure">
                        <span IsOptional="False">
                            <input id={{item?.annotateOptionId}} type="checkbox" name="rdbGroup"
                                value={{item?.annotateOptionId}} [checked]="item?.checked"
                                (change)="changeGender($event)">
                            <label for={{item?.annotateOptionId}}>{{item?.optionDescription}} </label>
                        </span>
                    </div>
                </div>

                <!-- bind textbox -->
                <div *ngIf="controlTypes.TextBox==annotationDetails.annotate?.controlTypeId">
                    <div class="divDynamicControl">
                        <textarea name="txtOption" rows="3" cols="20" id="txtOption" style="width:560px;"></textarea>
                    </div>
                </div>

            </div>
        </div>

        <div *ngIf="isTextVisible" id="pnlOptionTextbox" class="divDynamicControl" style=" margin-bottom: 0; padding: 10px 10px 5px 22px;">
            <label id="lblOptionFreeTextTitle"
                style="margin-left: 0px; padding-bottom: 5px;">{{optionFreeTextTitle}}</label>
            <textarea name="txtoptionTextbox" [(ngModel)]="optionTextValue" (input)="optionTextChange($event)" rows="3"
                cols="20" id="txtoptionTextbox" style="width:600px;"></textarea>
        </div>
    </div>


    <div>
        <input type="submit" name="btnSubmit" value="Submit" id="btnSubmit"            [ngClass]="isSubmitButtonEnabled?'CMbtn':'CMbtnDisabled'"
            (click)="submitClick()" style="color:White;height:30px;width:120px;float: right;" />
        <input type="button" id="btnCan" value="Cancel" (click)="closePopup()" class="btnActive" style="width: 120px; height: 30px;" />
    </div>
</div>