import { Component, Injectable, OnInit } from '@angular/core';

@Injectable()
export class mockReport implements OnInit {

  jsonObject: JSON;

  arrayObj: any = [
    {
        "reportId": 1,
        "reportName": "HIV Flowsheet",
        "description": "Patient Flowsheet Report",
        "reportFileName": "PatientFlowsheet.rdl",
        "panelId": 1,
        "ordinal": 1,
        "categoryId": 1,
        "panelLabel": "HIV FLOWSHEET",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 78,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 7,
        "reportName": "HIV Quality Measures",
        "description": "HIV Quality Measures Report",
        "reportFileName": "HHS_Quality_Measures.rdl",
        "panelId": 12,
        "ordinal": 7,
        "categoryId": 1,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 79,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 8,
        "reportName": "Site Profile Report",
        "description": "Site Profile Report",
        "reportFileName": "SitePatientProfile.rdl",
        "panelId": 3,
        "ordinal": 8,
        "categoryId": 2,
        "panelLabel": "CMS REPORTED THAT OUT OF 1.25 MILLION ELIGIBLE PROVIDERS, 460,000 (ALMOST 40%) DID NOT SUBMIT PQRS DATA IN 2013, THE MOST RECENT YEAR FOR WHICH DATA IS AVAILABLE (AS OF 2015). THOSE PROVIDERS WILL LOSE 1.5% IN TOTAL REIMBURSEMENTS FOR CMS PATIENTS OVER THE NEXT YEAR, WHILE THE 642,000 PROVIDERS WHO MET PQRS CRITERIA WILL RECEIVE A 0.5% INCREASE IN CMS REIMBURSEMENTS. CMS ALSO REPORTED THAT OVERALL PARTICIPATION GREW FROM 15% IN 2007 ELIGIBLE PROVIDERS MAY ELECT TO PARTICIPATE IN PQRS AS",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 80,
        "categoryNm": "Population Reports"
    },
    {
        "reportId": 10,
        "reportName": "VACS Population Summary Tables",
        "description": "VACS Population Summary Tables",
        "reportFileName": "VACS_Summary_Tables.rdl",
        "panelId": 3,
        "ordinal": 9,
        "categoryId": 1,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 81,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 11,
        "reportName": "VACS Population Summary Charts",
        "description": "VACS Population Summary Charts",
        "reportFileName": "VACS_Summary_Charts.rdl",
        "panelId": 3,
        "ordinal": 8,
        "categoryId": 1,
        "panelLabel": "PQRS DEVELOPED FROM SEVERAL PIECES OF CONGRESSIONAL LEGISLATION. IN 2006 THE TAX RELIEF AND HEALTH CARE ACT (TRHCA) INCLUDED A PROVISION FOR A 1.5% INCENTIVE PAYMENT TO ELIGIBLE PROVIDERS WHO SUCCESSFULLY SUBMITTED QUALITY DATA TO CMS. THIS PROVISION INCLUDED A CAP ON PAYMENTS. THE 2007 MEDICARE, MEDICAID, AND SCHIP EXTENSION ACT EXTENDED THE PROGRAM THROUGH 2008 AND 2009. IT ALSO REMOVED THE TRHCA PAYMENT CAP. THE MEDICARE IMPROVEMENTS FOR PATIENTS AND PROVIDERS ACT MADE PQRS",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 82,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 12,
        "reportName": "OI Prophylaxis Quality Measure",
        "description": "OI Prophylaxis Quality Measure",
        "reportFileName": "OI_Prophylaxis_Quality_Measure.rdl",
        "panelId": 3,
        "ordinal": 10,
        "categoryId": 1,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 83,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 13,
        "reportName": "Custom Query",
        "description": "Custom Query",
        "reportFileName": "CustomQuery.rdl",
        "panelId": 6,
        "ordinal": 1,
        "categoryId": 3,
        "panelLabel": "PQRS DEVELOPED FROM SEVERAL PIECES OF CONGRESSIONAL LEGISLATION. IN 2006 THE TAX RELIEF AND HEALTH CARE ACT (TRHCA) INCLUDED A PROVISION FOR A 1.5% INCENTIVE PAYMENT TO ELIGIBLE PROVIDERS WHO SUCCESSFULLY SUBMITTED QUALITY DATA TO CMS. THIS PROVISION INCLUDED A CAP ON PAYMENTS. THE 2007 MEDICARE, MEDICAID, AND SCHIP EXTENSION ACT EXTENDED THE PROGRAM THROUGH 2008 AND 2009. IT ALSO REMOVED THE TRHCA PAYMENT CAP. THE MEDICARE IMPROVEMENTS FOR PATIENTS AND PROVIDERS ACT MADE PQRS",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 84,
        "categoryNm": "Custom Queries and Reports"
    },
    {
        "reportId": 15,
        "reportName": "PQRS/MIPS Measures",
        "description": "PQRS/MIPS Measures",
        "reportFileName": "Quality_Measures.rdl",
        "panelId": 8,
        "ordinal": 9,
        "categoryId": 4,
        "panelLabel": "PQRS Report <p></p><i>cms reported that out of 1.25 million eligible providers, did not submit pqrs data in 2013, the most recent year for which data is available (as of 2015). those providers will lose 1.5% in total reimbursements for cms patients over the next year, while the 642,000 providers who met pqrs criteria will receive a 0.5% increase in cms reimbursements. cms also reported that overall participation grew from 15% in 2007 eligible providers may elect to participate in pqrs as</s></i>",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 85,
        "categoryNm": "PQRS Reports"
    },
    {
        "reportId": 20,
        "reportName": "Usage Analytics Report",
        "description": "Usage Analytics Report",
        "reportFileName": "CHORUS_Usage_Report.rdl",
        "panelId": 10,
        "ordinal": 1,
        "categoryId": 5,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 99,
        "categoryNm": "Admin Report"
    },
    {
        "reportId": 16,
        "reportName": "Annotation Status",
        "description": "Annotation Status",
        "reportFileName": "Provider_Annotation_Status.rdl",
        "panelId": 9,
        "ordinal": 1,
        "categoryId": 1,
        "panelLabel": "PQRS developed from several pieces of Congressional legislation. In 2006 the Tax Relief and Health Care Act (TRHCA) included a provision for a 1.5% incentive payment to eligible providers who successfully submitted quality data to CMS. This provision included a cap on payments. The 2007 Medicare, Medicaid, and SCHIP Extension Act extended the program through 2008 and 2009. It also removed the TRHCA payment cap. The Medicare Improvements for Patients and Providers Act made PQRS",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 108,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 5,
        "reportName": "Careware Encounter Data",
        "description": "Careware Encounter Data",
        "reportFileName": "Careware Encounter Data - Expanded 2013.rdl",
        "panelId": 2,
        "ordinal": 5,
        "categoryId": 1,
        "panelLabel": "cms reported that out of 1.25 million eligible providers, 460,000 (almost 40%) did not submit pqrs data in 2013, the most recent year for which data is available (as of 2015). those providers will lose 1.5% in total reimbursements for cms patients over the next year, while the 642,000 providers who met pqrs criteria will receive a 0.5% increase in cms reimbursements. cms also reported that overall participation grew from 15% in 2007 eligible providers may elect to participate in pqrs as",
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 110,
        "categoryNm": "HIV Reports"
    },
    {
        "reportId": 21,
        "reportName": "Retention LFU",
        "description": "Retention LFU",
        "reportFileName": "Provider_Alert_Status.rdl",
        "panelId": 11,
        "ordinal": 1,
        "categoryId": 6,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 118,
        "categoryNm": "Studies"
    },
    {
        "reportId": 18,
        "reportName": "Custom Query Verify",
        "description": null,
        "reportFileName": "CustomQuery_Verify.RDL",
        "panelId": 6,
        "ordinal": 1,
        "categoryId": 3,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 121,
        "categoryNm": "Custom Queries and Reports"
    },
    {
        "reportId": 22,
        "reportName": "Sample PDF Demo",
        "description": "Sample PDF Demo",
        "reportFileName": "SamplePDF.rdl",
        "panelId": null,
        "ordinal": 1,
        "categoryId": 6,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 122,
        "categoryNm": "Studies"
    },
    {
        "reportId": 23,
        "reportName": "CHORUS Users",
        "description": "CHORUS Users",
        "reportFileName": "CHORUS_User_Report.rdl",
        "panelId": null,
        "ordinal": 1,
        "categoryId": 7,
        "panelLabel": null,
        "siteId": 999,
        "customizedFlg": false,
        "siteReportId": 123,
        "categoryNm": "Administration"
    }
]

   constructor() {
    this.jsonObject = <JSON>this.arrayObj;

  }

  public isGet = true;
  public isPost = false;

  ngOnInit(): void {}
}
