import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'pnl-cohorts-reporting-year',
  templateUrl: './pnl-cohorts-reporting-year.component.html',
  styleUrls: ['./pnl-cohorts-reporting-year.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PnlCohortsReportingYearComponent implements OnInit {

  reportPath: string='';
  @Input() rptpanCohorts: FormControl;
  cohortsData: IData[] = [] as IData[];

  @Input() rptpanCohortYears: FormControl;
  cohortYears: IData[] = [] as IData[];
  rptbtnColor: string;
  noSearchPan: boolean = false;
  isCohortSelected : boolean = false;
  isRunDisabled : boolean = true;
  isYearSelected : boolean = false;
  rawdata : IReportParamData[] = [] as IReportParamData[]
  filteredData: IReportParamData[] = [] as IReportParamData[]

  constructor(
    public panelService:PanelService
  ) {
    this.rptpanCohorts = new FormControl;
    this.rptpanCohortYears = new FormControl;
    this.rptbtnColor = this.rptbtnColor= btnColor.btnSecondColor;

    this.panelService.GetPanelData(ReportPanelTypes.pnlCohortsReportingYear).subscribe(s =>
    {
     
      this.rawdata = s;
      this.comboFilter("All");
      this.cohortYears=s[1].data.sort((a, b) => +b.key - +a.key);
    });
  }

  ngOnInit(): void {
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

  checkCohort(text: string): void {
    this.isCohortSelected = text.length > 0;
    this.setButtonColor();
  }

  checkYear(text: string): void {
    this.isYearSelected = text.length > 0;
    this.setButtonColor();
  }


  setButtonColor(): void
  {

    this.isRunDisabled = (!(this.isCohortSelected && this.isYearSelected));

    if (this.isRunDisabled)
    {
      this.rptbtnColor = btnColor.btnSecondColor;
    }
    else
    {
      this.rptbtnColor= btnColor.btnPrimaryColor;
    }
  }

  comboFilter(searchString :string)
  {

   this.filteredData = this.panelService.ComboAutoComplete(searchString, 0, this.rawdata, true);
   this.cohortsData = this.filteredData[0].data.map(value=>value);
   
  }





}
