<div class="rules-engine-container">
  <div class="rules-engine-header">
    <h1>Rules Engine Management</h1>
    <p>Configure and manage rules and workflows for your site.</p>
  </div>

  <div class="rules-engine-content">
    <mat-tab-group>
      <!-- Rules Tab -->
      <mat-tab label="Rules">
        <div class="tab-content">
          <app-rules-list
            [siteId]="siteId"
            [rules]="rules"
            [ruleTypes]="ruleTypes"
            [selectedRuleType]="selectedRuleTypeId"
            (ruleSelected)="onRuleSelected($event)"
            (ruleDeleted)="onRuleDeleted($event)"
            (ruleTypeChanged)="onRuleTypeChanged($event)"
            (refreshRules)="loadRulesByType(selectedRuleType)"
            (createRule)="openRuleDialog()">
          </app-rules-list>
        </div>
      </mat-tab>

      <!-- Workflows Tab -->
      <mat-tab label="Workflows">
        <div class="tab-content">
          <app-workflows-list
            [siteId]="siteId"
            [workflows]="workflows"
            (workflowSelected)="onWorkflowSelected($event)"
            (workflowDeleted)="onWorkflowDeleted($event)"
            (refreshWorkflows)="loadWorkflows()"
            (createWorkflow)="openWorkflowDialog()"
            (executeWorkflow)="executeWorkflow($event)">
          </app-workflows-list>
        </div>
      </mat-tab>

      <!-- Execution History Tab -->
      <mat-tab label="Execution History">
        <div class="tab-content">
          <app-execution-history
            [siteId]="siteId"
            [workflows]="workflows"
            [executionHistory]="workflowProcesses"
            [selectedWorkflowId]="historyFilterWorkflowId"
            (viewProcessDetails)="viewProcessDetails($event)"
            (viewWorkflowStatus)="viewWorkflowStatus($event)"
            (workflowFilterChanged)="onHistoryWorkflowFilterChanged($event)"
            (refreshHistory)="loadWorkflowHistory()">
          </app-execution-history>
          <div *ngIf="loadingHistory">
            <mat-spinner diameter="40"></mat-spinner>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

<!-- Rule Dialog Template -->
<ng-template #ruleDialog>
  <h2 mat-dialog-title>{{isEditMode ? 'Edit Rule' : 'Create New Rule'}}</h2>
  <mat-dialog-content>
    <form [formGroup]="ruleForm">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Name</mat-label>
        <input matInput formControlName="name" placeholder="Enter rule name">
        <mat-error *ngIf="ruleForm.get('name')?.hasError('required')">Name is required</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Type</mat-label>
        <mat-select formControlName="type" [disabled]="isEditMode">
          <mat-option *ngFor="let type of ruleTypes" [value]="type.id">{{type.name}}</mat-option>
        </mat-select>
        <mat-error *ngIf="ruleForm.get('type')?.hasError('required')">Type is required</mat-error>
        <mat-hint *ngIf="isEditMode">Rule type cannot be changed after creation</mat-hint>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" placeholder="Enter rule description" rows="3"></textarea>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Version</mat-label>
        <input matInput formControlName="version" placeholder="Enter version (e.g., 1.0.0)">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Action</mat-label>
        <input matInput formControlName="action" placeholder="Enter rule action">
      </mat-form-field>

      <div class="json-editor-section">
        <h3>Rule Configuration</h3>
        <div *ngIf="loadingRuleDefinition" class="loading-definition">
          <mat-spinner diameter="30"></mat-spinner>
          <span>Loading rule definition...</span>
        </div>
        <div *ngIf="!loadingRuleDefinition && ruleDefinition && ruleDefinition.length > 0">
          <app-json-editor
            [ruleDefinition]="ruleDefinition"
            [ruleData]="ruleForm.get('jsonData')?.value || '{}'"
            (jsonDataChange)="onJsonDataChange($event)">
          </app-json-editor>
        </div>
        <div *ngIf="!loadingRuleDefinition && (!ruleDefinition || ruleDefinition.length === 0)">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>JSON Data</mat-label>
            <textarea matInput formControlName="jsonData" placeholder="Enter rule JSON data" rows="5"></textarea>
            <mat-error *ngIf="ruleForm.get('jsonData')?.hasError('invalidJson')">Invalid JSON format</mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close>Cancel</button>
    <button type="button" mat-raised-button color="primary" [disabled]="ruleForm.invalid" (click)="saveRule()">Save</button>
  </mat-dialog-actions>
</ng-template>

<!-- Workflow Dialog Template -->
<ng-template #workflowDialog>
  <h2 mat-dialog-title>{{isEditMode ? 'Edit Workflow' : 'Create New Workflow'}}</h2>
  <mat-dialog-content>
    <mat-tab-group>
      <mat-tab label="Workflow Details">
        <form [formGroup]="workflowForm" class="workflow-form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter workflow name">
            <mat-error *ngIf="workflowForm.get('name')?.hasError('required')">Name is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Type</mat-label>
            <input matInput formControlName="type" placeholder="Enter workflow type">
            <mat-error *ngIf="workflowForm.get('type')?.hasError('required')">Type is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description</mat-label>
            <textarea matInput formControlName="description" placeholder="Enter workflow description" rows="3"></textarea>
          </mat-form-field>
        </form>
      </mat-tab>

      <mat-tab label="Rule Sequence & Dependencies" *ngIf="isEditMode">
        <div class="rule-sequence-tab">
          <app-workflow-details
            [siteId]="siteId"
            [workflow]="selectedWorkflow"
            [workflowRules]="workflowRules"
            [availableRules]="availableRules"
            (ruleAdded)="addRuleToWorkflow($event)"
            (ruleRemoved)="removeRuleFromWorkflow($event)"
            (rulesReordered)="onWorkflowRulesReordered($event)"
            (saveRules)="saveWorkflowRules()">
          </app-workflow-details>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close>Cancel</button>
    <button type="button" mat-raised-button color="primary" [disabled]="workflowForm.invalid" (click)="saveWorkflow()">Save Workflow</button>
    <button type="button" mat-raised-button color="accent" *ngIf="isEditMode" (click)="saveWorkflowRules()">Save Rule Order</button>
  </mat-dialog-actions>
</ng-template>

<!-- Workflow Rules Dialog Template -->
<ng-template #workflowRulesDialog>
  <h2 mat-dialog-title>Manage Rules for {{selectedWorkflow?.name}}</h2>
  <mat-dialog-content>
    <app-workflow-details
      [siteId]="siteId"
      [workflow]="selectedWorkflow"
      [workflowRules]="workflowRules"
      [availableRules]="availableRules"
      (ruleAdded)="addRuleToWorkflow($event)"
      (ruleRemoved)="removeRuleFromWorkflow($event)"
      (rulesReordered)="onWorkflowRulesReordered($event)"
      (saveRules)="saveWorkflowRules()">
    </app-workflow-details>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close>Close</button>
    <button type="button" mat-raised-button color="primary" (click)="saveWorkflowRules()">Save Changes</button>
  </mat-dialog-actions>
</ng-template>

<!-- Execute Workflow Dialog Template -->
<ng-template #executeWorkflowDialog>
  <h2 mat-dialog-title>Execute Workflow: {{selectedWorkflow?.name}}</h2>
  <mat-dialog-content>
    <div *ngIf="!executionInProgress && !executionCompleted">
      <p>Are you sure you want to execute this workflow?</p>
      <p><strong>Description:</strong> {{selectedWorkflow?.description}}</p>
    </div>

    <div *ngIf="executionInProgress">
      <mat-progress-bar mode="determinate" [value]="executionProgress"></mat-progress-bar>
      <p class="progress-text">{{executionStatus}}</p>
      <p>Executing rule: {{currentExecutingRule}}</p>
    </div>

    <div *ngIf="executionCompleted">
      <div class="execution-result" [ngClass]="{'success': executionSuccess, 'error': !executionSuccess}">
        <mat-icon>{{executionSuccess ? 'check_circle' : 'error'}}</mat-icon>
        <span>{{executionSuccess ? 'Execution completed successfully' : 'Execution failed'}}</span>
      </div>

      <div *ngIf="executionError" class="error-message">
        <p><strong>Error:</strong> {{executionError}}</p>
      </div>

      <div class="execution-details">
        <h3>Execution Details</h3>
        <div *ngFor="let result of executionResults" class="rule-execution-result">
          <div class="rule-header">
            <span class="rule-name">{{result.ruleName}}</span>
            <mat-icon [ngClass]="{'success': result.success, 'error': !result.success}">
              {{result.success ? 'check_circle' : 'error'}}
            </mat-icon>
          </div>
          <div class="rule-execution-time">
            <span>Executed at: {{result.executionTime | date:'medium'}}</span>
          </div>
          <div *ngIf="!result.success" class="rule-error">
            <p>{{result.error}}</p>
          </div>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close *ngIf="!executionInProgress">Close</button>
    <button type="button" mat-raised-button color="primary" (click)="startWorkflowExecution()" *ngIf="!executionInProgress && !executionCompleted">Execute</button>
    <button type="button" mat-raised-button color="warn" (click)="cancelExecution()" *ngIf="executionInProgress">Cancel</button>
  </mat-dialog-actions>
</ng-template>

<!-- Process Details Dialog Template -->
<ng-template #processDetailsDialog>
  <h2 mat-dialog-title>Workflow Execution Details</h2>
  <mat-dialog-content>
    <app-execution-details
      [selectedProcess]="selectedProcess"
      [processRuleResults]="processRuleResults"
      [loadingProcessDetails]="loadingProcessDetails">
    </app-execution-details>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close>Close</button>
    <button type="button" mat-raised-button color="primary" (click)="rerunWorkflow(selectedProcess || undefined)" *ngIf="selectedProcess?.isComplete">
      <mat-icon>replay</mat-icon> Re-run Workflow
    </button>
  </mat-dialog-actions>
</ng-template>

<!-- Workflow Status Dialog Template -->
<ng-template #workflowStatusDialog>
  <h2 mat-dialog-title>Workflow Execution Details</h2>
  <mat-dialog-content>
    <app-workflow-status
      [workflowStatus]="workflowStatus"
      [selectedProcess]="selectedProcess"
      [loadingWorkflowStatus]="loadingWorkflowStatus"
      [rules]="rules"
      (refreshStatus)="refreshWorkflowStatus()">
    </app-workflow-status>
  </mat-dialog-content>
</ng-template>
