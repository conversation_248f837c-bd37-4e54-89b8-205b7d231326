import { IPatientsRegimen } from "../pnl-custom-query/regimen-builder/regimen-builder.models";

export interface ICustomQueryParamOptions {
  sex: ICustomQueryOption[];
  ethnicity: ICustomQueryOption[];
  race: ICustomQueryOption[];
  patientStatus: ICustomQueryOption[];
  patientLocation: ICustomQueryOption[];
  patientProvider: ICustomQueryOption[];
}


export interface ICustomQueryOption {
  code: string;
  description: string;
}

export class PatientSelectionCriteria {
  Sex: string = "-1";
  Ethnicity: string = "-1";
  PatientRace: string = "-1";
  Age: string = "|";
  HivStatus: string = "|";
  ViralLoad: string = "|";
  CDCount: string = "|";
  NadirCD4Count: string = "|";
  HCVStatus: string = "-1";
  ARTMedication: string = "-1";
  HCVMedication: string = "-1";
  PatientStatus: string = "-1";
  LastActiveVisit: string = "-1";
  PatientLocation: string = "-1";
  PrimaryProvider: string = "-1";
}

export interface RegimentSelectionCriteria {
  Id: string,
  levelId: Number,
  MedicationGroup: string,
  Status: Number,
  Brandname: string,
  Option: Number,
  BrandId: string,
  discontinuedLookback: Number,
  discontinuedRDBtnVal: Number,
  currentlookBack: string
}


export interface ICustomQueryParms {
  PatientSelectionCriteria: PatientSelectionCriteria[];
  RegimenSelection: IPatientsRegimen[];
}


export class PatientSelectionCriteriaInitList {
  CustomQueryDDLPatientSex: string = '';
  CustomQueryDDLPatientEthnicity: string = '';
  CustomQueryDDLPatientRace: string = '';
  CustomQueryDDLPatientAge: string = '';
  CustomQueryDDLHivStatus: string = '';
  CustomQueryDDLHCVStatus: string ='';
  CustomQueryDDLLastHIVViralLoad: string = '';
  CustomQueryDDLCDCount: string = '';
  CustomQueryDDLNadirCD4Count: string = '';
  CustomQueryDDLPatientStatus: string = '';
  CustomQueryDDLLastActiveVisit: string = '';
  CustomQueryDDLPatientLocation: string = '';
  CustomQueryDDLPrimaryProvider: string = '';
  CustomQueryDDLARTMedication: string = '';
  CustomQueryDDLHCVMedication: string = '';
}



export class CriteriaSummaryResults {
  column1: string = "";
  column2: string = "";
  column3: string = "";
}
