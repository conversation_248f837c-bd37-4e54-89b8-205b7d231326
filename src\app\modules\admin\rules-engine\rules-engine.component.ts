import { Component, OnInit, ViewChild, TemplateRef, AfterViewInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { RulesEngineService } from '../services/rules-engine.service';
import { ToastrService } from 'ngx-toastr';
import {
  Rule,
  Workflow,
  RuleExecutionResult,
  RuleType,
  WorkflowProcess,
  WorkflowStatus,
  ExecutorTransaction,
  RuleDetail,
  WorkflowStatusDetail,
  WorkflowRuleDependency
} from '../models/rule.model';
import { RuleDefinitionModel } from '../models/rule-definition.model';
import { Observable, of, interval, forkJoin } from 'rxjs';
import { takeWhile, switchMap, finalize, catchError } from 'rxjs/operators';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';

@Component({
  selector: 'app-rules-engine',
  templateUrl: './rules-engine.component.html',
  styleUrls: ['./rules-engine.component.scss'],
})
export class RulesEngineComponent implements OnInit, AfterViewInit {
  // Site ID
  siteId: string = '';

  // Rules data
  rules: Rule[] = [];
  filteredRules: Rule[] = [];
  ruleSearchText: string = '';
  ruleColumns: string[] = ['ruleID', 'name', 'description', 'version', 'actions'];
  selectedRuleType: string = '';
  selectedRuleTypeId: number | null = null;
  loadingRules: boolean = false;

  // Workflows data
  workflows: Workflow[] = [];
  filteredWorkflows: Workflow[] = [];
  workflowSearchText: string = '';
  workflowColumns: string[] = ['workflowID', 'name', 'type', 'description', 'actions'];
  loadingWorkflowIds: number[] = [];

  // Rule types
  ruleTypes: RuleType[] = [];

  // Form data
  ruleForm: FormGroup;
  workflowForm: FormGroup;
  isEditMode: boolean = false;
  selectedRule: Rule | null = null;
  selectedWorkflow: Workflow | null = null;

  // Workflow rules management
  workflowRules: Rule[] = [];
  availableRules: Rule[] = [];
  filteredAvailableRules: Rule[] = [];
  availableRuleSearchText: string = '';

  // Execution data
  executionInProgress: boolean = false;
  executionCompleted: boolean = false;
  executionProgress: number = 0;
  executionStatus: string = '';
  currentExecutingRule: string = '';
  executionSuccess: boolean = false;
  executionError: string = '';
  executionResults: RuleExecutionResult[] = [];
  executionGuid: string = '';

  // Execution History data
  workflowProcesses: WorkflowProcess[] = [];
  filteredWorkflowProcesses: MatTableDataSource<WorkflowProcess> = new MatTableDataSource<WorkflowProcess>([]);
  historyColumns: string[] = ['processId', 'workflow', 'startTime', 'duration', 'status', 'actions'];
  historyFilterWorkflowId: number | null = null;
  historyFilterStatus: string | null = null;
  historyFilterStartDate: Date | null = null;
  historyFilterEndDate: Date | null = null;
  loadingHistory: boolean = false;

  // Process Details data
  selectedProcess: WorkflowProcess | null = null;
  processRuleResults: RuleExecutionResult[] = [];
  loadingProcessDetails: boolean = false;

  // Workflow Status data
  workflowStatus: WorkflowStatus | null = null;
  loadingWorkflowStatus: boolean = false;

  // Rule Definition data
  ruleDefinition: RuleDefinitionModel[] = [];
  loadingRuleDefinition: boolean = false;

  // Dialog templates
  @ViewChild('ruleDialog') ruleDialog!: TemplateRef<unknown>;
  @ViewChild('workflowDialog') workflowDialog!: TemplateRef<unknown>;
  @ViewChild('workflowRulesDialog') workflowRulesDialog!: TemplateRef<unknown>;
  @ViewChild('executeWorkflowDialog') executeWorkflowDialog!: TemplateRef<unknown>;
  @ViewChild('processDetailsDialog') processDetailsDialog!: TemplateRef<unknown>;
  @ViewChild('workflowStatusDialog') workflowStatusDialog!: TemplateRef<unknown>;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private layoutService: LayoutService,
    private userContext: UserContext,
    private rulesService: RulesEngineService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private toastr: ToastrService
  ) {
    // Initialize forms
    this.ruleForm = this.formBuilder.group({
      ruleID: [0],
      name: ['', Validators.required],
      description: [''],
      type: ['', Validators.required],
      version: ['1.0.0'],
      action: [''],
      jsonData: ['{}', [this.validateJson]]
    });

    this.workflowForm = this.formBuilder.group({
      workflowID: [0],
      name: ['', Validators.required],
      description: [''],
      type: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.layoutService.showSpinner();
    this.siteId = this.userContext.GetCurrentSiteValue().toString();

    // Load rule types first
    this.loadRuleTypes();

    // Load workflows
    this.loadWorkflows();

    // Subscribe to rule type changes in the form
    this.ruleForm.get('type')?.valueChanges.subscribe(ruleType => {
      if (ruleType) {
        this.loadRuleDefinition(ruleType);
      } else {
        this.ruleDefinition = [];
      }
    });

    // Load workflow execution history
    this.loadWorkflowHistory();
  }

  ngAfterViewInit(): void {
    // Set up paginator for workflow processes
    if (this.paginator) {
      this.filteredWorkflowProcesses.paginator = this.paginator;
    }
  }

  // Rule types methods
  loadRuleTypes(): void {
    this.rulesService.getRuleTypes().subscribe(
      (data) => {
        this.ruleTypes = data;
        this.layoutService.hideSpinner();
      },
      (error) => {
        console.error('Error loading rule types:', error);
        this.layoutService.hideSpinner();
      }
    );
  }

  onRuleTypeChange(): void {
    if (this.selectedRuleType) {
      this.loadRulesByType(this.selectedRuleType);
    } else {
      this.rules = [];
      this.filteredRules = [];
    }
  }

  getSelectedRuleTypeDescription(): string {
    if (!this.selectedRuleType) return '';
    const ruleType = this.ruleTypes.find(type => type.id === this.selectedRuleType);
    return ruleType ? ruleType.description : '';
  }

  // Rules methods
  loadRulesByType(ruleType: string): void {
    this.loadingRules = true;
    this.rules = [];
    this.filteredRules = [];

    this.rulesService.getRules({ ruleType }).subscribe(
      (data) => {
        this.rules = data;
        this.filteredRules = [...this.rules];
        this.loadingRules = false;
      },
      (error) => {
        console.error(`Error loading rules for type ${ruleType}:`, error);
        this.loadingRules = false;
      }
    );
  }


  filterRules(): void {
    if (!this.ruleSearchText) {
      this.filteredRules = [...this.rules];
      return;
    }

    const searchText = this.ruleSearchText.toLowerCase();
    this.filteredRules = this.rules.filter(rule =>
      rule.name.toLowerCase().includes(searchText) ||
      (rule.description && rule.description.toLowerCase().includes(searchText))
    );
  }

  openRuleDialog(): void {
    this.isEditMode = false;
    this.selectedRule = null;
    this.ruleForm.reset({
      ruleID: 0,
      type: this.selectedRuleType,
      version: '1.0.0',
      jsonData: '{}'
    });

    // Load rule definition for the selected rule type
    this.loadRuleDefinition(this.selectedRuleType);

    this.dialog.open(this.ruleDialog, {
      width: '800px'
    });
  }

  /**
   * Load rule definition for a specific rule type
   * @param ruleType The rule type to load definition for
   */
  loadRuleDefinition(ruleType: string): void {
    if (!ruleType) {
      this.ruleDefinition = [];
      return;
    }

    this.loadingRuleDefinition = true;
    this.ruleDefinition = [];

    this.rulesService.getRuleDefinitionByType(ruleType).subscribe({
      next: (schema) => {
        console.log('Rule definition schema:', schema);
        this.ruleDefinition = this.rulesService.parseRuleDefinition(schema);
        console.log('Parsed rule definition:', this.ruleDefinition);
        this.loadingRuleDefinition = false;
      },
      error: (error) => {
        console.error(`Error loading rule definition for type ${ruleType}:`, error);
        this.loadingRuleDefinition = false;
        this.toastr.error('Failed to load rule definition', 'Error');
      }
    });
  }

  editRule(rule: Rule): void {
    this.isEditMode = true;
    this.selectedRule = rule;
    this.ruleForm.patchValue({
      ruleID: rule.ruleID,
      name: rule.name,
      description: rule.description,
      type: rule.type,
      version: rule.version,
      action: rule.action,
      jsonData: rule.jsonData
    });

    // Load rule definition for the selected rule type
    this.loadRuleDefinition(rule.type);

    this.dialog.open(this.ruleDialog, {
      width: '800px'
    });
  }

  /**
   * Handle JSON data changes from the JSON editor
   * @param jsonData The updated JSON data
   */
  onJsonDataChange(jsonData: string): void {
    // Update the form control with the new JSON data
    this.ruleForm.patchValue({
      jsonData: jsonData
    });

    // Mark the form control as dirty to trigger validation
    this.ruleForm.get('jsonData')?.markAsDirty();
  }

  saveRule(): void {
    if (this.ruleForm.invalid) {
      return;
    }

    const ruleData: Rule = {
      ...this.ruleForm.value,
      createdDate: new Date(),
      modifiedDate: new Date()
    };

    // Show spinner while saving
    this.layoutService.showSpinner();

    if (this.isEditMode && this.selectedRule) {
      // Update existing rule
      this.rulesService.updateRule(ruleData.ruleID, ruleData).subscribe(
        (updatedRule) => {
          console.log('Rule update response:', updatedRule);
          this.layoutService.hideSpinner();

          // Check if updatedRule is null or undefined
          if (updatedRule) {
            const index = this.rules.findIndex(r => r.ruleID === updatedRule.ruleID);
            if (index !== -1) {
              this.rules[index] = updatedRule;
              this.filteredRules = [...this.rules];
            }
            this.toastr.success('Rule updated successfully', 'Success');
          } else {
            // If the server returns null/undefined but the operation was successful
            // Update the local rule data
            const index = this.rules.findIndex(r => r.ruleID === ruleData.ruleID);
            if (index !== -1) {
              this.rules[index] = ruleData;
              this.filteredRules = [...this.rules];
            }
            this.toastr.success('Rule updated successfully', 'Success');
          }

          // Refresh the rules list to ensure we have the latest data
          this.loadRulesByType(this.selectedRuleType);

          this.dialog.closeAll();
        },
        (error) => {
          console.error('Error updating rule:', error);
          this.layoutService.hideSpinner();
          this.toastr.error('Failed to update rule', 'Error');

          // For demo purposes, update locally if API fails
          const index = this.rules.findIndex(r => r.ruleID === ruleData.ruleID);
          if (index !== -1) {
            this.rules[index] = ruleData;
            this.filteredRules = [...this.rules];
          }
          this.dialog.closeAll();
        }
      );
    } else {
      // Create new rule
      this.rulesService.createRule(ruleData).subscribe(
        (newRule) => {
          this.layoutService.hideSpinner();
          this.toastr.success('Rule created successfully', 'Success');

          this.rules.push(newRule);
          this.filteredRules = [...this.rules];

          // Refresh the rules list to ensure we have the latest data
          this.loadRulesByType(this.selectedRuleType);

          this.dialog.closeAll();
        },
        (error) => {
          console.error('Error creating rule:', error);
          this.layoutService.hideSpinner();
          this.toastr.error('Failed to create rule', 'Error');

          // For demo purposes, create locally if API fails
          const newRule = {
            ...ruleData,
            ruleID: Math.max(...this.rules.map(r => r.ruleID), 0) + 1
          };
          this.rules.push(newRule);
          this.filteredRules = [...this.rules];
          this.dialog.closeAll();
        }
      );
    }
  }

  deleteRule(rule: Rule): void {
    if (confirm(`Are you sure you want to delete the rule "${rule.name}"?`)) {
      this.layoutService.showSpinner();
      this.rulesService.deleteRule(rule.ruleID).subscribe(
        () => {
          this.layoutService.hideSpinner();
          this.toastr.success(`Rule "${rule.name}" deleted successfully`, 'Success');

          this.rules = this.rules.filter(r => r.ruleID !== rule.ruleID);
          this.filteredRules = [...this.rules];

          // Refresh the rules list to ensure we have the latest data
          this.loadRulesByType(this.selectedRuleType);
        },
        (error) => {
          this.layoutService.hideSpinner();
          console.error('Error deleting rule:', error);
          this.toastr.error(`Failed to delete rule "${rule.name}"`, 'Error');

          // For demo purposes, delete locally if API fails
          this.rules = this.rules.filter(r => r.ruleID !== rule.ruleID);
          this.filteredRules = [...this.rules];
        }
      );
    }
  }

  executeRule(rule: Rule): void {
    // This would typically call the API to execute the rule
    alert(`Rule "${rule.name}" execution would be implemented here.`);
  }

  // Workflows methods
  loadWorkflows(): void {
    this.rulesService.getWorkflows().subscribe(
      (data) => {
        this.workflows = data;
        this.filteredWorkflows = [...this.workflows];
      },
      (error) => {
        console.error('Error loading workflows:', error);

      }
    );
  }


  openWorkflowDialog(): void {
    this.isEditMode = false;
    this.selectedWorkflow = null;
    this.workflowForm.reset({
      workflowID: 0
    });
    this.dialog.open(this.workflowDialog, {
      width: '600px'
    });
  }

  editWorkflow(workflow: Workflow): void {
    this.isEditMode = true;
    this.selectedWorkflow = workflow;
    this.workflowForm.patchValue({
      workflowID: workflow.workflowID,
      name: workflow.name,
      description: workflow.description,
      type: workflow.type
    });

    // Load workflow rules for editing sequence and dependencies
    this.loadWorkflowRules(workflow.workflowID);

    this.dialog.open(this.workflowDialog, {
      width: '850px',
      height: '750px',
      autoFocus: false,
      disableClose: false,
      panelClass: 'workflow-dialog'
    });
  }

  saveWorkflow(): void {
    if (this.workflowForm.invalid) {
      return;
    }

    const workflowData: Workflow = {
      ...this.workflowForm.value,
      createdDate: new Date(),
      modifiedDate: new Date()
    };

    // Show spinner while saving
    this.layoutService.showSpinner();

    if (this.isEditMode && this.selectedWorkflow) {
      // Update existing workflow
      this.rulesService.updateWorkflow(workflowData.workflowID, workflowData).subscribe(
        (updatedWorkflow) => {
          console.log('Workflow update response:', updatedWorkflow);
          this.layoutService.hideSpinner();

          // Check if updatedWorkflow is null or undefined
          if (updatedWorkflow) {
            const index = this.workflows.findIndex(w => w.workflowID === updatedWorkflow.workflowID);
            if (index !== -1) {
              this.workflows[index] = updatedWorkflow;
              this.filteredWorkflows = [...this.workflows];
            }
            this.toastr.success('Workflow updated successfully', 'Success');
          } else {
            // If the server returns null/undefined but the operation was successful
            // Update the local workflow data
            const index = this.workflows.findIndex(w => w.workflowID === workflowData.workflowID);
            if (index !== -1) {
              this.workflows[index] = workflowData;
              this.filteredWorkflows = [...this.workflows];
            }
            this.toastr.success('Workflow updated successfully', 'Success');
          }

          // Refresh the workflows list to ensure we have the latest data
          this.loadWorkflows();

          this.dialog.closeAll();
        },
        (error) => {
          console.error('Error updating workflow:', error);
          this.layoutService.hideSpinner();
          this.toastr.error('Failed to update workflow', 'Error');

          // For demo purposes, update locally if API fails
          const index = this.workflows.findIndex(w => w.workflowID === workflowData.workflowID);
          if (index !== -1) {
            this.workflows[index] = workflowData;
            this.filteredWorkflows = [...this.workflows];
          }
          this.dialog.closeAll();
        }
      );
    } else {
      // Create new workflow
      this.rulesService.createWorkflow(workflowData).subscribe(
        (newWorkflow) => {
          this.layoutService.hideSpinner();
          this.toastr.success('Workflow created successfully', 'Success');

          this.workflows.push(newWorkflow);
          this.filteredWorkflows = [...this.workflows];

          // Refresh the workflows list to ensure we have the latest data
          this.loadWorkflows();

          this.dialog.closeAll();
        },
        (error) => {
          console.error('Error creating workflow:', error);
          this.layoutService.hideSpinner();
          this.toastr.error('Failed to create workflow', 'Error');

          // For demo purposes, create locally if API fails
          const newWorkflow = {
            ...workflowData,
            workflowID: Math.max(...this.workflows.map(w => w.workflowID), 0) + 1
          };
          this.workflows.push(newWorkflow);
          this.filteredWorkflows = [...this.workflows];
          this.dialog.closeAll();
        }
      );
    }
  }

  deleteWorkflow(workflow: Workflow): void {
    if (confirm(`Are you sure you want to delete the workflow "${workflow.name}"?`)) {
      this.layoutService.showSpinner();
      this.rulesService.deleteWorkflow(workflow.workflowID).subscribe(
        () => {
          this.layoutService.hideSpinner();
          this.toastr.success(`Workflow "${workflow.name}" deleted successfully`, 'Success');

          this.workflows = this.workflows.filter(w => w.workflowID !== workflow.workflowID);
          this.filteredWorkflows = [...this.workflows];

          // Refresh the workflows list to ensure we have the latest data
          this.loadWorkflows();
        },
        (error) => {
          this.layoutService.hideSpinner();
          console.error('Error deleting workflow:', error);
          this.toastr.error(`Failed to delete workflow "${workflow.name}"`, 'Error');

          // For demo purposes, delete locally if API fails
          this.workflows = this.workflows.filter(w => w.workflowID !== workflow.workflowID);
          this.filteredWorkflows = [...this.workflows];
        }
      );
    }
  }

  // Workflow rules management
  manageWorkflowRules(workflow: Workflow): void {
    this.selectedWorkflow = workflow;
    this.loadWorkflowRules(workflow.workflowID);
    this.dialog.open(this.workflowRulesDialog, {
      width: '850px',
      height: '550px',
      autoFocus: false,
      disableClose: false,
      panelClass: 'workflow-dialog'
    });
  }

  loadWorkflowRules(workflowId: number): void {
    this.layoutService.showSpinner();
    this.rulesService.getWorkflowRules(workflowId).subscribe(
      (rules) => {
        this.workflowRules = rules;
        this.loadAllRulesForWorkflow();
        this.layoutService.hideSpinner();
      },
      (error) => {
        console.error('Error loading workflow rules:', error);
        this.layoutService.hideSpinner();
      }
    );
  }

  loadAllRulesForWorkflow(): void {
    // We need to load all rules from all rule types to show in the available rules list
    this.availableRules = [];
    this.filteredAvailableRules = [];

    // Load rules for each rule type
    const loadRulePromises = this.ruleTypes.map(ruleType => {
      return new Promise<void>((resolve) => {
        this.rulesService.getRules({ ruleType: ruleType.id }).subscribe(
          (rules) => {
            // Add these rules to the available rules list
            this.availableRules = [...this.availableRules, ...rules];
            resolve();
          },
          (error) => {
            console.error(`Error loading rules for type ${ruleType.id}:`, error);
          }
        );
      });
    });

    // When all rule types are loaded, update the available rules
    Promise.all(loadRulePromises).then(() => {
      this.updateAvailableRules();
    });
  }

  updateAvailableRules(): void {
    // Filter out rules that are already in the workflow
    const workflowRuleIds = this.workflowRules.map(r => r.ruleID);
    this.availableRules = this.availableRules.filter(r => !workflowRuleIds.includes(r.ruleID));
    this.filteredAvailableRules = [...this.availableRules];

    // Apply search filter if there is one
    this.filterAvailableRules();
  }

  filterAvailableRules(): void {
    if (!this.availableRuleSearchText) {
      this.filteredAvailableRules = [...this.availableRules];
      return;
    }

    const searchText = this.availableRuleSearchText.toLowerCase();
    this.filteredAvailableRules = this.availableRules.filter(rule =>
      rule.name.toLowerCase().includes(searchText) ||
      (rule.description && rule.description.toLowerCase().includes(searchText)) ||
      (rule.type && rule.type.toLowerCase().includes(searchText))
    );
  }

  addRuleToWorkflow(rule: Rule): void {
    if (!this.selectedWorkflow) return;

    this.rulesService.assignRuleToWorkflow(this.selectedWorkflow.workflowID, rule.ruleID).subscribe(
      () => {
        this.workflowRules.push(rule);
        this.updateAvailableRules();
      },
      (error) => {
        console.error('Error adding rule to workflow:', error);

        // For demo purposes, update locally if API fails
        this.workflowRules.push(rule);
        this.updateAvailableRules();
      }
    );
  }

  removeRuleFromWorkflow(rule: Rule): void {
    if (!this.selectedWorkflow) return;

    this.rulesService.removeRuleFromWorkflow(this.selectedWorkflow.workflowID, rule.ruleID).subscribe(
      () => {
        this.workflowRules = this.workflowRules.filter(r => r.ruleID !== rule.ruleID);
        this.updateAvailableRules();
      },
      (error) => {
        console.error('Error removing rule from workflow:', error);

        // For demo purposes, update locally if API fails
        this.workflowRules = this.workflowRules.filter(r => r.ruleID !== rule.ruleID);
        this.updateAvailableRules();
      }
    );
  }

  dropRule(event: CdkDragDrop<Rule[]>): void {
    if (event.previousIndex === event.currentIndex) return;

    moveItemInArray(this.workflowRules, event.previousIndex, event.currentIndex);
  }

  saveWorkflowRules(): void {
    if (!this.selectedWorkflow) return;

    // Create a map of rule IDs to their new order
    const ruleOrders: Record<number, number> = {};
    this.workflowRules.forEach((rule, index) => {
      ruleOrders[rule.ruleID] = index + 1;
    });

    this.rulesService.updateRuleOrder(this.selectedWorkflow.workflowID, ruleOrders).subscribe(
      () => {
        this.toastr.success('Workflow rules saved successfully', 'Success');
        this.dialog.closeAll();
      },
      (error) => {
        console.error('Error saving rule order:', error);
        this.toastr.error('Failed to save workflow rules', 'Error');

        // For demo purposes, close dialog anyway if API fails
        this.dialog.closeAll();
      }
    );
  }

  /**
   * Handle reordered rules from the workflow-details component
   * @param rules The reordered rules array
   */
  onWorkflowRulesReordered(rules: Rule[]): void {
    if (!this.selectedWorkflow) return;

    // Update the workflowRules array with the new order
    this.workflowRules = [...rules];
  }

  // Workflow execution
  executeWorkflow(workflow: Workflow): void {
    this.selectedWorkflow = workflow;
    this.executionInProgress = false;
    this.executionCompleted = false;
    this.executionProgress = 0;
    this.executionStatus = '';
    this.currentExecutingRule = '';
    this.executionSuccess = false;
    this.executionError = '';
    this.executionResults = [];
    this.executionGuid = this.generateGuid();

    this.dialog.open(this.executeWorkflowDialog, {
      width: '700px',
      disableClose: true
    });
  }

  startWorkflowExecution(): void {
    if (!this.selectedWorkflow) return;

    this.executionInProgress = true;
    this.executionStatus = 'Starting workflow execution...';

    this.rulesService.executeWorkflow(this.siteId, this.selectedWorkflow.workflowID, this.executionGuid).subscribe({
      next: () => {
        this.pollExecutionStatus();
      },
      error: (error) => {
        console.error('Error executing workflow:', error);
        this.executionInProgress = false;
        this.executionCompleted = true;
        this.executionSuccess = false;
        this.executionError = 'Failed to execute workflow. Please try again later.';
      }
    });
  }

  pollExecutionStatus(): void {
    if (!this.selectedWorkflow) return;

    // Poll the workflow status every 2 seconds
    interval(2000)
      .pipe(
        takeWhile(() => this.executionInProgress),
        switchMap(() => this.getWorkflowStatus())
      )
      .subscribe({
        next: (status) => {
          this.updateExecutionStatus(status);
        },
        error: (error) => {
          console.error('Error polling workflow status:', error);
          this.executionInProgress = false;
          this.executionCompleted = true;
          this.executionSuccess = false;
          this.executionError = 'Failed to get workflow execution status.';
        }
      });
  }

  getWorkflowStatus(): Observable<any> {
    if (!this.selectedWorkflow) return of(null);

    return this.rulesService.getWorkflowStatus(this.siteId, this.selectedWorkflow.workflowID, this.executionGuid);
  }

  updateExecutionStatus(status: any): void {
    if (!status) return;

    this.executionProgress = status.progress;
    this.executionStatus = status.status;
    this.currentExecutingRule = status.currentRule || '';

    if (status.progress >= 100) {
      this.executionInProgress = false;
      this.executionCompleted = true;
      this.executionSuccess = true;
      this.loadExecutionResults();
    }
  }

  loadExecutionResults(): void {
    if (!this.selectedWorkflow || !this.executionGuid) return;

    // Get the execution results from the API
    this.rulesService.getWorkflowExecutionResults(this.siteId, this.selectedWorkflow.workflowID, this.executionGuid)
      .subscribe({
        next: (results) => {
          this.executionResults = results;

          // Check if any rules failed
          this.executionSuccess = this.executionResults.every(result => result.success);
          if (!this.executionSuccess) {
            const failedRules = this.executionResults.filter(result => !result.success);
            this.executionError = `${failedRules.length} rule(s) failed during execution.`;
          }
        },
        error: (error) => {
          console.error('Error loading execution results:', error);
          this.executionResults = [];
          this.executionSuccess = false;
          this.executionError = 'Failed to load execution results.';
        }
      });
  }

  cancelExecution(): void {
    this.executionInProgress = false;
    this.dialog.closeAll();
  }

  // Helper methods
  validateJson(control: any): { [key: string]: any } | null {
    if (!control.value) {
      return null;
    }

    try {
      JSON.parse(control.value);
      return null;
    } catch (e) {
      return { 'invalidJson': true };
    }
  }

  generateGuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validates if a string is a valid GUID format
   * @param guid The string to validate
   * @returns True if the string is a valid GUID format, false otherwise
   */
  isValidGuid(guid: string): boolean {
    if (!guid) return false;

    // Regular expression for GUID format
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return guidRegex.test(guid);
  }

  /**
   * Formats a GUID to display only the last section
   * @param guid The full GUID string
   * @returns The last section of the GUID
   */
  formatGuid(guid?: string): string {
    if (!guid) return '';

    // Debug information
    console.log('Formatting GUID:', guid);

    // Validate that it's a proper GUID
    if (!this.isValidGuid(guid)) {
      console.warn('Invalid GUID format:', guid);
      return guid.length > 8 ? guid.substring(guid.length - 8) : guid;
    }

    // Split the GUID by hyphens and get the last part
    const parts = guid.split('-');
    if (parts.length > 0) {
      return parts[parts.length - 1];
    }

    // If the GUID doesn't have hyphens, return the last 8-12 characters
    return guid.length > 8 ? guid.substring(guid.length - 8) : guid;
  }



  // Execution History methods
  loadWorkflowHistory(): void {
    this.loadingHistory = true;

    // Prepare filter options
    const options: {
      workflowId?: number;
      startDate?: Date;
      endDate?: Date;
      includeDetails?: boolean;
    } = {
      includeDetails: true
    };

    if (this.historyFilterWorkflowId !== null) {
      options.workflowId = this.historyFilterWorkflowId;
    }

    if (this.historyFilterStartDate) {
      options.startDate = this.historyFilterStartDate;
    }

    if (this.historyFilterEndDate) {
      options.endDate = this.historyFilterEndDate;
    }

    this.rulesService.getWorkflowProcessHistory(this.siteId, options).subscribe({
      next: (data) => {
        this.workflowProcesses = data;
        this.filterWorkflowHistory();
        this.loadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading workflow history:', error);
        this.loadingHistory = false;

        // Show error message to user
        alert('Failed to load workflow history. Please try again later.');
      }
    });
  }



  filterWorkflowHistory(): void {
    let filtered = [...this.workflowProcesses];

    // Apply status filter if selected
    if (this.historyFilterStatus) {
      if (this.historyFilterStatus === 'Completed') {
        filtered = filtered.filter(process => process.isComplete && process.isSuccess);
      } else if (this.historyFilterStatus === 'Failed') {
        filtered = filtered.filter(process => process.isComplete && !process.isSuccess);
      } else if (this.historyFilterStatus === 'Running') {
        filtered = filtered.filter(process => !process.isComplete);
      }
    }

    // Update the data source
    this.filteredWorkflowProcesses.data = filtered;

    // Reset paginator if it exists
    if (this.filteredWorkflowProcesses.paginator) {
      this.filteredWorkflowProcesses.paginator.firstPage();
    }
  }

  resetHistoryFilters(): void {
    this.historyFilterWorkflowId = null;
    this.historyFilterStatus = null;
    this.historyFilterStartDate = null;
    this.historyFilterEndDate = null;
    this.loadWorkflowHistory();
  }

  getWorkflowName(workflowId?: number, workflowName?: string): string {
    // If workflowName is provided, use it
    if (workflowName) return workflowName;

    // If no workflowId, return Unknown
    if (!workflowId) return 'Unknown';

    // First check if the workflow is in our loaded workflows array
    const workflow = this.workflows.find(w => w.workflowID === workflowId);
    if (workflow) {
      return workflow.name;
    }

    // If we don't have the workflow in our array, try to load it
    // For now, return the ID as a fallback
    this.loadWorkflowById(workflowId);
    return `Workflow ${workflowId}`;
  }

  loadWorkflowById(workflowId: number): void {
    // Check if we already tried to load this workflow to avoid multiple calls
    if (this.loadingWorkflowIds.includes(workflowId)) {
      return;
    }

    this.loadingWorkflowIds.push(workflowId);

    this.rulesService.getWorkflowById(workflowId).subscribe({
      next: (workflow) => {
        // Add the workflow to our array if it's not already there
        if (!this.workflows.some(w => w.workflowID === workflow.workflowID)) {
          this.workflows.push(workflow);

          // Update any displayed processes that use this workflow
          this.filteredWorkflowProcesses.data = [...this.filteredWorkflowProcesses.data];
        }

        // Remove from loading array
        const index = this.loadingWorkflowIds.indexOf(workflowId);
        if (index !== -1) {
          this.loadingWorkflowIds.splice(index, 1);
        }
      },
      error: (error) => {
        console.error(`Error loading workflow ${workflowId}:`, error);

        // Remove from loading array
        const index = this.loadingWorkflowIds.indexOf(workflowId);
        if (index !== -1) {
          this.loadingWorkflowIds.splice(index, 1);
        }
      }
    });
  }

  calculateDuration(process?: WorkflowProcess): string {
    if (!process) return 'N/A';

    // Use firstExecutionTime and lastExecutionTime if available, otherwise fall back to startTime and endTime
    const startTimeField = process.firstExecutionTime || process.startTime;
    const endTimeField = process.lastExecutionTime || process.endTime;

    if (!startTimeField) return 'N/A';

    const start = new Date(startTimeField).getTime();
    const end = endTimeField ? new Date(endTimeField).getTime() : new Date().getTime();

    const durationMs = end - start;
    const seconds = Math.floor(durationMs / 1000);

    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes} min ${remainingSeconds} sec`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const remainingMinutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hr ${remainingMinutes} min`;
    }
  }

  getStatusText(process?: WorkflowProcess): string {
    if (!process) return 'Unknown';

    // Use lastExecutionTime to determine if the process is still running
    const lastExecTime = process.lastExecutionTime || process.endTime;

    if (!process.isComplete) {
      // Check if it's been running for more than an hour
      if (!lastExecTime) {
        const startTime = process.firstExecutionTime || process.startTime;
        if (startTime) {
          const startTimeMs = new Date(startTime).getTime();
          const currentTimeMs = new Date().getTime();
          const hourInMs = 60 * 60 * 1000;

          if (currentTimeMs - startTimeMs > hourInMs) {
            return 'Aborted';
          }
        }
        return 'Running';
      } else {
        // If there's a lastExecutionTime but isComplete is false, it's not running
        return 'Completed';
      }
    } else if (process.isSuccess) {
      return 'Completed';
    } else {
      return 'Failed';
    }
  }

  viewProcessDetails(process: WorkflowProcess): void {
    this.selectedProcess = process;
    this.loadingProcessDetails = true;
    this.processRuleResults = [];

    const dialogRef = this.dialog.open(this.processDetailsDialog, {
      width: '800px',
      maxHeight: '90vh'
    });

    // Get the process ID (handle both property names)
    const processId = process.workflowProcessId || process.workFlowProcessId;

    // Load rule execution results for this process
    if (process.workflowId && processId) {
      this.loadProcessRuleResults(process.workflowId, processId);
    } else {
      console.error('Missing required parameters for loadProcessRuleResults:',
        'workflowId =', process.workflowId,
        'processId =', processId);
      this.loadingProcessDetails = false;
      alert('Cannot load process details: Missing required parameters');
    }

    dialogRef.afterClosed().subscribe(() => {
      this.selectedProcess = null;
      this.processRuleResults = [];
    });
  }

  loadProcessRuleResults(workflowId: number, processId: string): void {
    // First, get the workflow rules to know which rules to query
    this.rulesService.getWorkflowRules(workflowId).subscribe({
      next: (rules) => {
        if (rules.length === 0) {
          this.loadingProcessDetails = false;
          return;
        }

        // Create an array of observables for each rule result
        const resultObservables = rules.map(rule =>
          this.rulesService.getDetailedRuleResult(this.siteId, workflowId, rule.ruleID, processId)
            .pipe(
              // Handle errors for individual rule results
              // but don't fail the entire forkJoin
              catchError(error => {
                console.error(`Error loading rule result for rule ${rule.ruleID}:`, error);
                return of(null);
              })
            )
        );

        // Use forkJoin to wait for all observables to complete
        forkJoin(resultObservables)
          .pipe(
            finalize(() => {
              this.loadingProcessDetails = false;
            })
          )
          .subscribe({
            next: (results) => {
              // Filter out null results and sort by execution time
              this.processRuleResults = results
                .filter((result): result is RuleExecutionResult => result !== null)
                .sort((a, b) => {
                  return new Date(a.executionTime).getTime() - new Date(b.executionTime).getTime();
                });
            },
            error: (error) => {
              console.error('Error loading rule results:', error);
              this.loadingProcessDetails = false;
              alert('Failed to load rule execution results. Please try again later.');
            }
          });
      },
      error: (error) => {
        console.error('Error loading workflow rules:', error);
        this.loadingProcessDetails = false;
        alert('Failed to load workflow rules. Please try again later.');
      }
    });
  }



  hasFileResult(result: RuleExecutionResult): boolean {
    // Check if the rule result has a file that can be downloaded
    if (!result || !result.data) return false;

    // Check for common file properties in the data
    return (
      (result.data.fileId !== undefined) ||
      (result.data.fileName !== undefined) ||
      (result.data.templateId !== undefined && result.success)
    );
  }

  downloadFileResult(workflowId?: number, ruleId?: number, processId?: string): void {
    if (!workflowId || !ruleId || !processId) {
      console.error('Missing required parameters for file download');
      return;
    }

    this.layoutService.showSpinner();

    this.rulesService.getWorkflowFileResult(this.siteId, workflowId, ruleId, processId)
      .pipe(
        finalize(() => {
          this.layoutService.hideSpinner();
        })
      )
      .subscribe({
        next: (fileData) => {
          // Create a blob from the file data and download it
          const blob = new Blob([fileData], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `rule-${ruleId}-result.pdf`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        },
        error: (error) => {
          console.error('Error downloading file:', error);
          alert('Error downloading file. Please try again later.');
        }
      });
  }

  viewWorkflowStatus(process: WorkflowProcess): void {
    this.selectedProcess = process;
    this.loadingWorkflowStatus = true;
    this.workflowStatus = null;

    // Debug information
    console.log('RulesEngineComponent.viewWorkflowStatus called with process:', process);

    const dialogRef = this.dialog.open(this.workflowStatusDialog, {
      width: '700px',
      maxHeight: '90vh'
    });

    // Get the process ID (handle both property names)
    const processId = process.workflowProcessId || process.workFlowProcessId;
    // Get the workflow ID (handle both property names)
    const workflowId = process.workflowId || process.workFlowId;

    // Load workflow status
    if (workflowId && processId) {
      console.log('Calling loadWorkflowStatus with:', workflowId, processId);

      // Check if the process ID is a valid GUID
      if (!this.isValidGuid(processId)) {
        console.error('Invalid process ID format:', processId);
        this.loadingWorkflowStatus = false;
        this.layoutService.hideSpinner();
        alert('Cannot load workflow status: Invalid process ID format');
        return;
      }

      this.loadWorkflowStatus(workflowId, processId);
    } else {
      console.error('Missing required parameters for loadWorkflowStatus:',
        'workflowId =', workflowId,
        'processId =', processId);
      this.loadingWorkflowStatus = false;
      this.layoutService.hideSpinner();
      alert('Cannot load workflow status: Missing required parameters');
    }

    dialogRef.afterClosed().subscribe(() => {
      this.selectedProcess = null;
      this.workflowStatus = null;
    });
  }

  loadWorkflowStatus(workflowId: number, processId: string): void {
    this.loadingWorkflowStatus = true;
    this.layoutService.showSpinner();

    // Debug information
    console.log('RulesEngineComponent.loadWorkflowStatus with parameters:');
    console.log('Site ID:', this.siteId);
    console.log('Workflow ID:', workflowId);
    console.log('Process ID:', processId);

    // Ensure processId is a valid GUID
    if (!processId || !this.isValidGuid(processId)) {
      console.error('Invalid process ID format:', processId);
      this.loadingWorkflowStatus = false;
      this.layoutService.hideSpinner();
      alert('Invalid process ID format. Cannot load workflow status.');
      return;
    }

    // Check if we're using the correct API route
    const url = ApiRoutes.WorkflowStatus
      .replace('{{siteId}}', this.siteId)
      .replace('{{workflowId}}', workflowId.toString())
      .replace('{{guid}}', processId);
    console.log('API URL:', url);

    this.rulesService.getWorkflowStatus(this.siteId, workflowId, processId)
      .pipe(
        finalize(() => {
          this.loadingWorkflowStatus = false;
          this.layoutService.hideSpinner();
        })
      )
      .subscribe({
        next: (status) => {
          console.log('Received workflow status:', status);

          // Check if we have executor transactions
          if (status?.Status?.executorTransactions) {
            console.log('Found executor transactions:', status.Status.executorTransactions.length);

            // Sort transactions by execution time
            if (status.Status.executorTransactions.length > 0) {
              status.Status.executorTransactions.sort((a, b) => {
                return new Date(a.StartExecution).getTime() - new Date(b.StartExecution).getTime();
              });
            }
          } else {
            console.warn('No executor transactions found in response');
          }

          this.workflowStatus = status;
        },
        error: (error) => {
          console.error('Error loading workflow status:', error);
          this.loadingWorkflowStatus = false;
          this.layoutService.hideSpinner();
          alert('Failed to load workflow status. Please try again later.');
        }
      });
  }

  refreshWorkflowStatus(): void {
    if (!this.selectedProcess || !this.selectedProcess.workflowId) {
      return;
    }

    // Get the process ID (handle both property names)
    const processId = this.selectedProcess.workflowProcessId || this.selectedProcess.workFlowProcessId;

    if (!processId) {
      console.error('Missing process ID for refreshWorkflowStatus');
      return;
    }

    this.loadWorkflowStatus(this.selectedProcess.workflowId, processId);
  }



  /**
   * Gets the status from the workflow status object, handling both new and legacy formats
   * @param status The workflow status object
   * @returns The status string
   */
  getStatusFromWorkflowStatus(status: WorkflowStatus | null): string {
    if (!status) return 'Unknown';

    // Check if using new format
    if (status.Status) {
      // Determine status based on executor transactions
      const transactions = this.getExecutorTransactions(status);
      if (transactions.length === 0) return 'Unknown';

      // Look for the Session transaction (RuleId = null)
      const sessionTransaction = transactions.find(t => t.RuleId === null);
      if (sessionTransaction) {
        // If session has started but not ended, it's running
        if (sessionTransaction.StartExecution && !sessionTransaction.EndExecution) {
          // Check if it's been running for more than an hour (potentially aborted)
          const startTime = new Date(sessionTransaction.StartExecution).getTime();
          const currentTime = new Date().getTime();
          const hourInMs = 60 * 60 * 1000;

          if (currentTime - startTime > hourInMs) {
            return 'Aborted';
          }
          return 'Running';
        }

        // If session has ended, check if any transactions failed
        if (sessionTransaction.EndExecution) {
          const hasFailedTransactions = transactions.some(t => t.Success === false);
          if (hasFailedTransactions) return 'Failed';
          return 'Completed';
        }
      }

      // Fallback to checking all transactions if no session transaction found
      const hasRunningTransactions = transactions.some(t => !t.EndExecution);
      if (hasRunningTransactions) return 'Running';

      const hasFailedTransactions = transactions.some(t => t.Success === false);
      if (hasFailedTransactions) return 'Failed';

      return 'Completed';
    }

    // Legacy format
    return status.status || 'Unknown';
  }

  /**
   * Gets the start time from the workflow status object, handling both new and legacy formats
   * @param status The workflow status object
   * @returns The start time
   */
  getStartTimeFromWorkflowStatus(status: WorkflowStatus | null): Date | null {
    if (!status) return null;

    // Check if using new format
    if (status.Status?.startTime) {
      return new Date(status.Status.startTime);
    }

    // Legacy format
    return status.startTime || null;
  }

  /**
   * Gets the end time from the workflow status object, handling both new and legacy formats
   * @param status The workflow status object
   * @returns The end time
   */
  getEndTimeFromWorkflowStatus(status: WorkflowStatus | null): Date | null {
    if (!status) return null;

    // Check if using new format
    if (status.Status) {
      if (status.Status.endTime) {
        return new Date(status.Status.endTime);
      }

      // If no explicit end time, check if all transactions are complete
      const transactions = this.getExecutorTransactions(status);
      if (transactions.length > 0) {
        const allComplete = transactions.every(t => t.EndExecution);
        if (allComplete) {
          // Use the latest end time from transactions
          const lastEndTime = transactions
            .filter(t => t.EndExecution)
            .map(t => new Date(t.EndExecution as string).getTime())
            .reduce((max, time) => Math.max(max, time), 0);

          return lastEndTime > 0 ? new Date(lastEndTime) : null;
        }
      }

      return null;
    }

    // Legacy format
    return status.endTime || null;
  }

  /**
   * Gets the executor transactions from the workflow status object
   * @param status The workflow status object
   * @returns The executor transactions array (never undefined)
   */
  getExecutorTransactions(status: WorkflowStatus | null): ExecutorTransaction[] {
    if (!status) return [];

    // Check if using new format
    if (status.Status?.executorTransactions) {
      return status.Status.executorTransactions;
    }

    return [];
  }

  /**
   * Gets the rule name by ID from the workflow status object
   * @param ruleId The rule ID
   * @param status The workflow status object
   * @returns The rule name
   */
  getRuleNameById(ruleId: number | null, status: WorkflowStatus): string {
    if (!ruleId) return 'Unknown Rule';

    // Check if using new format
    if (status.Status?.RuleDetails) {
      const rule = status.Status.RuleDetails.find(r => r.RuleID === ruleId);
      if (rule) return rule.Name;
    }

    // Try to find in rules array
    const rule = this.rules.find(r => r.ruleID === ruleId);
    if (rule) return rule.name;

    return `Rule ${ruleId}`;
  }

  /**
   * Formats an execution exception for display
   * @param exception The execution exception
   * @returns The formatted exception string
   */
  formatExecutionException(exception: any): string {
    if (!exception) return 'Unknown error';

    if (typeof exception === 'string') {
      return exception;
    }

    if (typeof exception === 'object') {
      try {
        return JSON.stringify(exception, null, 2);
      } catch (e) {
        return 'Error parsing exception';
      }
    }

    return String(exception);
  }

  /**
   * Formats a JSON string for display
   * @param jsonString The JSON string
   * @returns The formatted JSON string
   */
  formatJsonString(jsonString: string): string {
    if (!jsonString) return '';

    try {
      const parsed = JSON.parse(jsonString);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      // If it's not valid JSON, return as is
      return jsonString;
    }
  }

  calculateStatusDuration(status: WorkflowStatus): string {
    if (!status) return 'N/A';

    const startTime = this.getStartTimeFromWorkflowStatus(status);
    if (!startTime) return 'N/A';

    const endTime = this.getEndTimeFromWorkflowStatus(status);
    const end = endTime ? endTime.getTime() : new Date().getTime();

    const durationMs = end - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);

    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes} min ${remainingSeconds} sec`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const remainingMinutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hr ${remainingMinutes} min`;
    }
  }

  rerunWorkflow(process?: WorkflowProcess): void {
    if (!process || !process.workflowId) {
      console.error('Invalid process for rerun');
      return;
    }

    // Find the workflow
    const workflow = this.workflows.find(w => w.workflowID === process.workflowId);
    if (!workflow) {
      console.error('Workflow not found for rerun');
      return;
    }

    // Close the current dialog
    this.dialog.closeAll();

    // Set the selected workflow and open the execution dialog
    this.selectedWorkflow = workflow;
    this.executionInProgress = false;
    this.executionCompleted = false;
    this.executionProgress = 0;
    this.executionStatus = '';
    this.currentExecutingRule = '';
    this.executionSuccess = false;
    this.executionError = '';
    this.executionResults = [];

    // Generate a new GUID for the execution
    this.executionGuid = this.generateGuid();
    console.log('Generated new execution GUID:', this.executionGuid);

    this.dialog.open(this.executeWorkflowDialog, {
      width: '600px',
      disableClose: true
    });
  }

  // New methods for child component events
  onRuleTypeChanged(ruleTypeId: number | null): void {
    this.selectedRuleTypeId = ruleTypeId;
    if (ruleTypeId !== null) {
      // Convert number to string for the rule type ID
      const ruleTypeIdStr = ruleTypeId.toString();
      const ruleType = this.ruleTypes.find(rt => rt.id === ruleTypeIdStr);
      if (ruleType) {
        this.selectedRuleType = ruleType.id;
        this.loadRulesByType(this.selectedRuleType);
      }
    } else {
      this.selectedRuleType = '';
      this.rules = [];
      this.filteredRules = [];
    }
  }

  onRuleSelected(rule: Rule): void {
    this.editRule(rule);
  }

  onRuleDeleted(rule: Rule): void {
    this.deleteRule(rule);
  }

  onWorkflowSelected(workflow: Workflow): void {
    this.editWorkflow(workflow);
  }

  onWorkflowDeleted(workflow: Workflow): void {
    this.deleteWorkflow(workflow);
  }

  onHistoryWorkflowFilterChanged(workflowId: number | null): void {
    this.historyFilterWorkflowId = workflowId;
    this.loadWorkflowHistory();
  }
}
