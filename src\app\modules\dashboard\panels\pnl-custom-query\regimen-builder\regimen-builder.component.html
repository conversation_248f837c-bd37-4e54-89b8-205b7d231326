<div id="MedicationRegimenToogle" style="width: 100%;" cdkDropListGroup>
    <div id="MedicationRegimen" style="width: 35%; height: 100%; float: left; padding-right: 4px;">
        <div class="PatientinfoLable" style="width: 100%; padding-bottom: 5px;">
            Regimen Builder
        </div>
        <div id="Medication" style="width: 100%; float: left; border-right: 1px solid #a1a1a1; padding: 5px;">
            <div class="PatientinfoLable" style="width: 100%; padding-bottom: 5px; padding-top: 2px;">
              Available Medications:
            </div>
            <div id="MedicationGroupDiv" class="PatientInfoDDLDiv" style="padding-bottom: 3px; width: 100%; float: left;">
                <select id="CustomQueryDDLMedicationGroup"
                  class="dropdownMenu"
                  [(ngModel)]="selectedMedication" (ngModelChange)="onMedicationSelect()">
                    <option value="All" selected>All</option>
                    <option
                      *ngFor="let medicationGroup of medicationsGroupsList; index as i"
                      [value]="medicationGroup.code">
                      {{ medicationGroup.description }}
                    </option>
                </select>
            </div>
            <div id="SearchMedication" class="PatientInfoDDLDiv" style="padding-bottom: 3px; width: 100%; float: left;">
                <input
                    minlength="2"
                    (keyup)="onAvailableMedicationSearch($any($event.target).value)"
                    placeholder="Search Medication"
                    class="dropdownMenu"
                    #input
                />
            </div>
            <div class="divider-div">
              <hr class="hr-text">
            </div>
            <div id="HeadCustomMedicationTree">
                <div id="CustomMedicationTree" class="CustomMedicationTree">
                    <div class="reference2">
                        <mat-tree [treeControl]="treeControl" [dataSource]="dataSource"  cdkDropList (cdkDropListDropped)="drop($event, 'medicationDrop', 0)">
                          <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding cdkDrag [cdkDragData]="node">
                              <button mat-icon-button disabled></button>
                              {{ node.brndNM }}
                              <span class="genericNM" *ngIf="node.brndNM !== node.genericNM">
                                ({{node.genericNM}})
                              </span>
                          </mat-tree-node>
                          <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
                              <button
                                mat-icon-button
                                matTreeNodeToggle
                                [attr.atria.lable]="'Toggle' + 'node.name'">
                                  <mat-icon class="mat-icon-rtl-mirror">
                                      {{ treeControl.isExpanded(node) ? "expand_more" : "chevron_right" }}
                                  </mat-icon>
                              </button>
                              {{ node.brndNM }}
                          </mat-tree-node>
                        </mat-tree>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="CreatedRegimen" style="width: 60%; float: left;">
      <div class="CustomErrorMsg">
          <label id="lblCustomError" class="field-validation-error" style="padding-top: 15px;"></label>
      </div>
      <div class="PatientinfoLable">
          Created Regimens:
      </div>
      <div id="currentLookback" class="currentLookback">
          Regimens ending&nbsp;more than &nbsp;
          <input name="txtcurrentLookback" [(ngModel)]="txtcurrentLookback" style="width: 50px; border-radius: 3px;" maxlength="3" type="text">&nbsp;
          days prior to extract date are considered as discontinued.
      </div>
      <div id="MainRegimenScroll" style="width: 105%; float: left; margin-bottom: 15px; margin-left: 17px;" >
          <div id="MainRegimens" class="scrollableContentDiv">
            <div class="droppedContentOuterDiv" *ngFor="let regimen of regimenPanelsList;index as i">
                <div *ngIf="i > 0">
                    <select class="dropdownMenuSmallWidth" name="methodType" (change)="updateMethod($event,i)">
                        <option
                            *ngFor="let methodType of chainMethodTypesList"
                            [selected]="methodType.value == regimen.chainMethod"
                            [value]="methodType.value">
                            {{methodType.text}}
                        </option>
                    </select>
                </div>
                <div class="droppedContentDiv">
                    <div [id]="'regimen_' + i" cdkDropList (cdkDropListDropped)="drop($event, 'existingRegimen', i)">
                        <div class="contentDroppedElement">
                            <div class="RegimenHeaderlable" [id]="'RegemenHeaderlbl_' + i">Regimen {{i+1}}:</div>
                            <div class="PatientInfoDDLDiv">
                                <select class="dropdownMenuSmall" name="regimenOption" (change)="updateRegimenOption($event, i)">
                                    <option *ngFor="let level of regimentOptions"
                                        [selected]="level.value == regimen.levelId" [value]="level.value">
                                        {{level.text}}
                                    </option>
                                </select>
                            </div>
                            <div *ngIf="regimen.levelId==3">
                              &nbsp;
                              <input type="radio" [id]="'regTakenDiscExpire_' + i" [checked]="regimen.discontinuedRDBtnVal==1" (click)="updateTakenAndDiscontinued($event,i)"  name="takenAndDisc" value="1" />
                              <label for="1">Ever</label>
                              &nbsp;
                              <input type="radio" [id]="'regTakenDiscDays_' + i" [checked]="regimen.discontinuedRDBtnVal==2" (click)="updateTakenAndDiscontinued($event,i)" name="takenAndDisc" value="2" >
                              <label for="2">In the past
                                <input [name]="'txtDiscDays_' + i" (change)="updateTakenAndDiscontinuedText($event,i)" [value]="regimen.discontinuedLookback" style="width: 50px; border-radius: 3px;" maxlength="3" type="text">
                                days
                              </label>
                            </div>
                        </div>
                        <div class="contentDroppedElement" *ngFor="let regimenList of regimen.regimenList; index as key">
                            <div class="RegimenHeaderlable">
                                <span>
                                    {{getMedicationGroupName(regimenList.group.code)}}
                                </span>
                            </div>
                            <div class="PatientInfoDDLDiv">
                                <select class="dropdownMenuSmall" (change)="updateMedGroup($event, i, key)">
                                    <option *ngFor="let medicationGroupOption of medicationGroupOptions"
                                        [selected]="medicationGroupOption.value == regimenList.selectedOption" [value]="medicationGroupOption.value" >
                                        {{medicationGroupOption.text}}
                                    </option>
                                </select>
                            </div>
                            <div class="RegimenHeaderlable" id="RegemenHeaderlbl_1">
                                <mat-chip-list #chipList>
                                    <mat-chip
                                        class="example-box"
                                        color="primary"
                                        *ngFor="let medication of regimenList.medicationName"
                                        [selectable]="selectable"
                                        [removable]="removable"
                                        (removed)="removeMedication(medication.brndNM, i)">
                                        {{medication.brndNM}}
                                        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                                    </mat-chip>
                                </mat-chip-list>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          </div>
          <div class="droppable" id="defaultRegimenDrop" name="droppedEle" cdkDropList (cdkDropListDropped)="drop($event, 'defaultDrop', 0)">
            <div class="txtAddRegimen">
                <div class="PatientinfoLableDrag">
                  Drag and drop a medication here to create a new regimen.
                </div>
            </div>
          </div>
      </div>
      <div id="RegimenFooterbtn" style="width: 100%;">
        <div class="RunMedReport" style="width: 100%" *ngIf="regimenPanelsList.length==0">
            <input type="button" value="Clear All" class="CMbtnDisabled" disabled="disabled" (click)="removeAllRegimen()"/>
            <input type="button" value="Save" class="CMbtnDisabledRightAlign" disabled="disabled" (click)="includedPatientsRegimen(regimenPanelsList)"/>
            <input type="button" value="Cancel" class="CMbtn" (click)="displaySelectionCriteria()" />
        </div>
        <div class="RunMedReport" style="width: 100%" *ngIf="regimenPanelsList.length>0">
            <input type="button" value="Clear All" class="CMbtnLeftAlign" (click)="removeAllRegimen()" />
            <input type="button" value="Save" class="CMbtn"  (click)="includedPatientsRegimen(regimenPanelsList)" />
            <input type="button" value="Cancel" class="CMbtn" (click)="displaySelectionCriteria()" />
        </div>
      </div>
    </div>
</div>
