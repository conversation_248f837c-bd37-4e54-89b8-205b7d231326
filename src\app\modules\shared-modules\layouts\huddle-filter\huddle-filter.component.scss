/* Container Styling */
.filter-container {
  background-color: #EAEAEA;
  border: 2px solid #A9A9A9;
  padding: 10px;
  width: 275px;
  border-radius: 15px;
  border: 0.8px solid lightgrey ;
  overflow-y: auto;
  height: 100%;
  font-family: "Museo 500", Arial, sans-serif;
}

.filter-tooltip {
  background-color: #ffffff;
  border: 2px solid #A9A9A9;
  height: 260px;
  padding: 16px;
  width: 260px;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(192,192,192);
  overflow-y: auto;
  height: 25%;
}

/* Title Styling */
.filter-title {
  color: #66a9d7;
  font-size: 16px;
  font-family: "Museo500-Regular";
  margin-bottom: 5px;
}

/* Filter Item Styling */
.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #0071bc;
  font-weight: bold;
  font-family: "Museo 700", Arial, sans-serif;
  font-size: 15pt;
  border-radius: 15px;
  border: 0.8px solid lightgrey;
  margin-top: 5px;
  padding-left: 10px;
  background-color: white;
  height: 35px; 
  cursor: pointer;
}

/* Active Filter Item */
.filter-item.active {
  background-color: #0071bc;
  color: white;
  font-size: 18pt;
}

/* Count Badge - Now a Rounded Square */
.filter-count {
  color: #0071bc;
  padding: 0px 3px;
  border-radius: 6px;
  font-size: 14px;
  min-width: 35px;
  text-align: center;
  display: inline-block;
  border: 2px solid #0071bc;
}

.active-count {
  background-color: #0071bc;
  color:white;
  border: 2px solid white
}

/* Filter Text */
.filter-text {
  flex: 1;
  text-align: left;
  font-family: "MuseoSans-700";
  font-size: 15px;
  font-weight: 500;
  padding-left: 10px;
}

/* Info Button */
.filterToolTip {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15px;
    height: 15px;
    margin-right: 7px;
    padding-top: 1px;
    border-radius: 50%;
    color: #999999;
    font-weight: bold;
    font-size: 12px;
    border: #999999;
    border-style: solid;
    border-width: 1px;
    cursor:default;
}

.filterToolTip:hover {
  color: white;
  background-color: #666666;
  border: #666666;
  border-style: solid;
  border-width: 1px;
}

.filter-item.active .filterToolTip{
  color: white;
  border: white;
  border-style: solid;
  border-width: 1px;
}

.filter-item.active .filterToolTip:hover{
  color: white;
  border: #666666;
  border-style: solid;
  border-width: 1px;
}