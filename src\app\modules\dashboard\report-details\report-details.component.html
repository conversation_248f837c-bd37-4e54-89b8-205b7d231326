<!-- Report Details Component -->
<div class="report-details-wrapper">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <span class="patient-count">{{ getTotalPatients() | number }} Patients</span>
      <div class="export-dropdown">
        <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
          <button class="downloadButton" (click)="toggleMenu(); $event.stopPropagation();">
            <mat-icon class="material-symbols-outlined download-icon">ios_share</mat-icon>Download or Print
          </button>
          <nav class="menu" *ngIf="menuOpen">
            <ul>
              <li>
                <div class="menu-header">
                  <div class="menu-title">
                    <mat-icon class="material-symbols-outlined menu-icon">ios_share</mat-icon>
                    <span>Export Options</span>
                  </div>
                </div>
              </li>
              <li><a (click)="exportToExcel(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">description</mat-icon>
                Export to Excel
              </a></li>
              <li><a (click)="exportToCSV(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">table_view</mat-icon>
                Export to CSV
              </a></li>
              <li><a (click)="exportToPDF(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">picture_as_pdf</mat-icon>
                Export to PDF
              </a></li>
              <li><a (click)="printTable(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">print</mat-icon>
                Print
              </a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-content">
      <ngx-spinner bdColor="rgba(0, 0, 0, 0.1)" size="medium" color="#0071BC" type="ball-scale-multiple">
        <p style="color: #0071BC; margin-top: 10px;">Loading report details...</p>
      </ngx-spinner>
    </div>
  </div>

  <!-- Tabulator Table Container -->
  <div class="table-container" *ngIf="!isLoading && hasData">
    <div id="report-details-table"></div>
  </div>

  <!-- No Data State -->
  <div class="no-data-container" *ngIf="!isLoading && !hasData">
    <div class="no-data-content">
      <mat-icon class="no-data-icon">info</mat-icon>
      <h3>No Data Available</h3>
      <p>No report details found for the selected criteria.</p>
    </div>
  </div>


</div>
