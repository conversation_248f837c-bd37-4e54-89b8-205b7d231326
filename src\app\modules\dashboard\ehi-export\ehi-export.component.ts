import { Component, OnInit, AfterViewInit, ViewChild, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import { <PERSON>pi<PERSON>and<PERSON> } from "src/app/shared-services/ep-api-handler/ep-api-handler";
import { Observable, Subscription, finalize } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { PanelService } from '../panels/PanelService';
import { ReportPanelTypes } from '../panels/report-panel-enums';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { PnlPatientSearchComponent } from '../panels/pnl-patient-search/pnl-patient-search.component';
import { SiteNameService } from "src/app/shared-services/site-name.service";
import { v4 as uuidv4 } from 'uuid';
import { IFileToPassword } from '../models/ehi-model';
import { DialogResponseComponent } from "../../shared-modules/layouts/dialogs/dialog-response/dialog-response.component";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { IChorusAccessViewModel, RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';

@Component({
  selector: 'ehi-export',
  templateUrl: './ehi-export.component.html',
  styleUrls: ['./ehi-export.component.scss']
})
export class EHIExportComponent implements OnInit, AfterViewInit {
  site: string = '';
  subject: string = '';
  showAllPatientsOption: boolean = false;
  isSSRSReport: boolean = false;
  exportGuid: string = '';
  openDialog: MatDialogRef<DialogResponseComponent, any> = {} as MatDialogRef<DialogResponseComponent, any>
  private pageSubscriptions: Subscription = new Subscription();

  // Panel state management - mutually exclusive panels (only one can be open at a time)
  public reportNavCollapsed: boolean = false;
  public filtersCollapsed: boolean = true;

  // Current report name for dynamic panel title
  public currentSelectedReport: string = "";

  @ViewChild('panelContainerRef', { read: ViewContainerRef }) _panelContainer!: ViewContainerRef;

  constructor(
    private userContext: UserContext,
    public panelService: PanelService,
    public activeRoute: ActivatedRoute,
    public siteNameService: SiteNameService,
    private apihandler: ApiHandler,
    public router: Router,
    private auditService: AuditService,
    private dialog: MatDialog,
    private layoutService: LayoutService
  ) {

    let tmpSite = this.activeRoute.snapshot.paramMap.get('siteId');
    if (tmpSite) {
      this.site = tmpSite;
    }

    this.auditService.setPageAudit(Page.EHIExport);

    this.userContext.SetCurrentSite(Number(this.site));
  }

  ngOnInit(): void {
    // Check sessionStorage for panel collapse state
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';

    // Apply collapse logic: collapse if flag is set AND it's not initial navigation
    if (shouldCollapse && !isInitialNavigation) {
      // User selected a specific report after initial navigation, collapse the reports panel
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }

    // Always clear the collapse flag after checking it
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }

    // Set initial navigation flag for direct access scenarios
    if (!isInitialNavigation && !shouldCollapse) {
      sessionStorage.setItem('chorus_initial_reports_navigation', 'true');
    }

    this.siteNameService.currentSiteName$.subscribe((siteName) => {
      this.subject = 'Request for EHI Patient Extract for all patients - site ' + this.site + ' ' + siteName;
    });

    this.exportGuid = this.generateGuid();
  }

  ngOnDestroy(): void {
    // Complete the destroyed$ subject to clean up subscriptions
    this.pageSubscriptions.unsubscribe();
  }

  ngAfterViewInit(): void {

    this.pageSubscriptions.add(
      this.userContext.getUserSecurity().subscribe((userAccess: IChorusAccessViewModel[]) => {
        this.exitToLoginIfNoAccessRole(userAccess, RoleTypes.EHI_Export);
      })
    );

    // Create the panel after the view has been initialized
    this.panelService.CreateReportPanel(ReportPanelTypes.pnlPatientSearch).then((componentType: any) => {
      if (this._panelContainer) {
        // Clear the container before adding a new component
        this._panelContainer.clear();

        // Dynamically create the component
        const componentRef = this._panelContainer.createComponent<PnlPatientSearchComponent>(componentType);

        // Set the showAllOption input only when loaded from EHI_ExportComponent
        componentRef.instance.showOnlyAllOption = this.showAllPatientsOption;
        componentRef.instance.isSSRSReport = this.isSSRSReport;

        // Subscribe to the Run Click event from the child component
        componentRef.instance.runClicked.subscribe((data) => {
          console.log('Run button clicked in child component with data:', data);

          var titleName = data.searchText > 0 ? data.searchText : "All_Patients"

          this.exportData(data).subscribe((res: HttpResponse<string>) => {
            if (res.status === 200) {
              this.downloadFile(res.body, 'EHI_Patient_Extract_' + titleName + '.zip');

              // After the main exportData call finishes, call DisplayPasswordPopup to fetch and show the access code.
              this.DisplayPasswordPopup();

            } else {
              alert("Error downloading file: status code" + res.status);
            }
          });

        });

        this.layoutService.hideSpinner();
      }
    });
  }

  /**
   * Example of how to use the new hasRole method from UserContext
   * This method shows how to check for a specific role directly

  private checkRoleAccess(): void {
    this.userContext.hasRole(RoleTypes.EHI_Export).subscribe(hasRole => {
      if (!hasRole) {
        this.userContext.ClearSession();
        this.router.navigateByUrl('/');
      }
    });
  }
  */

  /**
   * Original method that checks for role access using the user access data
   * This can be replaced with the simpler checkRoleAccess method above
   */
  private exitToLoginIfNoAccessRole(userAccess : IChorusAccessViewModel[], requiredAccess : RoleTypes){
    const siteIdNum = Number(this.site);

    // Find the access entry that matches the current siteId
    const siteAccess = userAccess.find(access => access.siteId === siteIdNum);

    if (siteAccess == null || siteAccess == undefined)
    {
      return;
    }

    // Check if the siteAccess exists and contains the EHI_Export role
    const hasRole = siteAccess?.userRoles.some(r => r.role === requiredAccess) ?? false;

    if (!hasRole)
    {
        this.userContext.ClearSession();
        this.router.navigateByUrl('/');
    }
  }

  private DisplayPasswordPopup(): void {
    var apiUrl = ApiRoutes.GetEHIExportPassword.toString();
    apiUrl = apiUrl.replace("{{exportId}}", this.exportGuid)

    this.apihandler.Get<IFileToPassword>(ApiTypes.V2, apiUrl, true, false).subscribe(
      (response: IFileToPassword) => {
        var msg = 'The file you requested is in your Downloads folder<br/><br/>Password: <strong>' + response.password + '</strong>';

        this.openDialog = this.dialog.open(DialogResponseComponent, {
          data: { title: "File Export", content: msg }, disableClose: true
        });
      },
      (error) => {
        console.error('Error fetching FileToCode:', error);
      }
    );
  }

  exportData(data: any): Observable<any> {
    this.layoutService.showSpinner();
    var demographicId = data.patientSearchType == 0 ? data.searchText : "-1";
    var apiUrl = ApiRoutes.GetEHIExport.toString();
    apiUrl = apiUrl.replace("{{siteId}}", this.site)
    apiUrl = apiUrl.replace("{{demographicId}}", demographicId)
    apiUrl = apiUrl.replace("{{exportId}}", this.exportGuid)
    //console.log(apiUrl);

    this.auditExportParameters(demographicId, this.site);

    return this.apihandler.Get<any>(ApiTypes.V2, apiUrl, true, true, 'blob'
    ).pipe(
      finalize(() => {
        // Hide spinner after API call completes (whether successful or failed)
        this.layoutService.hideSpinner();
      })
    );
  }

  private auditExportParameters(demographicsId: string, siteId: string): void {
    // Reset as we are adding new parameters
    this.auditService.resetParamAudits();

    this.auditService.addParamAudit('siteId', siteId || '', false);
    this.auditService.addParamAudit('demographicsId', demographicsId || '', false);

    // Save all audited parameters
    this.auditService.saveCurrentAudit();
  }

  // Method to trigger file download
  downloadFile(content: any, fileName: string): void {
    try {
      // Create the blob and object URL
      const blob = new Blob([content], { type: 'application/zip'});
      const a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = fileName;

      // Append, trigger the click, and remove the element
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(a.href); // Revoke the object URL after download
      document.body.removeChild(a);
      //alert('The file you requested is in your Downloads folder. Check your email for instructions on accessing it.')
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed: ' + error);
    }
  }

  get mailtoLinkAllPatients(): string {
    const subjectEnc = encodeURIComponent(this.subject);
    return `mailto:<EMAIL>?subject=${subjectEnc}`;

  }

  public generateGuid(): string {
    return uuidv4();
 }

  // Panel management methods - mutually exclusive panels
  toggleReportNav(): void {
    if (this.reportNavCollapsed) {
      // If report nav is collapsed, expand it and collapse filters
      this.reportNavCollapsed = false;
      this.filtersCollapsed = true;
    } else {
      // If report nav is expanded, collapse it and expand filters
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }
  }

  toggleFilters(): void {
    if (this.filtersCollapsed) {
      // If filters are collapsed, expand them and collapse report nav
      this.filtersCollapsed = false;
      this.reportNavCollapsed = true;
    } else {
      // If filters are expanded, collapse them and expand report nav
      this.filtersCollapsed = true;
      this.reportNavCollapsed = false;
    }
  }

  // Handle current report name change from report navigation component
  onCurrentReportChanged(reportName: string): void {
    this.currentSelectedReport = reportName;
  }

  // Handle report selection event - automatically collapse the reports panel
  onReportSelected(): void {
    this.reportNavCollapsed = true;
    this.filtersCollapsed = false;
  }

  // Get the dynamic title for the Select Report panel
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }

}
