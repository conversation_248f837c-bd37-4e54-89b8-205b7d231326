.PALcontent {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #D6D7DA;
    float: left;
    padding: 15px 20px;
    width: 635px;
    height: 450px;
    padding: 6px 12px 12px; width: 662px;
}

.bodyDiv {
    background: #ebedf3;
    font-family: Arial, Helvetica, sans-serif;
    color: #999999;
    font-size: 13px;
    margin: 0px auto;
}

.PALcontent h1 {
    font-size: 16px;
    color: #4e4e53;
    font-weight: bold;
}

.alertDetailsSeperator {
    border-bottom: 1px solid #D5D5D5;
    width: 100%;
    height: auto;
    margin-bottom: 10px;
}

.DQAuppercontent {
    /*height: 223px;*/
    height: 233px;
    margin-bottom: 20px;
    border: 1px solid #E3E3E3;
    background: none repeat scroll 0 0 #f8f8f8;
    /*padding: 10px 30px;*/
    padding: 5px 0 5px 10px;
}

#divAlertDesc {
    margin-bottom: 10px;
    margin-right: 10px;
    /*height: 55px;*/
    height: auto;
    overflow: hidden;
}

.DQAlowercontent {
    border: 1px solid #f8f8f8;
    /*height: 170px;*/
    height: auto;
}

.divDynamicControl {
    color: rgb(70, 70, 70);
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 7px;
}

.divDynamicControl input[type=checkbox] {
    vertical-align: middle;
}

.divDynamicControl input[type=radio] {
    vertical-align: middle;
}

.divDynamicControl label {
    display: block;
    margin: -14px 0px 0px 20px;
    vertical-align: middle;
}

.CMbtnDisabled {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
    pointer-events:none;
}

.btnActive {
    color: #FFFFFF;
    font-size: 15px;
    border: none;
    padding: 0px 10px 0px 10px;
    width: 150px;
    text-align: center;
    height: 40px;
    float: left;
    cursor: pointer;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #005F9F), color-stop(0.5, #00518F));
    background-image: -o-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
}

.btnActive:hover {
    background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F));
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}


.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

    .CMbtn:hover {
        background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
        background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
    }
