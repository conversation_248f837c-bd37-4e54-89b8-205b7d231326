<?xml version="1.0" encoding="Windows-1252"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug 2012' ">
    <FullPath>Debug 2012</FullPath>
    <OutputPath>bin\Debug</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2016</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>Chorus_Portal</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Chorus_Portal/Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
    <TargetServerURL>http://ord-testsql01/reports</TargetServerURL>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'DebugLocal' ">
    <FullPath>DebugLocal</FullPath>
    <OutputPath>bin\DebugLocal</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2008R2</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>EpividianReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <FullPath>Release</FullPath>
    <OutputPath>bin\Release</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2008R2</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>EpividianReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug 2019' ">
    <FullPath>Debug 2019</FullPath>
    <OutputPath>bin\Debug</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2016</TargetServerVersion>
    <Platform />
    <TargetReportFolder>EpividianReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup>
    <State>$base64$PFNvdXJjZUNvbnRyb2xJbmZvIHhtbG5zOnhzZD0iaHR0cDovL3d3dy53My5vcmcvMjAwMS9YTUxTY2hlbWEiIHhtbG5zOnhzaT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS9YTUxTY2hlbWEtaW5zdGFuY2UiIHhtbG5zOmRkbDI9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDAzL2VuZ2luZS8yIiB4bWxuczpkZGwyXzI9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDAzL2VuZ2luZS8yLzIiIHhtbG5zOmRkbDEwMF8xMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDA4L2VuZ2luZS8xMDAvMTAwIiB4bWxuczpkZGwyMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEwL2VuZ2luZS8yMDAiIHhtbG5zOmRkbDIwMF8yMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEwL2VuZ2luZS8yMDAvMjAwIiB4bWxuczpkZGwzMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDExL2VuZ2luZS8zMDAiIHhtbG5zOmRkbDMwMF8zMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDExL2VuZ2luZS8zMDAvMzAwIiB4bWxuczpkZGw0MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEyL2VuZ2luZS80MDAiIHhtbG5zOmRkbDQwMF80MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEyL2VuZ2luZS80MDAvNDAwIiB4bWxuczpkZGw1MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEzL2VuZ2luZS81MDAiIHhtbG5zOmRkbDUwMF81MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEzL2VuZ2luZS81MDAvNTAwIiB4bWxuczpkd2Q9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vRGF0YVdhcmVob3VzZS9EZXNpZ25lci8xLjAiPg0KICA8RW5hYmxlZD50cnVlPC9FbmFibGVkPg0KICA8UHJvamVjdE5hbWU+U0FLPC9Qcm9qZWN0TmFtZT4NCiAgPEF1eFBhdGg+U0FLPC9BdXhQYXRoPg0KICA8TG9jYWxQYXRoPlNBSzwvTG9jYWxQYXRoPg0KICA8UHJvdmlkZXI+U0FLPC9Qcm92aWRlcj4NCjwvU291cmNlQ29udHJvbEluZm8+</State>
    <DataSources>@(DataSource)</DataSources>
    <DataSets>@(DataSet)</DataSets>
    <Reports>@(Report)</Reports>
  </PropertyGroup>
  <ItemGroup>
    <DataSource Include="dsAuthProvider.rds" />
    <DataSource Include="dsChorus.rds" />
    <DataSource Include="dsDIRECTOR.rds" />
    <DataSource Include="dsOPERA.rds" />
    <DataSource Include="dsSite.rds" />
  </ItemGroup>
  <ItemGroup>
    <Report Include="AllProvidersByLocationsWithHIVPositivePatientCount.rdl" />
    <Report Include="Annotation_Detail.rdl" />
    <Report Include="Bonus_Quality_Measures.rdl" />
    <Report Include="Careware Encounter Data - Expanded 2013.rdl" />
    <Report Include="CHORUS_Monthly_Usage_Report.rdl" />
    <Report Include="CHORUS_Monthly_Usage_Report_Detail.rdl" />
    <Report Include="CHORUS_Monthly_Usage_Report_Detail_FirstTimeUsers.rdl" />
    <Report Include="CHORUS_Site_ETL_Performance_Report.rdl" />
    <Report Include="CHORUS_Usage_Report.rdl" />
    <Report Include="CHORUS_User_Report.rdl" />
    <Report Include="CHSLocationsWithHIVPositivePatients.rdl" />
    <Report Include="ClassificationAudit.rdl" />
    <Report Include="CohortPatientListing.rdl" />
    <Report Include="Concept_Classification_Status_Report.rdl" />
    <Report Include="Cstm_Provider_High_Risk_LTF_Summary.rdl" />
    <Report Include="CustomQuery.rdl" />
    <Report Include="DISEASE_MANAGEMENT_002.rdl" />
    <Report Include="FILL_PATIENT_FORMS_001.rdl" />
    <Report Include="FlowsheetResults.rdl" />
    <Report Include="FlowsheetResults_HCVAb.rdl" />
    <Report Include="HCVPatientListing.rdl" />
    <Report Include="HCVRiskScores.rdl" />
    <Report Include="HHS_Cohort_Quality_Measures_Lab_Detail.rdl" />
    <Report Include="HHS_Cohort_Quality_Measures_On_ART_Detail.rdl" />
    <Report Include="HHS_Quality_Measures_V3.rdl" />
    <Report Include="HIV_PrEP_Treatment.rdl" />
    <Report Include="IBDPatientsBiologicsTreatment.rdl" />
    <Report Include="IDCPatientsWithoutIDCProvider.rdl" />
    <Report Include="IDCPatientsWithoutIDCProvider_IDCVisits.rdl" />
    <Report Include="Immunization_Detail.rdl" />
    <Report Include="Lab_Detail.rdl" />
    <Report Include="Medication_Detail.rdl" />
    <Report Include="Medication_Lab_Detail.rdl" />
    <Report Include="OPERAProfile_Demographics2.rdl" />
    <Report Include="Operations_Dashboard.rdl" />
    <Report Include="PatientChart.rdl" />
    <Report Include="PatientChart_Sub_Allergies.rdl" />
    <Report Include="PatientChart_Sub_Demographics.rdl" />
    <Report Include="PatientChart_Sub_Documents.rdl" />
    <Report Include="PatientChart_Sub_History.rdl" />
    <Report Include="PatientChart_Sub_Labs.rdl" />
    <Report Include="PatientChart_Sub_Medications.rdl" />
    <Report Include="PatientChart_Sub_PDF_Reports.rdl" />
    <Report Include="PatientChart_Sub_Problems.rdl" />
    <Report Include="PatientChart_Sub_Procedures.rdl" />
    <Report Include="PatientChart_Sub_Vaccinations.rdl" />
    <Report Include="PatientChart_Sub_Visits.rdl" />
    <Report Include="PatientChart_Sub_Vitals.rdl" />
    <Report Include="PatientFlowsheet.rdl" />
    <Report Include="PatientList.rdl" />
    <Report Include="PatientMedicationIntervals.rdl" />
    <Report Include="PatientMostRecentVisits.rdl" />
    <Report Include="PatientVisitDetail.rdl" />
    <Report Include="Patient_High_Risk_LTF_Detail.rdl" />
    <Report Include="Patient_Quality_Measures_DTL.rdl" />
    <Report Include="PCF_Measures.rdl" />
    <Report Include="PCF_Measures_Detail.rdl" />
    <Report Include="Provider_Alert_Status.rdl" />
    <Report Include="Provider_Annotation_Status.rdl" />
    <Report Include="Provider_High_Risk_LTF_Summary.rdl" />
    <Report Include="Quality_Measures.rdl" />
    <Report Include="Quality_Measures_Detail.rdl" />
    <Report Include="Quality_Trend.rdl" />
    <Report Include="RecentlySeenHIVHCV_Active.rdl" />
    <Report Include="REPRIEVEPatientSelection.rdl" />
    <Report Include="rptSiteVisits.rdl" />
    <Report Include="rptSiteVisitsSubReport.rdl" />
    <Report Include="RWDS_PDIDataPreview.rdl" />
    <Report Include="RWDS_PDIDataPreview_All.rdl" />
    <Report Include="RWDS_PDIDataPreview_Client.rdl" />
    <Report Include="RWDS_PDIDataPreview_ExcelFormat.rdl" />
    <Report Include="RyanWhite_PatientsVisits2018_IDC.rdl" />
    <Report Include="RyanWhite_PatientsVisits2018_MyersPark.rdl" />
    <Report Include="RyanWhite_PatientsVisits2018_NEID.rdl" />
    <Report Include="Service_Detail_HHS.rdl" />
    <Report Include="Service_Detail_PQRS.rdl" />
    <Report Include="SitePatientProfile.rdl" />
    <Report Include="Site_Category_Trend_Alert.rdl" />
    <Report Include="Sub_Annotation_Detail.rdl" />
    <Report Include="Sub_Category_Report.rdl" />
    <Report Include="Targeted_Quality_Measures.rdl" />
    <Report Include="Unmapped_Qualitative_Values_001.rdl" />
    <Report Include="User_Activity_Detail_Report.rdl" />
    <Report Include="User_Login_Audit.rdl" />
    <Report Include="VACS_Detail.rdl" />
    <Report Include="VACS_Detail_VACS_Trend.rdl" />
    <Report Include="VACS_Summary_Charts.rdl" />
    <Report Include="VACS_Summary_Tables.rdl" />
    <Report Include="VisitsToReview.rdl" />
    <Report Include="Visits_Detail.rdl" />
    <Report Include="Visits_Overview.rdl" />
    <Report Include="Visit_Detail.rdl" />
    <Report Include="VitalSigns_FilterByDateRange.rdl" />
    <Report Include="VitalSigns_FilterByPatient.rdl" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Reporting Services\Microsoft.ReportingServices.MSBuilder.targets" />
</Project>