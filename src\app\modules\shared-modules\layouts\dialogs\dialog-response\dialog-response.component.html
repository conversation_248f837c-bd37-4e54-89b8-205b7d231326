<h2 mat-dialog-title *ngIf="dialogTypeAction | async">{{title}}</h2>
<mat-dialog-content class="mat-typography">
  <mat-icon class="icon">warning</mat-icon><p class="content" [innerHTML]="content"></p>
</mat-dialog-content>
<mat-dialog-actions align="center" style="padding: 6px;">
  <button mat-button [mat-dialog-close]="true" cdkFocusInitial>{{dialogCloseText}}</button>
  <ng-container *ngIf="dialogActionText | async as actionText">
    <button mat-button (click)="closeDialog(actionText)">{{actionText}}</button>
  </ng-container>
</mat-dialog-actions>
