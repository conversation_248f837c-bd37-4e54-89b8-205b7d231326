import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, UrlSegment } from '@angular/router';
import { filter, map } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { EpividianCommon } from 'src/app/modules/utility/EpividianCommon';


@Component({
  selector: 'app-download',
  templateUrl: './download.component.html',
  styleUrls: ['./download.component.css']
})
export class DownloadComponent implements OnInit {

  public urlSegments: UrlSegment[] = [];
  public fullPath: string = '';

  constructor(private router: Router,
              private activatedRoute: ActivatedRoute,
              private userContext: UserContext,
              private epividianCommon: EpividianCommon) { }
  ngOnInit() {
    this.userContext.LoadUserObjFromLocalStorage();

    var filePath = location.pathname;
    filePath = filePath.replace("/Download/Shared Documents/", "");
    var downloadFile = ApiRoutes.DownloadFile.replace("{{FileNamePath}}", filePath);
    this.fullPath = downloadFile;  
    downloadFile = downloadFile.replace("Shared%20Documents//", "");
    this.downloadFile(downloadFile);

  }

  downloadFile(downloadFilePath: string) {
    var fileName = this.epividianCommon.getLastSegmentOfUrl(downloadFilePath);
    if (fileName == null) {
      return; 
    } else {
      this.userContext.apihandler.Get<Blob>(ApiTypes.V2, downloadFilePath, false, false, 'blob').subscribe(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
      
        a.download = fileName?.toString() ?? ''; // Set the file name, with null check
       a.style.display = 'none'; // Hide the anchor element
        document.body.appendChild(a); // Append to the document

        // Add an event listener to close the window
        a.addEventListener('click', () => {
          setTimeout(() => {
            window.close(); // Close the window
          }, 400); // Delay in milliseconds
        });

        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a); // Remove the anchor from the document
      });
    }
  }

}
