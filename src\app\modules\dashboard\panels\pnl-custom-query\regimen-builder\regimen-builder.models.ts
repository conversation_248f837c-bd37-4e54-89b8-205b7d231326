export class ICreateRegimen {
    regimenId!: number;
    regimenList!: string[];
    medicationGroupId!: number;
    medicationOptions!: string[];
    medicationList!: object[];
}

export interface IRegimenMedicationsAndGroups {
    availableMedications: IAvailableMedicationAsc[],
    medicationGroup: IMedicationGroup[]
}

export interface IRegimenPanel
{
  position: number, // index
  levelId: number,
  regimenList: IRegimenGroup[],
  chainMethod: any
  currentlookBack: Number,
  idtoSend: string,
  discontinuedLookback: number,
  discontinuedRDBtnVal: number
}

export interface IRegimenGroup
{
  group: IMedicationGroup,
  selectedOption: number,
  medicationName: IAvailableMedicationAsc[]
}

export interface IMedicationGroup
{
  code: number,
  description: string
}

export const ChainMethodTypesList = [
  {value: 1, text:"AND"},
  {value: 2, text:"OR"}
]

export const RegimentOptions = [
  {value: 1, text:'Is currently taking'},
  {value: 2, text:'Has ever taken'},
  {value: 3, text:'Has taken and discontinued'},
  {value: 4, text:'Has never taken'}
]


export const MedicationGroupOptions = [
  {value: 2, text:'Regimen Contains'},
  {value: 1, text:'Complete Regimen'}
]

export interface IMedicationsTreeData {
  brndNM: string;
  groupId: number;
  brndId: number;
  genericNM: string;
  children?:IMedicationsChildTreeData[];
}

export interface IMedicationsChildTreeData {
  groupId: number;
  groupNM: string;
  brndId: number;
  brndNM: string;
  genericNM: string;
  therapeuticClassDesc: string;
}

export interface IMedicationsTreeFlatNode{
  expandable: boolean;
  brndNM:string;
  level:number;
  groupId: number;
  brndId: number;
  genericNM: string;
}

export interface IAvailableMedicationAsc
{
  brndId: number,
  brndNM: string,
  groupId: number,
  groupNM: string,
  genericNM: string,
  therapeuticClassDesc: string
}

// DMS -- CURRENTLY UNUSED!
// export interface ITherapeuticClasses
// {
//   classId: number,
//   className: string
// }

// DMS -- CURRENTLY UNUSED!
// export interface IMedicationGroups
// {
//   code: number,
//   description: string
// }

// DMS -- CURRENTLY UNUSED!
// export interface ISelectionCriteria
// {
//   selectionType: ICodeDescription[]
// }

// DMS -- CURRENTLY UNUSED!
// export interface ICodeDescription
// {
//   code: string,
//   description: string
// }

export interface IPatientsRegimen {
  id: string,
  levelId: Number,
  medicationGroup: string,
  status: string,
  brandName: string,
  option: string,
  brandId: string,
  discontinuedLookback: number,
  discontinuedRDBtnVal: number,
  currentLookBack: string
}

// DMS -- CURRENTLY UNUSED!
// export interface IRegimenSummary {
//   id: number,
//   levelName: string,
//   medGroupSelections: IRegimenMedSelection
// }

// DMS -- CURRENTLY UNUSED!
// export interface IRegimenMedSelection {
//   medicationGroup: string,
//   status: string,
//   brandName: string[],
//   option: string,
//   discontinuedLookback: string,
//   discontinuedRDBtnVal: string,
// }
