import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, forkJoin } from 'rxjs';
import { RecordResultDialogComponent } from '../record-result-dialog/record-result-dialog.component';
import { RCOurReachCallVM, RCOutreachModel } from '../retention-models';
import { RetentionService } from '../retention.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
  selector: 'outreach-calls',
  templateUrl: './outreach-calls.component.html',
  styleUrls: ['./outreach-calls.component.scss']
})
export class OutreachCallsComponent implements OnInit, OnDestroy {
  outreachCallsData: RCOurReachCallVM = new RCOurReachCallVM();
  siteId: Number = 0;
  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    public dialog: MatDialog,
    public retentionService: RetentionService,
    public userContext: UserContext,
    private auditService: AuditService
  ) { 
    this.auditService.setPageAudit(Page.OutreachCalls);
  }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void 
  {

    this.siteId = this.userContext.GetCurrentSiteValue();
    if (this.siteId != 0) {
      this.loadOutreachCallsData(this.siteId.toString());
    }

    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe((siteId: Number) => {
        this.siteId = siteId;
        this.loadOutreachCallsData(siteId.toString())
      })
    );
         
  }

  loadOutreachCallsData(siteId: string) {
    this.spinnerService.show();
     // Create observables for your API calls
    const outreachCallObservable = this.retentionService.GetOutreachCallDetail(siteId);

    // Use forkJoin to combine observables
    forkJoin([outreachCallObservable]).subscribe(([outreachCallResult]) => {
      if (outreachCallResult) {
        this.outreachCallsData = outreachCallResult;
      }

      // Inside this callback, both API calls have completed
      this.spinnerService.hide();
    });
  }

  
  // Function is used to open Dialog box when user clicks on Record Result list
  openDialog(patient: RCOutreachModel): void {
    this.retentionService.setSelectedPatient(patient);
    const dialogRef = this.dialog.open(RecordResultDialogComponent, {
      width: '430px',
      height: '290px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      this.loadOutreachCallsData(this.siteId.toString());
    });
  }

}
