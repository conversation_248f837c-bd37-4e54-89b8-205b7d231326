import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LoginService } from 'src/app/modules/login/services/login/login.service';
import { DialogResponseComponent } from '../dialog-response/dialog-response.component';


@Component({
  selector: 'app-support-detail-dialog',
  templateUrl: './support-details-dialog.component.html',
  styleUrls: ['./support-details-dialog.component.scss']
})
export class SupportDetailsDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any // The context object
  ) {
    // Assign the data to be used locally
   // this.data = data;
   }
  ngOnInit(): void {

  }

  
  objectKeys(obj: any): string[] {
    return Object.keys(obj);
  }
  

  // Any additional methods or lifecycle hooks needed can be added here
}