.upload{
    font-weight:bold;
    color:#28a745;
    margin-left: 15px;
    line-height: 36px;
}
.footer-text {
    position: absolute;
    bottom: 50px;
    color: gray;
}

.styled-table {
   // border-collapse: collapse;
    margin: 25px 0;
    font-size: 0.9em;
    font-family: sans-serif;
    //box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    border-radius: 15px;
}

.styled-table thead tr {
    background-color: #ffffff;
    color: #66A9D7;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}
.styled-table th {
  padding: 12px 15px;
  border-bottom: 1px solid #dddddd;
  color: silver;
}
.styled-table td {
    padding: 12px 15px;
}
.styled-table tbody tr:hover {
  background-color: #eeeded
}

.styled-table tbody tr {
  //  border-bottom: 1px solid #dddddd;
    color: #66A9D7;
    background-color: #ffffff;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

/*
.styled-table tbody tr:nth-of-type(even) {
    background-color: #ffffff;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}
*/

.styled-table tbody tr:last-of-type {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}
.styled-table tbody tr.active-row {
    font-weight: bold;
    color: #ffffff;
}

tr:last-child {
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}
/*
thead, tbody { display: block; }

tbody {
    overflow-y: auto;
}
*/

.dialog-popup {
  background-color: #f7f7f7;
  border-radius: 5px;
  padding: 10px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
}

.save-btn, .cancel-btn {
  padding: 5px 10px;
  border-radius: 5px;
  background-color: #3498db;
  color: #fcfcff;
  width: 50px;
  font-weight: bold;
  margin-left: 5px;
}

.docIcon {
  filter : grayscale(100);
}

.docIconHeader
{
  padding-left: 20px !important;
}

// Document Library Header Styles
.document-header {
  color: #0071BC;
  font-family: Museo500-Regular;
}

.header-divider {
  padding: 0px;
  margin: 0px;
}

.main-container {
  display: inline-block;
  padding: 0px;
  margin: 0px;
  vertical-align: top;
}

.toolbar-container {
  display: inline-block;
  vertical-align: top;
  width: 100%;
  margin-top: 8px;
}

.toolbar-spacer {
  display: inline-block;
  padding: 0px;
  margin: 0px;
  vertical-align: top;
  height: 5px;
}

.toolbar-buttons {
  display: inline-block;
  padding: 0px;
  margin: 0px;
}

.toolbar-button {
  height: 33px;
  margin-right: 5px;
  width: 100px;
  display: inline-block;
}

.toolbar-button-icon {
  padding: 0px;
  margin: 0px;
}

.toolbar-button-text {
  display: inline-block;
  vertical-align: top;
}

.toolbar-divider {
  display: inline-block;
  border-color: silver;
  color: silver;
  margin-left: 5px;
  margin-right: 5px;
  width: 1px;
  background-color: silver;
}

.breadcrumb-container {
  display: inline-block;
  vertical-align: middle;
  padding: 0px;
  margin: 0px;
}

.breadcrumb-icon {
  color: silver;
}

.breadcrumb-text {
  display: inline-block;
  vertical-align: top;
  color: silver;
}

.breadcrumb-item {
  display: inline-block;
  cursor: pointer;
}

.breadcrumb-separator {
  display: inline-block;
}

// Table Styles
.table-container {
  padding: 0px;
  margin: 0px;
  vertical-align: top;
}

.document-table {
  vertical-align: top;
  margin-top: 8px;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}

.table-header-icon {
  width: 10px;
  cursor: pointer;
  color: #66a9d7;
}

.table-header-name {
  width: 45%;
  cursor: pointer;
  color: #66a9d7;
}

.table-header-size {
  width: 30px;
}

.table-header-modified {
  width: 15%;
  cursor: pointer;
  color: #66a9d7;
}

.table-header-modified-by {
  width: 15%;
  cursor: pointer;
  color: #66a9d7;
}

.table-header-actions {
  width: 30px;
  border-top-right-radius: 15px;
}

.file-name-cell {
  cursor: pointer;
}

.table-cell-clickable {
  cursor: pointer;
}

.table-actions-cell {
  border-bottom-right-radius: 15px;
}

.action-button {
  border: 0px;
  background-color: inherit;
}

// Upload Status Styles
.upload-error {
  color: red;
}

// Modal Styles
.modal-row {
  margin-bottom: 15px;
}

// Clickable link styles
.clickable-link {
  cursor: pointer;
}
