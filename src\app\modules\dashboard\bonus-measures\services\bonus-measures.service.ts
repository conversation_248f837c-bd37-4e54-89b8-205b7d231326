import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiTypes, ApiRoutes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { BonusMeasure } from 'src/app/shared-services/bonus-measures/model/bonus-measures-model';
import { mockBonusMeasures } from 'src/app/shared-services/bonus-measures/mock-data';

@Injectable({
  providedIn: 'root'
})
export class BonusMeasuresService {

  constructor(
    private apiHandler: ApiHandler,
    private userContext: UserContext
  ) { }

  /**
   * Get bonus measures data
   * @param siteId Optional site ID, if not provided will use current site from UserContext
   * @returns Observable with bonus measure data
   */
  public getBonusMeasures(siteId?: string): Observable<BonusMeasure[]> {
    // For now, return mock data for testing
    // TODO: Replace with real API call when ready
    //return of(mockBonusMeasures);

    // Uncomment below when real API is ready:
    
    // Get siteId from parameter or use current site from UserContext
    const currentSiteId = siteId || this.userContext.GetCurrentSiteValue().toString();

    // Replace the siteId placeholder in the API route
    //const apiRoute = ApiRoutes.BonusMeasures.replace('{{siteId}}', currentSiteId);
    const year = new Date().getFullYear();
    const apiRoute = ApiRoutes.BonusMeasuresTestAPI.replace('{{siteId}}', currentSiteId).replace('{{year}}', year.toString());

    // Make the API call and transform the data
    return this.apiHandler.Get<any[]>(
      ApiTypes.V2,
      apiRoute,
      false
    ).pipe(
      map(data => this.transformApiResponse(data))
    );
    
  }

  /**
   * Transform the API response to match our model and add calculated fields
   * @param apiData Raw API response data
   * @returns Transformed bonus measures data
   */
  private transformApiResponse(apiData: any[]): BonusMeasure[] {
    return apiData.map(item => ({
      rowNum: item.RowNum,
      reportId: item.ReportId,
      measureId: item.MeasureId,
      measureCd: item.MeasureCd,
      cohortName: item.CohortName,
      location: item.Location,
      primaryProvider: item.PrimaryProvider,
      reportingPeriod: item.ReportingPeriod,
      rollingWeek: item.RollingWeek,
      invertedFlg: item.InvertedFlg,
      cohortDescription: item.CohortDescription,
      totCohortCnt: item.TotCohortCnt,
      totSchedCnt: item.TotSchedCnt,
      totVisitCnt: item.TotVisitCnt,
      measureNm: item.MeasureNm,
      measureOrd: item.MeasureOrd,
      cohortCnt: item.CohortCnt,
      reportLevelDesc1: item.ReportLevelDesc1,
      reportLevelDesc2: item.ReportLevelDesc2,
      reportLevelDesc3: item.ReportLevelDesc3,
      meetingDesc: item.MeetingDesc,
      notMeetingDesc: item.NotMeetingDesc,
      cohortId: item.CohortId,
      displayOrderNo: item.DisplayOrderNo,
      locationGroupId: item.LocationGroupId,
      locationGroupName: item.LocationGroupName,
      
      // Calculate derived fields
      totalMeasures: item.TotCohortCnt,
      unsatisfiedCount: item.CohortCnt && item.TotCohortCnt ? 
        item.TotCohortCnt - item.CohortCnt : 0,
      satisfiedPercentage: item.CohortCnt && item.TotCohortCnt ? 
        Math.round((item.CohortCnt / item.TotCohortCnt) * 100) : 0,
      unsatisfiedPercentage: item.CohortCnt && item.TotCohortCnt ? 
        Math.round(((item.TotCohortCnt - item.CohortCnt) / item.TotCohortCnt) * 100) : 0
    }));
  }
}
