import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DocumentLibraryComponent } from 'src/app/modules/documents/document-library/document-library.component';
import { DocumentsRoutingModule } from './documents.routing.module';
import { MatIconModule } from '@angular/material/icon';
import {MatMenuModule} from '@angular/material/menu';
import { DownloadComponent } from './download/download.component';

@NgModule({
  declarations: [DocumentLibraryComponent, DownloadComponent],
  imports: [
    CommonModule,
    DocumentsRoutingModule,
    FormsModule,
    MatIconModule,
    MatMenuModule
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
  exports: []
})
export class DocumentsModule {

  constructor(){
    parentModule: DocumentsModule
  }

}
