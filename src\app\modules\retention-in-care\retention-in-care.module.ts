import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RetentionInCareRoutingModule } from './retention-in-care.routing.module';
import { AdministrativeActionItemsComponent } from './administrative-action-items/administrative-action-items.component';
import { LeftMenuNavigationComponent } from './left-menu-navigation/left-menu-navigation.component';
import { OutreachCallsComponent } from './outreach-calls/outreach-calls.component';
import { OutreachRetentionComponent } from './outreach-retention/outreach-retention.component';
import { FullAlertHistoryComponent } from './annotation-status-report/full-alert-history/full-alert-history.component';
import { PatientDetailsComponent } from './annotation-status-report/patient-details/patient-details.component';
import { ProviderPatientsListComponent } from './annotation-status-report/provider-patients-list/provider-patients-list.component';
import { ProvidersListComponent } from './annotation-status-report/providers-list/providers-list.component';
import { AnnotationStatusReportComponent } from './annotation-status-report/annotation-status-report.component';
import { RecordResultDialogComponent } from './record-result-dialog/record-result-dialog.component';
import { MatSelectModule } from '@angular/material/select';
import {MatIconModule} from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';

@NgModule({
  declarations: [
    AdministrativeActionItemsComponent,
    LeftMenuNavigationComponent,
    OutreachCallsComponent,
    OutreachRetentionComponent,
    AnnotationStatusReportComponent,
    FullAlertHistoryComponent,
    PatientDetailsComponent,
    ProviderPatientsListComponent,
    ProvidersListComponent,
    RecordResultDialogComponent
  ],
  imports: [
    CommonModule,
    RetentionInCareRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
  exports: []
})
export class RetentionInCareModule {

  constructor(){
    parentModule: RetentionInCareModule
  }

}
