<div class="searchCriteriaDiv">
        <!-- Cohort Type Row -->
        <div class="field-row">
            <label class="field-label">Cohort Type*:</label>
            <input mat-formfield class="form-select cohort-input" [matAutocomplete]="auto" name="rptpanCohorts"
            #search (blur)="checkCohort(search.value)" (keyup)="comboFilter(search.value)"
            [formControl]="rptpanCohorts" placeholder="Select or search for a cohort type">
              <mat-autocomplete #auto="matAutocomplete">
                <mat-option class="matHeaderAndSubText" *ngFor="let option of cohortsData" [innterText]="option.value" [value]="option.key">
                      <span class="option-main-text">{{option.key}}</span>
                      <div class="option-sub-text">{{option.subtext}}</div>
                </mat-option>
              </mat-autocomplete>
        </div>

        <!-- Measure Year Row -->
        <div class="field-row">
            <label class="field-label" for="rptpanCohortYears">Measure Year*:</label>
            <select class="form-select form-select-md year-select" name="rptpanCohortYears" #years
            id="rptpanCohortYears" (change)="checkYear(years.value)" [formControl]="rptpanCohortYears" title="Select measure year">
                <option *ngFor="let option of cohortYears" [value]="option.key">{{option.value}}</option>
            </select>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="panelService.InitBoldReport()" [disabled]="isRunDisabled"
            id="reportViewer_Control_viewReportClick" aria-describedby="reportViewer_Control_viewReportClick"
            [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
</div>
