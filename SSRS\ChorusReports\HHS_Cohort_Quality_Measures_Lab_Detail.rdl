﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="Quality_Measure_Lab_Details">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@userId">
            <Value>=Parameters!userId.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@measureCd">
            <Value>=Parameters!measureCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@REPORTING_PERIOD">
            <Value>=Parameters!reportingPeriod.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@cohortCd">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@alertLvl">
            <Value>=Parameters!alertLvl.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT	LAB.MRN, 
		LAB.PATIENT_ID, 
		LAB.PATIENT_NAME, 
		LAB.BIRTH_DT, 
		LAB.LST_VST_DT, 
		LAB.COLL_DT,  
		ISNULL(LAB.LAB_CODE_DESC,'') + ISNULL(': '+ LAB.RESULTS,'') AS RESULTS, 
		LAB.LAB_CODE_DESC, 
		LAB.ALERT_LVL, 
		LAB.MEASURE_ID, 
		LAB.PROVIDER_ID, 
		AM.OPEN_FLG,
		EXTRACT_DT,
		LAB.MEASURE_ANNOTATE_ID
FROM REPORT.GET_HHS_COHORT_LAB_DETAIL(@userId, @measureCd, @cohortCd, @providerCd, @locationCd, @REPORTING_PERIOD) AS LAB 
LEFT OUTER JOIN DM.ANNOTATE_MEASURE AS AM ON AM.PATIENT_ID  = LAB.PATIENT_ID 
										 AND AM.PROVIDER_ID = LAB.PROVIDER_ID 
										 AND AM.MEASURE_ID  = LAB.MEASURE_ID  
										 AND AM.REPORTING_PERIOD = @REPORTING_PERIOD
WHERE (CASE WHEN @alertLvl IS NULL THEN ALERT_LVL WHEN @alertLvl = '' OR @alertLvl = 0 THEN 0 ELSE 1 END = LAB.ALERT_LVL) </CommandText>
      </Query>
      <Fields>
        <Field Name="MRN">
          <DataField>MRN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PATIENT_ID">
          <DataField>PATIENT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PATIENT_NAME">
          <DataField>PATIENT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BIRTH_DT">
          <DataField>BIRTH_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LST_VST_DT">
          <DataField>LST_VST_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="COLL_DT">
          <DataField>COLL_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="RESULTS">
          <DataField>RESULTS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LAB_CODE_DESC">
          <DataField>LAB_CODE_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ALERT_LVL">
          <DataField>ALERT_LVL</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROVIDER_ID">
          <DataField>PROVIDER_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="OPEN_FLG">
          <DataField>OPEN_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ANNOTATE_ID">
          <DataField>MEASURE_ANNOTATE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.41042in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.7125in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.44255in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.85382in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.85625in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.85459in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.98842in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.80875in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.46759in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>HHS</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>MRN</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Name</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Birth</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox45">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Last Visit Date</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox44">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Last Collection</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Results</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Comments</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.18634in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!ALERT_LVL.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox307">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox307</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox308">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!PATIENT_NAME.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox308</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox309">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!BIRTH_DT.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox309</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox310">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!LST_VST_DT.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox310</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox311">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!COLL_DT.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox311</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox312">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!RESULTS.Value</SortExpression>
                            <SortTarget>Quality_Measure_Lab_Details</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox312</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25926in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <GaugePanel Name="GaugePanel1">
                          <StateIndicators>
                            <StateIndicator Name="Alert">
                              <GaugeInputValue>
                                <Value>=iif(Fields!ALERT_LVL.Value="True",1,0)</Value>
                                <Multiplier>1</Multiplier>
                                <DataElementOutput>NoOutput</DataElementOutput>
                              </GaugeInputValue>
                              <TransformationType>None</TransformationType>
                              <TransformationScope>Tablix2</TransformationScope>
                              <MinimumValue>
                                <Value>NaN</Value>
                                <Multiplier>1</Multiplier>
                              </MinimumValue>
                              <MaximumValue>
                                <Value>NaN</Value>
                                <Multiplier>1</Multiplier>
                              </MaximumValue>
                              <IndicatorStyle>None</IndicatorStyle>
                              <IndicatorImage>
                                <Source>External</Source>
                                <Value />
                              </IndicatorImage>
                              <ScaleFactor>1</ScaleFactor>
                              <IndicatorStates>
                                <IndicatorState Name="ThreeSignsDiamond">
                                  <StartValue>
                                    <Value>0</Value>
                                    <Multiplier>1</Multiplier>
                                  </StartValue>
                                  <EndValue>
                                    <Value>0.5</Value>
                                    <Multiplier>1</Multiplier>
                                  </EndValue>
                                  <Color>Red</Color>
                                  <ScaleFactor>1</ScaleFactor>
                                  <IndicatorStyle>Circle</IndicatorStyle>
                                  <IndicatorImage>
                                    <Source>External</Source>
                                    <Value />
                                  </IndicatorImage>
                                </IndicatorState>
                              </IndicatorStates>
                              <Angle>0</Angle>
                              <StateDataElementName />
                              <rd:IconsSet>ThreeSigns</rd:IconsSet>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                </Border>
                                <ShadowOffset>2pt</ShadowOffset>
                              </Style>
                              <Top>4</Top>
                              <Left>4</Left>
                              <Height>92</Height>
                              <Width>92</Width>
                              <ActionInfo>
                                <Actions>
                                  <Action>
                                    <Drillthrough>
                                      <ReportName>PatientFlowsheet</ReportName>
                                      <Parameters>
                                        <Parameter Name="DEMOGRAPHICS_ID">
                                          <Value>=Fields!PATIENT_ID.Value</Value>
                                        </Parameter>
                                        <Parameter Name="USER_ID">
                                          <Value>=Parameters!userId.Value</Value>
                                        </Parameter>
                                      </Parameters>
                                    </Drillthrough>
                                  </Action>
                                </Actions>
                              </ActionInfo>
                            </StateIndicator>
                          </StateIndicators>
                          <BackFrame>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>Gainsboro</BackgroundColor>
                              <BackgroundGradientType>DiagonalLeft</BackgroundGradientType>
                              <BackgroundGradientEndColor>Gray</BackgroundGradientEndColor>
                              <BackgroundHatchType>None</BackgroundHatchType>
                              <ShadowOffset>0pt</ShadowOffset>
                            </Style>
                            <FrameBackground>
                              <Style>
                                <BackgroundColor>Silver</BackgroundColor>
                                <BackgroundGradientType>DiagonalLeft</BackgroundGradientType>
                                <BackgroundGradientEndColor>Gray</BackgroundGradientEndColor>
                                <BackgroundHatchType>None</BackgroundHatchType>
                              </Style>
                            </FrameBackground>
                            <FrameImage>
                              <Source>External</Source>
                              <Value />
                            </FrameImage>
                          </BackFrame>
                          <TopImage>
                            <Source>External</Source>
                            <Value />
                          </TopImage>
                          <DataSetName>Quality_Measure_Lab_Details</DataSetName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                          </Style>
                        </GaugePanel>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MRN">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MRN.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MRN</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PATIENT_NAME">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PATIENT_NAME.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.00;(0.00)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PATIENT_NAME</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="BIRTH_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Format(Cdate(FormatDateTime(Fields!BIRTH_DT.Value,DateFormat.ShortDate)),"yyyy-MM-dd")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>BIRTH_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LST_VST_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Format(Cdate(FormatDateTime(Fields!LST_VST_DT.Value,DateFormat.ShortDate)),"yyyy-MM-dd")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LST_VST_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="COLL_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(FormatDateTime(Fields!COLL_DT.Value,DateFormat.ShortDate) = DateValue("January 1, 0001"), "", Format(Cdate(FormatDateTime(Fields!COLL_DT.Value,DateFormat.ShortDate)),"yyyy-MM-dd"))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>yyyy-MM-dd</Format>
                                    <Color>=IIF(Year(Fields!COLL_DT.Value) &lt;&gt; Parameters!reportingPeriod.Value AND Fields!ALERT_LVL.Value = IIF(Parameters!inverted_flg.Value = TRUE, "1", "0"),"Red","Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>COLL_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, IIF(Fields!COLL_DT.Value = DateValue("January 1, 0001"),"Red", "Silver"), IIF(Fields!COLL_DT.Value = DateValue("January 1, 0001"),"Red","White"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="RESULTS">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!RESULTS.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>=IIF(Year(Fields!COLL_DT.Value) &lt;&gt; Parameters!reportingPeriod.Value AND Fields!ALERT_LVL.Value = IIF(Parameters!inverted_flg.Value = TRUE, "1", "0"),"Red","Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>RESULTS</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>PatientFlowsheet</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!PATIENT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!userId.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, IIF(ISNOTHING(Fields!RESULTS.Value) OR Fields!RESULTS.Value="" ,"Red", "Silver"), IIF(ISNOTHING(Fields!RESULTS.Value) OR Fields!RESULTS.Value="" ,"Red","White"))</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Annotate</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>= IIf(isNothing(Fields!OPEN_FLG.Value),"Blue",IIF(Fields!OPEN_FLG.Value=TRUE,"Red","Green"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Hyperlink>= "javascript:var a=  OpenModalbox('../_layouts/15/Chorus/Member/Anotation.aspx?DemographicId="+Fields!PATIENT_ID.Value.ToString()+"&amp;ProviderId="+Fields!PROVIDER_ID.Value.ToString()+"&amp;MeasureId="+Fields!MEASURE_ID.Value.ToString()+"&amp;ReportingYear="+Parameters!reportingPeriod.Value.ToString()+"&amp;MeasureAnnotateId="+Fields!MEASURE_ANNOTATE_ID.Value.ToString()+"&amp;VisitDate=0"+"', '500px', '730px');"</Hyperlink>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.2pt</PaddingLeft>
                            <PaddingRight>0.2pt</PaddingRight>
                            <PaddingTop>0.2pt</PaddingTop>
                            <PaddingBottom>0.2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <RepeatColumnHeaders>true</RepeatColumnHeaders>
            <DataSetName>Quality_Measure_Lab_Details</DataSetName>
            <Top>0.55625in</Top>
            <Height>0.91319in</Height>
            <Width>7.9273in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <LineHeight>1pt</LineHeight>
            </Style>
          </Tablix>
          <Textbox Name="Textbox16">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=FormatNumber(Parameters!cohortCnt.Value, 0)</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=IIf(Parameters!alertLvl.Value=1, " patient/s meet the ", " patient/s do not meet the")</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> g</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>uideline: </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!guidelineDesc.Value</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>13pt</FontSize>
                      <FontWeight>Normal</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#272424</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox13</rd:DefaultName>
            <Top>0.17639cm</Top>
            <Left>0.00003cm</Left>
            <Height>1.23648cm</Height>
            <Width>20.12073cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>Lavender</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>1.46944in</Height>
        <Style />
      </Body>
      <Width>7.9528in</Width>
      <Page>
        <PageHeader>
          <Height>0.64931in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Left>5.79167in</Left>
              <Height>0.58333in</Height>
              <Width>2.12989in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox48">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Total Patients for " &amp; Parameters!cohortNm.Value &amp; " Cohort" &amp;  IIf(IsNothing(Parameters!locationCd.Value) OR Parameters!locationCd.Value ="","", " -&gt; " &amp; Parameters!locationCd.Value) &amp; IIf(IsNothing(Parameters!providerCd.Value) OR Parameters!providerCd.Value="","", " -&gt; " &amp; Parameters!providerCd.Value) &amp; ": " &amp; Parameters!totPatients.Value</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.00002cm</Top>
              <Left>0.00001in</Left>
              <Height>0.57986in</Height>
              <Width>5.72222in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Line Name="Line1">
              <Top>0.22792in</Top>
              <Left>0.00001in</Left>
              <Height>0in</Height>
              <Width>4.89312in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
              </Style>
            </Line>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>1.39583in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox52">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>DATA NOTES</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>The HHS HIV Standards of Care measures are derived from the Laboratory Monitoring Schedule of the “Guidelines for the Use of Antiretroviral Agents in HIV-1 infected Adults and Adolescents”, last updated on April 8, 2015  by the US National Institutes of Health (see http://aidsinfo.nih.gov/guidelines). The Other Quality Measures are additional measures created by Epividian by medical experts in the OPERA® research network.</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>For patients with a recent HIV diagnosis (last 6 months) or are new to the practice (first visit was within the last 6 months), the Engaged Patient measure is adjusted to allow for only one visit in the last year.</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Patients who are deceased are included in these measures.</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>White</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox52</rd:DefaultName>
              <Height>1.39583in</Height>
              <Width>7.9528in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <BackgroundColor>#0b6c9f</BackgroundColor>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="labCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labcd</Prompt>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>String</DataType>
      <Prompt>COHORT_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="parentLabCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>parentlab</Prompt>
    </ReportParameter>
    <ReportParameter Name="measureCd">
      <DataType>String</DataType>
      <Prompt>measurecd</Prompt>
    </ReportParameter>
    <ReportParameter Name="alertLvl">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>Visable:</Prompt>
      <ValidValues>
        <ParameterValues>
          <ParameterValue>
            <Label>All</Label>
          </ParameterValue>
          <ParameterValue>
            <Value>0</Value>
            <Label>Does Not Meet Guideline</Label>
          </ParameterValue>
          <ParameterValue>
            <Value>1</Value>
            <Label>Meets Guideline</Label>
          </ParameterValue>
        </ParameterValues>
      </ValidValues>
    </ReportParameter>
    <ReportParameter Name="guidelineDesc">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Guideline:</Prompt>
    </ReportParameter>
    <ReportParameter Name="totPatients">
      <DataType>String</DataType>
      <Prompt>Total Patients:</Prompt>
    </ReportParameter>
    <ReportParameter Name="labCd2">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd2</Prompt>
    </ReportParameter>
    <ReportParameter Name="labCd3">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd3</Prompt>
    </ReportParameter>
    <ReportParameter Name="cohortCnt">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>locationCd</Prompt>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>providerCd</Prompt>
    </ReportParameter>
    <ReportParameter Name="toggleLocation">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleLocation</Prompt>
    </ReportParameter>
    <ReportParameter Name="toggleProvider">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleProvider</Prompt>
    </ReportParameter>
    <ReportParameter Name="userId">
      <DataType>String</DataType>
      <Prompt>userId</Prompt>
    </ReportParameter>
    <ReportParameter Name="cohortNm">
      <DataType>String</DataType>
      <Prompt>cohortNm</Prompt>
    </ReportParameter>
    <ReportParameter Name="rollingWeek">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>rollingWeek</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="reportingPeriod">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>reportingPeriod</Prompt>
    </ReportParameter>
    <ReportParameter Name="measureCnt">
      <DataType>Integer</DataType>
      <Prompt>measureCnt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="inverted_flg">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>inverted_flg</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>3</NumberOfColumns>
      <NumberOfRows>11</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>labCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>parentLabCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>measureCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>alertLvl</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>guidelineDesc</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>totPatients</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>labCd2</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>labCd3</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>cohortCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>toggleLocation</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>7</RowIndex>
          <ParameterName>toggleProvider</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>7</RowIndex>
          <ParameterName>userId</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>8</RowIndex>
          <ParameterName>cohortNm</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>8</RowIndex>
          <ParameterName>rollingWeek</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>8</RowIndex>
          <ParameterName>reportingPeriod</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>9</RowIndex>
          <ParameterName>measureCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>10</RowIndex>
          <ParameterName>inverted_flg</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Language>en-US</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>6abdf4ea-82a8-4e68-977e-c644bc4f81d6</rd:ReportID>
</Report>