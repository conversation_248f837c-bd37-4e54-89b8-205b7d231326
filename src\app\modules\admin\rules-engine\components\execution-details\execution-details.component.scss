.execution-details-container {
  padding: 16px 0;

  .process-header {
    margin-bottom: 20px;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 100px;
      }

      .value {
        flex: 1;
      }
      
      .guid-full {
        color: #999;
        font-size: 0.85em;
      }
    }
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.success {
        background-color: #e6f4ea;
        color: #137333;
      }

      &.error {
        background-color: #fce8e6;
        color: #c5221f;
      }

      &.running {
        background-color: #e8f0fe;
        color: #1a73e8;
      }
    }
  }

  h3 {
    margin-top: 24px;
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
  }

  .no-results-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  table {
    width: 100%;
    margin-bottom: 16px;
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.success {
        background-color: #e6f4ea;
        color: #137333;
      }

      &.error {
        background-color: #fce8e6;
        color: #c5221f;
      }
    }
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      color: #999;
    }

    p {
      font-size: 16px;
    }
  }
  
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
  gap: 8px;
}
