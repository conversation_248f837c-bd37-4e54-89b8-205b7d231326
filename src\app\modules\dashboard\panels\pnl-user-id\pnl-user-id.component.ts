import { Component, OnInit } from '@angular/core';
import { PanelService } from '../PanelService';
import { btnColor } from '../report-panel-enums';

@Component({
  selector: 'pnl-user-id',
  templateUrl: './pnl-user-id.component.html',
  styleUrls: ['./pnl-user-id.component.scss']
})
export class PnlUserIdComponent implements OnInit {

  noSearchPan: boolean = false;
  rptbtnColor: string;
  isRunDisabled: boolean = false;

  constructor(public panelService:PanelService) {
    this.rptbtnColor = btnColor.btnPrimaryColor;
  }

  ngOnInit(): void {
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
