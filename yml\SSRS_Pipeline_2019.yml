# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger: none

pool:
  vmImage: 'windows-latest'

variables:
  solution: 'SSRS\ChorusReports.sln'
  buildPlatform: 'Default'
  buildConfiguration: 'Debug 2019'

steps:
- task: VSBuild@1
  inputs:
    solution: '$(solution)'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'
    msbuildArguments: '/p:SkipInvalidConfigurations=true /p:DeployOnBuild=true /p:WebPublishMethod=FileSystem /p:publishUrl="$(build.artifactstagingdirectory)\\" /p:DeployDefaultTarget=WebPublish'

- task: CopyFiles@2
  inputs:
    sourceFolder: "$(system.defaultworkingdirectory)/"
    contents: |
      SSRS/**/*.rdl
      SSRS/**/yml/**
      SSRS/**/*.rds
      SSRS/**/*.rsd
      SSRS/**/bin/**
    targetFolder: '$(Build.artifactStagingDirectory)'

- task: PublishBuildArtifacts@1
  inputs:
    PathToPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'SSRS_Packages'
    publishLocation: 'Container'