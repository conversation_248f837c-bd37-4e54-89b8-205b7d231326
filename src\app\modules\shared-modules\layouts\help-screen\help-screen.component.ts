import { Component } from '@angular/core';
import {NestedTreeControl} from '@angular/cdk/tree';
import {MatTreeNestedDataSource} from '@angular/material/tree';
import { helpScreenService } from './services/help/help-screen.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ActivatedRoute } from '@angular/router';

interface HelpTree {
  name: string;
  id: string;
  isGroup: boolean;
  children?: HelpTree[];
}

@Component({
  selector: 'app-help-screen',
  templateUrl: './help-screen.component.html',
  styleUrls: ['./help-screen.component.css'],
})
export class HelpScreenComponent {
  siteId: string = '0';
  helpTree= new NestedTreeControl<HelpTree>(node => node.children);
  helpDataSource = new MatTreeNestedDataSource<HelpTree>();
  public helpParents: any[] = [];
  public helpContent: SafeHtml = "";

  constructor(public helpService: helpScreenService, 
              private sanitizer: DomSanitizer,
              private activeRoute: ActivatedRoute,) {}

  ngOnInit() {
      //Bind the site context to the page.
      this.siteId = this.activeRoute.snapshot.paramMap.get('siteId') ?? '0';
      this.getHelpIds(this.siteId);
  }

  public getHelpIds(siteId: string){
    this.helpService.getHelpHeaders(siteId).subscribe((res) => {
      res.forEach(element => {
        element.children?.sort((a, b) => (a.name < b.name ? -1 : 1));
      });
      this.helpDataSource.data  = res;
    });
  }

  public getHelpContent(item: HelpTree, siteId: string){
    this.helpService.getHelpContent(Number(item.id),siteId).subscribe((res) => {
      if (res.body===undefined)
      {
        res.body = "";
      }
      this.helpContent = this.sanitizer.bypassSecurityTrustHtml("<h4>" + item.name + "</h4><br/>" + res.body);
    });
  }

  hasChild = (_: number, node: HelpTree) => !!node.children && node.children.length > 0;

}
