﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.80617in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.80617in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.47917in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Open</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Width>1.1pt</Width>
                            </TopBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Closed</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Width>1.1pt</Width>
                            </TopBorder>
                            <RightBorder>
                              <Color>Gray</Color>
                              <Width>1.1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= SUM(Fields!OPEN_FLG.Value)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Annotation_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="StatusFlg">
                                      <Value>=true</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PROVIDER_NM"),Fields!PROVIDER_NM.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="measureCd">
                                      <Value>=iif(inscope("MEASURE_CD"),Fields!MEASURE_CD.Value,iif(Parameters!drillthroughMeasureCd.Value &lt;&gt; "", Parameters!drillthroughMeasureCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=Sum(Fields!OPEN_FLG.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="REPORTING_PERIOD">
                                      <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>1.1pt</Width>
                            </Border>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= SUM(Fields!CLOSED_FLG.Value)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>DarkGreen</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Annotation_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="StatusFlg">
                                      <Value>=false</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION_NM"),Fields!LOCATION_NM.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PROVIDER_NM"),Fields!PROVIDER_NM.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="measureCd">
                                      <Value>=iif(inscope("MEASURE_CD"),Fields!MEASURE_CD.Value,iif(Parameters!drillthroughMeasureCd.Value &lt;&gt; "", Parameters!drillthroughMeasureCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>= SUM(Fields!CLOSED_FLG.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="REPORTING_PERIOD">
                                      <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>1.1pt</Width>
                            </Border>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>1.77503in</Size>
                    <CellContents>
                      <Textbox Name="Textbox25">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Location</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox25</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Color>Gray</Color>
                            <Width>1.1pt</Width>
                          </TopBorder>
                          <LeftBorder>
                            <Color>Gray</Color>
                            <Width>1.1pt</Width>
                          </LeftBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.10616in</Size>
                        <CellContents>
                          <Textbox Name="Textbox21">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>Provider</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox21</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <TopBorder>
                                <Color>Gray</Color>
                                <Width>1.1pt</Width>
                              </TopBorder>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>2.2145in</Size>
                            <CellContents>
                              <Textbox Name="Textbox23">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>#</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox23</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <TopBorder>
                                    <Color>Gray</Color>
                                    <Width>1.1pt</Width>
                                  </TopBorder>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <VerticalAlign>Middle</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <FixedData>true</FixedData>
                              <RepeatOnNewPage>true</RepeatOnNewPage>
                            </TablixMember>
                          </TablixMembers>
                          <FixedData>true</FixedData>
                          <RepeatOnNewPage>true</RepeatOnNewPage>
                        </TablixMember>
                      </TablixMembers>
                      <FixedData>true</FixedData>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="All">
                    <GroupExpressions>
                      <GroupExpression>All</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>All</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>0.61033in</Size>
                    <CellContents>
                      <Textbox Name="Group1">
                        <CanGrow>true</CanGrow>
                        <ToggleImage>
                          <InitialState>true</InitialState>
                        </ToggleImage>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>All</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Group1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                            <Width>1.1pt</Width>
                          </Border>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>Lavender</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="LOCATION_NM">
                        <GroupExpressions>
                          <GroupExpression>=Fields!LOCATION_NM.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!LOCATION_NM.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>1.1647in</Size>
                        <CellContents>
                          <Textbox Name="LOCATION_NM1">
                            <CanGrow>true</CanGrow>
                            <ToggleImage>
                              <InitialState>=iif(CountDistinct(Fields!LOCATION_NM.Value,"LOCATION_NM") = 1,True,False)</InitialState>
                            </ToggleImage>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!LOCATION_NM.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>LOCATION_NM1</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>Gray</Color>
                                <Style>Solid</Style>
                                <Width>1.1pt</Width>
                              </Border>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>Lavender</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="PROVIDER_NM">
                            <GroupExpressions>
                              <GroupExpression>=Fields!PROVIDER_NM.Value</GroupExpression>
                            </GroupExpressions>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Fields!PROVIDER_NM.Value</Value>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>1.10616in</Size>
                            <CellContents>
                              <Textbox Name="PROVIDER_NM">
                                <CanGrow>true</CanGrow>
                                <ToggleImage>
                                  <InitialState>=iif(CountDistinct(Fields!PROVIDER_NM.Value,"PROVIDER_NM") = 1,True,False)</InitialState>
                                </ToggleImage>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!PROVIDER_NM.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>PROVIDER_NM</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>Gray</Color>
                                    <Style>Solid</Style>
                                    <Width>1.1pt</Width>
                                  </Border>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>Lavender</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <Group Name="MEASURE_CD">
                                <GroupExpressions>
                                  <GroupExpression>=Fields!MEASURE_CD.Value</GroupExpression>
                                </GroupExpressions>
                              </Group>
                              <SortExpressions>
                                <SortExpression>
                                  <Value>=Fields!MEASURE_CD.Value</Value>
                                </SortExpression>
                              </SortExpressions>
                              <TablixHeader>
                                <Size>2.2145in</Size>
                                <CellContents>
                                  <Textbox Name="MEASURE_CD1">
                                    <CanGrow>true</CanGrow>
                                    <ToggleImage>
                                      <InitialState>=iif(CountDistinct(Fields!MEASURE_CD.Value,"MEASURE_CD") = 1 , True,False)</InitialState>
                                    </ToggleImage>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>=Fields!MEASURE_CD.Value &amp; ":" &amp; Fields!NAME.Value</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Left</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>MEASURE_CD1</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>Gray</Color>
                                        <Style>Solid</Style>
                                        <Width>1.1pt</Width>
                                      </Border>
                                      <LeftBorder>
                                        <Style>None</Style>
                                      </LeftBorder>
                                      <BackgroundColor>Lavender</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <Group Name="Details" />
                                </TablixMember>
                              </TablixMembers>
                              <Visibility>
                                <Hidden>=iif(CountDistinct(Fields!MEASURE_CD.Value,"MEASURE_CD")=1,False,True)</Hidden>
                                <ToggleItem>PROVIDER_NM</ToggleItem>
                              </Visibility>
                            </TablixMember>
                          </TablixMembers>
                          <Visibility>
                            <Hidden>=iif(CountDistinct(Fields!PROVIDER_NM.Value,"PROVIDER_NM")=1,False,True)</Hidden>
                            <ToggleItem>LOCATION_NM1</ToggleItem>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                      <Visibility>
                        <Hidden>=iif(CountDistinct(Fields!LOCATION_NM.Value,"LOCATION_NM")=1,False,True)</Hidden>
                        <ToggleItem>Group1</ToggleItem>
                      </Visibility>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_ANNOTATE_MEASURE</DataSetName>
            <Top>0.3175cm</Top>
            <Left>0.27517cm</Left>
            <Height>0.72917in</Height>
            <Width>6.70803in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>1.42917in</Height>
        <Style />
      </Body>
      <Width>8.22208in</Width>
      <Page>
        <PageHeader>
          <Height>0.78698in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Left>0.00785in</Left>
              <Height>1.49688cm</Height>
              <Width>4.51084cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox13">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Provider Annotation Status Report</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Top>0.01522cm</Top>
              <Left>4.70716cm</Left>
              <Height>1.48166cm</Height>
              <Width>9.30834cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox14">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Normal</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox14</rd:DefaultName>
              <Top>1.80313cm</Top>
              <Height>0.02646cm</Height>
              <Width>20.88409cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Color>Purple</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </TopBorder>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.01522cm</Top>
              <Left>5.96236in</Left>
              <Height>0.58333in</Height>
              <Width>2.12639in</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageWidth>12in</PageWidth>
        <LeftMargin>0.5in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
      <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
<DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_ANNOTATE_MEASURE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@REPORTING_YEAR">
            <Value>=Parameters!REPORTING_YEAR.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>
					SELECT LOCATION_NM, PROVIDER_NM, MEASURE_CD, OPEN_FLG, CLOSED_FLG,NAME
					FROM [REPORT].[GET_PROVIDER_ANNOTATION_STATUS_DTL](@USER_ID, @REPORTING_YEAR)
				</CommandText>
      </Query>
      <Fields>
        <Field Name="LOCATION_NM">
          <DataField>LOCATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PROVIDER_NM">
          <DataField>PROVIDER_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NAME">
          <DataField>NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OPEN_FLG">
          <DataField>OPEN_FLG</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CLOSED_FLG">
          <DataField>CLOSED_FLG</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					DECLARE @SITE_ID INT
					SET                @SITE_ID =
					(SELECT        REPLACE(DB_NAME(), 'SITE', ''))
					SELECT        SITE_NM, CITY_TXT, STATE_TXT
					FROM            [CHORUS].[ADMIN].[SITE]
					WHERE        (STATUS_CD = 'A') AND (SITE_ID = @SITE_ID)
				</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					SELECT        EXTRACT_DT
					FROM            CLEAN.SITE
				</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="drillthroughProviderCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughProviderCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughLocationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughLocationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughMeasureCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughMeasureCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughName">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughName</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="REPORTING_YEAR">
      <DataType>String</DataType>
      <Prompt>REPORTING_YEAR</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>faef4e45-6f86-4d5f-ae3d-9230d18844b3</rd:ReportID>
</Report>