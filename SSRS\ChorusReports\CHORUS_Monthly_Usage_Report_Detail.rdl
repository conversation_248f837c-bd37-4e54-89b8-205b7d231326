<?xml version="1.0" encoding="utf-8"?>
<Report MustUnderstand="df" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:df="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition/defaultfontfamily">
  <df:DefaultFontFamily>Segoe UI</df:DefaultFontFamily>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsChorus">
      <DataSourceReference>dsChorus</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>53080eff-f8b3-4dd4-b857-c32e2d9acc23</rd:DataSourceID>
    </DataSource>
    <DataSource Name="dsAuthProvider">
      <DataSourceReference>dsAuthProvider</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>d428b2a6-393c-4efd-a2c8-553123c6f2ae</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="AllUsers">
      <Query>
        <DataSourceName>dsChorus</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@SITE_ID">
            <Value>=Parameters!SITE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@TYPE">
            <Value>=Parameters!TYPE.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@LOGINMONTHFIRSTDAY">
            <Value>=Parameters!LOGINMONTHFIRSTDAY.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@NPI_ID">
            <Value>=Parameters!NPI_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>DECLARE @siteDb SYSNAME;
DECLARE @sql NVARCHAR(MAX) = N''

-- Step 1: Pre-create temp table
CREATE TABLE #ProviderData (
    DB_NAME SYSNAME,
    SITE_ID INT,
    FIRST_NM NVARCHAR(255),
    LAST_NM NVARCHAR(255),
    NPI_ID NVARCHAR(255)
);


DECLARE @databases TABLE (
    Site_ID INT,
    DbName SYSNAME,
    Site_NM NVARCHAR(255)
);

INSERT INTO @databases (Site_ID, DbName, Site_NM)
SELECT Site_ID, 'SITE' + CAST(Site_ID AS VARCHAR), SITE_NM
FROM ADMIN.SITE
WHERE Site_ID NOT IN (0, 9901, 9902);

-- Step 2: Build dynamic SQL to insert into temp table from existing SITE databases
DECLARE db_cursor CURSOR FOR
SELECT Site_ID, DbName FROM @databases;

OPEN db_cursor;
FETCH NEXT FROM db_cursor INTO @siteDb, @siteDb;

WHILE @@FETCH_STATUS = 0
BEGIN
    BEGIN TRY
        IF EXISTS (
            SELECT 1 
            FROM sys.databases 
            WHERE name = @siteDb AND state = 0
        )
        BEGIN
            DECLARE @checkTableSQL NVARCHAR(MAX);
            SET @checkTableSQL = 'IF EXISTS (SELECT 1 FROM [' + @siteDb + '].INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ''CLEAN'' AND TABLE_NAME = ''PROVIDER'')
            BEGIN
                INSERT INTO #ProviderData
                SELECT ''' + @siteDb + ''' AS DB_NAME, SITE_ID, FIRST_NM, LAST_NM, NPI_ID 
                FROM [' + @siteDb + '].CLEAN.PROVIDER 
                WHERE NPI_ID IS NOT NULL;
            END';
            EXEC sp_executesql @checkTableSQL;
        END
    END TRY
    BEGIN CATCH
        PRINT 'Skipping database ' + @siteDb + ' due to error.';
    END CATCH
    FETCH NEXT FROM db_cursor INTO @siteDb, @siteDb;
END

CLOSE db_cursor;
DEALLOCATE db_cursor;

-- Step 3: Use #ProviderData in main query

WITH SITE_NAMES AS (
    SELECT Site_ID,
           CASE Site_ID
                WHEN 117 THEN '117 (AHF)'
                WHEN 107 THEN '107 (WFH)'
                WHEN 109 THEN '109 (Sinai)'
                WHEN 110 THEN '110 (Mills)'
                WHEN 116 THEN '116 (FIGHT)'
                WHEN 123 THEN '123 (Amity)'
                WHEN 124 THEN '124 (Rosedale)'
                WHEN 125 THEN '125 (Neighborhood)'
                WHEN 126 THEN '126 (CAN)'
                WHEN 127 THEN '127 (Fairgrove)'
                WHEN 131 THEN '131 (Ingham)'
                ELSE CAST(Site_ID AS VARCHAR) + ' (' + Site_NM + ')'
           END AS SITE_NAME
    FROM @databases
)


SELECT
USERNAME,
LOGIN_COUNT,
LoginMonthFirstDay,
SITE_ID,
TYPE,
NPI_ID
FROM
(
-- App Logins
SELECT DISTINCT User_NM AS USERNAME,
	    COUNT(Login_ID) AS LOGIN_COUNT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,sg.SITE_ID
	  ,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal') AS TYPE
	  ,NPI_ID
  FROM [CHORUS].[CHORUS].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USER_NM
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
	LEFT JOIN #ProviderData PD ON (SG.SITE_ID = PD.SITE_ID AND UD.FIRST_NM = PD.FIRST_NM AND UD.LAST_NM = PD.LAST_NM)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND EMAIL_TXT not like '%@epividian.com'
	 AND SG.SITE_ID = @SITE_ID
	 AND Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal') = @TYPE
	 AND CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) = @LOGINMONTHFIRSTDAY
  GROUP BY CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USER_NM,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal'), NPI_ID
 UNION
  -- Web Portal Logins
SELECT DISTINCT Username,	
	    COUNT(Login_ID) AS LOGIN_COUNT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,sg.SITE_ID
	  ,'Web Portal' AS TYPE
	  ,NPI_ID
  FROM [Authprovider].[dbo].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USERNAME
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
	LEFT JOIN #ProviderData PD ON (SG.SITE_ID = PD.SITE_ID AND UD.FIRST_NM = PD.FIRST_NM AND UD.LAST_NM = PD.LAST_NM)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND USERNAME not like '%@epividian.com'
	 AND SG.SITE_ID = @SITE_ID
	 AND @TYPE = 'Web Portal'
	 AND CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) = @LOGINMONTHFIRSTDAY
  GROUP BY CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USERNAME, NPI_ID) A
WHERE 1 = CASE
      WHEN @NPI_ID = 'None' then 1
      WHEN NPI_ID IS NOT NULL THEN 1
      END

	  	  DROP TABLE IF EXISTS #ProviderData;</CommandText>
      </Query>
      <Fields>
        <Field Name="USERNAME">
          <DataField>USERNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LOGIN_COUNT">
          <DataField>LOGIN_COUNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LoginMonthFirstDay">
          <DataField>LoginMonthFirstDay</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SITE_ID">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TYPE">
          <DataField>TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NPI_ID">
          <DataField>NPI_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>0cm</Top>
            <Left>0cm</Left>
            <Height>1.67993cm</Height>
            <Width>5.312cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="HIVFlowsheet">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>List of Users</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#0097c4</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <Left>2.09134in</Left>
            <Height>0.66221in</Height>
            <Width>6.00213in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>1pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox47">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Epividian® CHORUS</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>™</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> Report</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Top>0.01389in</Top>
            <Left>8.10736in</Left>
            <Height>0.38267in</Height>
            <Width>2.16862in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.44792in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.36458in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.96875in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.90625in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.82611in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>User</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value># logins per month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Web Portal or App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>NPI ID</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!USERNAME.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                  <Style>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LOGIN_COUNT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TYPE.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NPI_ID.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>AllUsers</DataSetName>
            <Top>0.71777in</Top>
            <Left>0.02778in</Left>
            <Height>0.5in</Height>
            <Width>9.51361in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Through: </Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Today()</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <Format>yyyy-MM-dd</Format>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>0.43823in</Top>
            <Left>8.10736in</Left>
            <Height>0.25in</Height>
            <Width>2.16862in</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>2.33208in</Height>
        <Style />
      </Body>
      <Width>10.28987in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="SITE_ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>SITE ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="TYPE">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>TYPE</Prompt>
    </ReportParameter>
    <ReportParameter Name="LOGINMONTHFIRSTDAY">
      <DataType>DateTime</DataType>
      <Nullable>true</Nullable>
      <Prompt>LOGINMONTHFIRSTDAY</Prompt>
    </ReportParameter>
    <ReportParameter Name="NPI_ID">
      <DataType>String</DataType>
      <Prompt>NPI ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>3</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>SITE_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>TYPE</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>LOGINMONTHFIRSTDAY</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>NPI_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>26ee0a8d-d30e-495c-bfdd-a7ec12a4705f</rd:ReportID>
</Report>