import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-json-toolbar',
  templateUrl: './json-toolbar.component.html',
  styleUrls: ['./json-toolbar.component.scss']
})
export class JsonToolbarComponent {
  @Input() viewMode: 'object' | 'json' = 'object';
  @Input() hasError: boolean = false;
  @Output() viewModeChange = new EventEmitter<'object' | 'json'>();
  @Output() applyChanges = new EventEmitter<void>();
  @Output() formatJson = new EventEmitter<void>();

  /**
   * Change the view mode
   * @param mode The new view mode
   */
  onViewModeChange(mode: 'object' | 'json'): void {
    this.viewMode = mode;
    this.viewModeChange.emit(mode);
  }

  /**
   * Apply changes
   */
  onApplyChanges(): void {
    this.applyChanges.emit();
  }

  /**
   * Format JSON
   */
  onFormatJson(): void {
    this.formatJson.emit();
  }
}
