@import '~@angular/material/prebuilt-themes/deeppurple-amber.css';
@import '../../../node_modules/bootstrap/scss/bootstrap';

// Override Angular Material primary color
.mat-primary .mat-button-wrapper {
  color: white !important;
}

// Force all primary buttons to use our blue color
.mat-raised-button.mat-primary,
.mat-flat-button.mat-primary,
.mat-fab.mat-primary,
.mat-mini-fab.mat-primary {
  background-color: #005f9f !important;
  color: white !important;
}

// Override Angular Material's deep purple color
.mat-button.mat-primary,
.mat-icon-button.mat-primary,
.mat-stroked-button.mat-primary {
  color: #005f9f !important;
}

// Override Angular Material's primary color in all contexts
button[color="primary"] {
  background-color: #005f9f !important;
  color: white !important;
}

// Ensure all buttons with btn-primary class use our blue
.btn-primary,
button.btn-primary {
  background-color: #005f9f !important;
  color: white !important;
  border-color: #0098BB !important;
}
/*@import '~bootstrap/dist/css/bootstrap.min.css';*/

/*html, body { height: 100%; width: 100%; overflow: hidden; font-size: 100% }*/
/* body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
.container { margin: 0%; } */
body {
  border: none !important;
  margin: 0px !important;
  padding: 0 !important;
}

@font-face {
    font-family: "Museo300-Regular";
    src: local("Museo300-Regular"), url(../../assets/fonts/Museo300-Regular.otf) format("truetype");
}
@font-face {
    font-family: "Museo500-Regular";
    src: local("Museo500-Regular"), url(../../assets/fonts/Museo500-Regular.otf) format("truetype");
}
@font-face {
    font-family: "MuseoSans-100";
    src: local("MuseoSans-100"), url(../../assets/fonts/MuseoSans-100.otf) format("truetype");
}
@font-face {
    font-family: "MuseoSans-300";
    src: local("MuseoSans-300"), url(../../assets/fonts/MuseoSans-300.otf) format("truetype");
}
@font-face {
    font-family: "MuseoSans-500";
    src: local("MuseoSans-500"), url(../../assets/fonts/MuseoSans-500.otf) format("truetype");
}
@font-face {
    font-family: "MuseoSans-700";
    src: local("MuseoSans-700"), url(../../assets/fonts/MuseoSans-700.otf) format("truetype");
}
@font-face {
    font-family: "Calibri";
    src: local("Calibri"), url(../../assets/fonts/Calibri.ttf) format("truetype");
    font-weight: normal;
    font-style: normal;
  }

.contactSupport{
    color: white;
    bottom: 1%;
    position: absolute;
    left: 45%;

}

#togglePassword{
    margin-top: 4px;
    cursor: pointer;
    z-index:9999;
    right: 15px;
    position: absolute;
}

@media only screen and (max-width: 768px) {
    [class*="contactSupport"] {
        color: white;
        bottom: 1%;
        position: absolute;
        left: 22%;

    }
}

.btn-primary, .mat-raised-button.mat-primary {
    color: #fff !important;
    background-color: #005f9f !important;
    /*#0097B9*/
    border-color: #0098BB !important;
}

.btn-primary:hover,
.btn-primary:focus,
.mat-raised-button.mat-primary:hover,
.mat-raised-button.mat-primary:focus {
    background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F)) !important;
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%) !important;
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%) !important;
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%) !important;
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%) !important;
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%) !important;
}

.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
    background-color: #005f9f;
    border-color: #0098BB;
}

.btn-primary .badge {
    color: #005f9f;
    background-color: #fff;
}

  .mat-mdc-dialog-surface mdc-dialog__surface
  {
    padding: 0 !important;
    margin: 0 !important;
  }

  .mat-mdc-option {
    line-height: normal !important;
  }
  .mdc-list-item__primary-text {
   // text-align: justify !important;
    font-size: 14px !important;
    font-weight: 100 !important;
  }
  .mat-expansion-panel-header-title {
    font-size: 16px !important;
  }

// Global dialog styling
.workflow-dialog {
  .mat-dialog-container {
    padding: 0 !important;
    overflow: hidden !important;

    .mat-dialog-content {
      max-height: none !important;
      margin: 0 !important;
      padding: 16px !important;
      display: flex !important;
      flex-direction: column !important;
      overflow: hidden !important;

      .mat-tab-group {
        flex: 1 !important;

        .mat-tab-body-wrapper {
          flex: 1 !important;
          overflow: hidden !important;
        }

        .mat-tab-body-content {
          overflow: hidden !important;
        }
      }
    }
  }
}
