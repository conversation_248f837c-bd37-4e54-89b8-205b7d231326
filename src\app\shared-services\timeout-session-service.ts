import { Injectable, On<PERSON><PERSON>roy, Inject, OnInit } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Observable, Subject, Subscription, firstValueFrom } from "rxjs";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "./ep-api-handler/ep-api-handler";
import { DialogSessionTimeoutComponent } from "../modules/shared-modules/layouts/dialogs/session-timeout-dialog/dialog-session-timeout.component";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { environment } from "src/environments/environment";
import { IChorusResponseToken } from "./ep-api-handler/models/login-response.model";
import { EpividianCommon } from "../modules/utility/EpividianCommon";
import { UserContext } from "./user-context/user-context.service";
import { Router } from "@angular/router";
import { ApiRoutes, ApiTypes } from './ep-api-handler/api-option-enums';
import { RouteCacheService } from './route-cache.service';

export interface ISessionState {
  ChorusVersion: string;
  SessionInfo: IChorusResponseToken;
  autoRefreshOffset: number;
  currentTime: string;
  currentTimeZone: number;
  mcurrentTime: string;
  currentTimeUTC: string;
  mcurrentTimeUTC: string;
  jwtIssuedTime: string;
  jwtIssuedTimeUTC: string;
  jwtExpiresTime: string;
  mjwtExpiresTime: string;
  jwtExpiresTimeUTC: string;
  mjwtExpiresTimeUTC: string;
  isTimerActive: boolean;
  timerCountDown: string;
  timerInfo: string;
}

@Injectable({
  providedIn: "root",
})

// Active time is the time the user is active on the page.
// If the user is not active on the page, then the session will timeout.
// This includes scrolling, clicking, tab changes and typing.
export class TimeoutSessionService implements OnDestroy, OnInit {

  public static DEFAULT_COUNTDOWN_SECONDS: number = 60;

  public static expirationTimer: any;
  public tokenExpired: Subject<boolean> = new Subject();
  public _countdown: number = TimeoutSessionService.DEFAULT_COUNTDOWN_SECONDS;
  public countDown: Subject<number> = new Subject<number>();
  public windowLastActiveTime: Subject<Date> = new Subject();
  public sessionState: ISessionState = {} as ISessionState;

  private lastRefreshCheck: Date = new Date();
  private tokenExpirationInMinutes: number = -1;
  private openDialogRef: MatDialogRef<DialogSessionTimeoutComponent> | null = null;
  private pageSubscriptions: Subscription = new Subscription();

  public get countdown(): Observable<number> {
    return this.countDown.asObservable();
  }

  //Only used to in support panel to get the session state
  public getSessionState(): ISessionState {
    this.sessionState.timerInfo = TimeoutSessionService.expirationTimer;
    this.sessionState.ChorusVersion = environment.appVersion;
    this.sessionState.SessionInfo = this.epividianCommon.GetSessionObjFromLocalStorage() || ({} as IChorusResponseToken);
    return this.sessionState;
  }

  constructor(
    private apihandler: ApiHandler,
    public dialog: MatDialog,
    private router: Router,
    private epividianCommon: EpividianCommon,
    private userContext: UserContext,
    private routeCacheService: RouteCacheService,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit(): void {
    this.lastRefreshCheck = new Date();
  }

  ngOnDestroy(): void {
    // Complete subjects and unsubscribe from all subscriptions to prevent memory leaks
    this.windowLastActiveTime.complete();
    this.tokenExpired.complete();
    this.pageSubscriptions.unsubscribe();
  }

  // Displays the session timeout warning dialog
  public showSessionTimeoutMessage() {
    this.openResponseDialog("Your session is about to expire. Please click OK to continue your session.");
  }

  // Updates the last known active time of the window
  public updateWindowLastActiveTime(timeStamp: Date) {
    this.windowLastActiveTime.next(timeStamp);
  }

  // Returns an observable for the window's last active time
  public getWindowLastActiveTime(): Observable<Date> {
    return this.windowLastActiveTime.asObservable();
  }

  // Checks if the session has expired or if an auto refresh is needed based on JWT expiration
  // Implementation to check session validity and refresh token if necessary
  // This method involves comparing current time with the JWT expiration and last activity timestamps
  // If session is invalid, clears session and navigates to login page. Otherwise, may refresh token
  public async checkIfSessionExpiredOrAutoRefreshNeeded(timeStamp: Date, jwtExpireTimeStamp: Date): Promise<{ action: boolean; jwtToken: IChorusResponseToken } | null> {
    let currentTime = new Date();
    let tmpToken = {} as IChorusResponseToken;
    const lastRefreshCheckDiff = currentTime.getTime() - this.lastRefreshCheck.getTime(); // Difference in milliseconds
    const lastRefreshCheckDiffInSeconds = lastRefreshCheckDiff / 1000; // Convert to seconds

    // Check if the last refresh check was more than 15 seconds ago
    if (lastRefreshCheckDiffInSeconds > 15) {
      this.lastRefreshCheck = new Date();

      // Check if JWT has expired
      let isJwtExpired = currentTime.getTime() > jwtExpireTimeStamp.getTime();
      if (!isJwtExpired) {
        return { action: false, jwtToken: tmpToken };
      }

      // Calculate the time difference between the current time and the provided timeStamp
      let timeDifference = currentTime.getTime() - timeStamp.getTime();

      const jwtToken = this.epividianCommon.GetSessionObjFromLocalStorage();

      if (jwtToken == null) {
        this.clearSessionGotoLogin();
        return null;
      }

      let isRefreshTokenValid = this.epividianCommon.isRefreshTokenValid(jwtToken);

      if (jwtToken == null || !isRefreshTokenValid) {
        this.clearSessionGotoLogin();
        return null;
      }

      // Assuming LoadSessionTimeout returns the session validity period
      const sessionValidityPeriod = this.epividianCommon.LoadSessionTimeout(jwtToken) * 60 * 1000;

      // Check if the timeStamp is within the current time (session is still valid)
      let isSessionValid = timeDifference <= sessionValidityPeriod;

      if (isJwtExpired && isSessionValid && isRefreshTokenValid) {

        this.epividianCommon.upsertToStorage("lastRefreshCheck", this.lastRefreshCheck, true, true);

        // Convert the Observable returned by refreshUserJWTToken to a Promise
        const refreshedJwt = await firstValueFrom(this.epividianCommon.refreshUserJWTToken(jwtToken));

        if (refreshedJwt == null) {
          this.clearSessionGotoLogin();
          return null;
        } else {
          this.userContext.apihandler.setbearToken(refreshedJwt.access_token);
          this.epividianCommon.SaveTokenToLocalStorage(refreshedJwt);
          return { action: true, jwtToken: refreshedJwt };
        }
      }
    }

    // JWT is not expired, no action needed
    return { action: false, jwtToken: tmpToken };
  }

  // Closes the session timeout dialog if open
  public closeDialog(stopCountdown: boolean = false) {
    if (this.openDialogRef) {
      this.openDialogRef.close(stopCountdown);
    }
  }

  // Stops the session expiration countdown
  public stopCountdown() {
    if (TimeoutSessionService.expirationTimer) {
      clearTimeout(TimeoutSessionService.expirationTimer);
    }
  }

  // Clears session tracking mechanisms and resets countdown
  public clearSessionTracker() {
    this.stopCountdown();
    this.closeDialog();
    this._countdown = 60;
  }

  // Begins the countdown for session expiration, updating every second
  public startCountdown() {
    // Set the countdown value
    this._countdown = 60;

    // Start the countdown timer
    TimeoutSessionService.expirationTimer = setInterval(() => {

      // Decrement the countdown value
      this._countdown--;

      if (this._countdown <= 0) {
        this.clearSessionGotoLogin();
      }

      // Optionally, if you need to notify other parts of your app about the countdown state
      this.countDown.next(this._countdown);
    }, 1000);
  }

  //Gets the Session Object from the local browsers and sets the token for the api to use.
  public createInactivityExpireTime(lastActivityDt: Date) {

    // If this code is called by a tab that is not currently visible then exit.
    if (document.visibilityState !== 'visible') {
      return;
    }

    // Make a copy of the input date to avoid mutating it
    const expireTime = new Date(lastActivityDt);

    if (this.tokenExpirationInMinutes == -1) {
      // Store the JWT Expire once, if changed on Epi side the user will need to relogin.
      let session = this.epividianCommon.GetSessionObjFromLocalStorage();
      this.tokenExpirationInMinutes = this.epividianCommon.LoadSessionTimeout(session, 0);
    }

    // Add the token expire minutes to the date
    expireTime.setMinutes(expireTime.getMinutes() + this.tokenExpirationInMinutes);

    this.startExpirationTimer(expireTime, this.tokenExpirationInMinutes);
  }

  private openResponseDialog(data: any) {

    // Only open a new dialog if there is not already one open
    if (!this.openDialogRef) {
      this.openDialogRef = this.dialog.open(DialogSessionTimeoutComponent, {
        data: data,
        height: "auto",
        width: "auto",
        disableClose: true,
      });

      // Ensure to clean up the reference when the dialog is closed
      this.pageSubscriptions.add(

        this.openDialogRef.afterClosed().subscribe((stopCountDown) => {

          if (stopCountDown) {
            this.epividianCommon.upsertToStorage("lastRefreshCheck", new Date(), true, true);

            this.stopCountdown();

            // The user clicked ok, Restart the inactivity monitor.
            this.createInactivityExpireTime(new Date());
          }

          // Reset the dialogRef to allow opening a new dialog later
          this.openDialogRef = null;
        })
      );
    }
  }

  // This is a method to clear session and navigate to the home page
  private clearSessionGotoLogin() {
    // Cache current route before clearing session
    const currentRoute = this.router.url;
    if (currentRoute && currentRoute !== '/' && !currentRoute.includes('/Auth/')) {
      this.routeCacheService.cacheRoute(currentRoute);
      console.log('Session timeout cached route:', currentRoute);
    }

    let getSession = this.epividianCommon.GetSessionObjFromLocalStorage();
    if (getSession?.access_token)
    {
      let logoutRoute = ApiRoutes.Logout.replace('{{fromInactive}}', true.toString());
      this.userContext.apihandler.Post(ApiTypes.AuthProvider, logoutRoute, '', true, true).subscribe((s) => {
        this.epividianCommon.ClearSession();
        this.clearSessionTracker();
        this.router.navigate(['/']);
      });
    }
  }

  private formatTimeout(delayMs: number): string {
    const totalSeconds = Math.floor(delayMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    const formattedMinutes = minutes > 0 ? `${minutes} minute${minutes > 1 ? "s" : ""}` : "";
    const formattedSeconds = seconds > 0 ? `${seconds} second${seconds > 1 ? "s" : ""}` : "";

    const formattedTime = [formattedMinutes, formattedSeconds]
      .filter(Boolean)
      .join(" and ");

    return formattedTime || "0 seconds";
  }

  // The expirationDateTime is the final time we should expire user access however we want to present a countDown.
  // Therefore we apply an offset for when we want to display the count down. In a production environment it's 1 minute.
  // However, in dev/testing we can utilize the offset to make the inactivity count down start sooner.
  private startExpirationTimer(expirationDateTime: Date, sessionTimeout: number) {

    const countDownOffsetMinutes = this.GetCountDownOffsetMinutes();
    expirationDateTime.setMinutes(expirationDateTime.getMinutes() - countDownOffsetMinutes);

    const currentTime = new Date();
    const currentTimeInMilliseconds = currentTime.getTime();
    const expirationTimeInMilliseconds = expirationDateTime.getTime();

    // getTime is from epoch so we have to subtract the current time
    const displayInactivityInMilliseconds =
      expirationTimeInMilliseconds - currentTimeInMilliseconds;

    // For the Support Panel Session State
    this.sessionState.autoRefreshOffset = this.apihandler.percentToNearestWholeNumber(sessionTimeout, 15);
    this.sessionState.currentTime = currentTime.toLocaleString();
    this.sessionState.currentTimeZone = currentTime.getTimezoneOffset();
    this.sessionState.currentTimeUTC = currentTime.toUTCString();

    // If we are already expired then notify the subscriber to display the count down.
    if (currentTimeInMilliseconds > expirationTimeInMilliseconds) {
      this.tokenExpired.next(true);
      return;
    }

    if (TimeoutSessionService.expirationTimer) {
      clearTimeout(TimeoutSessionService.expirationTimer);
    }

    this.sessionState.timerCountDown = this.formatTimeout(displayInactivityInMilliseconds);

    TimeoutSessionService.expirationTimer = setTimeout(
      () => this.tokenExpired.next(true),
      displayInactivityInMilliseconds
    );
  }

  // We define the count down offset between the expiration date time and when we should show the count down.
  // In prod we use an offset of 60 seconds for the 60 second inactivity timer.
  // However, in dev and test we can set a higher offset to facilitate quicker testing.
  private GetCountDownOffsetMinutes() {

    let offsetSession = 1;

    if (environment.hasOwnProperty("countDownOffsetMinutes") && environment["countDownOffsetMinutes"] > 1) {
      offsetSession = environment["countDownOffsetMinutes"];
    }

    return offsetSession;
  }

}
