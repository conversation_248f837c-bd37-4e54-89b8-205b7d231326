import { Component, Injectable, OnInit } from '@angular/core';


@Injectable()
export class mockChorusLogin implements OnInit {

public  jsonObject: JSON;

  arrayObj: any =
    {
    access_token: "validtoken",
    token_type: "bearer",
    expires_in: 1199,
    refresh_token: "refresh-token-guid",
    userName: "{\"USER_ID\":\"0fb75169-dc9f-4250-9f1e-409ea86a6b05\",\"UserName\":null,\"FIRST_NM\":\"Chad\",\"LAST_NM\":\"Sandor\",\"IS_AGREE\":true,\"ACTIVE_FLG\":true,\"SITE_ID\":996,\"STATUS_CD\":11,\"isAutorized\":true,\"REVOKED_ACTIVE_FLAG\":true,\"TFA_ACTIVE_FLAG\":false,\"AUTH_CODE_ACTIVE_FLAG\":true,\"Is_TFA_Login\":false,\"TFA_DT\":\"2022-05-06T08:27:50.3761732\",\"TFA_DEVICE_ID\":\"819bca650fcaf048\",\"DEVICE_ID\":\"819bca650fcaf048\",\"METHOD_ID\":0,\"MOB_NUM\":\"**********\",\"CHORUS_APP_FLG\":true,\"ISPIN_ACTIVATE\":true,\"DATA_LOADING_FLG\":false,\"LastPasswordChangedDate\":27,\"PatientSurveyStatus\":false,\"ProviderSurveyStatus\":false,\"PROVIDER_ID\":24570,\"PROVIDER_NAME\":\"Claire Brown\",\"LOCATION_ID\":-1,\"IsStaffRoleWithStudyLocation\":false,\"IS_CHORUS_APP_BIOMETRICS_AUTHENTICATION\":true,\"SHOW_BIOMETRICS_SETUP\":true,\"IS_REDACTION\":false,\"ProviderList\":[1968,23892,44407,47216],\"IS_PROVIDER_PARTICIPATING\":true,\"IS_PATIENT\":true,\"EXTRACT_DT\":\"2022-06-19 12:00 AM\"}",
    issued: "Tue, 14 Jun 2022 10:49:38 GMT",
    expires: "Tue, 14 Jun 2022 11:09:38 GMT"
}


   constructor() {
    this.jsonObject = <JSON>this.arrayObj;

  }

  public isPost = true;
  /*public isGet = false;*/

  ngOnInit(): void {}
}

