import { Injectable } from '@angular/core';
import { Observable, share } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { FormField, FormModel, Rule } from '../../../models/dynamic-form-model';
import { ParamMap } from '@angular/router';

@Injectable({
    providedIn: 'root'
  })
  export class DynamicFormsService {
    constructor(private userContext: UserContext) { }

    /*
    GetRules(ruleId)
    {
      var url = ApiRoutes.GetRules.toString().replace(
          '{{ruleId}}',
          ruleId
        );
      return this.userContext.apihandler.Get<Rule>(ApiTypes.V2, url);
    }
    */

    GetRuleData(dataUrl: string, workflowId: number|null= null, workflowGuid: string = ""): Observable<any>
    {
        // Only append workflowId and workflowGuid if both are provided
        if (workflowId !== null && workflowGuid) {
          const separator = dataUrl.includes('?') ? '&' : '?';
          dataUrl += `${separator}workflowid=${workflowId}&guid=${encodeURIComponent(workflowGuid)}`;
        }

        return this.userContext.apihandler.Get(ApiTypes.V2, dataUrl);
    }

    ExecuteWorkFlow(siteId: string, workflowId: number, workflowGuid: string, formData: any, queryStringParams: ParamMap): Observable<any> 
    {
      var url = ApiRoutes.ExecuteWorkFlow.toString().replace("{{siteId}}", siteId)
                                                    .replace('{{workflowId}}', workflowId.toLocaleString()
                                                  );

        if (workflowGuid) {
          url = url.replace('{{workflowGuid}}', workflowGuid);
        }
        else
        {
          url = url.replace('?guid={{workflowGuid}}', '');
        }

        let additionalParams = '';

        // Append each query param to additionalParams string
        queryStringParams.keys.forEach(key => {
            let value = queryStringParams.get(key);
            if (value!=null)
            {
              additionalParams += `&${key}=${encodeURIComponent(value)}`;
            }
        });

        // Append the additional parameters to the URL
        if (additionalParams) {
            url += !url.includes('?') ? '?' + additionalParams.slice(1) : additionalParams;
        }

        // Prepare formData as JSON
        formData = formData ? JSON.stringify(formData) : "{}";

        // Make the API call
        return this.userContext.apihandler.Post(ApiTypes.V2, url, formData);
    }

    //Currently Not being used but will be needed in the future for some status results
    /*
    StatusWorkFlow(siteId: string, workflowId: number, workflowGuid: string, queryStringParams: ParamMap): Observable<any> 
{
    // Replace workflowId and workflowGuid in the URL
    var url = ApiRoutes.StatusWorkFlow.toString().replace('{{siteId}}', siteId)
                                      .replace('{{workflowId}}', workflowId.toLocaleString()
                                    );

    if (workflowGuid) {
        url = url.replace('{{workflowGuid}}', workflowGuid);
    } else {
        url = url.replace('?guid={{workflowGuid}}', '');
    }

    let additionalParams = '';

    // Append each query param to additionalParams string
    queryStringParams.keys.forEach(key => {
        let value = queryStringParams.get(key);
        if (value != null) {
            additionalParams += `&${key}=${encodeURIComponent(value)}`;
        }
    });

    // Append the additional parameters to the URL
    if (additionalParams) {
        url += !url.includes('?') ? '?' + additionalParams.slice(1) : additionalParams;
    }

    // Make the API call
    return this.userContext.apihandler.Get(ApiTypes.V2, url);
}
    */


    PostValidation(){
      
    }
    
  }