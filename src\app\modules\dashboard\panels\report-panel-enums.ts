export enum ReportPanelTypes {
  none=0,
  pnlPatientSearch=1,
  pnlFromToDate=2,
  pnlCohorts=3,
  pnlUserID=5,
  pnlCustomQuery=6,
  pnlCustomized=7,
 	pnlReportingYear=8,
 	pnlMultiReportingYear=9,
	pnlLstWeek=10,
	pnlRetention=11,
	pnlCohortsReportingYear=12,
	pnlDiseaseManagement=13,
  pnlQrdaExtract=14,
  pnlLocation=15,
  pnlHcvLocation=16, /* Relink */
  pnlUsageDates=17
}


export enum btnColor {
  btnPrimaryColor="btn btn-primary btn-sm btnWidth",
  btnSecondColor="btn btn-secondary btn-sm btnWidth"
}


export enum DiseaseManagementControlTypes  {
  Location = 1,
  Provider = 2,
  Condition = 3
}
