.OutreachDeatilDiv {
    float: left;
    width: 100%;
    padding: 2px 10px 10px 10px;
    color: black;    
    font-weight: bold;
}

.OutReachList {
    float: left;
    width: 30%;
    padding-right: 20px;
    padding-left: 20px;
}

.OutReachList1 {
    float: left;
    width: 40%;
    margin-left: 150px;    
    padding-right: 20px;
    padding-left: 20px;
}

.OutReachList2 {
    float: left;
    width: 30%;
    padding-right: 20px;
    padding-left: 20px;
    margin-left: 450px;   
}
.OutReachListheading
{
    float: left; 
    width: 100%; 
    font-size: 16px;
    padding-left:10px;
    padding-top: 10px;
    width: 82%;
    padding-top: 10px;
    padding-bottom: 10px;
    position:fixed;
    color:black;
}
.OutReachDetailInnerDiv label {
    font-size: smaller;
    font-weight: 500;
}

.OutReachDetailInnerDiv label.labelText{
    font-weight: normal;
    padding: 0px 10px 0px 5px;
}

.OutReachDetailInnerDiv label.labelText.italicText{
    font-weight: 500;
    padding: 0px 10px 0px 5px;
    font-style: italic;
}

.OutReachDetailInnerDiv label.labelText.boldText{
    font-weight: 500;
    padding: 0px 10px 0px 5px;
}

.OutReachDetailInnerDiv label.labelText.redText{
    font-weight: 500;
    color: #FF0000;
}

.OutReachDetailInnerDiv label.labelWithUnderline{
    font-size: smaller;
    font-weight: 500;
    text-decoration: underline;
}

.OutReachDetailInnerDiv {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
    margin-top: 5px;
    height: 600px;
}

.OutReachDetailInnerDiv a {
    cursor: pointer; 
    font-size: smaller;
    font-weight: 500;
    outline: none;
    color: #4b8cd4;
    text-decoration: none;
    cursor:pointer;
}

.OutReachDetailInnerDiv .administrativeActionDiv {
    position: relative;
    padding-bottom: 10px;
}

.OutReachDetailInnerDiv .administrativeActionDiv .linkDiv a {
    outline: none;
    color: #4b8cd4;
    text-decoration: none;
    cursor:pointer;
    font-size: smaller;
    font-weight: 500;
    padding-right: 15px;
}

.hiddenDiv {
    display: none;
}