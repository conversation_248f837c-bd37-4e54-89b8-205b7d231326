import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, Optional } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { Subscription } from "rxjs";
import { TimeoutSessionService } from "src/app/shared-services/timeout-session-service";

@Component({
  selector: "dialog-session-timeout",
  templateUrl: "./dialog-session-timeout.component.html",
  styleUrls: ["./dialog-session-timeout.component.css"],
})
export class DialogSessionTimeoutComponent implements OnInit, OnDestroy {
  public message: any; // Stores message displayed on the popup.
  public countdown: number = TimeoutSessionService.DEFAULT_COUNTDOWN_SECONDS; // Set offset value so the dialog is not blank on initial display.

  private pageSubscriptions: Subscription = new Subscription();

  constructor(
    public dialogRef: MatDialogRef<DialogSessionTimeoutComponent>,
    private timeoutSessionService: TimeoutSessionService,
    @Optional() @Inject(MAT_DIALOG_DATA) public msg: any
  ) {
    this.message = this.msg;
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.timeoutSessionService.startCountdown();

    this.pageSubscriptions.add(
      this.timeoutSessionService.countdown.subscribe((countdown: number) => {
        this.countdown = countdown;
      })
    );
  }

  public closeDialog() {
    // We only need to close the dialog.
    // The app.component.ts mouse click event will fire userActivityDetected()
    // who will then refresh the users token if required.
    this.timeoutSessionService.stopCountdown();
    this.timeoutSessionService.closeDialog(true);
  }
}
