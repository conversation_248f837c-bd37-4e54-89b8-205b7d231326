<div class="searchCriteriaDiv" >
  <form>
        <div [hidden]="noSearchPan">
            <!-- Label and Dropdown Row -->
            <div class="label-dropdown-row">
                <div class="label-container">
                    <label class="field-label">Patient Search*:</label>
                </div>
                <div class="dropdown-container">
                    <select title="Patient Search" #patient class="form-select form-select-sm" name="ddlPatient" (change)="onPatientSelected(patient.value)">
                        <option *ngFor="let option of paneldata" [value]="option.key">{{option.value}}</option>
                    </select>
                </div>
            </div>

            <!-- Patient Search Input Row -->
            <div class="input-row">
                <input mat-form-field #search matInput [formControl]="patientSearch"
                 [matAutocomplete]="auto" class="form-control form-control-sm patient-search-input" type="text"  name="patientSearch"
                 (keyup)="autoComplete(search.value)"
                 (blur)="checkTextLength(search.value)"  placeholder="Type search term here, e.g. Patient Name">
                <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onOptionSelected($event)">
                  <mat-option (click)="runReady($event)" *ngFor="let option of autoCompleteItems | async" [value]="option">{{option.value}}</mat-option>
                </mat-autocomplete>
            </div>

            <!-- Run Button Row -->
            <div class="button-row">
                <button type="button" (click)="isSSRSReport ? panelService.InitBoldReport() : onRunClick()" id="reportViewer_Control_viewReportClick"
                 [disabled]="isRunDisabled" aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">
                    Run
                </button>
            </div>

            <div>
                <input mat-form-field style="width:1px;" matInput [formControl]="rptpanTxtSearch" type="text" hidden  name="txtSearch">
            </div>

        </div>
         <div class="elementDiv" style="">
            <!--If this comes from the custom query display criteria-->
                <div [hidden]="!showCustomCriteria">
                    <div style="display: inline-block;padding-right: 20px; vertical-align: top;" [innerHTML]="criteriaSummarycolumn1"></div>
                    <div style="display: inline-block;padding-right: 20px; vertical-align: top;" [innerHTML]="criteriaSummarycolumn2"></div>
                    <div style="display: inline-block;"></div>
                </div>
         </div>
  </form>

</div>
