.searchCriteriaDiv {
    position: relative;
    margin: 2px;
    padding: 15px;
    width: 100%;
    z-index: 1;
    background-color: white;
}

// Label and Dropdown Row
.label-dropdown-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
}

.label-container {
    flex-shrink: 0;
}

.field-label {
    color: black;
    font-weight: 500;
    margin: 0;
}

.dropdown-container {
    flex-shrink: 0;
}

.dropdown-container select {
    width: 120px;
    max-width: 120px;
}

// Patient Search Input Row
.input-row {
    margin-bottom: 15px;
}

.patient-search-input {
    width: 100%;
    max-width: 100%;
}

// Run Button Row
.button-row {
    margin-top: 15px;
    padding: 0 16px;
}

.run-button {
    width: 100% !important;
    padding: 8px 16px !important;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
    font-size: 14px;
    height: auto;
    min-height: 35px;
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}

.btnWidth {
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 30px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}
