// THIS IS A TEMPLATE FOR THE CLASS DEFINITION!

// See ../SHARED-SERVICES/onfiguration.service.ts to add values for Dev/Test/Prod/Local

// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  mockData: false,
  apiV1: "",
  apiV2: "",
  authProvider: "",
  reportingUrl: "",
  pdfFiller: "",
  encodeBrowserObjects: false,
  showBoldReportParameters: false,
  sessionTimeout: 0,
  env: "",
  appVersion: "",
  AppStore:"",
  PlayStore:"",
  ShowSupportLink: false
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
