﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:cl="http://schemas.microsoft.com/sqlserver/reporting/2010/01/componentdefinition" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2010/01/reportdefinition">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					DECLARE @SITE_ID INT

					SET @SITE_ID = (SELECT SITE_ID FROM [CLEAN].[SITE])

					SELECT        SITE_NM, CITY_TXT, STATE_TXT
					FROM            [CHORUS].[ADMIN].[SITE]
					WHERE        (STATUS_CD = 'A') AND (SITE_ID = @SITE_ID)
				</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					SELECT        EXTRACT_DT
					FROM            CLEAN.SITE
				</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="CRS_PROVIDER_LTF_DTL">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ID">
            <Value>=Parameters!ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@IS_LOCATION">
            <Value>=Parameters!IS_LOCATION.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ANNOTATE_OPTION">
            <Value>=Parameters!ANNOTATE_OPTION.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>
					SELECT  P.DEMOGRAPHICS_ID, P.MRN, PATIENT_NM, P.PRIMARY_PROVIDER_ID, REPORTING_PERIOD, ROLLING_WEEK, ' ' MISSED_APPOINTMENTS
					, ' ' AT_RISK_FACTORS, ' ' HIGH_RISK_FACTORS, MEASURE_ID, MEASURE_CD,  ROLLING_WEEK_DT, ORIGNAL_ALERT_CREATED, ANNOTATION_DT, [STATUS], NOTES,
					IIF(RW.DEMOGRAPHICS_ID IS NOT NULL, 'Y',NULL) RYAN_WHITE_FLG
					FROM [REPORT].[GET_CRS_PROVIDER_LTF_DTL](@USER_ID, @IS_LOCATION, @ID, @ANNOTATE_OPTION) P
					INNER JOIN ANALYSIS.COHORT_ASSIGNMENT CA ON (p.DEMOGRAPHICS_ID = CA.DEMOGRAPHICS_ID)
					LEFT OUTER JOIN (SELECT DEMOGRAPHICS_ID
					FROM ANALYSIS.COHORT_ASSIGNMENT CA
					WHERE CA.COHORT_ID = 1001) RW ON(P.DEMOGRAPHICS_ID = RW.DEMOGRAPHICS_ID)
					WHERE CA.COHORT_ID = @COHORT_ID
					ORDER BY ORIGNAL_ALERT_CREATED DESC, ANNOTATION_DT DESC, PATIENT_NM
				</CommandText>
      </Query>
      <Fields>
        <Field Name="DEMOGRAPHICS_ID">
          <DataField>DEMOGRAPHICS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MRN">
          <DataField>MRN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PATIENT_NM">
          <DataField>PATIENT_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ROLLING_WEEK">
          <DataField>ROLLING_WEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MISSED_APPOINTMENTS">
          <DataField>MISSED_APPOINTMENTS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AT_RISK_FACTORS">
          <DataField>AT_RISK_FACTORS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="HIGH_RISK_FACTORS">
          <DataField>HIGH_RISK_FACTORS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER_ID">
          <DataField>PRIMARY_PROVIDER_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ROLLING_WEEK_DT">
          <DataField>ROLLING_WEEK_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ORIGNAL_ALERT_CREATED">
          <DataField>ORIGNAL_ALERT_CREATED</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ANNOTATION_DT">
          <DataField>ANNOTATION_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="STATUS">
          <DataField>STATUS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOTES">
          <DataField>NOTES</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RYAN_WHITE_FLG">
          <DataField>RYAN_WHITE_FLG</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Line Name="Line1">
            <Top>0.01958in</Top>
            <Left>0.025in</Left>
            <Height>0in</Height>
            <Width>11.11632in</Width>
            <Style>
              <Border>
                <Color>Purple</Color>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.6875in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.84375in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.84375in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.48542in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.6in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.95833in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox71">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>LFU Alerts</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox71</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.53125in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox83">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Measure</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox83</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox81">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Original Alert Date</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox81</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox78">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Annotation Date</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox78</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox75">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Status</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox75</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox36">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Notes</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox36</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox211">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!MEASURE_CD.Value</SortExpression>
                            <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox211</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!ORIGNAL_ALERT_CREATED.Value</SortExpression>
                            <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!ANNOTATION_DT.Value</SortExpression>
                            <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!STATUS.Value</SortExpression>
                            <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!NOTES.Value</SortExpression>
                            <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>Gainsboro</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>#545454</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.03958in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox241">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox241</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.22in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MEASURE_CD">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MEASURE_CD.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MEASURE_CD</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ORIGNAL_ALERT_CREATED">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ORIGNAL_ALERT_CREATED.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>dd-MMM-yyyy</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ORIGNAL_ALERT_CREATED</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ANNOTATION_DT">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ANNOTATION_DT.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>dd-MMM-yyyy</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ANNOTATION_DT</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="STATUS">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!STATUS.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>STATUS</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NOTES">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NOTES.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NOTES</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="DEMOGRAPHICS_ID">
                                      <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="INDEX_DT">
                                      <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.03125in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox313">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox313</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <RightBorder>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>5.6559in</Size>
                    <CellContents>
                      <Textbox Name="Textbox18">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value xml:space="preserve">  </Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>Gainsboro</Color>
                                </Style>
                              </TextRun>
                              <TextRun>
                                <Label>AnnotationDescription</Label>
                                <Value>=Parameters!ANNOTATE_DESC.Value</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>Gainsboro</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox18</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                          </Border>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#545454</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>1.98924in</Size>
                    <CellContents>
                      <Textbox Name="Textbox231">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Patient</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>Gainsboro</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox231</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.625in</Size>
                        <CellContents>
                          <Textbox Name="Textbox289">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>MRN</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>11pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>Gainsboro</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox289</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>Gray</Color>
                                <Style>Solid</Style>
                              </Border>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.52083in</Size>
                            <CellContents>
                              <Textbox Name="Textbox1">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>Ryan White</Value>
                                        <Style>
                                          <FontStyle>Normal</FontStyle>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>11pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <TextDecoration>None</TextDecoration>
                                          <Color>Gainsboro</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox1</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>Gray</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <VerticalAlign>Middle</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>1in</Size>
                                <CellContents>
                                  <Textbox Name="Textbox284">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value># Missed Appointments</Value>
                                            <Style>
                                              <FontStyle>Normal</FontStyle>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>11pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                              <TextDecoration>None</TextDecoration>
                                              <Color>Gainsboro</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox284</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>Gray</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BackgroundColor>#1f4e78</BackgroundColor>
                                      <VerticalAlign>Middle</VerticalAlign>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.77083in</Size>
                                    <CellContents>
                                      <Textbox Name="Textbox279">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>At Risk Factors Identified (out of 1)</Value>
                                                <Style>
                                                  <FontStyle>Normal</FontStyle>
                                                  <FontFamily>Calibri</FontFamily>
                                                  <FontSize>11pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                  <TextDecoration>None</TextDecoration>
                                                  <Color>Gainsboro</Color>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox279</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>Gray</Color>
                                            <Style>Solid</Style>
                                          </Border>
                                          <BackgroundColor>#1f4e78</BackgroundColor>
                                          <VerticalAlign>Middle</VerticalAlign>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>0.75in</Size>
                                        <CellContents>
                                          <Textbox Name="Textbox274">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>High Risk Factors Identified (out of 3)</Value>
                                                    <Style>
                                                      <FontStyle>Normal</FontStyle>
                                                      <FontFamily>Calibri</FontFamily>
                                                      <FontSize>11pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                      <TextDecoration>None</TextDecoration>
                                                      <Color>Gainsboro</Color>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox274</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>Gray</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <BackgroundColor>#1f4e78</BackgroundColor>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <FixedData>true</FixedData>
                                          <RepeatOnNewPage>true</RepeatOnNewPage>
                                        </TablixMember>
                                      </TablixMembers>
                                      <FixedData>true</FixedData>
                                    </TablixMember>
                                  </TablixMembers>
                                  <FixedData>true</FixedData>
                                </TablixMember>
                              </TablixMembers>
                              <FixedData>true</FixedData>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                      <FixedData>true</FixedData>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>5.6559in</Size>
                    <CellContents>
                      <Textbox Name="Textbox232">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox232</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                          </Border>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#0b6c9f</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>1.98924in</Size>
                    <CellContents>
                      <Textbox Name="Textbox246">
                        <CanGrow>true</CanGrow>
                        <UserSort>
                          <SortExpression>=Fields!PATIENT_NM.Value</SortExpression>
                          <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                        </UserSort>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox246</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                          </Border>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#545454</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.625in</Size>
                        <CellContents>
                          <Textbox Name="Textbox294">
                            <CanGrow>true</CanGrow>
                            <UserSort>
                              <SortExpression>=Fields!MRN.Value</SortExpression>
                              <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                            </UserSort>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style />
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox294</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>Gray</Color>
                                <Style>Solid</Style>
                              </Border>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>#545454</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.52083in</Size>
                            <CellContents>
                              <Textbox Name="Textbox2">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style />
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox2</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>Gray</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>#545454</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>1in</Size>
                                <CellContents>
                                  <Textbox Name="Textbox295">
                                    <CanGrow>true</CanGrow>
                                    <UserSort>
                                      <SortExpression>=Max(Fields!MISSED_APPOINTMENTS.Value)</SortExpression>
                                      <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                                    </UserSort>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value />
                                            <Style />
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox295</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Color>Gray</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <LeftBorder>
                                        <Style>None</Style>
                                      </LeftBorder>
                                      <RightBorder>
                                        <Style>None</Style>
                                      </RightBorder>
                                      <BackgroundColor>#545454</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.77083in</Size>
                                    <CellContents>
                                      <Textbox Name="Textbox296">
                                        <CanGrow>true</CanGrow>
                                        <UserSort>
                                          <SortExpression>=Max(Fields!AT_RISK_FACTORS.Value)</SortExpression>
                                          <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                                        </UserSort>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style />
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox296</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>Gray</Color>
                                            <Style>Solid</Style>
                                          </Border>
                                          <LeftBorder>
                                            <Style>None</Style>
                                          </LeftBorder>
                                          <RightBorder>
                                            <Style>None</Style>
                                          </RightBorder>
                                          <BackgroundColor>#545454</BackgroundColor>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>0.75in</Size>
                                        <CellContents>
                                          <Textbox Name="Textbox297">
                                            <CanGrow>true</CanGrow>
                                            <UserSort>
                                              <SortExpression>=Max(Fields!HIGH_RISK_FACTORS.Value)</SortExpression>
                                              <SortExpressionScope>PATIENT_NM</SortExpressionScope>
                                            </UserSort>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style />
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox297</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>Gray</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <LeftBorder>
                                                <Style>None</Style>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Style>None</Style>
                                              </RightBorder>
                                              <BackgroundColor>#545454</BackgroundColor>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <FixedData>true</FixedData>
                                        </TablixMember>
                                      </TablixMembers>
                                      <FixedData>true</FixedData>
                                    </TablixMember>
                                  </TablixMembers>
                                  <FixedData>true</FixedData>
                                </TablixMember>
                              </TablixMembers>
                              <FixedData>true</FixedData>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                      <FixedData>true</FixedData>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="PATIENT_NM">
                    <GroupExpressions>
                      <GroupExpression>=Fields!PATIENT_NM.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!PATIENT_NM.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>1.98924in</Size>
                    <CellContents>
                      <Textbox Name="PATIENT_NM">
                        <CanGrow>true</CanGrow>
                        <CanShrink>true</CanShrink>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Fields!PATIENT_NM.Value</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>PATIENT_NM</rd:DefaultName>
                        <ActionInfo>
                          <Actions>
                            <Action>
                              <Drillthrough>
                                <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                <Parameters>
                                  <Parameter Name="DEMOGRAPHICS_ID">
                                    <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                  </Parameter>
                                  <Parameter Name="DEMOGRAPHICS_ID">
                                    <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                  </Parameter>
                                </Parameters>
                              </Drillthrough>
                            </Action>
                          </Actions>
                        </ActionInfo>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.625in</Size>
                        <CellContents>
                          <Textbox Name="MRN">
                            <CanGrow>true</CanGrow>
                            <CanShrink>true</CanShrink>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!MRN.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>MRN</rd:DefaultName>
                            <ActionInfo>
                              <Actions>
                                <Action>
                                  <Drillthrough>
                                    <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                    <Parameters>
                                      <Parameter Name="DEMOGRAPHICS_ID">
                                        <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                      </Parameter>
                                      <Parameter Name="INDEX_DT">
                                        <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                      </Parameter>
                                    </Parameters>
                                  </Drillthrough>
                                </Action>
                              </Actions>
                            </ActionInfo>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.52083in</Size>
                            <CellContents>
                              <Textbox Name="RYAN_WHITE_FLG">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!RYAN_WHITE_FLG.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>RYAN_WHITE_FLG</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>1in</Size>
                                <CellContents>
                                  <Textbox Name="MISSED_APPOINTMENTS_FLG">
                                    <CanGrow>true</CanGrow>
                                    <CanShrink>true</CanShrink>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>=MAX(Fields!MISSED_APPOINTMENTS.Value)</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>MISSED_APPOINTMENTS_FLG</rd:DefaultName>
                                    <ActionInfo>
                                      <Actions>
                                        <Action>
                                          <Drillthrough>
                                            <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                            <Parameters>
                                              <Parameter Name="DEMOGRAPHICS_ID">
                                                <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                              </Parameter>
                                              <Parameter Name="INDEX_DT">
                                                <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                              </Parameter>
                                            </Parameters>
                                          </Drillthrough>
                                        </Action>
                                      </Actions>
                                    </ActionInfo>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.77083in</Size>
                                    <CellContents>
                                      <Textbox Name="AT_RISK_FACTORS">
                                        <CanGrow>true</CanGrow>
                                        <CanShrink>true</CanShrink>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=MAX(Fields!AT_RISK_FACTORS.Value)</Value>
                                                <Style>
                                                  <FontFamily>Calibri</FontFamily>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>AT_RISK_FACTORS</rd:DefaultName>
                                        <ActionInfo>
                                          <Actions>
                                            <Action>
                                              <Drillthrough>
                                                <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                                <Parameters>
                                                  <Parameter Name="DEMOGRAPHICS_ID">
                                                    <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="INDEX_DT">
                                                    <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                                  </Parameter>
                                                </Parameters>
                                              </Drillthrough>
                                            </Action>
                                          </Actions>
                                        </ActionInfo>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>Solid</Style>
                                          </Border>
                                          <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>0.75in</Size>
                                        <CellContents>
                                          <Textbox Name="HIGH_RISK_FACTORS">
                                            <CanGrow>true</CanGrow>
                                            <CanShrink>true</CanShrink>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Max(Fields!HIGH_RISK_FACTORS.Value)</Value>
                                                    <Style>
                                                      <FontFamily>Calibri</FontFamily>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>HIGH_RISK_FACTORS</rd:DefaultName>
                                            <ActionInfo>
                                              <Actions>
                                                <Action>
                                                  <Drillthrough>
                                                    <ReportName>Patient_High_Risk_LTF_Detail</ReportName>
                                                    <Parameters>
                                                      <Parameter Name="DEMOGRAPHICS_ID">
                                                        <Value>=Fields!DEMOGRAPHICS_ID.Value</Value>
                                                      </Parameter>
                                                      <Parameter Name="INDEX_DT">
                                                        <Value>=Fields!ROLLING_WEEK_DT.Value</Value>
                                                      </Parameter>
                                                    </Parameters>
                                                  </Drillthrough>
                                                </Action>
                                              </Actions>
                                            </ActionInfo>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <BackgroundColor>= IIf(RunningValue(Fields!PATIENT_NM.Value, countdistinct, nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember />
                                        <TablixMember>
                                          <Group Name="Details" />
                                          <TablixMembers>
                                            <TablixMember>
                                              <HideIfNoRows>true</HideIfNoRows>
                                            </TablixMember>
                                          </TablixMembers>
                                        </TablixMember>
                                        <TablixMember />
                                      </TablixMembers>
                                    </TablixMember>
                                  </TablixMembers>
                                </TablixMember>
                              </TablixMembers>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <RepeatColumnHeaders>true</RepeatColumnHeaders>
            <RepeatRowHeaders>true</RepeatRowHeaders>
            <DataSetName>CRS_PROVIDER_LTF_DTL</DataSetName>
            <Top>0.05292in</Top>
            <Left>0.025in</Left>
            <Height>2.28041in</Height>
            <Width>11.11632in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>2.33333in</Height>
        <Style />
      </Body>
      <Width>12.14132in</Width>
      <Page>
        <PageHeader>
          <Height>0.62375in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Left>0.06251in</Left>
              <Height>1.49687cm</Height>
              <Width>4.51084cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox13">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>LFU Alerts for: </Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Label>LFU_Alert</Label>
                      <Value>=IIF(Parameters!LFU_ALERTS_FOR.Value = nothing OR trim(Parameters!LFU_ALERTS_FOR.Value) = "", "All Locations", Parameters!LFU_ALERTS_FOR.Value)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Left>4.84598cm</Left>
              <Height>1.48166cm</Height>
              <Width>17.36228cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Left>8.81286in</Left>
              <Height>0.58333in</Height>
              <Width>2.2868in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>8.27in</PageHeight>
        <PageWidth>11.69in</PageWidth>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>00000000-0000-0000-0000-000000000000</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>0</Value>
        </Values>
      </DefaultValue>
      <Prompt>ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="IS_LOCATION">
      <DataType>Boolean</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>true</Value>
        </Values>
      </DefaultValue>
      <Prompt>IS LOCATION</Prompt>
    </ReportParameter>
    <ReportParameter Name="ANNOTATE_OPTION">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value>NULL</Value>
        </Values>
      </DefaultValue>
      <Prompt>ANNOTATE_OPTION</Prompt>
    </ReportParameter>
    <ReportParameter Name="LFU_ALERTS_FOR">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>LFU ALERTS FOR</Prompt>
    </ReportParameter>
    <ReportParameter Name="ANNOTATE_DESC">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>Annotate Description</Prompt>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>String</DataType>
      <Prompt>COHORT ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0f5f31dd-9fc3-419a-88e6-730bbfdf1fb7</rd:ReportID>
</Report>