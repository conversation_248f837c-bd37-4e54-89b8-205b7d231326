{
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "ng serve with edge",
      "type": "msedge",
      "request": "launch",
      "url": "http://localhost:4200",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "ng serve",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "npm: start",
      "url": "http://localhost:4200/"
    },
    {
      "name": "ng serve demo",
      "type": "chrome",
      "request": "launch",
      "env": {"NODE_ENV": "demo"},
      "preLaunchTask": ["npm: build"],

      "url": "http://localhost:4200/"
    },
    {
      "name": "ng serve Localhost with edge",
      "type": "msedge",
      "request": "launch",
      "url": "http://localhost/Chorus",
      "webRoot": "${workspaceFolder}"
    }
  ]
}
