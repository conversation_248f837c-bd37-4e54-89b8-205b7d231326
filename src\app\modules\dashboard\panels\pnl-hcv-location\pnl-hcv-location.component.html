<div class="searchCriteriaDiv">
  <p class="lblWarningMsg">{{lblWarningMsg}}</p>
  <div *ngIf="noData">
    <p class="noDataMsg">{{noDataMsg}}</p>
  </div>
  <div class="elementDiv">
    Location :
  </div>
  <div class="elementDiv">
    <input #location matInput mat-form-field class="form-select form-select-sm" style="width:250px" [matAutocomplete]="autoLocation" name="rptpanLocation" (keyup)="comboFilter(location.value)" [formControl]="locationSearch">
    
    <mat-autocomplete #autoLocation="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChange($event)">
  
      <!-- We verify values exist so that an exception is not thrown -->
      <mat-option *ngFor="let locData of filteredLocations?.data ?? []" [value]="locData">
          {{ locData.value }}
      </mat-option>

    </mat-autocomplete>  
  
  </div>
  <div class="elementDiv">
      <button type="button" (click)="panelService.InitBoldReport()" [disabled]="!isRunButtonEnable" id="reportViewer_Control_viewReportClick"  aria-describedby="reportViewer_Control_viewReportClick" [class]="rptbtnColor">Run</button>
  </div>
</div>
