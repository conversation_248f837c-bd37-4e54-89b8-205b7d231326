import { Injectable } from "@angular/core";
import { ApiRoutes, ApiTypes } from "src/app/shared-services/ep-api-handler/api-option-enums";
import { ApiHandler } from "src/app/shared-services/ep-api-handler/ep-api-handler";
import { UserContext } from "src/app/shared-services/user-context/user-context.service";

@Injectable({
  providedIn: 'root'
})

export class HuddleFilterService {
    constructor(private apiHandler: ApiHandler, private userContext: UserContext) {}

    public GetFilters(siteId: string){
        let url = ApiRoutes.HuddleGetFilters.toString().replace("{{siteId}}", siteId);

        return this.userContext.apihandler.Get(ApiTypes.V2, url);
    }
}