.workflow-status-container {
  padding: 16px 0;

  .status-header {
    margin-bottom: 20px;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 100px;
      }

      .value {
        flex: 1;
      }

      .guid-full {
        color: #999;
        font-size: 0.85em;
      }
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.success {
        background-color: #e6f4ea;
        color: #137333;
      }

      &.error {
        background-color: #fce8e6;
        color: #c5221f;
      }

      &.running {
        background-color: #e8f0fe;
        color: #1a73e8;
      }
    }
  }

  .status-details {
    margin: 20px 0;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .info-row {
      display: flex;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 120px;
      }
    }
  }

  h3 {
    margin-top: 24px;
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
  }

  .no-results-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .rule-header {
    display: flex;
    align-items: center;

    .rule-order {
      margin-right: 8px;
      font-weight: 500;
    }

    .rule-name {
      flex: 1;

      &.session-transaction {
        font-weight: 500;
        color: #1976d2;
        font-style: italic;
      }
    }

    mat-icon {
      margin-left: 8px;

      &.success {
        color: #4caf50;
      }

      &.error {
        color: #f44336;
      }

      &.running {
        color: #1976d2;
      }
    }
  }

  .rule-error {
    background-color: #ffebee;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;

    p {
      margin: 0;
      color: #d32f2f;
    }
  }

  .rule-data {
    margin-bottom: 16px;

    h4 {
      margin-bottom: 8px;
      color: #555;
    }

    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  .rule-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .no-status-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      color: #999;
    }

    p {
      font-size: 16px;
    }
  }

  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    p {
      margin-top: 16px;
      color: #666;
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
  gap: 8px;
}
