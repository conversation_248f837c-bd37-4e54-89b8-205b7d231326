.execution-history-container {
  padding: 16px;

  .filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    mat-form-field {
      width: 300px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .history-table-container {
    position: relative;
    min-height: 200px;

    table {
      width: 100%;
    }

    .process-id-container {
      display: flex;
      align-items: center;

      .guid-display {
        font-family: monospace;
      }

      .copy-button {
        margin-left: 4px;
        width: 24px;
        height: 24px;
        line-height: 24px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.success {
        background-color: #e6f4ea;
        color: #137333;
      }

      &.error {
        background-color: #fce8e6;
        color: #c5221f;
      }

      &.running {
        background-color: #e8f0fe;
        color: #1a73e8;
      }
    }

    .no-data-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #666;

      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
        color: #999;
      }

      p {
        font-size: 16px;
      }
    }
  }
}
