import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { RuleDefinitionModel } from '../../../models/rule-definition.model';
import { JsonSchemaService } from '../../../services/json-schema.service';

@Component({
  selector: 'app-json-editor',
  templateUrl: './json-editor.component.html',
  styleUrls: ['./json-editor.component.scss']
})
export class JsonEditorComponent implements OnInit, OnChanges {
  @Input() ruleDefinition: RuleDefinitionModel[] = [];
  @Input() ruleData = '{}';
  @Output() jsonDataChange = new EventEmitter<string>();

  // View mode properties
  viewMode: 'object' | 'json' = 'object';
  jsonError: string | null = null;
  formattedJsonData = '';

  // Parsed JSON data for object view
  parsedJsonData: Record<string, unknown> | null = null;

  // Dirty state tracking
  viewDirty = false;

  constructor(private jsonSchemaService: JsonSchemaService) { }

  ngOnInit(): void {
    this.initForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['ruleDefinition'] || changes['ruleData']) {
      this.initForm();
    }
  }

  /**
   * Initialize the form based on the rule definition and rule data
   */
  initForm(): void {
    console.log('JsonEditorComponent.initForm: ruleDefinition=', this.ruleDefinition);
    console.log('JsonEditorComponent.initForm: ruleData=', this.ruleData);

    if (!this.ruleDefinition || this.ruleDefinition.length === 0) {
      console.warn('No rule definition available, skipping form initialization');
      return;
    }

    // Parse the rule data if it exists
    try {
      // Handle the case where the data might be already stringified JSON
      if (typeof this.ruleData === 'string') {
        // Try to parse it as JSON
        try {
          this.parsedJsonData = JSON.parse(this.ruleData);
          console.log('JsonEditorComponent.initForm: Successfully parsed JSON data');
        } catch (parseError) {
          console.error('JsonEditorComponent.initForm: Error parsing JSON, trying double-stringified JSON', parseError);
          // If it fails, it might be a double-stringified JSON
          try {
            // Try to parse it as a double-stringified JSON
            this.parsedJsonData = JSON.parse(JSON.parse(this.ruleData));
            console.log('JsonEditorComponent.initForm: Successfully parsed double-stringified JSON data');
          } catch (doubleParseError) {
            console.error('JsonEditorComponent.initForm: Error parsing double-stringified JSON:', doubleParseError);
            // Use the original string as is
            this.parsedJsonData = {};
          }
        }
      } else {
        // If it's not a string, use it as is
        console.log('JsonEditorComponent.initForm: ruleData is not a string, using as is');
        this.parsedJsonData = this.ruleData || {};
      }

      console.log('JsonEditorComponent.initForm: parsedJsonData=', this.parsedJsonData);

      // Format the JSON for display
      this.formattedJsonData = JSON.stringify(this.parsedJsonData, null, 2);
      console.log('JsonEditorComponent.initForm: formattedJsonData=', this.formattedJsonData);
      this.jsonError = null;

      // Default to object view
      this.viewMode = 'object';
    } catch (e) {
      console.error('JsonEditorComponent.initForm: Error processing rule data:', e);
      this.parsedJsonData = {};
      this.jsonError = 'Invalid JSON format';
    }
  }

  /**
   * Handle view mode change
   */
  onViewModeChange(mode: 'object' | 'json'): void {
    if (this.viewDirty) {
      const confirm = window.confirm("You have unsaved changes. Apply before switching?");
      if (confirm) {
        this.applyChanges();
      }
    }
    this.viewMode = mode;
  }

  /**
   * Handle JSON data change from the raw JSON view
   */
  onJsonDataChange(jsonData: string): void {
    this.formattedJsonData = jsonData;
    this.viewDirty = true;
  }

  /**
   * Handle JSON error change from the raw JSON view
   */
  onJsonErrorChange(error: string | null): void {
    this.jsonError = error;
  }

  /**
   * Handle object data change from the object view
   */
  onObjectDataChange(data: Record<string, unknown>): void {
    this.parsedJsonData = data;
    this.viewDirty = true;
  }

  /**
   * Format the JSON
   */
  formatJson(): void {
    try {
      const parsedJson = JSON.parse(this.formattedJsonData);
      this.formattedJsonData = JSON.stringify(parsedJson, null, 2);
      this.jsonError = null;
    } catch (e) {
      this.jsonError = 'Invalid JSON format';
    }
  }

  /**
   * Apply changes from either view
   */
  applyChanges(): void {
    if (this.jsonError) {
      return;
    }

    try {
      if (this.viewMode === 'json') {
        // Apply changes from raw JSON view
        const parsedJson = JSON.parse(this.formattedJsonData);
        this.parsedJsonData = parsedJson;
        this.ruleData = JSON.stringify(parsedJson);
      } else {
        // Apply changes from object view
        this.formattedJsonData = JSON.stringify(this.parsedJsonData, null, 2);
        this.ruleData = this.formattedJsonData;
      }

      // Emit the change
      this.jsonDataChange.emit(this.ruleData);
      this.viewDirty = false;

      console.log('Changes applied successfully:', this.parsedJsonData);
    } catch (e) {
      console.error('Failed to apply changes:', e);
      this.jsonError = `Failed to apply changes: ${e}`;
    }
  }


}
