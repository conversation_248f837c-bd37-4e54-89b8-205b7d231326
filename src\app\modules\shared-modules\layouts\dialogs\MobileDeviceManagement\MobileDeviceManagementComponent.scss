.btn-group {
    display: flex;
    justify-content: space-between;
  }

.pin-details {
    display: flex;
    align-items: center; /* This will vertically center align the items if they have different heights */
    width: 100%;
}

.SSTopDiv img {
    border: none;
}

.PinFont {
    font-size: 12px;
}

.MessageDiv {
    font-size: 12px;
    font-weight: bold;
    color:#005b9f;
    padding: 15px;
    width: 100%;
    justify-content: center; 
}

.SSTopDiv a {
    outline: none;
    color: #4b8cd4;
    font-weight: bold;
    text-decoration: none;
    cursor: pointer;
}

.SSTopDiv a:hover {
    color: #005b9f;
}

.SSTopDiv p {
    text-align: justify;
    line-height: 19px;
}

.SStopstrip {
    height: 30px;
    border: 1px #d3d5da solid;
    padding: 5px 10px 5px 10px;
    background: rgb(249, 249, 251);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* 	 		 	 		Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
    /* IE6-9 */
}

.SStopstripleft {
    float: left;
    width: auto;
}

.SStopstripleft span {
    color: #444444;
    font-size: 12px;
    font-weight: bold;
    float: left;
}

.SStopstripleft select {
    background: transparent;
    width: 315px;
    font-size: 12px;
    font-weight: bold;
    border: 0;
    border-radius: 0;
    -webkit-appearance: none;
    cursor: pointer;
    text-overflow: '';
    text-indent: 0.01px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.SStopstripleft select option {
    margin-left: 2px;
}



.SStablegridbg {
    width: 100%;
    float: left;
    height: 30vh;
}

.SStablegridbg tr:nth-child(odd) {
    background-color: #FFF;
}

.SStablegridbg table {
    width: 100%;
    border-collapse: collapse;
}

.SStablegridbg table tr td {
    background: #FFF;
    border: 1px solid #d3d5da;
    word-wrap: break-word;
}

.SStablegridbg table td,
.SStablegridbg table th {
    padding: 8px 14px;
    text-align: center;
    font-size: 12px;
    border: 1px #6b6b6b solid;
    border-bottom: none;
    text-align: left;
    color: #93959a;
    font-family:Arial, Helvetica, sans-serif;
}

.SStablegridbg table th {
    font-family:Arial, Helvetica, sans-serif;
    height: 34px;
    border: 1px #d3d5da solid;
    border-bottom: none;
    color: #93959a;
    text-transform: uppercase;
    font-size: 12px;
    border-top: none;
    background: rgb(249, 249, 251);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* 	 		 	 		Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
    /* IE6-9 */
}

.SStablegridbg table .right {
    text-align: right;
}

.SStablegridbg table .center {
    text-align: center;
}

.SStablewrap {
    width: 100%;
}

.SStablewrap table {
    width: 100%;
    table-layout: fixed;
}

.DeviceFooterDiv {
    padding: 12px 0 0 0;
    float: left;
    width: 100%;
    height: 100px;
}

.DeviceDownloadLinkDiv {
    float: left;
    width: 50%;
    border: 1px solid #d3d5da;
    margin-bottom: 25px;
}

.SSFooterDeviceStrip {
    height: 30px;
    border-bottom: 1px #d3d5da solid;
    padding: 5px 16px 0px 10px;
    background: rgb(249, 249, 251);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* 	 		 	 		Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
    /* IE6-9 */
}


.lblHeaderDownloadLink {
    text-align: right;
    margin-right: 4px;
    margin-top: 2px;
}

.DeviceGridSSinner_table {
    float: left;
    overflow: auto;
    width: 100% !important;
}

.DeviceListHeader {
    color: #6e6e6e;
    float: left;
    font-weight: bold;
    padding: 7px 0px 7px 10px;
    width: 100%;
    font-size: 12px;
}

.SentMessage {
    float: right;
    font-weight: bold;
    margin-left: 5px;
    font-size: 12px;
    color: #5591d6;
}

.DeviceOptions {
    float: left;
    padding: 8px 0 0 7px;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

.CMbtnDisabled {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
    pointer-events:none;
}

.CMbtn:hover {
    background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F));
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}



.DownloadStoreImg {
    height: 70px;
    padding-left: 10px;
}

.DeviceFlgRed {
    color: #ea0000;
    cursor: pointer;
}

.DeviceFlgBlue {
    color: #0090d6;
    cursor: pointer;
}

a {
    outline: none;
    color: #5591d6;
    font-weight: bold;
    text-decoration: none !important;
}

  .mat-mdc-dialog-surface mdc-dialog__surface
  {
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
  }
