@media screen, print {
    .provider-view {
        float: left; 
        width: 100%; 
        padding: 5px;
    }
    
    .table-title {
        padding: .3em .3em .3em .3em;
        position: relative;
        font-size: 13px;
        border-left: 0 none;
        border-right: 0 none;
        border-top: 0 none;
        text-align: center;
        font-weight:500;
        border: 1px solid #aed0ea;
        background: #deedf7; 
        color: #222;
        display: block !important;
        font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }
    
    .CMbtnDisabled {
        background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
        border: medium none;
        color: #FFFFFF;
        cursor: pointer;
        float: left;
        font-size: 14px;
        height: 40px;
        padding: 0 10px;
        text-align: center;
        width: 150px;
    }
    
    .CMbtn {
        background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
        border: medium none;
        color: #FFFFFF;
        cursor: pointer;
        float: left;
        height: 25px;
        padding: 1px 10px;
        text-align: center;
        width: 150px;
        font-weight: 500;
        font-size: smaller;
        text-decoration: none;
    }
    
    .CMbtn:hover {
        background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
        background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
    }
    
    .imgIcons {
        background-color: transparent; 
        cursor: pointer; 
        float: right; 
        padding-right: 20px; 
        position: relative;
        width: 47px;
        height: 27px;
    }
    
    .hiddenTd {
        display: none;
    }
    
    .ui-widget-content{
        background:#f2f5f7 50% top repeat-x;
        color:#362b36;
        width: 165px;
    }
    
    .ui-jqgrid-view {
        position: relative;
        float: left;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        left: 0;
        top: 0;
        padding: 0;
        font-size: 11px;
    }
    
    .ui-state-default, .ui-jqgrid-hdiv {
        background: none repeat scroll 0 0 #2779aa !important;
        color: white !important;
        margin-left: -1px !important;
        margin-top: -1px !important;
        border-top-left-radius: 8px;
        border-top-right-radius: 5px;
        font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }
    
    /* no need of this */
    .ui-widget-header {
        border: 1px solid #aed0ea;
        background: #deedf7; /*url(images/ui-bg_highlight-soft_100_deedf7_1x100.png) 50% 50% repeat-x; */
        color: #222;
        font-weight: bold;
        display: block !important;
    }
    
    .ColorLocationRCProviderlevel {
        background-color: #dcdcdc !important;
        font-weight:bold !important;
        cursor:pointer;    
        padding-left: 5px; 
        text-decoration: none;    
        color: #222;   
    }
    
    .ColorLocationRCProviderlevel a {
        cursor:pointer;
        text-decoration: none;  
        color: #222;  
    }
    
    .ColorLocationRCProviderlevel a:hover {
        color:#2779aa;
    }
    
    .searchButton {
        color: #362b36;
        width: 160px;
        text-align: center;
    }
    
    .providerLevelData {
        cursor:pointer;    
        padding-left: 5px;
        height: 25px; 
    }
    
    .dropdownMenu{
        font-size:small;
        width: 110px;
    }
    
    table.ui-jqgrid-htable {
        min-width: 100% !important;
    }
    
    table.ui-jqgrid-htable th {
        border: 1px solid #FFF !important;
    }
    table.ui-jqgrid-htable td {
        border: 1px solid #CCC !important;
    }
    
    #actionButtons a {
        visibility: hidden;
    }

    table.ui-jqgrid-htable tr.searchCriteriaTR {
        display: none;
    }
      
}