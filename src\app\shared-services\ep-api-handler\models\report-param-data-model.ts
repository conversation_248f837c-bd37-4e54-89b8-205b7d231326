export interface IReportParamData {
  groups: IParamGroup[]
  name: string;
  type: string;
  data: IData[];
  default: string;
}

export interface IData {
  groupId: number;
  key: number;
  value: string;
  subtext: string;
}

export interface IParamGroup {
  id: number;
  name: string;
}

const blankData: IData = {
  groupId: 0,
  key: 0,
  value: '',
  subtext: '',
}; 
export const blankDataArray: IData[] = [blankData];

const blankParamGroup: IParamGroup = {
  id: 0,
  name: '',
};
const blankParamGroupArray: IParamGroup[] = [blankParamGroup];

export const blankReportParamData: IReportParamData = {
  groups: blankParamGroupArray,
  name: '',
  type: '',
  data: blankDataArray,
  default: '',
};
