import { Component, OnDestroy, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { RetentionService } from '../../retention.service';
import { ActivatedRoute } from '@angular/router';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'full-alert-history',
  templateUrl: './full-alert-history.component.html',
  styleUrls: ['./full-alert-history.component.scss']
})
export class FullAlertHistoryComponent implements OnInit, OnDestroy {
  patientFullAlertHistory: any = [];  
  patientDetails: any = [];
  patientId : any;
  siteId: number = 0;
  private pageSubscriptions: Subscription = new Subscription;
  
  constructor(
    private location: Location,
    private _Activatedroute:ActivatedRoute,
    public retentionInCare: RetentionService,
    private userContext: UserContext
  ) {}
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.patientId = this._Activatedroute.snapshot.paramMap.get("patientId");

    this.siteId = this.userContext.GetCurrentSiteValue();
    if (this.siteId != 0) {
      this.loadFullAlertHistory(this.siteId.toString(),this.patientId);
    }

    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(site => {
      this.loadFullAlertHistory(site.toString(),this.patientId);
      })
    );

  }

  private loadFullAlertHistory(site: string, patientId: string) {
    // Patient Measure Details API call to show Patient details
    this.retentionInCare.GetPatientMeasureDetail(site,this.patientId).subscribe(result => {
      if(result){
        this.patientDetails = result;
      }
    })

    // Full Alert History API call to show history of Patient
    this.retentionInCare.GetFullOutreachHistory(site,this.patientId).subscribe(result => {
      if(result){
        this.patientFullAlertHistory = result;
      }
    })
  }


  // Function used to go back to previous page
  goBack() {
    this.location.back();
  }
}
