<div class="PALcontent">
    <form #recordResultForm="ngForm">
        <div id="Chorusloader"></div>
        <div id="divMsg"></div>
        <h1 [innerHTML]="this.patientDetails ? this.patientDetails.patientName : this.patientName"></h1>
        <div class="div-container">
            <label class="custom_input">Record Result:</label>
            <select name="recordResult" class="dropdownMenu" name="rrOption" [formControl]="rrOption" (change)="onSelectionChange()">
                <option selected="selected" value="-1" label="Select"></option>
                <optgroup *ngFor="let providerGroup of recordResultOptions[0]?.groups" [label]="providerGroup.name">
                    <option
                        *ngFor="let providerData of panelService.getGroupData(providerGroup.id, recordResultOptions[0])"
                        [value]="providerData.key">
                        {{providerData.value}}
                    </option>
                </optgroup>
            </select>
        </div>
        <div class="div-container">
            <label class="custom_input">Notes:</label>
            <textarea ngModel name="resultText" [formControl]="resultText" rows="3" cols="40">
            </textarea>
        </div>
        <div class="div-container btnContainer">
            <button class="CMbtn" [disabled]="!isSubmitButtonEnable" [class]="rptbtnColor" (click)="onClickSave()" type="submit">Save</button>
            <button class="CMbtn" cdkFocusInitial (click)="onNoClick()">Cancel</button>
        </div>
    </form>
</div>