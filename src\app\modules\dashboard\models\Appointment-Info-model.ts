export interface AppointmentInfo {
      appointmentType:string;
      firstName:string;
      fullName:string;
      hcvDiagFlg:number;
      hivDiagFlg:number;
      lastName:string;
      measureGap:QualityGapModel;
      mrn:string;
      patientAnalysisDisplay:string;
      patientId:number;
      patientSearchDisplay:string;
      providerId:number;
      scheduleDt:string
      scheduleId:string;
      scheduleTm:Date;
      siteId:number;
      statusCd:string;
      statusDesc:string;
      statusId:number;
      gridRowColor:string;
      FlowSheetImage:string;
}

export interface QualityGapModel {
      patientId: number;
      measureGap: number;
      measureGapDesc: MeasureGapDescModel[]; 
}

export interface MeasureGapDescModel {
      source: string;
      measureCd: string;
      reportLevel1Desc: string;
      desc: string;
      formLink?: FormLinkModel;
}

export interface FormLinkModel {
      formURL: string;
      submittedDate: Date;
}

export interface AppointmentProviders {
      fulL_NM: string;
      provideR_ID: number;
      lasT_NM: string;
      firsT_NM: string;
      group: string;
      primarY_FLG: boolean
}

export enum PatientScheduleStatus{
      Cancelled = "3",//1
      Mistake = "7",
      NoShow = "2",
      Completed = "1"
}
   
