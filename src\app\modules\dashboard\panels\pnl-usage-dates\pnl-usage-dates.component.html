<div class="searchCriteriaDiv">
        <div class="elementDiv">
            Start Date*:
        </div>
        <div class="elementDiv">
            <input type="date" (change)="readyToRun()" mat-formfield  name="rptpanStartDt" [formControl]="rptpanStartDt">
        </div>
        <div class="elementDiv">
            Stop Date*:
        </div>
        <div class="elementDiv">
            <input type="date" (change)="readyToRun()" mat-formfield name="rptpanStopDt" [formControl]="rptpanStopDt">
        </div>
        <div class="elementDiv">
            First Login Month*:
        </div>
        <div class="elementDiv">
            <input type="date" (change)="readyToRun()" mat-formfield name="rptpanFirstLoginMonth" [formControl]="rptpanFirstLoginMonth">
        </div>
        <div class="elementDiv">
            <button type="button" (click)="runReport()" id="reportViewer_Control_viewReportClick" 
            [disabled]="!allowReport"
            aria-describedby="reportViewer_Control_viewReportClick" [class]="rptbtnColor">Run</button>
        </div>
</div>
<p *ngIf="fg.invalid" class="error-message">Please add valid dates for all required fields</p>
