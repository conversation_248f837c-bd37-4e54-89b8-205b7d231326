import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

@Component({
  selector: 'app-admin-content',
  templateUrl: './admin-content.component.html',
  styleUrls: ['./admin-content.component.scss'],
})
export class AdminContentComponent implements OnInit {
  siteId: string = '';

  public  pdfFillerManagerPath = 'pdf-filler-manager';

  constructor(
    private layoutService: LayoutService,
    private userContext: UserContext,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.layoutService.hideSpinner();
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
   // this.pdfFillerManagerPath = this.pdfFillerManagerPath + this.siteId;
  }

  /**
   * Navigate to a specific admin section
   * @param section The section identifier to navigate to
   */
  navigateToSection(section: string): void {
    console.log(`Navigating to ${section} section`);

    if (section === this.pdfFillerManagerPath)
    {
     this.router.navigate(['/Admin', section, this.siteId]);
    }
    else
    {
      this.router.navigate(['/Admin', section]);
    }
    // Navigate to the appropriate section

  }
}
