
<div *ngIf="updateAvailable" id="overlay" class="overlay">
    <div class="overlay-content">
        <h2>New Version Available</h2>
        <p>A new version of Chorus is available. Please click refresh to update.</p>
        <div class="button-container">
            <button id="refresh-button" (click)="reloadApp()">Refresh</button>
            <button id="later-button" (click)="postponeUpdate()">Later</button>
        </div>
    </div>
</div>

<router-outlet></router-outlet>

<ngx-spinner bdColor="rgba(0, 0, 0, 0.2)" size="medium" type="ball-spin-rotate"><p style="color: white" >Loading...</p></ngx-spinner>
