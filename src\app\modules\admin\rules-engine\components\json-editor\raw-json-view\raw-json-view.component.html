<div class="raw-json-container">
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>JSON Data</mat-label>
    <textarea
      matInput
      [ngModel]="rawJsonValue"
      (ngModelChange)="onRawJsonChange($event)"
      rows="20"
      placeholder="Enter JSON data"
      [ngModelOptions]="{standalone: true}">
    </textarea>
    <mat-error *ngIf="jsonError">{{ jsonError }}</mat-error>
  </mat-form-field>
</div>
