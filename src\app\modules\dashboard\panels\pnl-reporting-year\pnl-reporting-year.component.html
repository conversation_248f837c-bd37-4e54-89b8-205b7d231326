<div class="searchCriteriaDiv">
    <form>
        <!-- Reporting Year Row -->
        <div class="field-row">
            <label class="field-label" for="rptpanReportingYear">Reporting Year*:</label>
            <select mat-form-field class="form-select form-select-sm year-select"
            name="rptpanReportingYear" id="rptpanReportingYear" (change)="readyToRun()"
            [formControl]="rptpanReportingYear" title="Select reporting year">
              <option *ngFor="let option of rptReportYearData" [value]="option.key">{{option.value}}</option>
            </select>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="panelService.InitBoldReport()" id="reportViewer_Control_viewReportClick"
            aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
    </form>
</div>
