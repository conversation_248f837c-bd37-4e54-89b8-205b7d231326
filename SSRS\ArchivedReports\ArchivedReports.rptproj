<?xml version="1.0" encoding="Windows-1252"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug 2012' ">
    <FullPath>Debug 2012</FullPath>
    <OutputPath>bin\Debug</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2016</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>ArchivedReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'DebugLocal' ">
    <FullPath>DebugLocal</FullPath>
    <OutputPath>bin\DebugLocal</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2008R2</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>ArchivedReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <FullPath>Release</FullPath>
    <OutputPath>bin\Release</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2008R2</TargetServerVersion>
    <Platform>Win32</Platform>
    <TargetReportFolder>ArchivedReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug 2019' ">
    <FullPath>Debug 2019</FullPath>
    <OutputPath>bin\Debug</OutputPath>
    <ErrorLevel>2</ErrorLevel>
    <OverwriteDatasets>False</OverwriteDatasets>
    <OverwriteDataSources>False</OverwriteDataSources>
    <TargetServerVersion>SSRS2016</TargetServerVersion>
    <Platform />
    <TargetReportFolder>ArchivedReports</TargetReportFolder>
    <TargetDatasetFolder>Datasets</TargetDatasetFolder>
    <TargetDatasourceFolder>Data Sources</TargetDatasourceFolder>
    <TargetReportPartFolder>Report Parts</TargetReportPartFolder>
  </PropertyGroup>
  <PropertyGroup>
    <State>$base64$PFNvdXJjZUNvbnRyb2xJbmZvIHhtbG5zOnhzZD0iaHR0cDovL3d3dy53My5vcmcvMjAwMS9YTUxTY2hlbWEiIHhtbG5zOnhzaT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS9YTUxTY2hlbWEtaW5zdGFuY2UiIHhtbG5zOmRkbDI9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDAzL2VuZ2luZS8yIiB4bWxuczpkZGwyXzI9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDAzL2VuZ2luZS8yLzIiIHhtbG5zOmRkbDEwMF8xMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDA4L2VuZ2luZS8xMDAvMTAwIiB4bWxuczpkZGwyMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEwL2VuZ2luZS8yMDAiIHhtbG5zOmRkbDIwMF8yMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEwL2VuZ2luZS8yMDAvMjAwIiB4bWxuczpkZGwzMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDExL2VuZ2luZS8zMDAiIHhtbG5zOmRkbDMwMF8zMDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDExL2VuZ2luZS8zMDAvMzAwIiB4bWxuczpkZGw0MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEyL2VuZ2luZS80MDAiIHhtbG5zOmRkbDQwMF80MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEyL2VuZ2luZS80MDAvNDAwIiB4bWxuczpkZGw1MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEzL2VuZ2luZS81MDAiIHhtbG5zOmRkbDUwMF81MDA9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vYW5hbHlzaXNzZXJ2aWNlcy8yMDEzL2VuZ2luZS81MDAvNTAwIiB4bWxuczpkd2Q9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vRGF0YVdhcmVob3VzZS9EZXNpZ25lci8xLjAiPg0KICA8RW5hYmxlZD50cnVlPC9FbmFibGVkPg0KICA8UHJvamVjdE5hbWU+U0FLPC9Qcm9qZWN0TmFtZT4NCiAgPEF1eFBhdGg+U0FLPC9BdXhQYXRoPg0KICA8TG9jYWxQYXRoPlNBSzwvTG9jYWxQYXRoPg0KICA8UHJvdmlkZXI+U0FLPC9Qcm92aWRlcj4NCjwvU291cmNlQ29udHJvbEluZm8+</State>
    <DataSources>@(DataSource)</DataSources>
    <DataSets>@(DataSet)</DataSets>
    <Reports>@(Report)</Reports>
  </PropertyGroup>
  <ItemGroup>
    <DataSource Include="dsChorus.rds" />
    <DataSource Include="dsDIRECTOR.rds" />
    <DataSource Include="dsOPERA.rds" />
    <DataSource Include="dsSite.rds" />
  </ItemGroup>
  <ItemGroup>
    <Report Include="DISEASE_MANAGEMENT_001.rdl" />
    <Report Include="PatientVisitDetail.rdl" />
    <Report Include="Provider_Alert_Status.rdl" />
    <Report Include="Provider_High_Risk_LTF_Summary.rdl" />
    <Report Include="RecentlySeenHIVHCV_Inactive.rdl" />
    <Report Include="rptSitePatientLabResults.rdl" />
    <Report Include="rptSiteVisits.rdl" />
    <Report Include="rptSiteVisitsSubReport.rdl" />
    <Report Include="RyanWhite_PatientsVisits2018.rdl" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Reporting Services\Microsoft.ReportingServices.MSBuilder.targets" />
</Project>