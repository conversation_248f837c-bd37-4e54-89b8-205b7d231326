# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger: none

pr:
  - main

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: PowerShell@2
  inputs:
    targetType: 'inline'
    script: |
      $url = "$(System.CollectionUri)$(System.TeamProject)/_apis/wit/workitems/$(System.PullRequest.PullRequestId)?api-version=6.0"
      $body = @"
      [
        {
          "op": "add",
          "path": "/fields/System.State",
          "value": "Ready to Test"
        }
      ]
      "@
      
      $response = Invoke-RestMethod -Uri $url -Method PATCH -Body $body -ContentType "application/json-patch+json" -Headers @{
        Authorization = "Bearer $(System.AccessToken)"
      }
      
      Write-Host "Work item updated: $($response.id)"
  env:
    SYSTEM_ACCESSTOKEN: $(System.AccessToken)