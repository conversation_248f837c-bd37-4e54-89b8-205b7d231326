﻿/*#region Site Schedule Webpart */
.SSTopDiv img {
    border: none;
}

.SSTopDiv a {
    outline: none;
    color: #4b8cd4;
    font-weight: bold;
    text-decoration: none;
    cursor:pointer;
}

    .SSTopDiv a:hover {
        color: #005b9f;
    }

.SSTopDiv p {
    text-align: justify;
    line-height: 19px;
}

.SStopstrip {
    height: 30px;
    border: 1px #d3d5da solid;
    padding: 10px 16px 0px 10px;
    background: rgb(249,249,251); /* Old browsers */
    background: -moz-linear-gradient(top, rgba(249,249,251,1) 0%, rgba(242,243,247,1) 2%, rgba(236,238,244,1) 92%, rgba(236,238,244,1) 98%, rgba(240,241,246,1) 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(249,249,251,1)), color-stop(2%,rgba(242,243,247,1)), color-stop(92%,rgba(236,238,244,1)), color-stop(98%,rgba(236,238,244,1)), color-stop(100%,rgba(240,241,246,1))); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* 	 		 	 		Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* IE10+ */
    background: linear-gradient(to bottom, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9fb', endColorstr='#f0f1f6',GradientType=0 ); /* IE6-9 */
}

.SStopstripleft {
    float: left;
    width: auto;
}
.printSStopstripleft {
    float: left;
    width: auto;
    display:inline !important;
}

    .SStopstripleft span {
        color: #444444;
        font-size: 12px;
        font-weight: bold;
        float: left;
    }

    .SStopstripleft div {
        /*background: url(images/arrowdown.png) no-repeat right #edeff4;*/
        float: left;
        /*overflow: hidden;*/
    }

    .SStopstripleft select {
        background: transparent;
        width: 315px;
        font-size: 12px;
        font-weight: bold;
        border: 0;
        border-radius: 0;
        -webkit-appearance: none;
        cursor: pointer;
        text-overflow: '';
        text-indent: 0.01px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

        .SStopstripleft select option {
            margin-left: 2px;
        }
/* CSS hacks for older browsers */

.SStopstripright {
    height: 20px;
    float: left;
    cursor: pointer;
}

    .SStopstripright input:hover {
        color: #005b9f;
    }

.SSInputDate {
    width: 115px;
    cursor: pointer;
    font-weight: bold;
}

.SStopstripright img {
    float: left;
}

#divFlowsheetTxtDate {
    position: absolute;
    top: 0;
    left: 0;
    opacity: .1;
    height: 20px;
    width: 165px;
    background-color: #EBEDF3;
    display:none;
}

#DateControlDiv {
    position: absolute;
    top: 0;
    left: 0;
    opacity: .1;
    height: 20px;
    width: 165px;
    background-color: #EBEDF3;
}


#imgDownArrow {
    float: left;
    margin-top: 5px;
    display:none;
}

#imgPrinter {
    display: none;
}

.SSgridcontent {
    width: 100%;
    float: left;
}

.SStablewrap {
    width: 100%;
}

    .SStablewrap table {
        width: 100%;
        table-layout: fixed;
    }
#grdFlowsheet {
    
     width: auto;
     height: auto;
     overflow: visible !important;
}
.tablehead {
     /*position: relative;*/
}
.SSinner_table {
    float: left;
     overflow: visible !important;
      width: auto;
     height: auto;
     /*margin-top:120px;
     padding-top:120px;*/
     /*position:fixed !important;
     top:60px;*/
    /*width: 100%;
    height:100% !important;*/
    
}
.PrintSSinner_table {
     overflow: visible !important;
      width: auto;
     height: auto;
}
    .SSinner_table mCustomScrollbar _mCS_4 {
        height: 100% !important;
    }
.GridbackColor {
    width: 50px;
    height: 50px;
    background-image: url(./assets/images/circle_50x50_black.png) no-repeat !important;
    background-size: 50px 50px;  
    border: none;
    cursor: pointer;
    font-weight:bold;
    font-size:14.5px;
    color:#e76407;
    //646456
    border-radius:25px;
    -webkit-print-color-adjust:exact;
}
.SStablegridbg {
    width: 100%;
    float: left;
}

    .SStablegridbg tr:nth-child(odd) {
       background-color: #FFF;
    }

    .SStablegridbg table {
        width: 100%;
        border-collapse: collapse;
    }

        .SStablegridbg table tr, td {
            background: #FFF;
            border: 1px solid #d3d5da;
            word-wrap: break-word;
        }

        .SStablegridbg table td, .SStablegridbg table th {
            padding: 8px 14px;
            text-align: center;
            font-weight: bold;
            border: 1px #6b6b6b solid;
            border-bottom: none;
            text-align: left;
        }

        .SStablegridbg table th {
            height: 34px;
            border: 1px #d3d5da solid;
            border-bottom: none;
            color: #93959a;
            text-transform: uppercase;
            font-size: 12px;
            border-top: none;
            background: rgb(249,249,251); /* Old browsers */
            background: -moz-linear-gradient(top, rgba(249,249,251,1) 0%, rgba(242,243,247,1) 2%, rgba(236,238,244,1) 92%, rgba(236,238,244,1) 98%, rgba(240,241,246,1) 100%); /* FF3.6+ */
            background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(249,249,251,1)), color-stop(2%,rgba(242,243,247,1)), color-stop(92%,rgba(236,238,244,1)), color-stop(98%,rgba(236,238,244,1)), color-stop(100%,rgba(240,241,246,1))); /* Chrome,Safari4+ */
            background: -webkit-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* 	 		 	 		Chrome10+,Safari5.1+ */
            background: -o-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* Opera 11.10+ */
            background: -ms-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* IE10+ */
            background: linear-gradient(to bottom, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* W3C */
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9fb', endColorstr='#f0f1f6',GradientType=0 ); /* IE6-9 */
        }

        .SStablegridbg table .right {
            text-align: right;
        }

        .SStablegridbg table .center {
            text-align: center;
        }

.SSfooter {
    width: 100%;
    float: left;
    min-height: 13px !important;
    height: 13px !important;
    /*margin-top: 13px;*/
    /*margin-bottom: 20px;*/
}

.SSfooterleft {
    float: left;
}

.SSfooterright {
    float: right;
}

.SSmainbuttons {
    border: 1px #d3d5da solid;
    padding: 10px 20px 10px 20px;
    color: #93959a;
    float: inherit;
    text-transform: uppercase;
    font-size: 12px;
    background: rgb(249,249,251); /* Old browsers */
    background: -moz-linear-gradient(top, rgba(249,249,251,1) 0%, rgba(242,243,247,1) 2%, rgba(236,238,244,1) 92%, rgba(236,238,244,1) 98%, rgba(240,241,246,1) 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(249,249,251,1)), color-stop(2%,rgba(242,243,247,1)), color-stop(92%,rgba(236,238,244,1)), color-stop(98%,rgba(236,238,244,1)), color-stop(100%,rgba(240,241,246,1))); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* 	 		 	 		Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* IE10+ */
    background: linear-gradient(to bottom, rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9fb', endColorstr='#f0f1f6',GradientType=0 ); /* IE6-9 */
}

#lblAlertSeconds {
    font-size: 25px;
    float: right;
    margin-right: 2px;
}

#divAlert {
    background-color: #60baff;
    color: white;
    float: left;
    padding: 18px;
    margin: -10px 10px 10px 0px;
    border-radius: 5px;
    width: 64px;
    height: 64px;
    box-sizing: border-box;
}

.divAlert .btnActive {
    color: #FFFFFF;
    font-size: 15px;
    border: none;
    padding: 0px 10px 0px 10px;
    width: 150px;
    text-align: center;
    height: 40px;
    float: left;
    cursor: pointer;
    background-image: -webkit-gradient( linear, left top, left bottom, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
    background-image: -o-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
}

    .divAlert .btnActive:hover {
        background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
        background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
    }

.divWaiting {
    position: absolute;
    text-align: center;
    top: 2%;
    height: 100%;
    width: 100%;
}

.modal-header h4 {
    margin: 0px auto;
    color: #4e4e53;
    font-weight: bold;
    font-size: 13px;
}

.GridRowColorCancelled {
border-left-width: 1px !important;
box-shadow: inset 6px 0px 0px 0px rgb(204,41,61) !important;
}
.GridRowColornoShow {
border-left-width: 1px !important;
box-shadow: inset 6px 0px 0px 0px rgb(242, 203, 29) !important;
}
.GridRowColorCompleted {border-left-width:1px !important; box-shadow: inset 6px 0px 0px 0px rgb(0, 156, 204) !important; -webkit-print-color-adjust: exact !important;}

.leftBorder {
    border-left-style: hidden !important;
    -webkit-border-left-style: hidden !important;
    -moz-border-left-style: hidden !important;
    padding-left: inherit !important
    }
/*#endregion */