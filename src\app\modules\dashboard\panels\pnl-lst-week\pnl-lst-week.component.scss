.searchCriteriaDiv {
    position: relative;
    margin: 2px;
    padding: 15px;
    width: 100%;
    z-index: 1;
    background-color: white;
}

// Field Row
.field-row {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field-label {
    color: black;
    font-weight: 500;
    margin: 0;
    font-size: 14px;
}

// Week Select
.week-select {
    width: 100%;
    max-width: 250px;
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

// Run Button Row
.button-row {
    margin-top: 15px;
    padding: 0 16px;
}

.run-button {
    width: 100% !important;
    padding: 8px 16px !important;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
    font-size: 14px;
    height: auto;
    min-height: 35px;
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}

.btnWidth {
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 30px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}
