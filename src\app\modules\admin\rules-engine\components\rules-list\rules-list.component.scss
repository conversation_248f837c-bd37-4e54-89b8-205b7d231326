.rules-container {
  padding: 16px;

  .filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    mat-form-field {
      width: 300px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .rules-table-container {
    position: relative;
    min-height: 200px;

    table {
      width: 100%;
    }

    .no-data-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #666;

      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
        color: #999;
      }

      p {
        font-size: 16px;
      }
    }
  }
}
