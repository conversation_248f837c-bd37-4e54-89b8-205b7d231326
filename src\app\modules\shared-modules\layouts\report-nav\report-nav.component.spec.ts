import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, NavigationEnd } from '@angular/router';
import { ElementRef } from '@angular/core';
import { Subject } from 'rxjs';
import { ReportNavComponent } from './report-nav.component';
import { LayoutService } from '../services/layout/layout.service';
import { IMenuSections } from '../models/menu-item.model';

describe('ReportNavComponent', () => {
  let component: ReportNavComponent;
  let fixture: ComponentFixture<ReportNavComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockLayoutService: jasmine.SpyObj<LayoutService>;
  let routerEventsSubject: Subject<any>;
  let menuSectionsSubject: Subject<IMenuSections[]>;

  beforeEach(async () => {
    routerEventsSubject = new Subject();
    menuSectionsSubject = new Subject();

    mockRouter = jasmine.createSpyObj('Router', ['navigateByUrl'], {
      events: routerEventsSubject.asObservable(),
      url: '/Dashboard/Report/999/Bonus_Quality_Measures'
    });

    mockLayoutService = jasmine.createSpyObj('LayoutService', ['loadNavMenu', 'getShowMenu'], {
      menuSections: menuSectionsSubject.asObservable()
    });

    await TestBed.configureTestingModule({
      declarations: [ReportNavComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: LayoutService, useValue: mockLayoutService },
        { provide: ElementRef, useValue: { nativeElement: { querySelector: () => null } } }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ReportNavComponent);
    component = fixture.componentInstance;
  });

  describe('GetCurrentReport', () => {
    beforeEach(() => {
      component.menuSections = [
        {
          categoryId: 1,
          categoryNm: 'Quality Measures',
          reportsForSection: [
            {
              reportFileName: 'Bonus_Quality_Measures.rdl',
              reportName: 'Bonus Quality Measures',
              categoryNm: 'Quality Measures',
              reportId: 1,
              description: 'Test report',
              panelId: 1,
              siteId: 999,
              panelLabel: 'Test'
            }
          ]
        }
      ];
    });

    it('should correctly identify current report without query parameters', () => {
      mockRouter.url = '/Dashboard/Report/999/Bonus_Quality_Measures';
      
      component.GetCurrentReport();
      
      expect(component.currentReportSection).toBe('Quality Measures');
      expect(component.currentReport).toBe('Bonus Quality Measures');
    });

    it('should correctly identify current report with query parameters', () => {
      mockRouter.url = '/Dashboard/Report/999/Bonus_Quality_Measures?run=1&param=value';
      
      component.GetCurrentReport();
      
      expect(component.currentReportSection).toBe('Quality Measures');
      expect(component.currentReport).toBe('Bonus Quality Measures');
    });

    it('should correctly identify CustomQuery report with query parameters', () => {
      mockRouter.url = '/Dashboard/Report/999/CustomQuery/Criteria?filter=test';
      
      component.GetCurrentReport();
      
      expect(component.currentReportSection).toBe('Custom Queries and Reports');
      expect(component.currentReport).toBe('Custom Query');
    });

    it('should handle complex query strings', () => {
      mockRouter.url = '/Dashboard/Report/999/Bonus_Quality_Measures?run=1&demographics_id=[123]&location=test%20location';
      
      component.GetCurrentReport();
      
      expect(component.currentReportSection).toBe('Quality Measures');
      expect(component.currentReport).toBe('Bonus Quality Measures');
    });

    it('should handle URLs with fragments', () => {
      mockRouter.url = '/Dashboard/Report/999/Bonus_Quality_Measures?run=1#section1';
      
      component.GetCurrentReport();
      
      expect(component.currentReportSection).toBe('Quality Measures');
      expect(component.currentReport).toBe('Bonus Quality Measures');
    });
  });

  describe('Router subscription', () => {
    it('should call GetCurrentReport when NavigationEnd event occurs', () => {
      spyOn(component, 'GetCurrentReport');
      
      component.ngAfterViewInit();
      
      // Simulate a NavigationEnd event
      routerEventsSubject.next(new NavigationEnd(1, '/Dashboard/Report/999/Test_Report?run=1', '/Dashboard/Report/999/Test_Report?run=1'));
      
      expect(component.GetCurrentReport).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from router subscription', () => {
      spyOn(component['routerSubscription'], 'unsubscribe');
      
      component.ngOnDestroy();
      
      expect(component['routerSubscription'].unsubscribe).toHaveBeenCalled();
    });
  });
});
