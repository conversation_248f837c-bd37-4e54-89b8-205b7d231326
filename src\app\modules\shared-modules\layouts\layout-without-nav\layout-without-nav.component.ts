import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { LayoutService } from '../services/layout/layout.service';

@Component({
  selector: 'app-layout-without-nav',
  templateUrl: './layout-without-nav.component.html',
  styleUrls: ['./layout-without-nav.component.css']
})
export class LayoutWithoutNavComponent implements OnInit {
  userName: string = "";
  isAuthenticated: boolean = false;

  constructor(public layoutService: LayoutService,
              public userContext: UserContext,
              public router: Router)
              {
                if (this.router.url=='/Auth/Login')
                {
                  this.isAuthenticated=false;
                }
                else
                {
                  this.isAuthenticated=true;
                }
  }

  ngOnInit(): void {

  }

}
