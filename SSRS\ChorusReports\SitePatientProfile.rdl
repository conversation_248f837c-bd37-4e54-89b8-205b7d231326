﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.7.34031.279</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.7.34031.279</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2023-09-21T17:02:43.4725258Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="TreatmentProfileBySexEthnicity">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@PROVIDER_CD">
            <Value>=Parameters!PROVIDER_CD.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@LOCATION_CD">
            <Value>=Parameters!LOCATION_CD.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT     @COHORT_ID as COHORT_ID,
					cohort_nm,
					provider_nm,
					location_nm  ,
					row_num ,
					row_label ,
					row_level ,
					ISNULL(total,0)  total,
					sum(iif(row_num = 2, total, 0)) over (partition by provider_nm, location_nm)  chrt_prov_total,
					sum(iif(row_num = 2, total, 0)) over (partition by location_nm)  chrt_loc_total,
					sum(iif(row_num = 2, total, 0)) over (partition by 1)  chrt_total,
					NULL VL_MDN_COHORT,
					NULL  VL_MDN_LOCATION,
					NULL  VL_MDN_PROVIDER,

					NULL  VL_MDN_COHORT_M,
					NULL  VL_MDN_M_LOCATION,
					NULL  VL_MDN_M_PROVIDER,

					NULL  VL_MDN_COHORT_F,
					NULL  VL_MDN_F_LOCATION,
					NULL  VL_MDN_F_PROVIDER,

					NULL  VL_MDN_COHORT_W,
					NULL  VL_MDN_W_LOCATION,
					NULL  VL_MDN_W_PROVIDER,

					NULL  VL_MDN_COHORT_H,
					NULL  VL_MDN_H_LOCATION,
					NULL  VL_MDN_H_PROVIDER,

					NULL  VL_MDN_COHORT_B,
					NULL  VL_MDN_B_LOCATION,
					NULL  VL_MDN_B_PROVIDER,

					NULL  VL_MDN_COHORT_O,
					NULL  VL_MDN_O_LOCATION,
					NULL  VL_MDN_O_PROVIDER,
					/*
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY total ASC )   OVER (partition by row_num) VL_MDN_COHORT,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY total ASC )   OVER (partition by row_num,location_nm) VL_MDN_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY total ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY male ASC )   OVER (partition by row_num) VL_MDN_COHORT_M,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY male ASC )   OVER (partition by row_num,location_nm) VL_MDN_M_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY male ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_M_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY female ASC )   OVER (partition by row_num) VL_MDN_COHORT_F,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY female ASC )   OVER (partition by row_num,location_nm) VL_MDN_F_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY female ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_F_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY white ASC )   OVER (partition by row_num) VL_MDN_COHORT_W,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY white ASC )   OVER (partition by row_num,location_nm) VL_MDN_W_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY white ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_W_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY hispanic ASC )   OVER (partition by row_num) VL_MDN_COHORT_H,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY hispanic ASC )   OVER (partition by row_num,location_nm) VL_MDN_H_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY hispanic ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_H_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY black ASC )   OVER (partition by row_num) VL_MDN_COHORT_B,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY black ASC )   OVER (partition by row_num,location_nm) VL_MDN_B_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY black ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_B_PROVIDER,

					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY other ASC )   OVER (partition by row_num) VL_MDN_COHORT_O,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY other ASC )   OVER (partition by row_num,location_nm) VL_MDN_O_LOCATION,
					PERCENTILE_CONT ( .5 ) WITHIN GROUP (ORDER BY other ASC )   OVER (partition by row_num,location_nm,provider_nm) VL_MDN_O_PROVIDER,
					*/
					ISNULL(ptnt_total,0) ptnt_total,
					ISNULL(Male,0) Male  ,
					ISNULL(Female,0) Female ,
					ISNULL(gndr_Unkown,0) gndr_Unkown,
					ISNULL(white,0) white  ,
					ISNULL(hispanic,0) hispanic ,
					ISNULL(black,0) black ,
					ISNULL(other,0) other,
					ISNULL(race_unkown,0) race_unkown
					FROM REPORT.Profile_TreatmentProfileBySexEthnicity_BySite(@COHORT_ID, @PROVIDER_CD, @LOCATION_CD)
					OPTION (MAXDOP 4)</CommandText>
      </Query>
      <Fields>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cohort_nm">
          <DataField>cohort_nm</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="provider_nm">
          <DataField>provider_nm</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="location_nm">
          <DataField>location_nm</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="row_num">
          <DataField>row_num</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="row_label">
          <DataField>row_label</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="row_level">
          <DataField>row_level</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="total">
          <DataField>total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ptnt_total">
          <DataField>ptnt_total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="chrt_prov_total">
          <DataField>chrt_prov_total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="chrt_loc_total">
          <DataField>chrt_loc_total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT">
          <DataField>VL_MDN_COHORT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="chrt_total">
          <DataField>chrt_total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Male">
          <DataField>Male</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_LOCATION">
          <DataField>VL_MDN_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_PROVIDER">
          <DataField>VL_MDN_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="gndr_Unkown">
          <DataField>gndr_Unkown</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_M">
          <DataField>VL_MDN_COHORT_M</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Female">
          <DataField>Female</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_M_LOCATION">
          <DataField>VL_MDN_M_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="white">
          <DataField>white</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="race_unkown">
          <DataField>race_unkown</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_M_PROVIDER">
          <DataField>VL_MDN_M_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="hispanic">
          <DataField>hispanic</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_F">
          <DataField>VL_MDN_COHORT_F</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="black">
          <DataField>black</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_F_LOCATION">
          <DataField>VL_MDN_F_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_F_PROVIDER">
          <DataField>VL_MDN_F_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="other">
          <DataField>other</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_W">
          <DataField>VL_MDN_COHORT_W</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_W_LOCATION">
          <DataField>VL_MDN_W_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_W_PROVIDER">
          <DataField>VL_MDN_W_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_H">
          <DataField>VL_MDN_COHORT_H</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_H_LOCATION">
          <DataField>VL_MDN_H_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_H_PROVIDER">
          <DataField>VL_MDN_H_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_B">
          <DataField>VL_MDN_COHORT_B</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_B_LOCATION">
          <DataField>VL_MDN_B_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_B_PROVIDER">
          <DataField>VL_MDN_B_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT_O">
          <DataField>VL_MDN_COHORT_O</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_O_LOCATION">
          <DataField>VL_MDN_O_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_O_PROVIDER">
          <DataField>VL_MDN_O_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Followup">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@PROVIDER_CD">
            <Value>=Parameters!PROVIDER_CD.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@LOCATION_CD">
            <Value>=Parameters!LOCATION_CD.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT       @COHORT_ID AS COHORT_ID, COHORT_NAME, PROVIDER, LOCATION, years_followup, PatientCount, cat_order
					FROM            REPORT.Profile_Followup_BySite(@COHORT_ID, @PROVIDER_CD, @LOCATION_CD) AS Profile_Followup_BySite_1</CommandText>
      </Query>
      <Fields>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COHORT_NAME">
          <DataField>COHORT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="years_followup">
          <DataField>years_followup</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PROVIDER">
          <DataField>PROVIDER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PatientCount">
          <DataField>PatientCount</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>LOCATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cat_order">
          <DataField>cat_order</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Followup_Summary">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@PROVIDER_CD">
            <Value>=Parameters!PROVIDER_CD.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@LOCATION_CD">
            <Value>=Parameters!LOCATION_CD.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT       @COHORT_ID AS COHORT_ID, cohort_name, provider, location, average_followup, max_followup, number_of_ptnts_in_followup
					FROM            REPORT.Profile_FollowupSummary_BySite(@COHORT_ID, @PROVIDER_CD, @LOCATION_CD) AS Profile_FollowupSummary_BySite_1</CommandText>
      </Query>
      <Fields>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COHORT_NAME">
          <DataField>cohort_name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AVERAGE_FOLLOWUP">
          <DataField>average_followup</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PROVIDER">
          <DataField>provider</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MAX_FOLLOWUP">
          <DataField>max_followup</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>location</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="NUMBER_OF_PTNTS_IN_FOLLOWUP">
          <DataField>number_of_ptnts_in_followup</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="TreatmentProfileByLabs2">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@PROVIDER_CD">
            <Value>=Parameters!PROVIDER_CD.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@LOCATION_CD">
            <Value>=Parameters!LOCATION_CD.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT DISTINCT  @COHORT_ID as COHORT_ID,
					COHORT_NAME, PROVIDER, LOCATION, SORT_ORDER, CATEGORY, CATEGORY_LEVEL, TOTAL TOTAL, TOTAL_ACTIVE_CNT, sum(iif(SORT_ORDER IN (1, 2), total, 0))
					OVER (partition BY IIF(SORT_ORDER = 1, 1, 2), PROVIDER, LOCATION) chrt_total, HAS_CD4_CNT, avg(CD4_AVG) OVER (partition BY COHORT_NAME, PROVIDER,
					LOCATION) CD4_AVG, PERCENTILE_DISC(.5) WITHIN GROUP (ORDER BY VL_AVG ASC) OVER (partition BY SORT_ORDER, COHORT_NAME, PROVIDER, LOCATION)
					VL_MDN_PROVIDER, PERCENTILE_DISC(.5) WITHIN GROUP (ORDER BY VL_AVG ASC) OVER (partition BY SORT_ORDER, COHORT_NAME, LOCATION) VL_MDN_LOCATION,
					PERCENTILE_DISC(.5) WITHIN GROUP (ORDER BY VL_AVG ASC) OVER (partition BY SORT_ORDER) VL_MDN_COHORT, SUPPRESSED SUPPRESSED,
					CD4_LESSTHAN_200 CD4_LESSTHAN_200, CD4_200TO499 CD4_200TO499, CD4_500PLUS CD4_500PLUS
					FROM            [REPORT].[Profile_TreatmentProfileByLab_BySite](@COHORT_ID, @PROVIDER_CD, @LOCATION_CD)</CommandText>
      </Query>
      <Fields>
        <Field Name="GROUP_TOTAL">
          <Value>=iiF(Fields!SORT_ORDER.Value = 2,Fields!TOTAL.Value,0)</Value>
        </Field>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COHORT_NAME">
          <DataField>COHORT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PROVIDER">
          <DataField>PROVIDER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>LOCATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SORT_ORDER">
          <DataField>SORT_ORDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CATEGORY">
          <DataField>CATEGORY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CATEGORY_LEVEL">
          <DataField>CATEGORY_LEVEL</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL">
          <DataField>TOTAL</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOTAL_ACTIVE_CNT">
          <DataField>TOTAL_ACTIVE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="chrt_total">
          <DataField>chrt_total</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HAS_CD4_CNT">
          <DataField>HAS_CD4_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CD4_AVG">
          <DataField>CD4_AVG</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_PROVIDER">
          <DataField>VL_MDN_PROVIDER</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_LOCATION">
          <DataField>VL_MDN_LOCATION</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VL_MDN_COHORT">
          <DataField>VL_MDN_COHORT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SUPPRESSED">
          <DataField>SUPPRESSED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CD4_LESSTHAN_200">
          <DataField>CD4_LESSTHAN_200</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CD4_200TO499">
          <DataField>CD4_200TO499</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CD4_500PLUS">
          <DataField>CD4_500PLUS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SiteName">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT SITE_ID, SITE_NM FROM [REPORT].[SiteName]()</CommandText>
      </Query>
      <Fields>
        <Field Name="site_id">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="site_nm">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="OperaSiteExtractDate">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT EXTRACT_DT FROM [REPORT].[SiteExtractDate] ()</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.73878in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.63081in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.53508in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.67708in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.625in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.22in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox217">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox217</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox218">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox218</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox219">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Sex</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox219</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox221">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Race / Ethnicity</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox221</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.2in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox21">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Share</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox21</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Male</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Female</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox25">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Unknown</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>White</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Hispanic </Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox27</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Black</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox28</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox29">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Other</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox29</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox30">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Unknown</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox28</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.2in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>#</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox3</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox208">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=1155623</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox208</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox209">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox209</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox210">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=75.1</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox210</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox211">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=24.9</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox211</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox216">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>N/A</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox211</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox212">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=33.9</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox212</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox213">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=18.8</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox213</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox214">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=44.1</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox214</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox215">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=3.2</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox215</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox220">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>N/A</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0;(0.0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox214</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#29679f</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="total">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!total.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT.Value,iif(inscope("provider_nm"),Fields!VL_MDN_PROVIDER.Value,Fields!total.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  SUM(Fields!total.Value),
																		Fields!row_num.Value=2,  SUM(Fields!total.Value),
																		Fields!row_num.Value=3,  SUM(Fields!total.Value),
																		Fields!row_num.Value=4,  SUM(Fields!total.Value),
																		Fields!row_num.Value=5,  SUM(Fields!total.Value),
																		Fields!row_num.Value=6,  SUM(Fields!total.Value),
																		Fields!row_num.Value=7,  SUM(Fields!total.Value),
																		Fields!row_num.Value=8,  SUM(Fields!total.Value),
																		Fields!row_num.Value=9,  SUM(Fields!total.Value),
																		Fields!row_num.Value=11, SUM(Fields!total.Value),
																		Fields!row_num.Value=12, SUM(Fields!total.Value),
																		Fields!row_num.Value=13, SUM(Fields!total.Value),
																		Fields!row_num.Value=14, SUM(Fields!total.Value)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>total</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="total1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>
																		=iif(Fields!row_num.Value = 10 OR Fields!row_num.Value = 15 OR Fields!row_num.Value = 16,"", 
																		iif(iif(Fields!row_num.Value = 1,Variables!tot_cdc_hiv.Value,sum(Fields!chrt_total.Value)) = 0, 
																		0,FormatPercent(sum(Fields!total.Value)/iif(Fields!row_num.Value = 1,Variables!tot_cdc_hiv.Value,
																		iif(inscope("location_nm"),min(Fields!chrt_loc_total.Value), 
																		iif(inscope("provider_nm"),min(Fields!chrt_prov_total.Value),
																		min(Fields!chrt_total.Value)))),1)))
																	</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>total1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Male">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!Male.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_M.Value,iif(inscope("provider_nm"),Fields!VL_MDN_M_PROVIDER.Value,Fields!Male.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Male.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Male</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Female">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!Female.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_F.Value,iif(inscope("provider_nm"),Fields!VL_MDN_F_PROVIDER.Value,Fields!Female.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!Female.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Female</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Female2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!Female.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_F.Value,iif(inscope("provider_nm"),Fields!VL_MDN_F_PROVIDER.Value,Fields!Female.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!gndr_Unkown.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=Microsoft.VisualBasic.Interaction.IIF(Fields!row_num.Value = 15 Or Fields!row_num.Value = 16, "#,0;(#,0)", "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Female</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="white">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!white.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_W.Value,iif(inscope("provider_nm"),Fields!VL_MDN_W_PROVIDER.Value,Fields!white.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!white.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>white</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="hispanic">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!hispanic.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_H.Value,iif(inscope("provider_nm"),Fields!VL_MDN_H_PROVIDER.Value,Fields!hispanic.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!hispanic.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>hispanic</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="black">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!black.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_B.Value,iif(inscope("provider_nm"),Fields!VL_MDN_B_PROVIDER.Value,Fields!black.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!black.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>black</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="other">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!other.Value),
																		Fields!row_num.Value=15, iif(inscope("location_nm"),Fields!VL_MDN_O_LOCATION.Value,iif(inscope("provider_nm"),Fields!VL_MDN_O_PROVIDER.Value,Fields!VL_MDN_COHORT_O.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=6,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!other.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=IIF(Fields!row_num.Value = 15 OR Fields!row_num.Value = 16, "#,0;(#,0)" , "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>other</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#f2f2f2</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="black2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Switch(Fields!row_num.Value=16, AVG(Fields!black.Value),
																		Fields!row_num.Value=15, iif(inscope("row_label"),Fields!VL_MDN_COHORT_B.Value,iif(inscope("provider_nm"),Fields!VL_MDN_B_PROVIDER.Value,Fields!black.Value)),
																		Fields!row_num.Value=10, "",
																		Fields!row_num.Value=1,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=2,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=3,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=4,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=5,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=7,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=8,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=9,  FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=11, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=12, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=13, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1),
																		Fields!row_num.Value=14, FormatPercent(iif(SUM(Fields!total.Value) = 0,0,Sum(Fields!race_unkown.Value)/SUM(Fields!total.Value)),1)
																		)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>=Microsoft.VisualBasic.Interaction.IIF(Fields!row_num.Value = 15 Or Fields!row_num.Value = 16, "#,0;(#,0)", "#,0.0;(#,0.0)")</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>black</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BottomBorder>
                              <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                              <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                              <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>#d9d9d9</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.06334in</Size>
                    <CellContents>
                      <Textbox Name="Textbox109">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Treatment Profile </Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>by </Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Sex &amp; Ethnicity</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox109</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Style>None</Style>
                          </TopBorder>
                          <BottomBorder>
                            <Style>None</Style>
                          </BottomBorder>
                          <LeftBorder>
                            <Style>None</Style>
                          </LeftBorder>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.77244in</Size>
                        <CellContents>
                          <Textbox Name="Textbox101">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox101</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <TopBorder>
                                <Style>None</Style>
                              </TopBorder>
                              <BottomBorder>
                                <Style>None</Style>
                              </BottomBorder>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.51042in</Size>
                            <CellContents>
                              <Textbox Name="Textbox96">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox96</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <TopBorder>
                                    <Style>None</Style>
                                  </TopBorder>
                                  <BottomBorder>
                                    <Style>None</Style>
                                  </BottomBorder>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.77244in</Size>
                        <CellContents>
                          <Textbox Name="Textbox73">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>Location</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox71</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.51042in</Size>
                            <CellContents>
                              <Textbox Name="Textbox97">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>Provider</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox97</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <TopBorder>
                                    <Style>None</Style>
                                  </TopBorder>
                                  <BottomBorder>
                                    <Style>None</Style>
                                  </BottomBorder>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <VerticalAlign>Bottom</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.77244in</Size>
                        <CellContents>
                          <Textbox Name="Textbox104">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox104</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <TopBorder>
                                <Style>None</Style>
                              </TopBorder>
                              <BottomBorder>
                                <Style>None</Style>
                              </BottomBorder>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.51042in</Size>
                            <CellContents>
                              <Textbox Name="Textbox98">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox98</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <TopBorder>
                                    <Style>None</Style>
                                  </TopBorder>
                                  <BottomBorder>
                                    <Style>None</Style>
                                  </BottomBorder>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>5.3462in</Size>
                    <CellContents>
                      <Textbox Name="Textbox116">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Total US HIV+, CDC (2011)</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox116</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BottomBorder>
                            <Color>#29679f</Color>
                          </BottomBorder>
                          <LeftBorder>
                            <Style>None</Style>
                          </LeftBorder>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#f2f2f2</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="row_label">
                    <GroupExpressions>
                      <GroupExpression>=Fields!row_label.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!row_num.Value</Value>
                    </SortExpression>
                    <SortExpression>
                      <Value>=Fields!row_level.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>2.06334in</Size>
                    <CellContents>
                      <Textbox Name="row_label">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=IIF(Fields!row_num.Value = 2, Fields!cohort_nm.Value &amp; " Patients", Fields!row_label.Value)</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>=IIF(Fields!row_level.Value = 1 OR Fields!row_level.Value = 2, "Bold","Default")</FontWeight>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>row_label</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BottomBorder>
                            <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                            <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                            <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                          </BottomBorder>
                          <LeftBorder>
                            <Style>None</Style>
                          </LeftBorder>
                          <RightBorder>
                            <Style>None</Style>
                          </RightBorder>
                          <BackgroundColor>#f2f2f2</BackgroundColor>
                          <PaddingLeft>=IIF(Fields!row_level.Value = 1, "5pt", IIF(Fields!row_level.Value = 2,"20pt","40pt"))</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="location_nm">
                        <GroupExpressions>
                          <GroupExpression>=Fields!location_nm.Value</GroupExpression>
                        </GroupExpressions>
                        <Filters>
                          <Filter>
                            <FilterExpression>=iif(Fields!row_num.Value=10,1,SUM(Fields!total.Value))</FilterExpression>
                            <Operator>GreaterThan</Operator>
                            <FilterValues>
                              <FilterValue DataType="Integer">0</FilterValue>
                            </FilterValues>
                          </Filter>
                        </Filters>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!location_nm.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>1.77244in</Size>
                        <CellContents>
                          <Textbox Name="location_nm">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!location_nm.Value &amp; " (" &amp; iif(Fields!row_num.Value = 1,Variables!tot_cdc_hiv.Value, Fields!chrt_loc_total.Value) &amp; ")"</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>location_nm</rd:DefaultName>
                            <ActionInfo>
                              <Actions>
                                <Action>
                                  <Drillthrough>
                                    <ReportName>SitePatientProfile</ReportName>
                                    <Parameters>
                                      <Parameter Name="COHORT_ID">
                                        <Value>=Fields!COHORT_ID.Value</Value>
                                      </Parameter>
                                      <Parameter Name="LOCATION_CD">
                                        <Value>=Fields!location_nm.Value</Value>
                                      </Parameter>
                                    </Parameters>
                                  </Drillthrough>
                                </Action>
                              </Actions>
                            </ActionInfo>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <BottomBorder>
                                <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                                <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                                <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                              </BottomBorder>
                              <LeftBorder>
                                <Style>None</Style>
                              </LeftBorder>
                              <RightBorder>
                                <Style>None</Style>
                              </RightBorder>
                              <BackgroundColor>#f2f2f2</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="provider_nm">
                            <GroupExpressions>
                              <GroupExpression>=Fields!provider_nm.Value</GroupExpression>
                            </GroupExpressions>
                            <Filters>
                              <Filter>
                                <FilterExpression>=iif(Fields!row_num.Value=10,1,SUM(Fields!total.Value))</FilterExpression>
                                <Operator>GreaterThan</Operator>
                                <FilterValues>
                                  <FilterValue DataType="Integer">0</FilterValue>
                                </FilterValues>
                              </Filter>
                            </Filters>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Fields!provider_nm.Value</Value>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>1.51042in</Size>
                            <CellContents>
                              <Textbox Name="provider_nm">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!provider_nm.Value &amp; " (" &amp; iif(Fields!row_num.Value = 1,Variables!tot_cdc_hiv.Value, Fields!chrt_prov_total.Value) &amp; ")"</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>provider_nm</rd:DefaultName>
                                <ActionInfo>
                                  <Actions>
                                    <Action>
                                      <Drillthrough>
                                        <ReportName>SitePatientProfile</ReportName>
                                        <Parameters>
                                          <Parameter Name="COHORT_ID">
                                            <Value>=Fields!COHORT_ID.Value</Value>
                                          </Parameter>
                                          <Parameter Name="PROVIDER_CD">
                                            <Value>=Fields!provider_nm.Value</Value>
                                          </Parameter>
                                        </Parameters>
                                      </Drillthrough>
                                    </Action>
                                  </Actions>
                                </ActionInfo>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BottomBorder>
                                    <Color>=IIF(Fields!row_num.Value=14,"#1f4e78", "LightGrey")</Color>
                                    <Style>=IIF(Fields!row_num.Value = 14, "Solid","")</Style>
                                    <Width>=IIF(Fields!row_num.Value = 14, "3pt","")</Width>
                                  </BottomBorder>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>#f2f2f2</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                          <Visibility>
                            <Hidden>true</Hidden>
                            <ToggleItem>location_nm</ToggleItem>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                      <Visibility>
                        <Hidden>true</Hidden>
                        <ToggleItem>row_label</ToggleItem>
                      </Visibility>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <FixedRowHeaders>true</FixedRowHeaders>
            <DataSetName>TreatmentProfileBySexEthnicity</DataSetName>
            <PageBreak>
              <BreakLocation>Start</BreakLocation>
            </PageBreak>
            <Top>1.24876in</Top>
            <Left>0.01045in</Left>
            <Height>1.12in</Height>
            <Width>11.20295in</Width>
            <Style>
              <Border>
                <Color>#1f4e78</Color>
                <Style>None</Style>
                <Width>2pt</Width>
              </Border>
              <BottomBorder>
                <Style>Solid</Style>
              </BottomBorder>
            </Style>
          </Tablix>
          <Rectangle Name="Rectangle2">
            <ReportItems>
              <Chart Name="Chart2">
                <ChartCategoryHierarchy>
                  <ChartMembers>
                    <ChartMember>
                      <Group Name="Chart2_CategoryGroup">
                        <GroupExpressions>
                          <GroupExpression>=Fields!years_followup.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!cat_order.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <Label>=Fields!years_followup.Value</Label>
                    </ChartMember>
                  </ChartMembers>
                </ChartCategoryHierarchy>
                <ChartSeriesHierarchy>
                  <ChartMembers>
                    <ChartMember>
                      <Label>Patient Count</Label>
                    </ChartMember>
                  </ChartMembers>
                </ChartSeriesHierarchy>
                <ChartData>
                  <ChartSeriesCollection>
                    <ChartSeries Name="PatientCount">
                      <ChartDataPoints>
                        <ChartDataPoint>
                          <ChartDataPointValues>
                            <Y>=Sum(Fields!PatientCount.Value)</Y>
                          </ChartDataPointValues>
                          <ChartDataLabel>
                            <Style>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                            <Label>#VALY</Label>
                            <Visible>true</Visible>
                          </ChartDataLabel>
                          <Style>
                            <Color>#1f4e78</Color>
                          </Style>
                          <ChartMarker>
                            <Style />
                          </ChartMarker>
                          <DataElementOutput>Output</DataElementOutput>
                        </ChartDataPoint>
                      </ChartDataPoints>
                      <Style />
                      <ChartEmptyPoints>
                        <Style />
                        <ChartMarker>
                          <Style />
                        </ChartMarker>
                        <ChartDataLabel>
                          <Style />
                        </ChartDataLabel>
                      </ChartEmptyPoints>
                      <ChartItemInLegend>
                        <Hidden>true</Hidden>
                      </ChartItemInLegend>
                      <ValueAxisName>Primary</ValueAxisName>
                      <CategoryAxisName>Primary</CategoryAxisName>
                      <ChartSmartLabel>
                        <CalloutLineColor>Black</CalloutLineColor>
                        <MinMovingDistance>0pt</MinMovingDistance>
                      </ChartSmartLabel>
                    </ChartSeries>
                  </ChartSeriesCollection>
                </ChartData>
                <ChartAreas>
                  <ChartArea Name="Default">
                    <ChartCategoryAxes>
                      <ChartAxis Name="Primary">
                        <Style>
                          <FontSize>12pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                        <ChartAxisTitle>
                          <Caption>Years of Follow Up
													</Caption>
                          <Style>
                            <FontSize>12pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </ChartAxisTitle>
                        <ChartMajorGridLines>
                          <Enabled>False</Enabled>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                            </Border>
                          </Style>
                        </ChartMajorGridLines>
                        <ChartMinorGridLines>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                              <Style>Dotted</Style>
                            </Border>
                          </Style>
                        </ChartMinorGridLines>
                        <ChartMinorTickMarks>
                          <Length>0.5</Length>
                        </ChartMinorTickMarks>
                        <CrossAt>NaN</CrossAt>
                        <Minimum>NaN</Minimum>
                        <Maximum>NaN</Maximum>
                        <LabelsAutoFitDisabled>true</LabelsAutoFitDisabled>
                        <ChartAxisScaleBreak>
                          <Style />
                        </ChartAxisScaleBreak>
                      </ChartAxis>
                      <ChartAxis Name="Secondary">
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                        <ChartAxisTitle>
                          <Caption>Axis Title</Caption>
                          <Style>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </ChartAxisTitle>
                        <ChartMajorGridLines>
                          <Enabled>False</Enabled>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                            </Border>
                          </Style>
                        </ChartMajorGridLines>
                        <ChartMinorGridLines>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                              <Style>Dotted</Style>
                            </Border>
                          </Style>
                        </ChartMinorGridLines>
                        <ChartMinorTickMarks>
                          <Length>0.5</Length>
                        </ChartMinorTickMarks>
                        <CrossAt>NaN</CrossAt>
                        <Location>Opposite</Location>
                        <Minimum>NaN</Minimum>
                        <Maximum>NaN</Maximum>
                        <ChartAxisScaleBreak>
                          <Style />
                        </ChartAxisScaleBreak>
                      </ChartAxis>
                    </ChartCategoryAxes>
                    <ChartValueAxes>
                      <ChartAxis Name="Primary">
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                        <ChartAxisTitle>
                          <Caption># Patients</Caption>
                          <Style>
                            <FontSize>12pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </ChartAxisTitle>
                        <Interval>200</Interval>
                        <ChartMajorGridLines>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                          </Style>
                        </ChartMajorGridLines>
                        <ChartMinorGridLines>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                              <Style>Dotted</Style>
                            </Border>
                          </Style>
                        </ChartMinorGridLines>
                        <ChartMinorTickMarks>
                          <Length>0.5</Length>
                        </ChartMinorTickMarks>
                        <CrossAt>NaN</CrossAt>
                        <Minimum>NaN</Minimum>
                        <Maximum>NaN</Maximum>
                        <ChartAxisScaleBreak>
                          <Style />
                        </ChartAxisScaleBreak>
                      </ChartAxis>
                      <ChartAxis Name="Secondary">
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                        <ChartAxisTitle>
                          <Caption>Axis Title</Caption>
                          <Style>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </ChartAxisTitle>
                        <ChartMajorGridLines>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                            </Border>
                          </Style>
                        </ChartMajorGridLines>
                        <ChartMinorGridLines>
                          <Style>
                            <Border>
                              <Color>Gainsboro</Color>
                              <Style>Dotted</Style>
                            </Border>
                          </Style>
                        </ChartMinorGridLines>
                        <ChartMinorTickMarks>
                          <Length>0.5</Length>
                        </ChartMinorTickMarks>
                        <CrossAt>NaN</CrossAt>
                        <Location>Opposite</Location>
                        <Minimum>NaN</Minimum>
                        <Maximum>NaN</Maximum>
                        <ChartAxisScaleBreak>
                          <Style />
                        </ChartAxisScaleBreak>
                      </ChartAxis>
                    </ChartValueAxes>
                    <Style>
                      <BackgroundColor>#d9d9d9</BackgroundColor>
                      <BackgroundGradientType>None</BackgroundGradientType>
                    </Style>
                  </ChartArea>
                </ChartAreas>
                <ChartLegends>
                  <ChartLegend Name="Default">
                    <Style>
                      <BackgroundGradientType>None</BackgroundGradientType>
                      <FontSize>8pt</FontSize>
                    </Style>
                    <ChartLegendTitle>
                      <Caption />
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </ChartLegendTitle>
                    <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                    <ColumnSeparatorColor>Black</ColumnSeparatorColor>
                  </ChartLegend>
                </ChartLegends>
                <ChartTitles>
                  <ChartTitle Name="Default">
                    <Caption>="Site Years of Patient Follow-up" + vbcrlf + "(n=" &amp; FORMAT(Sum(Fields!NUMBER_OF_PTNTS_IN_FOLLOWUP.Value, "Followup_Summary"), "N0") &amp; " average=" &amp; FORMAT(Sum(Fields!AVERAGE_FOLLOWUP.Value, "Followup_Summary"),"N1") &amp; " years  max=" &amp; FORMAT(Sum(Fields!MAX_FOLLOWUP.Value, "Followup_Summary"),"N1") &amp; " years)"</Caption>
                    <Style>
                      <BackgroundGradientType>None</BackgroundGradientType>
                      <FontSize>12pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>General</TextAlign>
                      <VerticalAlign>Top</VerticalAlign>
                    </Style>
                  </ChartTitle>
                </ChartTitles>
                <Palette>BrightPastel</Palette>
                <ChartBorderSkin>
                  <Style>
                    <BackgroundColor>Gray</BackgroundColor>
                    <BackgroundGradientType>None</BackgroundGradientType>
                    <Color>White</Color>
                  </Style>
                </ChartBorderSkin>
                <ChartNoDataMessage Name="NoDataMessage">
                  <Caption>No Data Available</Caption>
                  <Style>
                    <BackgroundGradientType>None</BackgroundGradientType>
                    <TextAlign>General</TextAlign>
                    <VerticalAlign>Top</VerticalAlign>
                  </Style>
                </ChartNoDataMessage>
                <DataSetName>Followup</DataSetName>
                <Height>5.10069in</Height>
                <Width>11.20295in</Width>
                <Style>
                  <Border>
                    <Color>LightGrey</Color>
                    <Style>Solid</Style>
                  </Border>
                  <BackgroundColor>#d9d9d9</BackgroundColor>
                  <BackgroundGradientType>None</BackgroundGradientType>
                </Style>
              </Chart>
            </ReportItems>
            <PageBreak>
              <BreakLocation>Start</BreakLocation>
            </PageBreak>
            <KeepTogether>true</KeepTogether>
            <Top>2.8621in</Top>
            <Left>0.01045in</Left>
            <Height>5.28819in</Height>
            <Width>11.20295in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Rectangle>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.55in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.22in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox37</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox112">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox112</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox113">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>HIV RNA</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox113</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox115">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>CD4</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox115</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.2in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox66">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Share</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox66</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox74">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Suppr</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox74</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox78">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>VL</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox78</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox82">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>CD4</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox82</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox103">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>&lt;200</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox103</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox105">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>200-499</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox105</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox108">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>500+</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox108</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.2in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox39">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>#</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox39</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox122">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox122</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox123">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox123</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox124">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Median</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox124</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#29679f</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox125">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Avg</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox125</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox127">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox127</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox128">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox128</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox129">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox129</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>2pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=SUM(Fields!TOTAL.Value)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SHARE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(Fields!SORT_ORDER.Value = 1, "",iif(Sum(Fields!chrt_total.Value) = 0, 0, FormatPercent(sum(Fields!TOTAL.Value)/Sum(Fields!chrt_total.Value,"CATEGORY"),1)))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>SHARE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SUPPRESSED">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(IIF(Fields!SORT_ORDER.Value = 1,Sum(Fields!TOTAL_ACTIVE_CNT.Value), Sum(Fields!TOTAL.Value)) =0,"", FormatPercent(sum(Fields!SUPPRESSED.Value)/IIF(Fields!SORT_ORDER.Value = 1,Sum(Fields!TOTAL_ACTIVE_CNT.Value), Sum(Fields!TOTAL.Value)),1))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>SUPPRESSED</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="VL_AVG">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(inscope("LOCATION"), MIN(Fields!VL_MDN_LOCATION.Value), iif(inscope("PROVIDER"), Fields!VL_MDN_PROVIDER.Value, Fields!VL_MDN_COHORT.Value))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>VL_AVG</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CD4_AVG">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=FormatNumber(Avg(Fields!CD4_AVG.Value),0)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CD4_AVG</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CD4_LESSTHAN_2001">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(SUM(Fields!HAS_CD4_CNT.Value) = 0,"",FormatPercent(sum(Fields!CD4_LESSTHAN_200.Value)/SUM(Fields!HAS_CD4_CNT.Value),1))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CD4_LESSTHAN_2001</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CD4_200TO499">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(SUM(Fields!HAS_CD4_CNT.Value) = 0,"",FormatPercent(sum(Fields!CD4_200TO499.Value)/SUM(Fields!HAS_CD4_CNT.Value),1))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CD4_200TO499</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CD4_500PLUS">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(SUM(Fields!HAS_CD4_CNT.Value) = 0,"",FormatPercent(sum(Fields!CD4_500PLUS.Value)/SUM(Fields!HAS_CD4_CNT.Value),1))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0.0%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CD4_500PLUS</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.81468in</Size>
                    <CellContents>
                      <Textbox Name="Textbox85">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Treatment Profile </Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>by </Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Labs</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox85</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>Black</Color>
                            <Style>Solid</Style>
                            <Width>2pt</Width>
                          </BottomBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.02496in</Size>
                        <CellContents>
                          <Textbox Name="Textbox70">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox70</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.96331in</Size>
                            <CellContents>
                              <Textbox Name="Textbox64">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox64</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.02496in</Size>
                        <CellContents>
                          <Textbox Name="Textbox71">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>Location</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox71</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.96331in</Size>
                            <CellContents>
                              <Textbox Name="Textbox65">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>Provider</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox65</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <VerticalAlign>Middle</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.02496in</Size>
                        <CellContents>
                          <Textbox Name="Textbox72">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox72</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>None</Style>
                              </Border>
                              <BottomBorder>
                                <Color>Black</Color>
                                <Style>Solid</Style>
                                <Width>2pt</Width>
                              </BottomBorder>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>1.96331in</Size>
                            <CellContents>
                              <Textbox Name="Textbox67">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox67</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>None</Style>
                                  </Border>
                                  <BottomBorder>
                                    <Color>Black</Color>
                                    <Style>Solid</Style>
                                    <Width>2pt</Width>
                                  </BottomBorder>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <Group Name="CATEGORY">
                    <GroupExpressions>
                      <GroupExpression>=Fields!CATEGORY.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!SORT_ORDER.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.81468in</Size>
                        <CellContents>
                          <Textbox Name="CATEGORY">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=IIF(Fields!CATEGORY_LEVEL.Value = 1, Fields!COHORT_NAME.Value &amp; " Patients", Fields!CATEGORY.Value)  &amp;
																			IIF(inscope("LOCATION"), "(" &amp; SUM(Fields!TOTAL.Value,"CATEGORY") &amp; ")","")
																		</Value>
                                    <MarkupType>HTML</MarkupType>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>=iif(Right(Me.Value,8) = "Patients", "Bold", "Normal")</FontWeight>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>CATEGORY</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                              <PaddingLeft>=IIF(Fields!CATEGORY_LEVEL.Value=2, "20pt", IIF(Fields!CATEGORY_LEVEL.Value=3,"40pt","2pt"))</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="LOCATION">
                            <GroupExpressions>
                              <GroupExpression>=Fields!LOCATION.Value</GroupExpression>
                            </GroupExpressions>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Fields!LOCATION.Value</Value>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>2.02496in</Size>
                            <CellContents>
                              <Textbox Name="LOCATION">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!LOCATION.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>LOCATION</rd:DefaultName>
                                <ActionInfo>
                                  <Actions>
                                    <Action>
                                      <Drillthrough>
                                        <ReportName>SitePatientProfile</ReportName>
                                        <Parameters>
                                          <Parameter Name="COHORT_ID">
                                            <Value>=Fields!COHORT_ID.Value</Value>
                                          </Parameter>
                                          <Parameter Name="LOCATION_CD">
                                            <Value>=Fields!LOCATION.Value</Value>
                                          </Parameter>
                                        </Parameters>
                                      </Drillthrough>
                                    </Action>
                                  </Actions>
                                </ActionInfo>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <Group Name="PROVIDER">
                                <GroupExpressions>
                                  <GroupExpression>=Fields!PROVIDER.Value</GroupExpression>
                                </GroupExpressions>
                              </Group>
                              <SortExpressions>
                                <SortExpression>
                                  <Value>=Fields!PROVIDER.Value</Value>
                                </SortExpression>
                              </SortExpressions>
                              <TablixHeader>
                                <Size>1.96331in</Size>
                                <CellContents>
                                  <Textbox Name="PROVIDER">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>=Fields!PROVIDER.Value</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style />
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>PROVIDER</rd:DefaultName>
                                    <ActionInfo>
                                      <Actions>
                                        <Action>
                                          <Drillthrough>
                                            <ReportName>SitePatientProfile</ReportName>
                                            <Parameters>
                                              <Parameter Name="COHORT_ID">
                                                <Value>=Fields!COHORT_ID.Value</Value>
                                              </Parameter>
                                              <Parameter Name="PROVIDER_CD">
                                                <Value>=Fields!PROVIDER.Value</Value>
                                              </Parameter>
                                            </Parameters>
                                          </Drillthrough>
                                        </Action>
                                      </Actions>
                                    </ActionInfo>
                                    <Style>
                                      <Border>
                                        <Color>LightGrey</Color>
                                        <Style>Solid</Style>
                                      </Border>
                                      <BackgroundColor>=IIf(RunningValue(Fields!CATEGORY.Value, CountDistinct, "Tablix1") Mod 2 = 0, "Silver", "White")</BackgroundColor>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember />
                              </TablixMembers>
                              <Visibility>
                                <Hidden>true</Hidden>
                                <ToggleItem>LOCATION</ToggleItem>
                              </Visibility>
                            </TablixMember>
                          </TablixMembers>
                          <Visibility>
                            <Hidden>true</Hidden>
                            <ToggleItem>CATEGORY</ToggleItem>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <FixedRowHeaders>true</FixedRowHeaders>
            <DataSetName>TreatmentProfileByLabs2</DataSetName>
            <SortExpressions>
              <SortExpression>
                <Value>=Fields!SORT_ORDER.Value</Value>
                <Direction>Descending</Direction>
              </SortExpression>
            </SortExpressions>
            <Top>0.06126in</Top>
            <Left>0.01045in</Left>
            <Height>0.87in</Height>
            <Width>11.20295in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Color>#1f4e78</Color>
                <Style>None</Style>
                <Width>2pt</Width>
              </Border>
              <BottomBorder>
                <Style>Solid</Style>
              </BottomBorder>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>8.19196in</Height>
        <Style />
      </Body>
      <Width>11.66132in</Width>
      <Page>
        <PageHeader>
          <Height>1.33779in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox226">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Format(Globals!ExecutionTime, "dd-MMM-yyyy")</Value>
                      <Style>
                        <FontSize>12pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox226</rd:DefaultName>
              <Top>0.12375in</Top>
              <Left>9.0134in</Left>
              <Height>0.25in</Height>
              <Width>2.05002in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Site Patient Profile Prepared For " + First(Fields!site_nm.Value, "SiteName")</Value>
                      <Style>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox2</rd:DefaultName>
              <Top>0.12375in</Top>
              <Left>0.12381in</Left>
              <Height>0.25in</Height>
              <Width>6.36586in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Data Valid Through: " + Format(First(Fields!EXTRACT_DT.Value, "OperaSiteExtractDate"), "dd-MMM-yyyy")</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox4</rd:DefaultName>
              <Top>0.44319in</Top>
              <Left>0.12381in</Left>
              <Height>0.25in</Height>
              <Width>6.35541in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox120">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIF(Parameters!LOCATION_CD.Value = NOTHING,"",Parameters!LOCATION_CD.Value)</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox120</rd:DefaultName>
              <Top>0.81001in</Top>
              <Height>0.25in</Height>
              <Width>11.2134in</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox126">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIF(Parameters!PROVIDER_CD.Value = Nothing, "", Parameters!PROVIDER_CD.Value)</Value>
                      <Style>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox120</rd:DefaultName>
              <Top>1.08779in</Top>
              <Left>0.01045in</Left>
              <Height>0.25in</Height>
              <Width>11.20295in</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>0.45833in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox227">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Copyright " + CStr(Year(Globals!ExecutionTime)) + " Epividian, Inc. All Rights Reserved."</Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox227</rd:DefaultName>
              <Top>0.14459in</Top>
              <Left>0.29048in</Left>
              <Height>0.25in</Height>
              <Width>4.42833in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageWidth>15in</PageWidth>
        <InteractiveHeight>0in</InteractiveHeight>
        <InteractiveWidth>0in</InteractiveWidth>
        <LeftMargin>1.5in</LeftMargin>
        <RightMargin>1.5in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="COHORT_ID">
      <DataType>Integer</DataType>
      <Prompt>COHORT_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="PROVIDER_CD">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PROVIDER CD</Prompt>
    </ReportParameter>
    <ReportParameter Name="LOCATION_CD">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>LOCATION CD</Prompt>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>PROVIDER_CD</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>LOCATION_CD</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Code>Dim values As New System.Collections.Generic.List(Of Integer)
		Dim valueCounts As New System.Collections.Generic.Dictionary(Of Integer, Integer)

		Function AddValue(newValue As Integer) As Integer
		values.Add(newValue)
		AddValue = newValue
		If Not valueCounts.ContainsKey(newValue) Then
		valueCounts.item(newValue) = 1
		Else
		valueCounts.item(newValue) += 1
		End If
		End Function

		Function GetMedian() As Double
		Dim count As Integer = values.Count
		If count = 0 Then
		Return 0
		Else
		values.Sort()
		If count Mod 2 = 1 Then
		Return values(CInt((count / 2) - 0.5))
		Else
		Dim index1 As Integer = count \ 2
		Dim index2 As Integer = index1 - 1

		Dim value1, value2 As Integer
		value1 = values(index1)
		value2 = values(index2)

		Return (value1 + value2) / 2
		End If
		End If
		End Function

		Function GetMode() As String
		Dim max As Integer = 0
		For Each v As Integer In valueCounts.Values
		If v &gt; max Then
		max = v
		End If
		Next v

		Dim maxCount As Integer = 0
		Dim retValue As String = ""
		For Each vcKvp As System.Collections.Generic.KeyValuePair(Of Integer, Integer) In valueCounts
		If vcKvp.Value = max Then
		maxCount += 1
		If Not String.IsNullOrEmpty(retValue) Then
		retValue &amp;= ", "
		End If
		retValue &amp;= vcKvp.Key
		End If
		Next vcKvp

		If maxCount = valueCounts.Count Then
		Return "N/A"
		End If

		Return retValue
		End Function</Code>
  <Variables>
    <Variable Name="tot_cdc_hiv">
      <Value>1155623</Value>
    </Variable>
  </Variables>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>f5a1ca88-db48-4de8-9d35-f08ede8c624d</rd:ReportID>
</Report>