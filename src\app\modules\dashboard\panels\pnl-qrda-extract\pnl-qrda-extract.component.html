<div class="searchCriteriaDiv">
  <form>
      <!-- Report Format Row -->
      <div class="field-row">
        <label class="field-label" for="rptpanReportingFormat">Report Format*:</label>
        <select mat-form-field class="form-select form-select-sm format-select"
        name="rptpanReportingFormat" id="rptpanReportingFormat" [formControl]="rptpanReportFormat" title="Select report format">
          <option value="2">QRDA I</option>
          <option value="1">QRDA III</option>
        </select>
      </div>

      <!-- Reporting Year Row -->
      <div class="field-row">
        <label class="field-label" for="rptpanReportingYear">Reporting Year*:</label>
        <select mat-form-field class="form-select form-select-sm year-select"
        name="rptpanReportingYear" id="rptpanReportingYear" [formControl]="rptpanReportingYear" title="Select reporting year">
          <option *ngFor="let option of rptReportYearData" [value]="option.key">{{option.value}}</option>
        </select>
      </div>

      <!-- Location Row -->
      <div class="field-row">
        <label class="field-label">Location:</label>
        <input mat-form-field class="form-select form-select-sm location-input" (change)="readyToRun()"
        [matAutocomplete]="autoLocation" name="rptpanLocation" [formControl]="locationSearch"
        placeholder="Select or search for a location">
        <mat-autocomplete #autoLocation="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChange($event)">
          <mat-option [value]="optionAll">{{optionAll.value}}</mat-option>
          <mat-option *ngFor="let locData of rptLocationData" [value]="locData">{{locData.value}}</mat-option>
        </mat-autocomplete>
      </div>

      <!-- MRN Row -->
      <div class="field-row">
        <label class="field-label" for="rptpanMrn">MRN:</label>
        <input mat-form-field class="form-input form-select-sm mrn-input"
        name="rptpanMrn" id="rptpanMrn" [formControl]="rptpanMrn" placeholder="Enter MRN (optional)" title="Enter patient MRN">
      </div>

      <!-- Run Button Row -->
      <div class="button-row">
        <button type="button" (click)="isSSRSReport ? panelService.InitBoldReport() : onRunClick()"
        id="reportViewer_Control_viewReportClick" [disabled]="isRunDisabled"
        aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">
            Run
        </button>
      </div>
  </form>
</div>
