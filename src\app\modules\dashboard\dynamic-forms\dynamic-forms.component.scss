.highlighted-control {
    background-color: #FFFF99;
    border: 1px solid #FFA500;
}

  .highlighted-checklist-item {
    background-color: #FFFF99;
    font-weight: bold;
  }

  .titleContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-items: flex-start;
    width: 100%;
}

.leftTitle {
    display: flex;
    align-items: flex-start;
    margin-left: 1em;
    flex: 1; /* Ensures the formFiller stays on the left */
    vertical-align: top;
}

.formFiller {
    font-size: 2em;
    font-family: Museo500-Regular;
    color: #2f80b6;
}

.centerTitle {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 300px;
    flex: 2; /* Takes the rest of the space */
}

.formName {
    margin: 0 auto;
    font-size: 2em;
    font-family: Museo500-Regular;
    color: #2f80b6;
    text-align: center;
}

.description {
    margin-top: 0.5em;
    font-size: 1em;
    color: #666;
    text-align: center;
}


.dynamicFormTitle {
    color: #2f80b6;
    margin-left: 1em;
    margin-right: 1em;
    font-family: Museo500-Regular;
}

.pageWrapper {
    display: flex;//    justify-content: space-between;
    //align-items: stretch;
    height: 86.0vh;
    //height: 100vh;
    overflow-y: auto;
}


.leftColumn {
    position: sticky;
    top: 0px;
    align-self: flex-start;
    display: flex;
    flex-direction: column; /* Stacks the progress bar and info panel vertically */
    width: 22%; /* Adjust this width as necessary */
    min-width: 335px;
    background-color: #eff1f6 !important;
}


.formWrapper {
    background-color: #eff1f6 !important;
    margin: 0 auto;
    display: flex-stretch;
    flex-direction: column;
    margin-right: 5px;
    //width: 0%;
    max-width: 70vw;

}

.progressWrapper {
    width: 95%;
    margin: 0 0 20px 0;
    padding-left: 0;
    border-radius: 10px;
    background-color: #eff1f6 !important;
}

.stepper-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0;
    background-color: #eff1f6 !important;
    padding: 0;
  }

  /* Common styles for all steppers */
  :host ::ng-deep .mat-horizontal-stepper-header-container {
    justify-content: flex-start; /* Align stepper headers to the left */
    width: 100%;
    margin: 0;
    background-color: #eff1f6 !important;
    overflow: hidden; /* Ensure content doesn't overflow the border */
    box-sizing: border-box; /* Include border in the element's dimensions */
    padding-right: 0px;
  }

  /* Fix for horizontal lines alignment */
  :host ::ng-deep .mat-stepper-horizontal-line {
    flex: none !important;
  }

  /*
  :host ::ng-deep .mat-stepper-horizontal-line {
    min-width: 15px !important;
    border-top: 1px solid #b8b8b8;
    margin: 0;
    flex:none !important;
    position: relative;
  }
  */

  /* Ensure the stepper is properly displayed */
  :host ::ng-deep .mat-horizontal-stepper {
    display: block;
    width: 100%;
  }

  /* Ensure equal width for all step headers in normal mode */
  :host:not(.history-mode) ::ng-deep .mat-horizontal-stepper-header {
    flex-basis: 32% !important; /* Each step takes up one-third of the width in normal mode */
    //padding: 0px 0px !important; /* Removed padding */
    margin: 0px !important; /* No margin */
    background-color: #eff1f6 !important;
    height: 35px !important; /* Reduced height */
  }

  /* Style for the stepper header */
  :host ::ng-deep .mat-step-header {
    background-color: #eff1f6 !important;
  }

  /* Adjust stepper in history mode */
  .history-mode-stepper {
    width: 100%;
    background-color: #eff1f6 !important;
    margin-left: 25%;
    padding-right: 50%;
  }

  /* Make the step headers wider in history mode */
  .history-mode-stepper ::ng-deep .mat-horizontal-stepper-header {
    flex-basis: 50% !important; /* Each step takes up half the width */
    padding: 0 8px !important; /* Removed padding */
    background-color: #eff1f6 !important;
    height: 35px !important; /* Reduced height */
  }


  /* Adjust the step icons in history mode */
  .history-mode-stepper ::ng-deep .mat-step-icon {
    margin-right: 2px; /* Further reduced spacing between icon and label */
  }

  /* Adjust the step icons in normal mode */
  :host ::ng-deep .mat-step-icon {
    margin-right: 2px; /* Reduced spacing between icon and label */
  }

  /* Disable forward navigation clicks */
  :host ::ng-deep .mat-horizontal-stepper-header-container .mat-horizontal-stepper-header {
    &.cdk-program-focused {
      background-color: transparent;
    }
  }

  /* Custom class to disable pointer events on forward steps */
  :host ::ng-deep .disable-forward-step {
    pointer-events: none;
    opacity: 0.7;
    cursor: not-allowed;
  }

  .mat-step-header {
    background-color: transparent;
    border: 1px solid lightgray;
   // padding: 5px 10px;
  }

  :host ::ng-deep .mat-horizontal-stepper-header-container {
    --mat-stepper-container-color: #eff1f6; /* Example color */
    background-color: var(--mat-stepper-container-color);
    border: #b8b8b8 2px solid;
    border-radius: 7px;
    height: 40px;
  }

  :host ::ng-deep .mat-step-icon {
    background-color: lightblue; /* Default circle background color */
    color: #fff; /* Default text color */
  }

  :host ::ng-deep .mat-step-icon-selected {
    background-color: #1976d2; /* Selected circle background color */
    color: #fff; /* Text color when selected */
  }

  ::ng-deep mat-step-header.mat-horizontal-stepper-header
  {
    padding: 0px 5px !important;
  }

  ::ng-deep .mat-horizontal-content-container {
    padding: 0px !important;
  }

  .step-label {
    font-weight: bold;
    color: #555;
  }

  .mat-step-header.mat-step-header-selected {
    background-color: #1976d2;
    color: white;
  }

.accordionHeader {
    background-color: #66a9d7 !important;
    color: white !important;
}


.mat-expansion-panel-header-title
{
    color: white !important;
}

.mat-expansion-panel-header:focus,
 .accordionHeader:focus,
 .at-expansion-panel-header:focus
{
    background-color: #2f80b6 !important;
}

.fieldRow {
    display: flex;
    padding-left: 1em;
}

.fieldWrapper {
    margin: 10px;
}

.field {
    display: flex;
    flex-direction: column;
}

.ValidationError {
    color: red;
}

.error {
    background-color: red;
}

.pass {
    background-color: green;
}

.buttonWrapper {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    background-color: #E8E8E8;
    height: 3rem;
    padding-right: 1rem;
    padding-left: 1rem;
    align-items: center;
}

.formButton {
    color: #fff;
    background-color: #005f9f !important;
    z-index: 1;
    height: 35px;
    margin: 10px;
    width: 5rem;
    border-radius: 12px !important;
}

.textareaInput {
  height: 125px;
  width: 54vw;
  resize: none;
}

.dateInput {
    width: 150px;
}

.checkboxInput {
    justify-content: space-evenly;
    margin-left: 5px;
}

.checkbox-label {
 margin-left: 5px;
}


.numberInput {
    width: 5rem;
}

.textInput {
    width: 225px;
}

.selectInput {
    width: fit-content;
}

.infoHeaderBg
{
    background-color: #E8E8E8 !important;
    color: black !important;
}

.infoHeaderBg:focus
{
    background-color: white !important;
    color: black !important;
}

.infoTitle
{
    color: black !important;
}

.infoPanel {
    display: flex;
    flex-direction: column;
    background-color: white;
    max-height: 78vh;
    width: 98%;
    overflow-y: auto;
    border-radius: 10px;
}

.infoPanelTitle{
        text-align: center;
        font-size: 1.1rem;
        padding-bottom: 5px;
        padding-top: 5px;
        font-family: Museo500-Regular;
}

.infoItem{
    color:black;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 8px;
}

.item-wrapper {

    align-items: center; /* Center items vertically */
    flex-wrap: nowrap; /* Prevent wrapping */
    width: 50%; /* Ensure it takes full width */

  }

.item{
    display: flex;
    white-space: nowrap;
    height: 25px;
}

.itemName-Limiter {
    display: inline-block; /* Allows width to be respected for text */
    max-width: 82%; /* Adjust width based on your requirement */
    white-space: nowrap; /* Prevents text from wrapping to the next line */
    overflow: hidden; /* Hides the overflowing text */
    text-overflow: ellipsis; /* Adds the ellipsis (...) to truncated text */
    vertical-align: middle; /* Aligns the text in the middle vertically (optional) */
    margin-top: 2px;
  }

.right-item {
    margin-left: auto; /* Pushes this item to the right side */
    flex-wrap: nowrap;
  }


.infoLabel{
    height: 30px;
    padding-left: 10px;
    padding-top: 5px;
    font-weight: bold;
}

.infoHR {
    margin: 5px;
}


.mat-step-header {
    padding: 8px;
    border-radius: 20px;
    background-color: #e0e0e0;
}

.mat-step-header.mat-active {
    background-color: #1976d2;
    color: white;
}

.mat-step-icon {
    background-color: #1976d2;
    color: white;
}

.mat-stepper-horizontal-line {
    border-top: 2px solid #1976d2;
}

.blueText
{
    color: #66A9D7;
}

.badge {
    background-color: #ff4081;
    color: white;
    border-radius: 12px;
    padding: 3px 10px;
    font-size: 12px;
    margin-left: 10px;
    vertical-align: middle;
}

.checkIcon {
    color: green;
    font-size: 18px;
    margin-left: 10px;
}

.infoHeaderBg {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.infoTitle {
    font-weight: bold;
}

.blueText {
    color: #66A9D7;
}

.downArrow {
    display: inline-block;
    transform: rotate(90deg);
    font-size: 16px;
}

.focusButton {
    background-color: #0071bc !important;
    color: white !important;
    width: auto;
    padding-left: 15px;
    padding-right: 15px;
    float: right;
    border-radius: 12px;
}

.input-row {
    display: flex;
    align-items: flex-start; /* Aligns the input (checkbox/radio) to the top of the left side */
  }

  .input-row input {
    margin-right: 8px; /* Adds space between the input and label */
    vertical-align: top; /* Ensures the input aligns with the top of the label */
    margin-top: 2px;
  }

  .form-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 24px;
    background-color: #f9f9f9;
    border-radius: 8px;
    width: 100%;
    margin: 0 auto;
    height: 82vh;
  }

  .status-icon {
    font-size: 72px;
    color: #1976d2; /* Blue color for the check icon */
    margin-bottom: 16px;
  }

  .check-icon {
    font-size: 5rem;
    height: 75px;
    width: 75px;
  }

  .status-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 8px 0;
  }

  .status-subtitle {
    font-size: 1rem;
    color: #888;
    margin-bottom: 24px;
  }

  .button-group {
    display: flex;
    gap: 16px;
  }

  .view-form-button {
    border: 1px solid #888;
    color: #888;
    height: 35px;
    margin: 10px;
    width: 200px;
    border-radius: 12px !important;
  }

  .done-button {
    color: white;
  }

  .rightContent {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.pdf-viewer-container {
    display: flex;
    flex-direction: column;
    height: 100%; /* Full height of the parent */
    //height: 100vh;
    width: 100%;
    padding: 0;
}

.pdf-frame-wrapper {
    flex-grow: 1; /* Expand to take up remaining space */
    overflow: hidden;
    border-radius: 8px;
    background-color: #f9f9f9;
    position: relative; /* For absolute positioning of loading indicator */
}

.pdf-frame {
    border: none; /* Removes border from iframe */
    width: 100%;
    height: 70vh;
}

/* PDF Loading Indicator */
.pdf-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #2f80b6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.submission-status-card {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    font-family: Arial, sans-serif;
    color: #333;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .status-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .card-status-icon {
    width: 10px;
    height: 10px;
    background-color: #1976d2; /* Blue color for the status dot */
    border-radius: 50%;
    margin-right: 8px;
  }

  .status-text {
    font-weight: bold;
    color: #1976d2;
  }

  .status-details p {
    margin: 4px 0;
    font-size: 0.9em;
    color: #555;
  }

.tableWrapper{
  max-height: 400px;
  overflow-y: auto;
  padding: 2px;
  border: 1px solid;
}
.tableWrapper th{
  background-color: #eff1f6;
}
