﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>ea9975c1-2d91-4661-9d42-1976f6249df1</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="visit_dtl">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@userId">
            <Value>=Parameters!userId.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@cohortCd">
            <Value>=Parameters!COHORT_ID.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
          <QueryParameter Name="@REPORTING_PERIOD">
            <Value>=Parameters!reportingPeriod.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@alertLvl">
            <Value>=Parameters!alertLvl.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT
      PROVIDER_NM,
	  LOCATION_NM,
	  APPOINTMENT_TYPE,
	  ISNULL(SCHEDULE_TM, '1900-01-01') AS SCHEDULE_TM,
	  SCHEDULE_STATUS,
	  ALERT_LVL	  
FROM  REPORT.[GET_VISIT_DETAIL](@userId, @cohortCd, @providerCd,@locationCd, @REPORTING_PERIOD)	   
WHERE        (CASE WHEN @alertLvl IS NULL THEN ALERT_LVL WHEN @alertLvl = '' OR
                         @alertLvl = 0 THEN 0 ELSE 1 END = ALERT_LVL)</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="PROVIDER_NM">
          <DataField>PROVIDER_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LOCATION_NM">
          <DataField>LOCATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="APPOINTMENT_TYPE">
          <DataField>APPOINTMENT_TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SCHEDULE_TM">
          <DataField>SCHEDULE_TM</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SCHEDULE_STATUS">
          <DataField>SCHEDULE_STATUS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ALERT_LVL">
          <DataField>ALERT_LVL</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="COUNT">
          <Value>=1</Value>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText> SELECT EXTRACT_DT FROM CLEAN.SITE
        </CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="table1">
            <TablixCorner>
              <TablixCornerRows>
                <TablixCornerRow>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox39">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox39</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BackgroundColor>Lavender</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox33">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox33</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BackgroundColor>Lavender</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                      <ColSpan>2</ColSpan>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell />
                </TablixCornerRow>
                <TablixCornerRow>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox42">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox42</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BackgroundColor>#0b6c9f</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox41">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Location</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox40</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BackgroundColor>#0b6c9f</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox40">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Provider</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox40</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#0b6c9f</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                </TablixCornerRow>
                <TablixCornerRow>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox43">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox43</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>Black</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox57">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox57</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>Black</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                  <TablixCornerCell>
                    <CellContents>
                      <Textbox Name="Textbox58">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox58</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>Black</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixCornerCell>
                </TablixCornerRow>
              </TablixCornerRows>
            </TablixCorner>
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>3.24333in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.77751in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>3.10833in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Chart Name="Chart3">
                          <ChartCategoryHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Group Name="Chart3_CategoryGroup">
                                  <GroupExpressions>
                                    <GroupExpression>=Fields!SCHEDULE_STATUS.Value</GroupExpression>
                                  </GroupExpressions>
                                </Group>
                                <SortExpressions>
                                  <SortExpression>
                                    <Value>=Fields!SCHEDULE_STATUS.Value</Value>
                                  </SortExpression>
                                </SortExpressions>
                                <Label>=Fields!SCHEDULE_STATUS.Value</Label>
                              </ChartMember>
                            </ChartMembers>
                          </ChartCategoryHierarchy>
                          <ChartSeriesHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Label>ALERT LVL</Label>
                              </ChartMember>
                            </ChartMembers>
                          </ChartSeriesHierarchy>
                          <ChartData>
                            <ChartSeriesCollection>
                              <ChartSeries Name="Series">
                                <ChartDataPoints>
                                  <ChartDataPoint>
                                    <ChartDataPointValues>
                                      <Y>=Count(Fields!ALERT_LVL.Value)</Y>
                                    </ChartDataPointValues>
                                    <ChartDataLabel>
                                      <Style />
                                    </ChartDataLabel>
                                    <ToolTip>=Count(Fields!ALERT_LVL.Value) &amp; " (" &amp; Fields!SCHEDULE_STATUS.Value &amp; ")"</ToolTip>
                                    <ActionInfo>
                                      <Actions>
                                        <Action>
                                          <Drillthrough>
                                            <ReportName>Visits_Detail</ReportName>
                                            <Parameters>
                                              <Parameter Name="cohortCd">
                                                <Value>=Parameters!COHORT_ID.Value</Value>
                                              </Parameter>
                                              <Parameter Name="hhsAlertLvl">
                                                <Value>=Parameters!alertLvl.Value</Value>
                                              </Parameter>
                                              <Parameter Name="alertLvl">
                                                <Value>=iif(Fields!ALERT_LVL.Value = "True",1,0)</Value>
                                              </Parameter>
                                              <Parameter Name="scheduleStatus">
                                                <Value>=Fields!SCHEDULE_STATUS.Value</Value>
                                              </Parameter>
                                              <Parameter Name="providerCd">
                                                <Value>=iif(inscope("PROVIDER_ID"),Fields!PROVIDER_NM.Value,Parameters!providerCd.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="locationCd">
                                                <Value>=iif(inscope("LOCATION_ID"),Fields!LOCATION_NM.Value,Parameters!locationCd.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="totPatients">
                                                <Value>=Count(Fields!ALERT_LVL.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="guidelineDesc">
                                                <Value>=Parameters!guidelineDesc.Value</Value>
                                              </Parameter>
                                              <Parameter Name="userId">
                                                <Value>=Parameters!userId.Value</Value>
                                              </Parameter>
                                              <Parameter Name="cohortNm">
                                                <Value>=Parameters!cohortNm.Value</Value>
                                              </Parameter>
                                              <Parameter Name="reportingPeriod">
                                                <Value>=Parameters!reportingPeriod.Value</Value>
                                              </Parameter>
                                            </Parameters>
                                          </Drillthrough>
                                        </Action>
                                      </Actions>
                                    </ActionInfo>
                                    <Style>
                                      <Border>
                                        <Color>White</Color>
                                      </Border>
                                    </Style>
                                    <ChartMarker>
                                      <Style />
                                    </ChartMarker>
                                    <DataElementOutput>Output</DataElementOutput>
                                  </ChartDataPoint>
                                </ChartDataPoints>
                                <Type>Shape</Type>
                                <Subtype>ExplodedPie</Subtype>
                                <Style />
                                <ChartEmptyPoints>
                                  <Style />
                                  <ChartMarker>
                                    <Style />
                                  </ChartMarker>
                                  <ChartDataLabel>
                                    <Style />
                                  </ChartDataLabel>
                                </ChartEmptyPoints>
                                <ValueAxisName>Primary</ValueAxisName>
                                <CategoryAxisName>Primary</CategoryAxisName>
                                <ChartSmartLabel>
                                  <CalloutLineColor>Black</CalloutLineColor>
                                  <MinMovingDistance>0pt</MinMovingDistance>
                                </ChartSmartLabel>
                              </ChartSeries>
                            </ChartSeriesCollection>
                          </ChartData>
                          <ChartAreas>
                            <ChartArea Name="Default">
                              <ChartCategoryAxes>
                                <ChartAxis Name="Primary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartCategoryAxes>
                              <ChartValueAxes>
                                <ChartAxis Name="Primary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartValueAxes>
                              <Style>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                              <ChartElementPosition>
                                <Height>100</Height>
                                <Width>100</Width>
                              </ChartElementPosition>
                              <ChartInnerPlotPosition>
                                <Top>13</Top>
                                <Left>20</Left>
                                <Height>60</Height>
                                <Width>60</Width>
                              </ChartInnerPlotPosition>
                            </ChartArea>
                          </ChartAreas>
                          <ChartLegends>
                            <ChartLegend Name="Default">
                              <Style>
                                <BackgroundGradientType>None</BackgroundGradientType>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>7pt</FontSize>
                                <FontWeight>SemiBold</FontWeight>
                                <Color>#0b6c9f</Color>
                              </Style>
                              <Position>BottomCenter</Position>
                              <Layout>WideTable</Layout>
                              <DockOutsideChartArea>true</DockOutsideChartArea>
                              <ChartElementPosition>
                                <Top>100</Top>
                                <Height>20</Height>
                                <Width>100</Width>
                              </ChartElementPosition>
                              <ChartLegendTitle>
                                <Caption />
                                <Style>
                                  <FontSize>8pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </ChartLegendTitle>
                              <AutoFitTextDisabled>true</AutoFitTextDisabled>
                              <MinFontSize>6pt</MinFontSize>
                              <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                              <ColumnSeparatorColor>Black</ColumnSeparatorColor>
                              <MaxAutoSize>0</MaxAutoSize>
                              <TextWrapThreshold>95</TextWrapThreshold>
                            </ChartLegend>
                          </ChartLegends>
                          <ChartTitles>
                            <ChartTitle Name="Title1">
                              <Caption>=Microsoft.VisualBasic.Interaction.iif(Parameters!alertLvl.Value = 0, "Missed Schedule Statuses", "Schedule Statuses")</Caption>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <BackgroundGradientType>None</BackgroundGradientType>
                                <FontFamily>Calibri Light</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>SemiBold</FontWeight>
                                <TextAlign>General</TextAlign>
                                <VerticalAlign>Top</VerticalAlign>
                                <Color>Purple</Color>
                              </Style>
                              <ChartElementPosition>
                                <Top>0.5</Top>
                                <Left>3</Left>
                                <Height>7.165</Height>
                                <Width>94</Width>
                              </ChartElementPosition>
                            </ChartTitle>
                          </ChartTitles>
                          <Palette>Pastel</Palette>
                          <ChartCustomPaletteColors>
                            <ChartCustomPaletteColor>="#3D82AB"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#EE5921"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#484848"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#3186AD"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#96C950"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#F3F6FC"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#A1C4D8"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#5C9DC0"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#BEBEBE"</ChartCustomPaletteColor>
                          </ChartCustomPaletteColors>
                          <ChartBorderSkin>
                            <Style>
                              <BackgroundColor>Gray</BackgroundColor>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <Color>White</Color>
                            </Style>
                          </ChartBorderSkin>
                          <ChartNoDataMessage Name="NoDataMessage">
                            <Caption>No Data Available</Caption>
                            <Style>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <TextAlign>General</TextAlign>
                              <VerticalAlign>Top</VerticalAlign>
                            </Style>
                          </ChartNoDataMessage>
                          <DataSetName>visit_dtl</DataSetName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>White</BackgroundColor>
                            <BackgroundGradientType>None</BackgroundGradientType>
                          </Style>
                        </Chart>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Chart Name="Chart2">
                          <ChartCategoryHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Group Name="Chart2_CategoryGroup">
                                  <GroupExpressions>
                                    <GroupExpression>=Fields!APPOINTMENT_TYPE.Value</GroupExpression>
                                  </GroupExpressions>
                                  <DomainScope>ALL</DomainScope>
                                </Group>
                                <SortExpressions>
                                  <SortExpression>
                                    <Value>=Fields!APPOINTMENT_TYPE.Value</Value>
                                  </SortExpression>
                                </SortExpressions>
                                <Label>=Fields!APPOINTMENT_TYPE.Value</Label>
                              </ChartMember>
                            </ChartMembers>
                          </ChartCategoryHierarchy>
                          <ChartSeriesHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Label>ALERT LVL</Label>
                              </ChartMember>
                            </ChartMembers>
                          </ChartSeriesHierarchy>
                          <ChartData>
                            <ChartSeriesCollection>
                              <ChartSeries Name="Series">
                                <ChartDataPoints>
                                  <ChartDataPoint>
                                    <ChartDataPointValues>
                                      <Y>=COUNT(Fields!ALERT_LVL.Value)</Y>
                                    </ChartDataPointValues>
                                    <ChartDataLabel>
                                      <Style />
                                    </ChartDataLabel>
                                    <ToolTip>=Count(Fields!ALERT_LVL.Value) &amp; " (" &amp; Fields!APPOINTMENT_TYPE.Value &amp; ")"</ToolTip>
                                    <ActionInfo>
                                      <Actions>
                                        <Action>
                                          <Drillthrough>
                                            <ReportName>Visits_Detail</ReportName>
                                            <Parameters>
                                              <Parameter Name="cohortCd">
                                                <Value>=Parameters!COHORT_ID.Value</Value>
                                              </Parameter>
                                              <Parameter Name="hhsAlertLvl">
                                                <Value>=Parameters!alertLvl.Value</Value>
                                              </Parameter>
                                              <Parameter Name="alertLvl">
                                                <Value>=iif(Fields!ALERT_LVL.Value = "True",1,0)</Value>
                                              </Parameter>
                                              <Parameter Name="appointmentType">
                                                <Value>=Fields!APPOINTMENT_TYPE.Value</Value>
                                              </Parameter>
                                              <Parameter Name="providerCd">
                                                <Value>=iif(inscope("PROVIDER_ID"),Fields!PROVIDER_NM.Value,Parameters!providerCd.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="locationCd">
                                                <Value>=iif(inscope("LOCATION_ID"),Fields!LOCATION_NM.Value,Parameters!locationCd.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="totPatients">
                                                <Value>=Count(Fields!ALERT_LVL.Value)</Value>
                                              </Parameter>
                                              <Parameter Name="guidelineDesc">
                                                <Value>=Parameters!guidelineDesc.Value</Value>
                                              </Parameter>
                                              <Parameter Name="cohortNm">
                                                <Value>=Parameters!cohortNm.Value</Value>
                                              </Parameter>
                                              <Parameter Name="userId">
                                                <Value>=Parameters!userId.Value</Value>
                                              </Parameter>
                                              <Parameter Name="reportingPeriod">
                                                <Value>=Parameters!reportingPeriod.Value</Value>
                                              </Parameter>
                                            </Parameters>
                                          </Drillthrough>
                                        </Action>
                                      </Actions>
                                    </ActionInfo>
                                    <Style>
                                      <Border>
                                        <Color>White</Color>
                                      </Border>
                                    </Style>
                                    <ChartMarker>
                                      <Style />
                                    </ChartMarker>
                                    <DataElementOutput>Output</DataElementOutput>
                                  </ChartDataPoint>
                                </ChartDataPoints>
                                <Type>Shape</Type>
                                <Subtype>Doughnut</Subtype>
                                <Style />
                                <ChartEmptyPoints>
                                  <Style />
                                  <ChartMarker>
                                    <Style />
                                  </ChartMarker>
                                  <ChartDataLabel>
                                    <Style />
                                  </ChartDataLabel>
                                </ChartEmptyPoints>
                                <ValueAxisName>Primary</ValueAxisName>
                                <CategoryAxisName>Primary</CategoryAxisName>
                                <ChartSmartLabel>
                                  <CalloutLineColor>Black</CalloutLineColor>
                                  <MinMovingDistance>0pt</MinMovingDistance>
                                </ChartSmartLabel>
                              </ChartSeries>
                            </ChartSeriesCollection>
                          </ChartData>
                          <ChartAreas>
                            <ChartArea Name="Default">
                              <ChartCategoryAxes>
                                <ChartAxis Name="Primary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartCategoryAxes>
                              <ChartValueAxes>
                                <ChartAxis Name="Primary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartValueAxes>
                              <Style>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                              <ChartElementPosition>
                                <Height>100</Height>
                                <Width>100</Width>
                              </ChartElementPosition>
                              <ChartInnerPlotPosition>
                                <Top>7</Top>
                                <Left>15</Left>
                                <Height>73</Height>
                                <Width>73</Width>
                              </ChartInnerPlotPosition>
                            </ChartArea>
                          </ChartAreas>
                          <ChartLegends>
                            <ChartLegend Name="Default">
                              <Style>
                                <BackgroundGradientType>None</BackgroundGradientType>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>7pt</FontSize>
                                <FontWeight>SemiBold</FontWeight>
                                <Color>#0b6c9f</Color>
                              </Style>
                              <Position>BottomCenter</Position>
                              <Layout>WideTable</Layout>
                              <DockOutsideChartArea>true</DockOutsideChartArea>
                              <ChartElementPosition>
                                <Top>100</Top>
                                <Height>20</Height>
                                <Width>100</Width>
                              </ChartElementPosition>
                              <ChartLegendTitle>
                                <Caption />
                                <Style>
                                  <FontSize>8pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </ChartLegendTitle>
                              <AutoFitTextDisabled>true</AutoFitTextDisabled>
                              <MinFontSize>6pt</MinFontSize>
                              <HeaderSeparatorColor>Black</HeaderSeparatorColor>
                              <ColumnSeparatorColor>Black</ColumnSeparatorColor>
                              <MaxAutoSize>0</MaxAutoSize>
                              <TextWrapThreshold>95</TextWrapThreshold>
                            </ChartLegend>
                          </ChartLegends>
                          <ChartTitles>
                            <ChartTitle Name="Title1">
                              <Caption>=iif(Parameters!alertLvl.Value=0,"Missed Appointment Types","Appointment Types")</Caption>
                              <Style>
                                <BackgroundGradientType>None</BackgroundGradientType>
                                <FontFamily>Calibri Light</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>SemiBold</FontWeight>
                                <TextAlign>General</TextAlign>
                                <VerticalAlign>Top</VerticalAlign>
                                <Color>Purple</Color>
                              </Style>
                              <ChartElementPosition>
                                <Top>0.5</Top>
                                <Left>3</Left>
                                <Height>7.165</Height>
                                <Width>94</Width>
                              </ChartElementPosition>
                            </ChartTitle>
                          </ChartTitles>
                          <Palette>Pastel</Palette>
                          <ChartCustomPaletteColors>
                            <ChartCustomPaletteColor>="#3D82AB"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#EE5921"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#484848"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#3186AD"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#96C950"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#F3F6FC"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#A1C4D8"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#5C9DC0"</ChartCustomPaletteColor>
                            <ChartCustomPaletteColor>="#BEBEBE"</ChartCustomPaletteColor>
                          </ChartCustomPaletteColors>
                          <ChartBorderSkin>
                            <Style>
                              <BackgroundColor>Gray</BackgroundColor>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <Color>White</Color>
                            </Style>
                          </ChartBorderSkin>
                          <ChartNoDataMessage Name="NoDataMessage">
                            <Caption>No Data Available</Caption>
                            <Style>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <TextAlign>General</TextAlign>
                              <VerticalAlign>Top</VerticalAlign>
                            </Style>
                          </ChartNoDataMessage>
                          <DataSetName>visit_dtl</DataSetName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>White</BackgroundColor>
                            <BackgroundGradientType>None</BackgroundGradientType>
                          </Style>
                        </Chart>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>2.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle4">
                          <ReportItems>
                            <Chart Name="Sparkline5">
                              <ChartCategoryHierarchy>
                                <ChartMembers>
                                  <ChartMember>
                                    <Group Name="Sparkline1_CategoryGroup5">
                                      <GroupExpressions>
                                        <GroupExpression>=Format(Fields!SCHEDULE_TM.Value,"yyyyMM")</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <SortExpressions>
                                      <SortExpression>
                                        <Value>=Microsoft.VisualBasic.DateAndTime.DateValue(Fields!SCHEDULE_TM.Value)</Value>
                                      </SortExpression>
                                    </SortExpressions>
                                    <Label>=Microsoft.VisualBasic.DateAndTime.MonthName(Microsoft.VisualBasic.DateAndTime.Month(Fields!SCHEDULE_TM.Value))</Label>
                                  </ChartMember>
                                </ChartMembers>
                              </ChartCategoryHierarchy>
                              <ChartSeriesHierarchy>
                                <ChartMembers>
                                  <ChartMember>
                                    <Label>ALERT LVL</Label>
                                  </ChartMember>
                                </ChartMembers>
                              </ChartSeriesHierarchy>
                              <ChartData>
                                <ChartSeriesCollection>
                                  <ChartSeries Name="Visits">
                                    <ChartDataPoints>
                                      <ChartDataPoint>
                                        <ChartDataPointValues>
                                          <Y>=Count(Fields!ALERT_LVL.Value)</Y>
                                        </ChartDataPointValues>
                                        <ChartDataLabel>
                                          <Style />
                                          <UseValueAsLabel>true</UseValueAsLabel>
                                        </ChartDataLabel>
                                        <ToolTip>=count(Fields!ALERT_LVL.Value) &amp; " Scheduled Visits"</ToolTip>
                                        <ActionInfo>
                                          <Actions>
                                            <Action>
                                              <Drillthrough>
                                                <ReportName>Visits_Detail</ReportName>
                                                <Parameters>
                                                  <Parameter Name="cohortCd">
                                                    <Value>=Parameters!COHORT_ID.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="alertLvl">
                                                    <Value>=iif(Fields!ALERT_LVL.Value = "True",1,0)</Value>
                                                  </Parameter>
                                                  <Parameter Name="hhsAlertLvl">
                                                    <Value>=Parameters!alertLvl.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="guidelineDesc">
                                                    <Value>=Parameters!guidelineDesc.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="totPatients">
                                                    <Value>=Count(Fields!ALERT_LVL.Value)</Value>
                                                  </Parameter>
                                                  <Parameter Name="visitDt">
                                                    <Value>=Fields!SCHEDULE_TM.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="providerCd">
                                                    <Value>=iif(inscope("PROVIDER_ID"),Fields!PROVIDER_NM.Value,Parameters!providerCd.Value)</Value>
                                                  </Parameter>
                                                  <Parameter Name="locationCd">
                                                    <Value>=iif(inscope("LOCATION_ID"),Fields!LOCATION_NM.Value,Parameters!locationCd.Value)</Value>
                                                  </Parameter>
                                                  <Parameter Name="userId">
                                                    <Value>=Parameters!userId.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="cohortNm">
                                                    <Value>=Parameters!cohortNm.Value</Value>
                                                  </Parameter>
                                                  <Parameter Name="reportingPeriod">
                                                    <Value>=Parameters!reportingPeriod.Value</Value>
                                                  </Parameter>
                                                </Parameters>
                                              </Drillthrough>
                                            </Action>
                                          </Actions>
                                        </ActionInfo>
                                        <Style>
                                          <Border>
                                            <Width>2pt</Width>
                                          </Border>
                                        </Style>
                                        <ChartMarker>
                                          <Type>Circle</Type>
                                          <Size>5pt</Size>
                                          <Style>
                                            <Border>
                                              <Color>Purple</Color>
                                              <Width>0.25pt</Width>
                                            </Border>
                                            <Color>Purple</Color>
                                          </Style>
                                        </ChartMarker>
                                        <DataElementOutput>Output</DataElementOutput>
                                      </ChartDataPoint>
                                    </ChartDataPoints>
                                    <Type>Line</Type>
                                    <Subtype>Smooth</Subtype>
                                    <Style />
                                    <ChartEmptyPoints>
                                      <Style />
                                      <ChartMarker>
                                        <Style />
                                      </ChartMarker>
                                      <ChartDataLabel>
                                        <Style />
                                      </ChartDataLabel>
                                    </ChartEmptyPoints>
                                    <ValueAxisName>Primary</ValueAxisName>
                                    <CategoryAxisName>Primary</CategoryAxisName>
                                    <ChartSmartLabel>
                                      <CalloutLineColor>Black</CalloutLineColor>
                                      <MinMovingDistance>0pt</MinMovingDistance>
                                    </ChartSmartLabel>
                                  </ChartSeries>
                                </ChartSeriesCollection>
                              </ChartData>
                              <ChartAreas>
                                <ChartArea Name="Default">
                                  <ChartCategoryAxes>
                                    <ChartAxis Name="Primary">
                                      <Style>
                                        <Border>
                                          <Color>="#0b6c9f"</Color>
                                        </Border>
                                        <FontSize>8pt</FontSize>
                                        <Color>#0b6c9f</Color>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption />
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <Margin>False</Margin>
                                      <ChartMajorGridLines>
                                        <Enabled>False</Enabled>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMajorTickMarks>
                                        <Type>Inside</Type>
                                        <IntervalType>Months</IntervalType>
                                      </ChartMajorTickMarks>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                    <ChartAxis Name="Secondary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <ChartMajorGridLines>
                                        <Enabled>False</Enabled>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Location>Opposite</Location>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                  </ChartCategoryAxes>
                                  <ChartValueAxes>
                                    <ChartAxis Name="Primary">
                                      <Style>
                                        <Border>
                                          <Color>="#0b6c9f"</Color>
                                        </Border>
                                        <FontSize>8pt</FontSize>
                                        <Color>#0b6c9f</Color>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption />
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <ChartMajorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                    <ChartAxis Name="Secondary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <ChartMajorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Location>Opposite</Location>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                  </ChartValueAxes>
                                  <Style>
                                    <BackgroundColor>#00ffffff</BackgroundColor>
                                    <BackgroundGradientType>None</BackgroundGradientType>
                                  </Style>
                                </ChartArea>
                              </ChartAreas>
                              <ChartTitles>
                                <ChartTitle Name="Title1">
                                  <Caption>=iif(Parameters!alertLvl.Value = 0, "12 Month Missed Visit Trend","12 Month Scheduled Visit Trend")</Caption>
                                  <Style>
                                    <BackgroundGradientType>None</BackgroundGradientType>
                                    <FontFamily>Calibri Light</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>SemiBold</FontWeight>
                                    <TextAlign>General</TextAlign>
                                    <VerticalAlign>Top</VerticalAlign>
                                    <Color>#0b6c9f</Color>
                                  </Style>
                                </ChartTitle>
                              </ChartTitles>
                              <Palette>Pastel</Palette>
                              <ChartBorderSkin>
                                <Style>
                                  <BackgroundColor>Gray</BackgroundColor>
                                  <BackgroundGradientType>None</BackgroundGradientType>
                                  <Color>White</Color>
                                </Style>
                              </ChartBorderSkin>
                              <ChartNoDataMessage Name="NoDataMessage">
                                <Caption>No Data Available</Caption>
                                <Style>
                                  <BackgroundGradientType>None</BackgroundGradientType>
                                  <TextAlign>General</TextAlign>
                                  <VerticalAlign>Top</VerticalAlign>
                                </Style>
                              </ChartNoDataMessage>
                              <DataSetName>visit_dtl</DataSetName>
                              <Height>2.25in</Height>
                              <Width>7.02084in</Width>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <BackgroundColor>White</BackgroundColor>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                            </Chart>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>3pt</Width>
                            </BottomBorder>
                          </Style>
                        </Rectangle>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.35833in</Size>
                    <CellContents>
                      <Textbox Name="Textbox35">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value xml:space="preserve">     </Value>
                                <Style>
                                  <FontFamily>Tahoma</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                              <TextRun>
                                <Value>=FormatNumber(Sum(Fields!COUNT.Value),0) &amp; " " &amp; iif(Parameters!alertLvl.Value = 1, "Completed ","Missed ") &amp; Parameters!guidelineDesc.Value</Value>
                                <Style>
                                  <FontFamily>Tahoma</FontFamily>
                                  <FontSize>9pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>#0b6c9f</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox35</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BackgroundColor>Lavender</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.35833in</Size>
                        <CellContents>
                          <Textbox Name="Textbox56">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Tahoma</FontFamily>
                                      <FontSize>11pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox56</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#0b6c9f</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.10833in</Size>
                            <CellContents>
                              <Textbox Name="Textbox59">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Tahoma</FontFamily>
                                          <FontSize>11pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox59</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>Black</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.35833in</Size>
                        <CellContents>
                          <Textbox Name="Textbox13">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontFamily>Tahoma</FontFamily>
                                      <FontSize>11pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox13</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#0b6c9f</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.10833in</Size>
                            <CellContents>
                              <Textbox Name="Textbox14">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Tahoma</FontFamily>
                                          <FontSize>11pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox14</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>Black</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="ALL">
                    <GroupExpressions>
                      <GroupExpression>=1</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=1</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>0.03125in</Size>
                    <CellContents>
                      <Textbox Name="Group1">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=""</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontWeight>Bold</FontWeight>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Group1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>Gray</Color>
                            <Style>Solid</Style>
                            <Width>3pt</Width>
                          </BottomBorder>
                          <BackgroundColor>White</BackgroundColor>
                          <VerticalAlign>Top</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="LOCATION_ID">
                        <GroupExpressions>
                          <GroupExpression>=Fields!LOCATION_NM.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!LOCATION_NM.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>1.98666in</Size>
                        <CellContents>
                          <Textbox Name="LOCATION_ID">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!LOCATION_NM.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontWeight>Bold</FontWeight>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Left</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>LOCATION_ID</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>None</Style>
                              </Border>
                              <BottomBorder>
                                <Color>Gray</Color>
                                <Style>Solid</Style>
                                <Width>3pt</Width>
                              </BottomBorder>
                              <BackgroundColor>White</BackgroundColor>
                              <VerticalAlign>Top</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="PROVIDER_ID">
                            <GroupExpressions>
                              <GroupExpression>=Fields!PROVIDER_NM.Value</GroupExpression>
                            </GroupExpressions>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Sum(Fields!COUNT.Value)</Value>
                              <Direction>Descending</Direction>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>1.26125in</Size>
                            <CellContents>
                              <Textbox Name="PROVIDER_NM">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!PROVIDER_NM.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontWeight>Normal</FontWeight>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>PROVIDER_NM</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <TopBorder>
                                    <Style>None</Style>
                                  </TopBorder>
                                  <BottomBorder>
                                    <Color>Gray</Color>
                                    <Width>3pt</Width>
                                  </BottomBorder>
                                  <LeftBorder>
                                    <Style>None</Style>
                                  </LeftBorder>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <BackgroundColor>White</BackgroundColor>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <KeepTogether>true</KeepTogether>
                            </TablixMember>
                            <TablixMember />
                          </TablixMembers>
                          <Visibility>
                            <Hidden>=iif(Parameters!toggleProvider.Value="1",False,True)</Hidden>
                            <ToggleItem>LOCATION_ID</ToggleItem>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                      <Visibility>
                        <Hidden>=iif(Parameters!toggleLocation.Value="1",False,True)</Hidden>
                        <ToggleItem>Group1</ToggleItem>
                      </Visibility>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <FixedColumnHeaders>true</FixedColumnHeaders>
            <DataSetName>visit_dtl</DataSetName>
            <Top>0.75932in</Top>
            <Height>6.18332in</Height>
            <Width>10.3in</Width>
            <Style />
          </Tablix>
          <Textbox Name="Textbox47">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Epividian® CHORUS</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>™</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> Report</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Format>dd-MMM-yyyy</Format>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> (data)</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Now()</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Format>dd-MMM-yyyy</Format>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> (run)</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontWeight>Normal</FontWeight>
                      <Color>Gray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Top>0.00881cm</Top>
            <Left>7.92011in</Left>
            <Height>0.58333in</Height>
            <Width>2.37989in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox48">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="Total Patients for " &amp; Parameters!cohortNm.Value &amp; " Cohort" &amp;  IIf(IsNothing(Parameters!locationCd.Value) OR Parameters!locationCd.Value ="","", " -&gt; " &amp; Parameters!locationCd.Value) &amp; IIf(IsNothing(Parameters!providerCd.Value) OR Parameters!providerCd.Value="","", " -&gt; " &amp; Parameters!providerCd.Value) &amp; ": " &amp; Parameters!totPatients.Value</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Top>0.00881cm</Top>
            <Left>0.14583in</Left>
            <Height>0.57986in</Height>
            <Width>6.29937in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>0.22132in</Top>
            <Left>0.14583in</Left>
            <Height>0in</Height>
            <Width>5.54937in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
            </Style>
          </Line>
        </ReportItems>
        <Height>6.94264in</Height>
        <Style />
      </Body>
      <Width>10.3in</Width>
      <Page>
        <PageHeight>8.5in</PageHeight>
        <PageWidth>11in</PageWidth>
        <InteractiveHeight>8.5in</InteractiveHeight>
        <InteractiveWidth>11in</InteractiveWidth>
        <LeftMargin>0.35in</LeftMargin>
        <RightMargin>0.35in</RightMargin>
        <TopMargin>0.35in</TopMargin>
        <BottomMargin>0.35in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="COHORT_ID">
      <DataType>String</DataType>
      <Prompt>COHORT_ID</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PROVIDER_CD</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>LOCATION_CD</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="alertLvl">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>alert Lvl</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="totPatients">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>totPatients</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="guidelineDesc">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>guidelineDesc</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="cohortCnt">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>cohortCnt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="measureCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>measureCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="parentLabCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>parentLabCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd2">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd2</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="labCd3">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>labCd3</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="toggleLocation">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleLocation</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="toggleProvider">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>toggleProvider</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="userId">
      <DataType>String</DataType>
      <Prompt>userId</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="cohortNm">
      <DataType>String</DataType>
      <Prompt>cohortNm</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="rollingWeek">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>rollingWeek</Prompt>
    </ReportParameter>
    <ReportParameter Name="reportingPeriod">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>reportingPeriod</Prompt>
    </ReportParameter>
    <ReportParameter Name="measureCnt">
      <DataType>Integer</DataType>
      <Prompt>measureCnt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="inverted_flg">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>inverted_flg</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>8</NumberOfColumns>
      <NumberOfRows>6</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>alertLvl</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>totPatients</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>6</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>guidelineDesc</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>7</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>cohortCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>measureCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>parentLabCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>labCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>labCd2</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>labCd3</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>toggleLocation</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>6</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>toggleProvider</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>7</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>userId</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>cohortNm</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>rollingWeek</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>reportingPeriod</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>measureCnt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>inverted_flg</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Language>en-US</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>86986f46-e563-4d59-b851-66958a427799</rd:ReportID>
</Report>