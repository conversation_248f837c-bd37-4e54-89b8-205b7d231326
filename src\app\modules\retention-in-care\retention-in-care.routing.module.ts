import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutWithoutNavComponent } from '../shared-modules/layouts/layout-without-nav/layout-without-nav.component';
import { EpividianGuardService } from 'src/app/shared-services/epividian-guard.service';
import { AdministrativeActionItemsComponent } from './administrative-action-items/administrative-action-items.component';
import { AnnotationStatusReportComponent } from './annotation-status-report/annotation-status-report.component';
import { FullAlertHistoryComponent } from './annotation-status-report/full-alert-history/full-alert-history.component';
import { PatientDetailsComponent } from './annotation-status-report/patient-details/patient-details.component';
import { ProviderPatientsListComponent } from './annotation-status-report/provider-patients-list/provider-patients-list.component';
import { ProvidersListComponent } from './annotation-status-report/providers-list/providers-list.component';
import { OutreachCallsComponent } from './outreach-calls/outreach-calls.component';
import { OutreachRetentionComponent } from './outreach-retention/outreach-retention.component';
import { MainLayoutComponent } from '../shared-modules/layouts/main-layout/main-layout.component';

const routes: Routes = [
  {
    path: 'OutreachAndRetention',
    component: MainLayoutComponent,
    canActivate: [EpividianGuardService],
    children: [
      {
        path: '', component: OutreachRetentionComponent,
        children: [
          {
            path: "",
            component: OutreachCallsComponent
          },
          {
            path: 'AdministrativeActionItems',
            component: AdministrativeActionItemsComponent
          },
          {
            path: "AnnotationStatusReport",
            component: AnnotationStatusReportComponent,
            children: [
              {
                path: "",
                component: ProvidersListComponent
              },
              {
                path: "PatientsByProvider/:providerId/:locationId",
                component: ProviderPatientsListComponent
              },
              {
                path: "PatientDetails/:patientId",
                component: PatientDetailsComponent
              },
              {
                path: "FullAlertHistory/:patientId",
                component: FullAlertHistoryComponent
              }
            ]
          }
        ]
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RetentionInCareRoutingModule {}
