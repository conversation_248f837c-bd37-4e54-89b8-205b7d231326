<div id="PatientMeasureDetail" class="OutreachDeatilDiv">
    <div style="width: 99%; float: left; padding: 5px 0 2px 0; border-bottom: 1px solid #a1a1a1;">
        <input class="CMbtn" type="button" (click)="goBack()" value="Back"/>
        <img 
            id="Img2" src="../../../../assets/images/printimg5.png" 
            alt="#" title="PDF" 
            style="background-color: transparent; width:50px; height:30px; cursor: pointer; float:right; padding-right:20px; position: relative;left: -67px;" 
            onClick="printPatientDetailsView()" 
        />                    
        <img 
            id="Img6" src="../../../../assets/images/ZHTML.png" alt="#" 
            title="HTML" 
            style="background-color: transparent; cursor: pointer; width:50px; height:30px; float: right; padding-right: 20px; position: relative;left: 20px;" 
            onClick="downloadPatientDetailsHtml()" 
        /> 
    </div>
    <div id="PatientMeasureDetailsView">
        <div  class="OutReachDetailInnerDiv" style="float: left; width: 99%;">
        <div style="float: left; width: 100%; margin: 10px 10px 0 10px;">
            <div>
                <label>Patient with Active Outreach Alerts:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.patienT_NM)"></label>
                <label>Provider:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.fulL_NM)"></label>
            </div>
            <div>
                <label>DOB:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.birtH_DT)"></label>
                <label>HIV Diagnosis Date:</label>
                <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.hiV_DIAG_DT)"></label>
            </div>
        </div>
        <div id="DivToggleMeasureDetail" style="float: left;">
            <div style="float: left; width: 100%; margin: 10px;">
                <div style="float: left; width: 25%;">
                    <label class="labelWithUnderline">Prior Attended Appointments</label>
                    <div class="RetentionGen">
                        <label>Initial at Practice: </label>
                        <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.initiaL_VISIT_DT)"></label>
                    </div>
                    <div class="RetentionGen">
                        <label>Most Recent: </label>
                        <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.mosT_RECENT_APPOINTMENT)"></label>
                    </div>
                </div>
                <div style="float: left; width: 25%;">
                    <label class="labelWithUnderline">Missed Appointments</label>
                    <div class="RetentionGen">
                        <label>Last: </label>
                        <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.lasT_MISSED_VISIT)"></label>
                    </div>
                    <div class="RetentionGen">
                        <label>Prior:  </label>
                        <label class="labelText"  [innerHTML]="retentionInCare.safeHtml(patientDetails.prioR_MISSED_VISIT)"></label>
                    </div>
                </div>
                <div style="float: left; width: 25%;">
                    <label class="labelWithUnderline">Alert History</label>
                    <div class="RetentionGen">
                        <label>Last: </label>
                        <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.lasT_OUTREACH_DT)"></label>
                    </div>
                    <div class="RetentionGen">
                        <label>Prior:  </label>
                        <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.prioR_OUTREACH_DT)"></label>
                    </div>
                    <div>
                        <a [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'FullAlertHistory', patientDetails.demographicS_ID]">
                            Full Alert History
                        </a>
                    </div>
                </div>
                <div style="float: left; width: 25%;">
                    <label class="labelWithUnderline">Phone Number</label>                            
                    <div id="DoNotCallHide">
                        <div class="RetentionGen">
                            <label>H: </label>
                            <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.homE_PHONE_NUM)"></label>
                        </div>
                        <div class="RetentionGen">
                            <label>M:  </label>
                            <label class="labelText" [innerHTML]="retentionInCare.safeHtml(patientDetails.mobilE_PHONE_NUM)"></label>
                        </div>
                        <div>
                            <a style="cursor: pointer" (click)="openDialog();">Record Result</a>
                        </div>
                    </div> 
                    <div id="DisplayRecordResultLink" class="RetentionGen">
                    </div>                           
                </div>
            </div>
            <div [innerHTML]="retentionInCare.safeHtml(patientDetails.detaiL_TXT)" class="detailsDiv"></div>
        </div>
    </div>
</div>
    <div id="OutreachDetailInnerNoRecordsDiv" style="display: none; float: left; margin-top: 15px; text-align: center; width: 100%;">
        <label class="RetentionGen">No records found for this patient.</label>
    </div>
</div>