import { Component, Inject, OnInit, AfterViewInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { QualityMeasuresService } from './services/quality-measures.service';
import { QualityMeasure } from '../../../shared-services/quality-measures/model/quality-measures-model';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { Chart, PieController, ArcElement, Tooltip, Legend } from 'chart.js';
import { QualityMeasureFilters } from './quality-measure-filters/quality-measure-filters.component';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { ActivatedRoute } from '@angular/router';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

@Component({
  selector: 'app-quality-measures',
  templateUrl: './quality-measures.component.html',
  styleUrl: './quality-measures.component.scss'
})
export class QualityMeasuresComponent implements OnInit, AfterViewInit {
  qualityMeasures: QualityMeasure[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  columnWidths: number[] = []; // External variable to store column widths
  groupByFields: number[] = []; // groupBy fields
  toggleGrouping: boolean = false; // Flag to track grouping state

  // Store the current filters
  currentFilters: QualityMeasureFilters | null = null;

  menuOpen = false;

  // Panel state management - mutually exclusive panels
  public reportNavCollapsed: boolean = false;
  public filtersCollapsed: boolean = true;
  public currentSelectedReport: string = "";

  // Store siteId for panel state logic
  private siteId: string = "";

  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }

  // Mutually exclusive panel toggle methods
  toggleReportNav(): void {
    if (this.reportNavCollapsed) {
      this.reportNavCollapsed = false;
      this.filtersCollapsed = true;
    } else {
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }
  }

  toggleFilters(): void {
    if (this.filtersCollapsed) {
      this.filtersCollapsed = false;
      this.reportNavCollapsed = true;
    } else {
      this.filtersCollapsed = true;
      this.reportNavCollapsed = false;
    }
  }



  // Current report changed handler
  onCurrentReportChanged(reportName: any): void {
    console.log('📝 onCurrentReportChanged called with:', reportName);
    this.currentSelectedReport = typeof reportName === 'string' ? reportName : reportName?.toString() || '';
    console.log('📝 Set currentSelectedReport to:', this.currentSelectedReport);
  }

  // Dynamic panel title
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }

  // Optional: close menu when clicking outside
  onClickOutsideMenu(event: MouseEvent) {
    const menu = document.querySelector('.dropdown');
    if (menu && !menu.contains(event.target as Node)) {
      this.menuOpen = false;
    }
  }

  constructor(
    private qualityMeasuresService: QualityMeasuresService,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private layoutService: LayoutService,
    private route: ActivatedRoute,
    private userContext: UserContext
  ) {
    console.log('🔧 Quality Measures component constructor started');

    // Get site ID from route parameters (following EHI_Export pattern)
    let tmpSite = this.route.snapshot.paramMap.get('siteId');
    if (tmpSite) {
      console.log('🔧 Found site ID in route params:', tmpSite);

      // Store siteId for use in ngOnInit
      this.siteId = tmpSite;

      // Only set the current site in UserContext if this is not an internal report (siteId 0)
      // Internal reports should not change the user's site context
      if (Number(tmpSite) !== 0) {
        this.userContext.SetCurrentSite(Number(tmpSite));
        console.log('🔧 Set current site to:', tmpSite);
      } else {
        console.log('🔧 Internal report (siteId 0) - not changing user site context');
      }

      // Initialize LayoutService with the site ID (following CustomQuery pattern)
      console.log('🔧 Initializing LayoutService with site ID:', tmpSite);
      this.layoutService.setNavandReload(Number(tmpSite));
      console.log('🔧 LayoutService navigation menu loading initiated');
    } else {
      console.warn('🔧 No site ID found in route parameters');
    }
  }

  // Track subscriptions to clean up on destroy
  private subscriptions: any[] = [];

  ngOnInit(): void {
    console.log('🚀 Quality Measures ngOnInit started');
    console.log('🚀 LayoutService state at ngOnInit:');
    console.log('🚀 - reportNavData:', this.layoutService.reportNavData);
    console.log('🚀 - menuSections observable:', this.layoutService.menuSections);

    // Register Chart.js components
    this.registerChartComponents();

    // Session storage integration for panel state persistence
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';
    console.log('🚀 Session storage - shouldCollapse:', shouldCollapse, 'isInitialNavigation:', isInitialNavigation);

    // Special handling for internal reports (siteId 0) - show filters by default
    if (Number(this.siteId) === 0) {
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
      console.log('🚀 Internal report detected - showing filters panel by default');
    } else {
      // Apply normal session storage logic for non-internal reports
      if (shouldCollapse && !isInitialNavigation) {
        this.reportNavCollapsed = true;
        this.filtersCollapsed = false;
        console.log('🚀 Applied session storage panel state - reports collapsed');
      }
    }

    // Clear session storage flags after checking
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }

    // Note: LayoutService initialization moved to ngAfterViewInit to match working pattern

    // Load quality measures data
    console.log('🚀 Loading quality measures data');
    this.loadQualityMeasuresData();

    console.log('🚀 Quality Measures ngOnInit completed');
  }

  private loadQualityMeasuresData(): void {
    // Load quality measures data
    this.spinnerService.show();

    const subscription = this.qualityMeasuresService.getMeasures().subscribe({
      next: (data) => {
        this.qualityMeasures = data.map(measure => ({
          ...measure,
          totalMeasures: measure.measuresSatisfied + measure.measuresUnsatisfied,
          satisfiedPercentage: Math.round((measure.measuresSatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100),
          unsatisfiedPercentage: Math.round((measure.measuresUnsatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100)
        }));
        this.spinnerService.hide();

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading quality measures data:', error);
        this.spinnerService.hide();
      }
    });

    this.subscriptions.push(subscription);

    // Initialize PDF plugin
    applyPlugin(jsPDF);

    // Set up resize observer for responsive table
    this.resizeObserver = () => {
      if (this.table) {
        this.table.redraw(true); // Trigger full rerender
      }
    };
    window.addEventListener('resize', this.resizeObserver);
  }

  ngOnDestroy(): void {
    // Remove the event listener when the component is destroyed
    window.removeEventListener('resize', this.resizeObserver);

    // Destroy Tabulator instance if it exists
    if (this.table) {
      this.table.destroy();
    }

    // Clean up all subscriptions
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  ngAfterViewInit(): void {
    console.log('🔄 Quality Measures ngAfterViewInit started');

    // LayoutService was initialized in constructor with setNavandReload()
    // The epividian-reportNav component should now have access to the site's reports
    console.log('🔄 LayoutService initialized in constructor, nav component should have data');

    // Initialize table
    this.initializeTable();
  }

  initializeTable(): void {
    this.table = new Tabulator("#quality-measures-table", {
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false, //disable row groups in download
      },
      data: this.qualityMeasures,
      printAsHtml: true, //enable html table printing
      printStyled: true, //copy Tabulator styling to HTML table
      groupBy: ["qualityName", "groupName", "locationName"],
      groupStartOpen: [true, true, true, true],
      groupToggleElement: "header", //toggle group on click anywhere in the group header
      headerSortElement: "<i class='material-icons'>arrow_drop_up</i>", //material icon for sorting
      rowHeight: 30,
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },
      columnHeaderVertAlign: "middle",
      columns: [
        { title: "Quality Name", field: "qualityName", visible: false, download: true }, //these 3 columns are hidden in the table but included in the download
        { title: "Group Name", field: "groupName", visible: false, download: true },
        { title: "Location Name", field: "locationName", visible: false, download: true },

        {
          download: false,
          field: "filter", //a dummy field to allow the .getField() method to work
          title: "<span style='padding-right: 40px;'>Measure</span>Location",
          hozAlign: "center",
          width: 240,
        },

        {
          title: "Provider",
          field: "providerName",
          width: 120
        },
        {
          title: "Trend",
          field: "trend",
          width: 60,
          cssClass: "whiteBorder",
          hozAlign: "center",
          headerHozAlign: "center",
          headerSort: false,
          formatter: (cell: any) => {
            const value = cell.getValue();
            if (value === -1) {
              return `<i class="material-icons" style="font-size: 20px; color: #808080;">south_east</i>`;
            } else if (value === 0) {
              return `<i class="material-icons" style="font-size: 20px; color: #29abe2;">arrow_forward</i>`;
            } else if (value === 1) {
              return `<i class="material-icons" style="font-size: 20px; color: #0071bc;">north_east</i>`;
            }
            return "";
          }
        },
        { title: "Qualifies", field: "totalMeasures", hozAlign: "center", headerHozAlign: "center", width: 92 },
        {
          title: "<div style='text-align: right'><div class='svg-icon satisfied-icon'></div></div>",
          titleDownload: "Measures Satisfied",
          field: "measuresSatisfied",
          hozAlign: "right",
          headerSort: false,
          width: 40,
          cssClass: "lightBlue"
        },
        {
          title: "Measures Satisfied",
          titleDownload: "%",
          field: "satisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 85,
          cssClass: "lightBlue",
          formatter: (cell: any) => `(${cell.getValue()}%)`
        },
        {
          title: "",
          field: "pieChart",
          headerSort: false,
          download: false,
          formatter: (cell: any) => {
            const data = cell.getRow().getData();
            const canvas = document.createElement("canvas");
            canvas.width = 20;
            canvas.height = 20;

            requestAnimationFrame(() => {
              this.drawPieChart(
                canvas,
                data.measuresSatisfied,
                data.measuresUnsatisfied,
                "#99d2f8",
                "#0071bc"
              );
            });
            return canvas;
          },
          hozAlign: "center",
          minWidth: 20,
          maxWidth: 30,
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon unsatisfied-icon'></div></div>",
          titleDownload: "Measures Unsatisfied",
          field: "measuresUnsatisfied",
          hozAlign: "right",
          width: 40,
          cssClass: "darkBlue"
        },
        {
          title: "Measures Unsatisfied",
          titleDownload: "%",
          field: "unsatisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 90,
          cssClass: "darkBlue",
          formatter: (cell: any) => `(${cell.getValue()}%)`
        },
        { title: "New", field: "newMeasures", hozAlign: "center", cssClass: "whiteBorder", width: 50 },
        { title: "Ongoing", field: "ongoingMeasures", hozAlign: "center" },
        { title: "Recently Satisfied", field: "recentlySatisfied", hozAlign: "center", headerWordWrap: true, width: 80 },
        { title: "Score", field: "operaScore", hozAlign: "center", headerHozAlign: "left", width: 80, cssClass: "whiteBorder",
          formatter: (cell: any) => {
            const value = cell.getValue();
            if (value === 'Leader') {
              return `<span style="color: #0071bc;">${value}</span>`;
            } else if (value === 'Median') {
              return `<span style="color: #808080;">${value}</span>`;
            } else {
              return `<span style="color: #0071bc;">${value}</span>`;
            }
          }
         },
        {
          title: "OPERA&copy; Average",
          titleDownload: "OPERA (c) Average %",
          field: "operaAverage",
          hozAlign: "right",
          headerWordWrap: true,
          width: 76,
          formatter: (cell: any) => `${cell.getValue()}%`
        },

      ]

    });

    // Subscribe to the tableBuilt event
    // https://tabulator.info/docs/6.3/events#table
    this.table.on("tableBuilt", () => {
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
      this.groupByFields = this.table.options.groupBy;

      // Set the group headers after the table is built
      // https://tabulator.info/docs/6.3/group#manage
      this.table.setGroupHeader((value: any, _count: any, data: any[], group: any) => {
        // Calculate the summation of the columns

        const totalMeasures = data.reduce((sum: number, row: any) => sum + row.totalMeasures, 0);
        const measuresSatisfied = data.reduce((sum: number, row: any) => sum + row.measuresSatisfied, 0);
        const measuresUnsatisfied = data.reduce((sum: number, row: any) => sum + row.measuresUnsatisfied, 0);
        const newMeasures = data.reduce((sum: number, row: any) => sum + row.newMeasures, 0);
        const ongoingMeasures = data.reduce((sum: number, row: any) => sum + row.ongoingMeasures, 0);
        const recentlySatisfied = data.reduce((sum: number, row: any) => sum + row.recentlySatisfied, 0);
        const satisfiedPercentage = totalMeasures > 0 ? Math.round((measuresSatisfied / totalMeasures) * 100) : 0;
        const unsatisfiedPercentage = totalMeasures > 0 ? Math.round((measuresUnsatisfied / totalMeasures) * 100) : 0;
        const operaAverage = Math.round((data.reduce((sum: number, row: any) => sum + row.operaAverage, 0) / data.length) * 100) / 100;

        // Determine which grouping level is being rendered
        const groupField = group.getField();
        const groupLevel = this.groupByFields.indexOf(groupField);
        const paddingLevel = 100; //padding on the left side of each column
        let indentationWidth: number = 32 + (groupLevel * paddingLevel);

        // Create the custom group header HTML string
        let groupHeaderHtml = `<div class="tabulator-group-toggle tabulator-row tabulator-unselectable tabulator-calcs tabulator-calcs-top tabulator-row-even" role="row">`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[3] + this.columnWidths[4] - indentationWidth}px;">${value}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[5]}px;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[6]}px; text-align: center;">${totalMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[7]}px; text-align: right;">${measuresSatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[8]}px; text-align: center;">(${satisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[9]}px; text-align: center;" data-piechart data-satisfied="${measuresSatisfied}" data-unsatisfied="${measuresUnsatisfied}"></div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[10]}px; text-align: right;">${measuresUnsatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[11]}px; text-align: center;">(${unsatisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[12]}px; text-align: center;">${newMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[13]}px; text-align: center;">${ongoingMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[14]}px; text-align: center;">${recentlySatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[15]}px;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[16]}px; text-align: right;">${operaAverage}%</div>`;
        groupHeaderHtml += `</div>`;

        return groupHeaderHtml;

      });

      // Attach scroll synchronization logic
      const tableElement = document.querySelector("#quality-measures-table .tabulator-tableholder");
      if (tableElement) {
        tableElement.addEventListener("scroll", (event) => {
          const scrollLeft = (event.target as HTMLElement).scrollLeft;
          this.syncGroupHeaderScroll(scrollLeft);
        });
      }
    });

    this.table.on("columnResized", () => {
      // Update custom group column widths when columns are resized
      // https://tabulator.info/docs/6.3/events#column
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
    });

    this.table.on("headerClick", (_e: any, column: any) => {
      // Check if the clicked column is the filter column
      // https://tabulator.info/docs/6.3/callbacks#column
      if (column.getField() === "filter") {
        this.toggleLocationGrouping();
      }
    });

    this.table.on("renderComplete", () => {
      this.renderGroupHeaderPieCharts();
    });

  }

  syncGroupHeaderScroll(scrollLeft: number): void {
    // Find all group headers and synchronize their scroll position
    // with the table's scroll position
    const groupHeaders = document.querySelectorAll(".tabulator-group");
    groupHeaders.forEach((header) => {
      (header as HTMLElement).style.transform = `translateX(-${scrollLeft}px)`;
    });
  }

  renderGroupHeaderPieCharts(): void {
    // Find all elements with the data-piechart attribute
    const pieChartElements = document.querySelectorAll('[data-piechart]');
    pieChartElements.forEach((element) => {
      // Clear any existing content
      element.innerHTML = '';

      const canvas = document.createElement('canvas');
      canvas.width = 20;
      canvas.height = 20;

      const satisfied = parseInt(element.getAttribute('data-satisfied') || '0', 10);
      const unsatisfied = parseInt(element.getAttribute('data-unsatisfied') || '0', 10);

      // Draw the pie chart using Chart.js
      this.drawPieChart(canvas, satisfied, unsatisfied, "#99d2f8", "#0071bc");

      // Append the canvas to the element
      element.appendChild(canvas);
    });
  }

  // Download functions
  // https://tabulator.info/docs/6.3/download
  downloadExcel(): void {
    this.table.download("xlsx", "quality-measures.xlsx");
  }

  downloadPDF(): void {
    this.table.download("pdf", "quality-measures.pdf", {
      orientation: "landscape",
      title: "Quality Measures",
    });
  }

  downloadCSV(): void {
    this.table.download("csv", "quality-measures.csv");
  }

  printTable(): void {
    this.table.print(false, true, true, true, true, true, true, true, true, true, false);
  }

  // Register Chart.js components
  private registerChartComponents(): void {
    Chart.register(PieController, ArcElement, Tooltip, Legend);
  }

  // Create pie chart using Chart.js
  drawPieChart(canvas: HTMLCanvasElement, satisfied: number, unsatisfied: number,
    satisfiedColor: string = '#99d2f8', unsatisfiedColor: string = '#0071bc'): void {

    const total = satisfied + unsatisfied;
    if (total === 0) return;

    // Create Chart.js pie chart
    new Chart(canvas, {
      type: 'pie',
      data: {
        datasets: [{
          data: [unsatisfied, satisfied],
          backgroundColor: [unsatisfiedColor, satisfiedColor],
          borderWidth: 1,
          borderColor: '#99d2f8',
          // Add a slight border radius for a smoother look
          borderRadius: 1
        }]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        interaction: {
          mode: undefined, // Disable all interactions
          intersect: false
        },
        animation: false,
        rotation: -90 * Math.PI / 180, // Start from the right side
      }
    });
  }

  // This function toggles the grouping of the table
  toggleLocationGrouping(): void {
    this.toggleGrouping = !this.toggleGrouping;

    if (this.toggleGrouping) {
      this.table.setGroupBy(["qualityName"]);
    } else {
      this.table.setGroupBy(["qualityName", "groupName", "locationName"]);
    }
  }

  /**
   * Calculate the total sum of all totalMeasures in the dataset
   * @returns The sum of all totalMeasures
   */
  getTotalMeasuresSum(): number {
    if (!this.qualityMeasures || this.qualityMeasures.length === 0) {
      return 0;
    }

    return this.qualityMeasures.reduce((sum, measure) => sum + measure.totalMeasures!, 0);
  }

  /**
   * Handle filter changes from the filter component
   */
  onFiltersChanged(filters: QualityMeasureFilters): void {
    this.currentFilters = filters;
    console.log('Filters changed in parent component:', filters);

    // Here you would typically call your API with the new filters
    // For now, we'll just log the filters

    // Example of how you might call your API when it's ready:
    // this.spinnerService.show();
    // this.qualityMeasuresService.getMeasuresWithFilters(filters).subscribe(data => {
    //   this.qualityMeasures = data.map(measure => ({
    //     ...measure,
    //     totalMeasures: measure.measuresSatisfied + measure.measuresUnsatisfied,
    //     satisfiedPercentage: Math.round((measure.measuresSatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100),
    //     unsatisfiedPercentage: Math.round((measure.measuresUnsatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100)
    //   }));
    //   this.table.setData(this.qualityMeasures);
    //   this.spinnerService.hide();
    // });
  }

  /**
   * Handle report selection from the report selector component
   */
  onReportSelected(report?: any): void {
    console.log('🎯 onReportSelected called in Quality Measures component with:', report);

    // Panel behavior: collapse reports and show filters
    this.reportNavCollapsed = true;
    this.filtersCollapsed = false;
    console.log('🎯 Panel state changed - reports collapsed, filters shown');

    // Here you would typically navigate to the selected report
    // or update the current view based on the selected report

    // Example:
    // if (report && report.route) {
    //   this.router.navigateByUrl(report.route);
    // }
  }
}
