.table-header-rotated th.row-header{width: auto;}
.table-header-rotated th.rotate-45{height: 80px;width: 45px; min-width: 45px; max-width: 48px;position: relative; vertical-align: bottom;padding: 0;font-size: 12px; line-height: 1;}
.table-header-rotated td{width: 40px;border-top: 1px solid #dddddd;border-left: 1px solid #dddddd;
                         border-right: 1px solid #dddddd;vertical-align: middle;text-align: center;}

.table-header-rotated th.rotate-45{height: 80px;width:45px;min-width: 45px;max-width: 48px;position: relative;
                                   vertical-align: bottom;padding: 0;font-size: 12px;line-height: 1;}

.table-header-rotated th.rotate-45 > div{position: relative;top: 0px;left: 40px; height: 100%;-ms-transform:skew(-45deg,0deg);
                                         -moz-transform:skew(-45deg,0deg);-webkit-transform:skew(-45deg,0deg);-o-transform:skew(-45deg,0deg);
                                         transform:skew(-45deg,0deg); overflow: hidden; border-top: 1px solid #dddddd;background-color: #2779aa !important; 
                                         -webkit-print-color-adjust: exact !important; -moz-color-adjust: exact !important; color:white !important; 
                                         -moz-color-adjust : exact !important;}

.table-header-rotated th.rotate-45 span {
  -ms-transform:skew(45deg,0deg) rotate(315deg);
  -moz-transform:skew(45deg,0deg) rotate(315deg);
  -webkit-transform:skew(45deg,0deg) rotate(315deg);
  -o-transform:skew(45deg,0deg) rotate(315deg);
  transform:skew(45deg,0deg) rotate(315deg);
  position: absolute;
  bottom: 30px; /* 40 cos(45) = 28 with an additional 2px margin*/
  left: -25px; /*Because it looked good, but there is probably a mathematical link here as well*/
  display: inline-block;
  /*// width: 100%;*/
  width: 85px; /* 80 / cos(45) - 40 cos (45) = 85 where 80 is the height of the cell, 40 the width of the cell and 45 the transform angle*/
  text-align: left;
  font-weight:500;
  /*// white-space: nowrap;*/ /*whether to display in one line or not*/
}


.table-header-rotated th.rotate-45-90w{height: 80px;width: 74px;min-width: 45px;max-width: 74px;position: relative;vertical-align: bottom;padding: 0;
                                       font-size: 12px;line-height: 1;}

.table-header-rotated th.rotate-45-90w > div{position: relative;top: 0px;left: 40px;height: 100%;-ms-transform:skew(-45deg,0deg);-moz-transform:skew(-45deg,0deg);
                                             -webkit-transform:skew(-45deg,0deg);-o-transform:skew(-45deg,0deg);transform:skew(-45deg,0deg);
                                             overflow: hidden;border-right: 1px solid #dddddd;border-top: 1px solid #dddddd;
                                             background-color: #2779aa !important;color:white !important;-webkit-print-color-adjust: exact !important;
                                             -moz-color-adjust: exact !important;}

.table-header-rotated th.rotate-45-90w span{ -ms-transform:skew(45deg,0deg) rotate(315deg);-moz-transform:skew(45deg,0deg) rotate(315deg);
                                             -webkit-transform:skew(45deg,0deg) rotate(315deg);-o-transform:skew(45deg,0deg) rotate(315deg); 
                                             transform:skew(45deg,0deg) rotate(315deg);position: absolute;bottom: 30px;left: -25px; 
                                             display:inline-block; width: 85px;text-align: left;font-weight:500;padding-top:25px;}
  
 /*.table-header-rotated th:nth-child(1) .rotate-45-90w {
       background-color:red !important;
       -webkit-print-color-adjust: exact !important;-moz-color-adjust: exact;
       width:120px;
        }*/

.locationData {background-color: #dcdcdc !important;-webkit-print-color-adjust: exact !important;-moz-color-adjust: exact !important;width:120px;}
.table-header-rotated td:nth-child(1) {background-color:#dcdcdc !important; -webkit-print-color-adjust: exact !important;-moz-color-adjust: exact !important; width:120px;}

/*.table-header-rotated th.rotate-45-90w th:nth-child(1) span {
    margin-left:30px !important;
}
.table-header-rotated th.rotate-45 th:nth-child(1) span {
    margin-left:30px !important;
}*/

/*#DIVBindMeasureHtml {overflow:visible !important;height:auto;overflow-y:visible !important;font-weight: normal !important;}*/