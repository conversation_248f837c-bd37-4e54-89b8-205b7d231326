import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RecordResultDialogComponent } from './record-result-dialog.component';

describe('RecordResultDialogComponent', () => {
  let component: RecordResultDialogComponent;
  let fixture: ComponentFixture<RecordResultDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RecordResultDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RecordResultDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
