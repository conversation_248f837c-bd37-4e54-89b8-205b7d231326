import {
  ChangeDetectorRef,
  Component,
  ComponentRef,
  NgZone,
  OnInit,
  ViewChild,
  ViewContainerRef, Inject, Optional, ComponentFactoryResolver, OnDestroy
} from '@angular/core';
import { ActivatedRoute, Router, UrlTree } from '@angular/router';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { BldRptViewerComponent } from './report-viewer/bld-rpt-viewer/bld-rpt-viewer.component';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ReportPanelTypes } from '../panels/report-panel-enums';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { IReportParameter } from './models/report-params-model';
import {
  ReportPanelFieldMaps,
  ReportPanelIdMaps,
} from './report-param-map-enums';
import { PanelService } from '../panels/PanelService';
import {
  IData,
} from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AnnotationComponent } from '../../annotation/annotation.component';
import { AnnotationService } from '../../annotation/annotation.service';
import { IMenuSections } from '../../shared-modules/layouts/models/menu-item.model';
import { HttpParams } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { EpividianCommon } from '../../utility/EpividianCommon';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';

//import { protectedResources } from '../../auth-config';

@Component({
  selector: 'app-report',
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.scss'],
})
export class ReportComponent implements OnInit, OnDestroy {

  public toolbarSettings: any;
  public exportSettings: any;
  public reportPath: string = '';
  public reportTitle: string = '';
  public parameters: any;
  public routerState: any;
  public reportName: string = '';
  public reportInfo: IReportList = <IReportList>{};
  public panelType: ReportPanelTypes = 0;
  public reportParameters: IReportParameter[] = [] as IReportParameter[];
  public bldRptRef: ComponentRef<BldRptViewerComponent> = {} as ComponentRef<BldRptViewerComponent>;
  public ssrsReportParams: IReportParameter[] = [];
  private pageSubscriptions: Subscription = new Subscription;

  @ViewChild('panelContainerRef', { read: ViewContainerRef, static: true })
  public _panelContainer!: ViewContainerRef;
  @ViewChild('bldReportContainerRef', { read: ViewContainerRef, static: true })
  public _panelContainer2!: ViewContainerRef;
  public componentRefs: ComponentRef<any>[] = [];
  public panelComponent: any;
  openDialog: MatDialogRef<AnnotationComponent, any> = {} as MatDialogRef<AnnotationComponent, any>

  private userId: string = "";
  private params: HttpParams= new HttpParams();
  private url: string = "";
  private queryTree: UrlTree = new UrlTree();
  private path: string = "";
  private siteId: string = "";
  public runReportAtInit: boolean = false;
  public isPopUp = false;
  private notifySubscription: Subscription = new Subscription();

  // Panel state management - mutually exclusive panels (only one can be open at a time)
  public reportNavCollapsed: boolean = false;
  public filtersCollapsed: boolean = true;

  // Current report name for dynamic panel title
  public currentSelectedReport: string = "";

  // Track whether a report is currently loaded
  public isReportLoaded: boolean = false;

  constructor(
    private router: Router,
    private userContext: UserContext,
    private cdr: ChangeDetectorRef,
    private apiHandler: ApiHandler,
    private panelService: PanelService,
    private ngZone: NgZone,
    public dialog: MatDialog,
    private annotationService: AnnotationService,
    private activeRoute: ActivatedRoute,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private componentFactoryResolver: ComponentFactoryResolver,
    private viewContainerRef: ViewContainerRef,
    private auditService: AuditService,
    private epividianCommon: EpividianCommon,
    private layoutService: LayoutService,
  ) {
    this.auditService.setPageAudit(Page.Report);
    this.initialConstructor();
  }
  ngOnDestroy(): void {
    this._panelContainer2.clear();
    this.isReportLoaded = false;
    this.pageSubscriptions.unsubscribe();
    if (this.notifySubscription) {
      this.notifySubscription.unsubscribe();
    }
  }


  initialConstructor() {

    //this.ssrsReportParams = this.panelService.InitializeReportParamSub();
    this.reportName = '';
    this.reportInfo = <IReportList>{};
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routerState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routerState) {
        this.reportInfo = JSON.parse(this.routerState.reportInfo);
        this.reportName = this.reportInfo.reportFileName.replace('.rdl', '');
        this.reportTitle = this.reportInfo.reportName;
        this.siteId = this.reportInfo.siteId.toString();

        this.auditService.setPageAudit(Page.Report, this.reportTitle);

        //need to base 64 en
        //this.reportName = btoa(this.reportName)
        this.reportPath = `/Chorus_Portal/${this.reportName}`;
        this.panelType = this.reportInfo.panelId;
      }
    }
    else
    {
      if (this.activeRoute.snapshot.paramMap.get('siteId')!=null || this.data!=null)
      {

        if (this.data!=null)
        {
          this.url = this.data
          this.siteId = this.url.split('/')[3];
          this.reportName = this.url.split('/')[4].split("?")[0];
          this.isPopUp = true;
        }
        else
        {
          //Users Site Context must be set here for direct page access
          this.url = this.router.url; // Gets the current URL
          this.siteId = this.activeRoute.snapshot.paramMap.get('siteId') ?? '';
          //parses the data from the Url
          this.reportName = this.activeRoute.snapshot.paramMap.get('reportFileName') ?? '';
        }

        // Only set the current site context if this is not an internal report (siteId 0)
        // Internal reports should not change the user's site context
        if (Number(this.siteId) !== 0) {
          this.userContext.SetCurrentSite(Number(this.siteId));
        }

        this.auditService.setPageAudit(Page.Report,this.reportName);

        this.queryTree = this.router.parseUrl(this.url); // Gets the query parameters from the URL
        this.runReportAtInit = Object.keys(this.queryTree.queryParams).length >0 ? true : false;

        this.params = new HttpParams({fromObject: {siteId: this.siteId, rpt: this.reportName+".rdl"}})
        this.pageSubscriptions.add(
          this.apiHandler.Get<IMenuSections>(ApiTypes.V2, ApiRoutes.Report, true, false, undefined, this.params).subscribe((data) => {
            this.reportInfo = {} as IReportList;
            this.reportInfo = data[0];
            //needed for reports not in db
            if (this.reportInfo!=undefined)
            {
              this.reportTitle = this.reportInfo.reportName;
              this.panelType = this.reportInfo.panelId;
            } else {
              //When Item does not exist in menu you must create a reportList Item to pass to the bold report panel service.
              this.reportInfo = { reportFileName: this.reportName+".rdl",
                                  reportName: this.reportName,
                                  reportId: 0,
                                  description: "none",
                                  panelId: 0,
                                  categoryId: 0,
                                  panelLabel: "none",
                                  siteId: Number(this.siteId),
                                  customizedFlag: false,
                                  siteReportId: 0,
                                  categoryNm: "none"
                                } as IReportList
              this.reportTitle = this.reportName;
              this.panelType = 0
            }
            this.reportPath = `/Chorus_Portal/${this.reportName}`;
          })
        );
      }
    }



     //checks application terms agreement on entering dashboard
    this.pageSubscriptions.add(
      this.userContext.GetUserInfoSub().subscribe (s => {
        this.userContext.SetUserInfo(s);
        if (this.runReportAtInit && this.queryTree?.queryParams?.["USER_ID"])
        {
          this.userId = this.queryTree.queryParams["USER_ID"];
        }
        else
        {
          this.userId = s.userId;
        }

        if (s.isAgree!==true) {
          this.router.navigate(['/TermsConditions']);
        }
      })
    );

    // Subscribe to the BoldReportRef subject in the PanelService
    this.pageSubscriptions.add(
      this.panelService.reportViewerRef.subscribe((s) => {
        // Clear the BoldReportViewer container
        this._panelContainer2.clear();
        this.isReportLoaded = false;
        this.cdr.detectChanges();
        this.bldRptRef = this._panelContainer2.createComponent(s);

        // Initialize the BoldReportViewer component with the report info, report path, and panel type
        this.bldRptRef.instance.InitBoldReportViewer(
          {
            reportInfo: this.reportInfo,
            reportPath: this.reportPath,
            panelType: this.panelType,
          },
          this.updateReportParams(this.ssrsReportParams)
        );

        // Mark that a report is now loaded
        this.isReportLoaded = true;
      })
    );
  }


  ngOnInit() {
    // Check sessionStorage for panel collapse state
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';

    // Special handling for internal reports (siteId 0) - show filters by default
    if (Number(this.siteId) === 0) {
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
      console.log('Internal report detected - showing filters panel by default');
    } else {
      // Apply normal collapse logic for non-internal reports
      if (shouldCollapse && !isInitialNavigation) {
        // User selected a specific report after initial navigation, collapse the reports panel
        this.reportNavCollapsed = true;
        this.filtersCollapsed = false;
      }
      // If isInitialNavigation is true, keep default panel states (reports open, filters closed)
    }

    // Always clear the collapse flag after checking it
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }

    // Create Reportcomponentreference. By using this we will call loadAnnotationPopup() from index.html
    window['reportComponentReference'] = { component: this, zone: this.ngZone, loadAnnotation: (action) => this.loadAnnotationPopup(action), };
    this.pageSubscriptions.add(
      this.panelService.GetReportParamsCall(this.reportName).subscribe((res) => {
        this.ssrsReportParams = this.ParseReportParams(res,this.queryTree);
        this.CreateReportPanel(this.panelType);
        this.layoutService.hideSpinner();
        //When Passing Params in we expect to auto run the report
        if (this.runReportAtInit==true)
        {
           this.runReport();
        }
      })
    );
  }

  private runReport()
  {
    this.updateReportParams(this.ssrsReportParams);
          setTimeout(() => {
            this.panelService.InitBoldReport();
          },150)

  }

  // Update the report parameters based on the current panel values
  private updateReportParams(ssrsParamsAnDefaults: IReportParameter[]): IReportParameter[] {
    // Loop through each components properties looking for report panel objects.
    for (var component of this.componentRefs) {
      for (var cp in component.instance) {
        // all report panel objects must start with "rptpan"
        if (cp.startsWith('rptpan')) {
          // Check if the current property name has a corresponding report parameter in the field map
          if (ReportPanelFieldMaps[cp]) {
            // Check if the current report parameter is already in the list of report parameters
            let reportParam = this.reportParameters.find(
              (f) => f.name == ReportPanelFieldMaps[cp]
            );

            let paramIdMap = ReportPanelIdMaps[cp];
            if (paramIdMap!=undefined && paramIdMap && reportParam) {
              var idValue = this.findKeyByValue(
                component.instance[paramIdMap],
                component.instance[cp].value.toString()
              );
              reportParam.labels = [idValue];
              reportParam.values = [idValue];
            } else if (reportParam && component.instance[cp].value!=undefined) {
              if (this.isIData(component.instance[cp].value)) {
                reportParam.labels = [component.instance[cp].value.key.toString()];
                reportParam.values = [component.instance[cp].value.key.toString()];
              } else {
              reportParam.labels = [component.instance[cp].value.toString()];
              reportParam.values = [component.instance[cp].value.toString()];
              }
            }
            // If the parameter is not in the list, add it to the list with the current value
            else {
              let isNullable = false;
              let paramIdMap = ReportPanelIdMaps[cp];
              let idValue = '';
              if (paramIdMap) {
                idValue = this.findKeyByValue(
                  component.instance[paramIdMap],
                  component.instance[cp].value.toString()
                ).toString();
              } else {

                if (component.instance[cp].value!=undefined && this.isIData(component.instance[cp].value)){
                  idValue = component.instance[cp].value.key.toString();
                } else if (component.instance[cp].value!=undefined)
                {
                    idValue = component.instance[cp].value.toString();
                } else {
                    idValue = "";
                  }
                }
                let newParam: IReportParameter = {
                  name: ReportPanelFieldMaps[cp],
                  labels: [idValue],
                  values: [idValue],
                  nullable: isNullable,
                };


              //this Checks if the parameter already exists in the list and will either add it to the array or update it.
              let doesParamExist = this.reportParameters.some(parms => parms['name'] === newParam.name )
              if (!doesParamExist)
              {
                this.reportParameters.push(newParam);
              } else {
                let tmpindex = this.reportParameters.findIndex(i => i.name===newParam.name);
                this.reportParameters[tmpindex] = newParam;
              }
            }
          }
        }
      }
      if (this.runReportAtInit)
      {
        let tmpTriggerPanelHide: any = component.instance;
        tmpTriggerPanelHide.hideSearchPanel();
      }
    }

    //Add the USER_ID Parameter to each report if it hasn't been added.
    let doesUserIdExist = this.reportParameters.some(parms => parms['name'] === 'USER_ID' )
    if (!doesUserIdExist)
    {
      this.reportParameters.push(this.AddUserIdReportParam());
    }
    else
    {
      let paramIndexToUpdate = this.reportParameters.findIndex(i => i.name==="USER_ID");
      this.reportParameters[paramIndexToUpdate] = this.AddUserIdReportParam();
    }



    ssrsParamsAnDefaults.forEach(f => {
      let doesParamExist = this.reportParameters.some(parms => parms['name'] === f.name )
      if (doesParamExist)
      {
        let tmpindex = this.reportParameters.findIndex(i => i.name===f.name);

        if (f.nullable==false && this.reportParameters[tmpindex].values[0]=='' && (f.values!==undefined && f.values[0]!==''))
        {

        this.reportParameters[tmpindex].values = f.values;
        }
      }
      else
      {
        if (f.nullable==false && (f.values!==undefined && f.values[0]!==''))
        {

        this.reportParameters.push(f);
        }
      }
    });

    // Audit the report parameters
    this.auditReportParameters(this.reportParameters);

    return this.reportParameters;
  }

  private auditReportParameters(params: IReportParameter[]): void {
    // Reset as we are adding new parameters
    this.auditService.resetParamAudits();

    // Loop through each report parameter
    params.forEach(param => {
      if (Array.isArray(param.values)) {
        // If values is an array, loop through it
        param.values.forEach(value => {
          // Explicitly set save to false to avoid saving the audit
          this.auditService.addParamAudit(param.name, value, false);
        });
      } else {
        // If values is not an array, call AddParamAudit directly
        // Explicitly set save to false to avoid saving the audit
        this.auditService.addParamAudit(param.name, param.values, false);
      }
    });

    this.auditService.saveCurrentAudit();
  }

  //Addes a default USER_ID report param to the list of params always!
  private AddUserIdReportParam(): IReportParameter
  {
    let userId: IReportParameter = {
    name: "USER_ID",
    labels: ["USER_ID"],
    values: [this.userId],
    nullable: false
    };
    return userId;
  }

  //finds the Value of the associated report panel
  private findKeyByValue(data: IData[], valueString: string): number {
    // search the IData for the first object that has a value property that matches
    const foundData = data.find((d) => d.key.toString() === valueString);
    return foundData ? parseInt(foundData.value) : 0;
  }

  public buildParameters(
    paramName: string,
    paramLbl: any,
    paramVal: any = paramLbl
  ) {
    return (this.parameters = [
      {
        name: paramName,
        labels: paramLbl,
        values: paramVal,
        nullable: false,
      },
    ]);
  }

  //This Dynamically adds the specific report panel component based on what has been defined in the report definition
  private async CreateReportPanel(panel: ReportPanelTypes): Promise<any> {
    switch (panel) {
      case ReportPanelTypes.pnlPatientSearch:
        const { PnlPatientSearchComponent } = await import(
          '../panels/pnl-patient-search/pnl-patient-search.component'
        );
        this.panelComponent = PnlPatientSearchComponent;
        break;

      case ReportPanelTypes.pnlReportingYear:
        const { PnlReportingYearComponent } = await import(
          '../panels/pnl-reporting-year/pnl-reporting-year.component'
        );
        this.panelComponent = PnlReportingYearComponent;
        break;

      case ReportPanelTypes.pnlCohorts:
        const { PnlCohortsComponent } = await import(
          '../panels/pnl-cohorts/pnl-cohorts.component'
        );
        this.panelComponent = PnlCohortsComponent;
        break;

      case ReportPanelTypes.pnlCohortsReportingYear:
        const { PnlCohortsReportingYearComponent } = await import(
          '../panels/pnl-cohorts-reporting-year/pnl-cohorts-reporting-year.component'
        );
        this.panelComponent = PnlCohortsReportingYearComponent;
        break;

      case ReportPanelTypes.pnlCustomQuery:
        const { PnlCustomQueryComponent } = await import(
          '../panels/pnl-custom-query/pnl-custom-query.component'
        );
        this.panelComponent = PnlCustomQueryComponent;
        break;

      case ReportPanelTypes.pnlFromToDate:
        const { PnlFromToDateComponent } = await import(
          '../panels/pnl-from-to-date/pnl-from-to-date.component'
        );
        this.panelComponent = PnlFromToDateComponent;
        break;

      case ReportPanelTypes.pnlLstWeek:
        const { PnlLstWeekComponent } = await import(
          '../panels/pnl-lst-week/pnl-lst-week.component'
        );
        this.panelComponent = PnlLstWeekComponent;
        break;

      case ReportPanelTypes.pnlMultiReportingYear:
        const { PnlMultiReportingYearComponent } = await import(
          '../panels/pnl-multi-reporting-year/pnl-multi-reporting-year.component'
        );
        this.panelComponent = PnlMultiReportingYearComponent;
        break;

      case ReportPanelTypes.pnlRetention:
        const { PnlRetentionComponent } = await import(
          '../panels/pnl-retention/pnl-retention.component'
        );
        this.panelComponent = PnlRetentionComponent;
        break;

      case ReportPanelTypes.pnlDiseaseManagement:
        const { PnlDiseaseManagementComponent } = await import(
          '../panels/pnl-disease-management/pnl-disease-management.component'
        );
        this.panelComponent = PnlDiseaseManagementComponent;
        break;

      case ReportPanelTypes.pnlQrdaExtract:
        const { PnlQrdaExtractComponent: PnlQrdaExtractComponent } = await import(
          '../panels/pnl-qrda-extract/pnl-qrda-extract.component'
        );
        this.panelComponent = PnlQrdaExtractComponent;
        break;

        case ReportPanelTypes.pnlLocation:
        const { PnlLocationComponent } = await import(
          '../panels/pnl-location/pnl-location.component'
        );
        this.panelComponent = PnlLocationComponent;
        break;

        case ReportPanelTypes.pnlHcvLocation:
          const { PnlHcvLocationComponent } = await import(
            '../panels/pnl-hcv-location/pnl-hcv-location.component'
          );
          this.panelComponent = PnlHcvLocationComponent;
          break;

        case ReportPanelTypes.pnlUsageDates:
          const { PnlUsageDatesComponent } = await import(
            '../panels/pnl-usage-dates/pnl-usage-dates.component'
          );
          this.panelComponent = PnlUsageDatesComponent;
          break;

      default:
        const { PnlUserIdComponent } = await import(
          '../panels/pnl-user-id/pnl-user-id.component'
        );
        this.panelComponent = PnlUserIdComponent;
        break;
    }
    if (this.panelComponent) {
      const componentRef = this._panelContainer.createComponent(
        this.panelComponent
      );

      this.componentRefs.push(componentRef);
    }
  }

  // Parse the report parameters from the provided parameter data
  public ParseReportParams(paramData: any, queryUrlTree: UrlTree): IReportParameter[] {
    var rptParams = [] as IReportParameter[];

    let queryParams = queryUrlTree.queryParams;
    let queryParamKeys: string[] = [];
    if (queryParams!=null)
    {
      queryParamKeys = Object.keys(queryParams).filter(f=> f!="showpanel" && f!="expire");
    }

    // Check if there are parameters in the object if so loops through all parameters
    let paramsFromApi = paramData.Parameters.Parameter;
    if (!Array.isArray(paramsFromApi))
    {
      paramsFromApi = [paramsFromApi];
    }

    //If there are no parameters then return an empty array
    if (paramsFromApi[0] == null || paramsFromApi[0] == undefined)
    {
      return rptParams;
    }

    if (paramsFromApi) {
      for (let paramEntry of paramsFromApi) {
        let currentDefaultValue = '';
        let tmpParam = {} as IReportParameter;
        tmpParam.name = paramEntry.Name;
        tmpParam.nullable = paramEntry.Nullable.toLowerCase() === 'true'? true : false;
        //Check if the parameter has default values
        if (paramEntry.DefaultValues) {
          currentDefaultValue = paramEntry.DefaultValues.Value;
          tmpParam.values = [currentDefaultValue]
          tmpParam.labels = [currentDefaultValue];
        }

        if (queryParamKeys.includes(paramEntry.Name))
        {
          currentDefaultValue = decodeURIComponent(queryParams[paramEntry.Name]);
          tmpParam.values = this.epividianCommon.JSONtryParse(currentDefaultValue);
          tmpParam.labels = this.epividianCommon.JSONtryParse(currentDefaultValue);
        }

        // Add the current parameter to the list of report parameters
        rptParams.push(tmpParam);
      }
    }
    return rptParams;
  }

  closeRptPop()
  {
    //setTimeout()
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(ReportComponent);
    const componentRef = componentFactory.create(this.viewContainerRef.injector);
    if (componentRef && 'destroy' in componentRef) {
      this.viewContainerRef.insert(componentRef.hostView);
      componentRef.destroy();
    }

    this.dialog.closeAll();
  }

  isIData(data: any): data is IData {
    return (
      typeof data.groupId === "number" &&
      typeof data.key === "number" &&
      typeof data.value === "string"
    );
  }

  loadAnnotationPopup(url:string) {
    this.annotationService.annotationUrl=url;
    this.openDialog = this.dialog.open(AnnotationComponent);
  }

  // Panel toggle methods - mutually exclusive panels
  toggleReportNav(): void {
    if (this.reportNavCollapsed) {
      // If report nav is collapsed, expand it and collapse filters
      this.reportNavCollapsed = false;
      this.filtersCollapsed = true;
    } else {
      // If report nav is expanded, collapse it and expand filters
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }
  }

  toggleFilters(): void {
    if (this.filtersCollapsed) {
      // If filters are collapsed, expand them and collapse report nav
      this.filtersCollapsed = false;
      this.reportNavCollapsed = true;
    } else {
      // If filters are expanded, collapse them and expand report nav
      this.filtersCollapsed = true;
      this.reportNavCollapsed = false;
    }
  }

  // Handle current report name change from report navigation component
  onCurrentReportChanged(reportName: string): void {
    this.currentSelectedReport = reportName;
  }

  // Handle report selection event - automatically collapse the reports panel
  onReportSelected(): void {
    this.reportNavCollapsed = true;
    this.filtersCollapsed = false;
  }

  // Get the dynamic title for the Select Report panel
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }

}
