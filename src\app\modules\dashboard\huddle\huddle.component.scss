.huddleHeader {
    width: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    align-items: center;
   // border-bottom: 1px solid #d3d5da;
}

.huddleTitle{
    color: #0071BC;
    font-size: 2rem;
    font-weight: normal;
    font-family: Museo500-Regular;
    margin: 0;
}

.printButtonContainer {
    display: flex;
    align-items: center;
}

.printButton {
    min-width: auto;
    padding: 8px;
    color: #0071BC;
    line-height: 1;
    top: 35px;
    left: 30px;
}

.wrapper{
    display: flex;
    width: 100%;
}

.sidebar{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 90vh;
}

.filterWrapper{
    padding-bottom: 10px;
}

.details{
    width: 81%;
}
