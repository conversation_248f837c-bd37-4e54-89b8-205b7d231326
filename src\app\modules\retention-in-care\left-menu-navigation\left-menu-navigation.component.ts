import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { RetentionService } from '../retention.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Subscription, forkJoin } from 'rxjs';
import { NgxSpinnerService } from 'ngx-spinner';
import { RetentionCommunicationService } from '../retention-communication.service';

@Component({
  selector: 'left-menu-navigation',
  templateUrl: './left-menu-navigation.component.html',
  styleUrls: ['./left-menu-navigation.component.scss']
})

export class LeftMenuNavigationComponent implements OnInit, OnDestroy {
  outreachCallsData: any = [];
  administrativeActionItems: any = [];
  siteId: number = 0;

  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private retentionInCare: RetentionService,
    private communicationService: RetentionCommunicationService,
    public userContext: UserContext
  ) { }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
    
  }

  ngOnInit(): void {

    this.siteId = this.userContext.GetCurrentSiteValue()
    if (this.siteId != 0) {
      this.loadData(this.siteId.toString());
    }

    //Bind the site context to the page.
    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe((siteId: Number) => {
        this.loadData(siteId.toString());
      })
    );

    this.pageSubscriptions.add(
        this.communicationService.notifyObservable$.subscribe(() => {
        this.loadData(this.siteId.toString());
      })
    );
  }
  

  loadData(siteId: string)
  {
    // Create observables for your API calls
    const outreachCallObservable = this.retentionInCare.GetOutreachCallDetail(siteId);
    const adminActionObservable = this.retentionInCare.GetAdminActionDetails(siteId);

    //Combine the observables using forkJoin
    this.pageSubscriptions.add(
      forkJoin([outreachCallObservable, adminActionObservable]).subscribe(
        ([outreachCallResult, adminActionResult]) => {
          // Both API calls have completed here
          if (outreachCallResult) {
            this.outreachCallsData = outreachCallResult;
          }
          if (adminActionResult) {
            this.administrativeActionItems = adminActionResult;
          }
          
          // Turn off your spinner here
          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();    
        }
      )
    );
  }
}
