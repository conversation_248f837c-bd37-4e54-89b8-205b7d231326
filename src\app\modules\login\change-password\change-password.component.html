<div class="cover-container container-fluid">
  <!-- BEGIN: Chorus login header section -->
  <div class="header-row-1 row g-0 d-flex justify-content-xxl-start justify-content-xl-start justify-content-lg-start justify-content-md-start justify-content-sm-center justify-content-center">
    <div class="logo-container col-lg-3 col-md-3 col-sm-12"></div>
  </div>
<!-- END: Chorus login header section -->
  <div class="eForgotPasswordContent" >
    <label id="eResetPasswordMessage" class="eResetPasswordMessage">{{errorMsg}}</label>
    <div [hidden]="!isValidUser">       
      <div class="row g-0 mx-2">
      <div class="col-lg-9 col-md-9 col-sm-12">
          <div class="input-group">
            <input id="newPassword" autofocus [(ngModel)]="newPasswordInput" [type]="hidenew ? 'password' : 'text'" #newPassword tabindex="1" class="form-control" name="newPassword" placeholder="New Password">
     <!-- <i class="far" [ngClass]="hidenew?'fa-eye-slash':'fa-eye'" (click)="newpasswordEvent()" id="togglePassword" ></i> -->
          </div>
                  </div>
            </div>
            <div></div>
            <div class="row g-0 mx-2" style="padding-top: 10px;">
            <div class="col-lg-9 col-md-9 col-sm-12">
                <div class="input-group">
            <input id="confirmPassword" [(ngModel)]="confirmPasswordInput" [type]="hideconfirm ? 'password' : 'text'" (keyup)="onChangePassword(newPassword.value,confirmPassword.value)" #confirmPassword tabindex="2" class="form-control" name="confirmPassword" placeholder="Confirm Password">
            <button class="btn btn-light" tabindex="3" (click)="UpdatePasswordWithLink(newPassword.value,confirmPassword.value)"  type="submit"></button>
            <!-- <i class="far btn btn-light" [ngClass]="hideconfirm?'fa-eye-slash':'fa-eye'" (click)="onChangePassword(confirmPassword.value)" id="togglePassword" ></i>-->
                </div>
              </div>
            </div>
            <div class="heading1">
            <label>Your password must:</label>
            <ul>
              <li class="passwordLength">be at least 8 characters long</li>
              <li class="passwordAlphaNumeric">contain at least one lowercase letter, one uppercase letter, one number, and one symbol like (%, *, $, or !). Must not contain spaces.</li>
              <li class="passwordPrevious">not be identical to your previous password</li>
            </ul>
            </div>
            <div class="heading2">
            <label >For maximum security:</label>
            <ul>
              <li>choose long password that you don't use elsewhere</li>
              <li>avoid easy-to-guess personal information like pet names</li>
              <li>use a password manager rather than writing it down</li>
            </ul>
            </div>

    </div>
  </div>
</div>
