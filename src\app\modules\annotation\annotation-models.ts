export class AnnotationRequest {
    ProviderId: number=0;
    DemographicId: number=0;
    MeasureId: number=0;
    ReportingYear: number=0;
    MeasureAnnotateId: number=0;
    IntervalDesc: string='';
    VisitDate: string='';
}

export class AnnotationDetails {
    demographicProviderName: string="";
    patientMeasure: PatientMeasure=new PatientMeasure();
    annotate: MeasureAnnotate=new MeasureAnnotate();
    optionsByMeasure: MeasureAnnotateOption[]=[];
}

export class PatientMeasure {
    annotateId: number=0;
    patientId: number=0;
    providerId: number=0;
    measureId: number=0;
    reportingPeriod: number=0;
    measureDt: Date | null=null;
    intervalDesc?: any;
    openFlg: boolean=true;
    measureAnnotateId: number=0;
    annotateOptionId: number=0;
    annotateDescription: string='';
    annotateFreetext: string='';
    annotateTypeId: number=0;
    response?: any;
    createdDt: Date=new Date();
    modifiedUser: string='';
    validFromDt: Date=new Date();
    primaryMeasureId: number|null=null;
}

export class MeasureAnnotate {
    measureAnnotateId: number=0;
    controlTypeId: number=0;
    headerName: string='';
    detailedDescription: string='';
    createdDt: Date=new Date();
    modifiedUser: string='';
    validFromDt: Date=new Date();
}

export class MeasureAnnotateOption {
    annotateOptionId: number=0;
    measureAnnotateId: number=0;
    optionDescription: string='';
    optionFreetextTitle?: any;
    ordinal: number=0;
    optionalFlg: boolean=false;
    createdDt: Date=new Date();
    modifiedUser: string='';
    validFromDt: Date=new Date();
    defaultFlg: boolean=false;
    checked:boolean=false;
}

export enum ControlType {
    RadioButton = 1,
    CheckBox = 2,
    TextBox = 3
}