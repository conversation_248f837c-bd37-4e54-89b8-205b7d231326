export interface Rule {
  ruleID: number;
  name: string;
  description: string;
  type: string;
  version: string;
  action: string;
  jsonData: string;
  createdDate: Date;
  modifiedDate: Date;
}

export interface Workflow {
  workflowID: number;
  name: string;
  description: string;
  type: string;
  createdDate: Date;
  modifiedDate: Date;
}

export interface WorkflowRule {
  workflowId: number;
  ruleId: number;
  orderNo: number;
  dependsOnRuleId?: number;
}

export interface WorkflowRuleDetail {
  workflowRuleID: number;
  workflowID: number;
  ruleID: number;
  orderNo: number;
  dependencyRules: number[];
  rule: Rule;
}

export interface RuleDetail {
  RuleID: number;
  Name: string;
  Description: string;
  Type: string;
  Version: string;
  Action: string;
  JsonData: string | null;
  CreatedDate: string;
  ModifiedDate: string;
}

export interface ExecutorTransaction {
  ExecutionLogId: number;
  RuleId: number | null;
  WorkFlowProcessId: string;
  WorkFlowId: number | null;
  RuleInputJson: string;
  ExecutionResult: string | null;
  ExecutionException: any | null;
  StartExecution: string;
  EndExecution: string | null;
  Success: boolean | null;
  RunBy: string;
}

export interface WorkflowRuleDependency {
  DependencyRuleId: number | null;
  RuleId: number;
  SequenceOrder: number;
  Status: number;
}

export interface WorkflowStatusDetail {
  workFlowProcessId: string;
  userName: string | null;
  workFlowId: number;
  startTime: string;
  endTime: string | null;
  RuleDetails: RuleDetail[];
  workFlowParams: any | null;
  executorTransactions: ExecutorTransaction[];
  workFlowRuleDependencies: WorkflowRuleDependency[];
}

export interface WorkflowStatus {
  WorkFlowId: number;
  Guid: string;
  Status: WorkflowStatusDetail;

  // Legacy properties for backward compatibility
  id?: string;
  status?: string;
  progress?: number;
  completedRules?: number;
  totalRules?: number;
  currentRule?: string;
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

export interface RuleExecutionResult {
  ruleId: number;
  ruleName: string;
  success: boolean;
  data: any;
  executionTime: Date;
  error?: string;
}

export interface RuleType {
  id: string;
  name: string;
  description: string;
  schema?: any;
}

export interface WorkflowProcess {
  // Original properties
  workflowProcessId?: string;
  workFlowProcessId?: string;  // Alternative property name with capital F from API
  workflowId?: number;
  workFlowId?: number;         // Alternative property name with capital F from API
  workflowName?: string;
  startTime?: Date;            // For backward compatibility
  endTime?: Date;              // For backward compatibility
  firstExecutionTime?: Date;
  lastExecutionTime?: Date;
  status?: string;
  isComplete?: boolean;
  isSuccess?: boolean;
  error?: string;
  ruleResults?: RuleExecutionResult[];
  metadata?: { [key: string]: any };

  // Additional properties from API response
  success?: boolean;           // API returns success instead of isSuccess
  transactionCount?: number;   // Number of transactions in the workflow
  runBy?: string;              // User who ran the workflow
}
