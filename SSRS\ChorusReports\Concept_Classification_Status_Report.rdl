﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.6in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.48in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.4in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox37">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Medication</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>MED_COUNT</Label>
                              <Value>= "(" &amp; CStr(FormatNumber(Sum(Fields!MED_NUM_RECORDS.Value, "MED_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox37</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Service</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>SER_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!SER_NUM_RECORDS.Value, "SER_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox8">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>DX</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>DX_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!DX_NUM_RECORDS.Value, "DX_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox8</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox11">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Lab</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>LAB_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!LAB_NUM_RECORDS.Value, "LAB_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox11</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox30">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Ins</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>INS_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!INS_NUM_RECORDS.Value, "INS_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox30</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox32">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Vax</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>VAX_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!VAX_NUM_RECORDS.Value, "VAX_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox32</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>LightGrey</Color>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Style>None</Style>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox35">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>HX</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Label>HX_COUNT</Label>
                              <Value>="(" &amp; CStr(FormatNumber(Sum(Fields!HX_NUM_RECORDS.Value, "HX_COUNT"),0)) &amp; ")"</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <Color>White</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox35</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                        </TopBorder>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                        </RightBorder>
                        <BackgroundColor>Black</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox12</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox24">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox24</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox28">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox28</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox34">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox34</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox38">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox38</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox44">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox44</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox48">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox48</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox54">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox54</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox58">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox58</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox64">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox64</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox68">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox68</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox74">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ID</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox74</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Style>Solid</Style>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox78">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>C</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox78</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>1pt</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>LightBlue</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MED_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MED_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MED_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!MED_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MED_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MED_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>#,0;(#,0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MED_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PROC_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!PROC_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PROC_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!PROC_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PROC_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!PROC_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>#,0;(#,0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PROC_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DX_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DX_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DX_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!DX_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DX_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DX_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>0;(0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DX_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="LAB_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!LAB_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>LAB_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!LAB_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="LAB_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!LAB_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>0;(0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>LAB_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="INS_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!INS_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>INS_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!INS_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="INS_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!INS_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>0;(0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>INS_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="VAX_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!VAX_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>VAX_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!VAX_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="VAX_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!VAX_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>#,0;(#,0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>VAX_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="HX_CLASS_ID">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!HX_CLASS_ID.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>HX_CLASS_ID</rd:DefaultName>
                      <ToolTip>=Fields!HX_GROUP_NM.Value</ToolTip>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Width>1.5pt</Width>
                        </LeftBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="HX_C">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!HX_C.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>#,0;(#,0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>HX_C</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>1.5pt</Width>
                        </RightBorder>
                        <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>4.00805in</Size>
                <CellContents>
                  <Textbox Name="Textbox336">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value />
                            <Style>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox336</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <TopBorder>
                        <Style>None</Style>
                      </TopBorder>
                      <LeftBorder>
                        <Style>None</Style>
                      </LeftBorder>
                      <BackgroundColor>White</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>1in</Size>
                    <CellContents>
                      <Textbox Name="Textbox2">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Objects</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox2</rd:DefaultName>
                        <ToolTip>Click + on the concept name to see the objects.</ToolTip>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Style>None</Style>
                          </TopBorder>
                          <LeftBorder>
                            <Color>Black</Color>
                            <Style>None</Style>
                          </LeftBorder>
                          <BackgroundColor>Black</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>4.00805in</Size>
                <CellContents>
                  <Textbox Name="Textbox337">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Epividian Concepts</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>15pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox337</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <RightBorder>
                        <Width>1.5pt</Width>
                      </RightBorder>
                      <BackgroundColor>LightBlue</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>1in</Size>
                    <CellContents>
                      <Textbox Name="Textbox3">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value />
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>15pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox3</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <LeftBorder>
                            <Width>1.5pt</Width>
                          </LeftBorder>
                          <RightBorder>
                            <Width>1.5pt</Width>
                          </RightBorder>
                          <BackgroundColor>LightBlue</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <Group Name="CONCEPT_NM">
                <GroupExpressions>
                  <GroupExpression>=Fields!CONCEPT_NM.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!CONCEPT_NM.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>4.00805in</Size>
                <CellContents>
                  <Textbox Name="CONCEPT_NM1">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Fields!CONCEPT_NM.Value</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                          <TextRun>
                            <Value xml:space="preserve">  </Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Right</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>CONCEPT_NM1</rd:DefaultName>
                    <ToolTip>Click + on the concept name to see the objects.</ToolTip>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <LeftBorder>
                        <Style>None</Style>
                      </LeftBorder>
                      <RightBorder>
                        <Width>1.5pt</Width>
                      </RightBorder>
                      <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>1pt</PaddingLeft>
                      <PaddingRight>4pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>1in</Size>
                    <CellContents>
                      <Rectangle Name="Rectangle1">
                        <ReportItems>
                          <Subreport Name="Subreport1">
                            <ReportName>Sub_Category_Report</ReportName>
                            <Parameters>
                              <Parameter Name="CONCEPT_ID">
                                <Value>=Fields!CONCEPT_ID.Value</Value>
                              </Parameter>
                              <Parameter Name="COLOR">
                                <Value>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</Value>
                              </Parameter>
                            </Parameters>
                            <Height>0.25in</Height>
                            <Width>1in</Width>
                            <Visibility>
                              <Hidden>true</Hidden>
                              <ToggleItem>CONCEPT_NM1</ToggleItem>
                            </Visibility>
                            <Style>
                              <Border>
                                <Color>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</Color>
                                <Style>None</Style>
                              </Border>
                              <LeftBorder>
                                <Width>1.5pt</Width>
                              </LeftBorder>
                              <RightBorder>
                                <Width>1.5pt</Width>
                              </RightBorder>
                            </Style>
                          </Subreport>
                        </ReportItems>
                        <KeepTogether>true</KeepTogether>
                        <ZIndex>4</ZIndex>
                        <ToolTip>Click + on the concept name to see the objects.</ToolTip>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>=IIF(RunningValue(Fields!CONCEPT_NM.Value, CountDistinct, Nothing) MOD 2 = 1,"Silver", "White")</BackgroundColor>
                        </Style>
                      </Rectangle>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="CONCEPT_DESC">
                        <GroupExpressions>
                          <GroupExpression>=Fields!CONCEPT_DESC.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!CONCEPT_DESC.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="Details1" />
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <RepeatRowHeaders>true</RepeatRowHeaders>
        <DataSetName>REPORT_DETAIL</DataSetName>
        <Height>0.9in</Height>
        <Width>12.56805in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.15in</Height>
    <Style>
      <Border>
        <Style>Solid</Style>
      </Border>
    </Style>
  </Body>
  <Width>12.56805in</Width>
  <Page>
    <PageHeader>
      <Height>3.46093in</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="DX_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>DX_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.155in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>DX_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.44667in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>DX_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.76611in</Top>
          <Left>1.76541in</Left>
          <Height>0.23611in</Height>
          <Width>1.08333in</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>DX_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.08556in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <ZIndex>3</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox13">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Problems</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.125in</Width>
          <ZIndex>4</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line3">
          <Top>1.16889in</Top>
          <Left>2.89041in</Left>
          <Height>2.1213in</Height>
          <Width>0in</Width>
          <ZIndex>5</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Textbox Name="Textbox14">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Added Last Month: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>1.155in</Top>
          <Left>0.01666in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>6</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox15">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Added Last Week: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>1.46056in</Top>
          <Left>0.01666in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>7</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox16">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Classified Last Month: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>1.76611in</Top>
          <Left>0.01666in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>8</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox18">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Classified Last Week: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>2.08186in</Top>
          <Left>0.01666in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>9</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>HX_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.16889in</Top>
          <Left>2.93208in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>10</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>HX_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.46056in</Top>
          <Left>2.93208in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>11</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox19">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>History</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>2.9043in</Left>
          <Height>0.25in</Height>
          <Width>1.28979in</Width>
          <ZIndex>12</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>HX_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.75222in</Top>
          <Left>2.93208in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>13</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>HX_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.08186in</Top>
          <Left>2.93208in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>14</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line4">
          <Top>1.16889in</Top>
          <Left>4.19409in</Left>
          <Height>2.1213in</Height>
          <Width>0in</Width>
          <ZIndex>15</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Textbox Name="Textbox20">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Medication</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>4.20798in</Left>
          <Height>0.25in</Height>
          <Width>1.34028in</Width>
          <ZIndex>16</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line5">
          <Top>1.16889in</Top>
          <Left>5.54826in</Left>
          <Height>2.1213in</Height>
          <Width>0in</Width>
          <ZIndex>17</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Textbox Name="Textbox21">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Lab Results</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>5.56215in</Left>
          <Height>0.25in</Height>
          <Width>1.38542in</Width>
          <ZIndex>18</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line6">
          <Top>1.155in</Top>
          <Left>6.93368in</Left>
          <Height>2.13519in</Height>
          <Width>0in</Width>
          <ZIndex>19</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Textbox Name="Textbox22">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Immunization</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>6.96146in</Left>
          <Height>0.25in</Height>
          <Width>1.34813in</Width>
          <ZIndex>20</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line7">
          <Top>1.155in</Top>
          <Left>8.29569in</Left>
          <Height>2.13519in</Height>
          <Width>0in</Width>
          <ZIndex>21</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Textbox Name="Textbox23">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Insurance</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>0.84597in</Top>
          <Left>8.32348in</Left>
          <Height>0.25in</Height>
          <Width>1.31609in</Width>
          <ZIndex>22</ZIndex>
          <Style>
            <Border>
              <Color>LightBlue</Color>
              <Style>Solid</Style>
            </Border>
            <BackgroundColor>LightBlue</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>MED_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.16889in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>23</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>MED_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.44667in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>24</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>MED_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.73834in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>25</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>LAB_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.16889in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>26</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>LAB_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.75222in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>27</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>VAX_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.16889in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.26479in</Width>
          <ZIndex>28</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>VAX_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.75222in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.26479in</Width>
          <ZIndex>29</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_ADDED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_ADDED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>INS_ADDED_LASTMONTH</rd:DefaultName>
          <Top>1.16889in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>30</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_CLASSIFIED_LASTMONTH">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_CLASSIFIED_LASTMONTH.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>INS_CLASSIFIED_LASTMONTH</rd:DefaultName>
          <Top>1.75222in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>31</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox51">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Date of Report:  Through</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox51</rd:DefaultName>
          <Top>0.11984in</Top>
          <Left>8.91397in</Left>
          <Height>0.22677in</Height>
          <Width>1.51054in</Width>
          <ZIndex>32</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>LAB_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.46055in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>33</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>VAX_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.46055in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.26479in</Width>
          <ZIndex>34</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_ADDED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_ADDED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>INS_ADDED_LASTWEEK</rd:DefaultName>
          <Top>1.46055in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>35</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>INS_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.07166in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>36</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>VAX_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.08556in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.26479in</Width>
          <ZIndex>37</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>LAB_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.08556in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>38</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_CLASSIFIED_LASTWEEK">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_CLASSIFIED_LASTWEEK.Value, "CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>MED_CLASSIFIED_LASTWEEK</rd:DefaultName>
          <Top>2.08186in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>39</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox52">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=DateAdd(DateInterval.Day, 1-WeekDay(Today), Today)</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>d</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox52</rd:DefaultName>
          <Top>0.40217in</Top>
          <Left>9.42451in</Left>
          <Height>0.20391in</Height>
          <Width>1in</Width>
          <ZIndex>40</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox7">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Concept Classification Status</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>18pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value />
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>18pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox6</rd:DefaultName>
          <Top>0.10815in</Top>
          <Left>1.14827in</Left>
          <Height>0.39173in</Height>
          <Width>7.72403in</Width>
          <ZIndex>41</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox25">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Total Classified: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>2.4013in</Top>
          <Left>0.01667in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>42</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox26">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Total Unclassified: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>2.72075in</Top>
          <Left>0.01667in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>43</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox27">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Total Not Followed: </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>3.07204in</Top>
          <Left>0.01667in</Left>
          <Height>0.25in</Height>
          <Width>1.62014in</Width>
          <ZIndex>44</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.4013in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <ZIndex>45</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.4013in</Top>
          <Left>2.91818in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>46</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.38742in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>47</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.39112in</Top>
          <Left>5.57603in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>48</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.405in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.26479in</Width>
          <ZIndex>49</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_CLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_CLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.405in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>50</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72075in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <ZIndex>51</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72075in</Top>
          <Left>2.93208in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>52</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72075in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>53</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72075in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>54</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72445in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.2898in</Width>
          <ZIndex>55</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_UNCLASSIFIED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_UNCLASSIFIED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>2.72445in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>56</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BackgroundColor>LightGrey</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="DX_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!DX_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04019in</Top>
          <Left>1.76541in</Left>
          <Height>0.25in</Height>
          <Width>1.08333in</Width>
          <ZIndex>57</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="HX_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!HX_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04019in</Top>
          <Left>2.94597in</Left>
          <Height>0.25in</Height>
          <Width>1.22035in</Width>
          <ZIndex>58</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="MED_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!MED_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04019in</Top>
          <Left>4.24964in</Left>
          <Height>0.25in</Height>
          <Width>1.25695in</Width>
          <ZIndex>59</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="LAB_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!LAB_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04019in</Top>
          <Left>5.57604in</Left>
          <Height>0.25in</Height>
          <Width>1.32986in</Width>
          <ZIndex>60</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="VAX_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!VAX_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04389in</Top>
          <Left>6.98923in</Left>
          <Height>0.25in</Height>
          <Width>1.2898in</Width>
          <ZIndex>61</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="INS_NOTFOLLOWED_TOTAL">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=Sum(Fields!INS_NOT_FOLLOWED.Value, "ADDL_CLASSIFIED_TABLE")</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <Format>#,0;(#,0)</Format>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <Top>3.04389in</Top>
          <Left>8.33735in</Left>
          <Height>0.25in</Height>
          <Width>1.30222in</Width>
          <ZIndex>62</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line1">
          <Top>3.35in</Top>
          <Height>0in</Height>
          <Width>12.56805in</Width>
          <ZIndex>63</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
        <Line Name="Line2">
          <Top>3.38in</Top>
          <Height>0in</Height>
          <Width>12.56805in</Width>
          <ZIndex>64</ZIndex>
          <Style>
            <Border>
              <Color>DimGray</Color>
              <Style>Solid</Style>
            </Border>
          </Style>
        </Line>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>8.5in</PageHeight>
    <PageWidth>11in</PageWidth>
    <InteractiveHeight>0in</InteractiveHeight>
    <InteractiveWidth>8.5in</InteractiveWidth>
    <LeftMargin>0.25in</LeftMargin>
    <RightMargin>0.25in</RightMargin>
    <TopMargin>0.25in</TopMargin>
    <BottomMargin>0.25in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="CLASSIFIED_TABLE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>WITH CTE AS (
SELECT 'MED' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND CLASS_CD = 3 THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND CLASS_CD = 3 THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[MEDICATION_PACKAGE]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL

SELECT 'DX' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND CLASS_CD = 3 THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND CLASS_CD = 3 THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[DISEASE_MEM]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL

SELECT 'PROC' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND SERVICE_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND SERVICE_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[SERVICE]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL

SELECT 'LAB' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND LAB_CODE_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND LAB_CODE_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[LAB_TITLES]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL

SELECT 'INS' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND INS_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND INS_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[INSURANCE]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL

SELECT 'VAX' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND IMMNZTN_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND IMMNZTN_GRP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[IMMUNIZATION]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

UNION ALL 

SELECT 'HX' AS TYPE_RECORDS, 
   	CASE 
		WHEN CAST(CREATED_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) THEN 'Y'
		ELSE 'N'
	END AS ADDED_WEEKLY,
	CASE 
		WHEN (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) THEN 'Y'
		ELSE 'N'
	END AS ADDED_MONTHLY,  
	CASE 
		WHEN CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AND HISTORY_GROUP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_WEEKLY,
	CASE 
		WHEN (CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND HISTORY_GROUP_ID NOT IN (12,13) THEN 'Y'
		ELSE 'N'
	END AS CLASSIFIED_ADDED_MONTHLY
FROM [STUDY].[HISTORY_GROUP_HISTORY_ASC]
WHERE (CAST(CREATED_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) OR 
			(CAST(VALID_FROM_DT AS DATE) BETWEEN DATEADD(M, -1, DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6)) AND DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6))

) 
SELECT
      ISNULL(DX_ADDED_LASTMONTH,0) DX_ADDED_LASTMONTH,
      ISNULL(DX_ADDED_LASTWEEK,0) DX_ADDED_LASTWEEK,
      ISNULL(DX_CLASSIFIED_LASTMONTH,0) DX_CLASSIFIED_LASTMONTH,
      ISNULL(DX_CLASSIFIED_LASTWEEK,0) DX_CLASSIFIED_LASTWEEK,
      ISNULL(HX_ADDED_LASTMONTH,0) HX_ADDED_LASTMONTH,
      ISNULL(HX_ADDED_LASTWEEK,0) HX_ADDED_LASTWEEK,
      ISNULL(HX_CLASSIFIED_LASTMONTH,0) HX_CLASSIFIED_LASTMONTH,
      ISNULL(HX_CLASSIFIED_LASTWEEK,0) HX_CLASSIFIED_LASTWEEK,
      ISNULL(INS_ADDED_LASTMONTH,0) INS_ADDED_LASTMONTH,
      ISNULL(INS_ADDED_LASTWEEK,0) INS_ADDED_LASTWEEK,
      ISNULL(INS_CLASSIFIED_LASTMONTH,0) INS_CLASSIFIED_LASTMONTH,
      ISNULL(INS_CLASSIFIED_LASTWEEK,0) INS_CLASSIFIED_LASTWEEK,
      ISNULL(LAB_ADDED_LASTMONTH,0) LAB_ADDED_LASTMONTH,
      ISNULL(LAB_ADDED_LASTWEEK,0) LAB_ADDED_LASTWEEK,
      ISNULL(LAB_CLASSIFIED_LASTMONTH,0) LAB_CLASSIFIED_LASTMONTH,
      ISNULL(LAB_CLASSIFIED_LASTWEEK,0) LAB_CLASSIFIED_LASTWEEK,
      ISNULL(MED_ADDED_LASTMONTH,0) MED_ADDED_LASTMONTH,
      ISNULL(MED_ADDED_LASTWEEK,0) MED_ADDED_LASTWEEK,
      ISNULL(MED_CLASSIFIED_LASTMONTH,0) MED_CLASSIFIED_LASTMONTH,
      ISNULL(MED_CLASSIFIED_LASTWEEK,0) MED_CLASSIFIED_LASTWEEK,
      ISNULL(PROC_ADDED_LASTMONTH,0) PROC_ADDED_LASTMONTH,
      ISNULL(PROC_ADDED_LASTWEEK,0) PROC_ADDED_LASTWEEK,
      ISNULL(PROC_CLASSIFIED_LASTMONTH,0) PROC_CLASSIFIED_LASTMONTH,
      ISNULL(PROC_CLASSIFIED_LASTWEEK,0) PROC_CLASSIFIED_LASTWEEK,
      ISNULL(VAX_ADDED_LASTMONTH,0) VAX_ADDED_LASTMONTH,
      ISNULL(VAX_ADDED_LASTWEEK,0) VAX_ADDED_LASTWEEK,
      ISNULL(VAX_CLASSIFIED_LASTMONTH,0) VAX_CLASSIFIED_LASTMONTH,
      ISNULL(VAX_CLASSIFIED_LASTWEEK ,0) VAX_CLASSIFIED_LASTWEEK 
FROM (	     
SELECT 
	  TYPE_RECORDS + '_' + RECORD AS COLUMN_NM,
	  CLASSIFIED	  
FROM
(SELECT TYPE_RECORDS, 
    SUM(IIF(ADDED_WEEKLY = 'Y', 1, 0)) AS ADDED_LASTWEEK, 
	SUM(IIF(CLASSIFIED_ADDED_WEEKLY = 'Y', 1, 0)) AS CLASSIFIED_LASTWEEK, 
	SUM(IIF(ADDED_MONTHLY = 'Y', 1, 0)) AS ADDED_LASTMONTH, 
	SUM(IIF(CLASSIFIED_ADDED_MONTHLY = 'Y', 1, 0)) AS CLASSIFIED_LASTMONTH
FROM CTE
GROUP BY TYPE_RECORDS) up
UNPIVOT
(CLASSIFIED FOR RECORD IN (ADDED_LASTWEEK, CLASSIFIED_LASTWEEK, ADDED_LASTMONTH, CLASSIFIED_LASTMONTH)) AS UNPVT) P
PIVOT
(MAX(CLASSIFIED)
FOR COLUMN_NM IN ([DX_ADDED_LASTMONTH],
      [DX_ADDED_LASTWEEK],
      [DX_CLASSIFIED_LASTMONTH],
      [DX_CLASSIFIED_LASTWEEK],
      [HX_ADDED_LASTMONTH],
      [HX_ADDED_LASTWEEK],
      [HX_CLASSIFIED_LASTMONTH],
      [HX_CLASSIFIED_LASTWEEK],
      [INS_ADDED_LASTMONTH],
      [INS_ADDED_LASTWEEK],
      [INS_CLASSIFIED_LASTMONTH],
      [INS_CLASSIFIED_LASTWEEK],
      [LAB_ADDED_LASTMONTH],
      [LAB_ADDED_LASTWEEK],
      [LAB_CLASSIFIED_LASTMONTH],
      [LAB_CLASSIFIED_LASTWEEK],
      [MED_ADDED_LASTMONTH],
      [MED_ADDED_LASTWEEK],
      [MED_CLASSIFIED_LASTMONTH],
      [MED_CLASSIFIED_LASTWEEK],
      [PROC_ADDED_LASTMONTH],
      [PROC_ADDED_LASTWEEK],
      [PROC_CLASSIFIED_LASTMONTH],
      [PROC_CLASSIFIED_LASTWEEK],
      [VAX_ADDED_LASTMONTH],
      [VAX_ADDED_LASTWEEK],
      [VAX_CLASSIFIED_LASTMONTH],
      [VAX_CLASSIFIED_LASTWEEK]))PVT </CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="DX_ADDED_LASTMONTH">
          <DataField>DX_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_ADDED_LASTWEEK">
          <DataField>DX_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_CLASSIFIED_LASTMONTH">
          <DataField>DX_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_CLASSIFIED_LASTWEEK">
          <DataField>DX_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_ADDED_LASTMONTH">
          <DataField>HX_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_ADDED_LASTWEEK">
          <DataField>HX_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_CLASSIFIED_LASTMONTH">
          <DataField>HX_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_CLASSIFIED_LASTWEEK">
          <DataField>HX_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_ADDED_LASTMONTH">
          <DataField>INS_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_ADDED_LASTWEEK">
          <DataField>INS_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_CLASSIFIED_LASTMONTH">
          <DataField>INS_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_CLASSIFIED_LASTWEEK">
          <DataField>INS_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_ADDED_LASTMONTH">
          <DataField>LAB_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_ADDED_LASTWEEK">
          <DataField>LAB_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_CLASSIFIED_LASTMONTH">
          <DataField>LAB_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_CLASSIFIED_LASTWEEK">
          <DataField>LAB_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_ADDED_LASTMONTH">
          <DataField>MED_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_ADDED_LASTWEEK">
          <DataField>MED_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_CLASSIFIED_LASTMONTH">
          <DataField>MED_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_CLASSIFIED_LASTWEEK">
          <DataField>MED_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_ADDED_LASTMONTH">
          <DataField>PROC_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_ADDED_LASTWEEK">
          <DataField>PROC_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_CLASSIFIED_LASTMONTH">
          <DataField>PROC_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_CLASSIFIED_LASTWEEK">
          <DataField>PROC_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_ADDED_LASTMONTH">
          <DataField>VAX_ADDED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_ADDED_LASTWEEK">
          <DataField>VAX_ADDED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_CLASSIFIED_LASTMONTH">
          <DataField>VAX_CLASSIFIED_LASTMONTH</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_CLASSIFIED_LASTWEEK">
          <DataField>VAX_CLASSIFIED_LASTWEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="REPORT_DETAIL">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>WITH BASE AS (
SELECT
      CONCEPT_NM, 
	  CONCEPT_DESC,
	  CONCEPT_ID,
	  ROW_NUM,
	  [MED],
	  [PROC],
	  [DX],
	  [HX],
	  NULL AS [INS],
	  [VAX],
	  [LAB]
FROM
    (SELECT 
	      CONCEPT_NM, CONCEPT_DESC, CONCEPT_ID,
		  ROW_NUM,
		  CATEGORY_TYPE_CD,
		  CATEGORY_ID
	FROM 
	    (SELECT CON.CONCEPT_NM, CON.CONCEPT_DESC,CON.CONCEPT_ID, CCA.CATEGORY_TYPE_CD, CCA.CATEGORY_ID, 
			ROW_NUMBER() OVER (PARTITION BY CCA.CONCEPT_ID, CCA.CATEGORY_TYPE_CD ORDER BY CCA.CATEGORY_ID) ROW_NUM
		FROM [MDM].[CONCEPT] CON
		LEFT OUTER JOIN [MDM].[CONCEPT_CATEGORY_ASC] CCA ON CCA.CONCEPT_ID = CON.CONCEPT_ID) D) P
PIVOT
(MAX(CATEGORY_ID)
FOR CATEGORY_TYPE_CD IN ([MED],[PROC],[DX],[HX],[VAX],[LAB]))
AS PVT
)
, MED AS (
SELECT 'MED' AS CATEGORY, MP.BRND_ID, MB.BRND_NM, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(CLASS_CD = 3, 1, 0)) AS CLASSIFIED, 
	SUM(IIF(CLASS_CD = 2, 1, 0)) AS UNCLASSIFIED, SUM(IIF(CLASS_CD = 1, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[MEDICATION_PACKAGE] MP
INNER JOIN [STUDY].[MEDICATION_BRAND] MB ON (MP.BRND_ID = MB.BRND_ID)
GROUP BY MP.BRND_ID,MB.BRND_NM
)
, [PROC] AS (
SELECT 'PROC' AS CATEGORY, S.SERVICE_GRP_ID, SG.SERVICE_GRP_NAME, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(S.SERVICE_GRP_ID NOT IN (12,13), 1, 0)) AS CLASSIFIED, 
	SUM(IIF(S.SERVICE_GRP_ID = 13, 1, 0)) AS UNCLASSIFIED, SUM(IIF(S.SERVICE_GRP_ID = 12, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[SERVICE] S
INNER JOIN [STUDY].[SERVICE_GROUP] SG ON (S.SERVICE_GRP_ID = SG.SERVICE_GRP_ID)
GROUP BY S.SERVICE_GRP_ID,SG.SERVICE_GRP_NAME
)
, DX AS (
SELECT 'DX' AS CATEGORY, DM.DISEASE_ID, D.DISEASE_NM, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(CLASS_CD = 3, 1, 0)) AS CLASSIFIED, 
	SUM(IIF(CLASS_CD = 2, 1, 0)) AS UNCLASSIFIED, SUM(IIF(CLASS_CD = 1, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[DISEASE_MEM] DM
INNER JOIN [STUDY].[DISEASE] D ON (DM.DISEASE_ID = D.DISEASE_ID)
GROUP BY DM.DISEASE_ID, D.DISEASE_NM

UNION

SELECT 'DX' AS CATEGORY, A.DISEASE_GRP_ID AS DISEASE_ID, A.DISEASE_GRP_NM AS DISEASE_NM, COUNT (B.DISEASE_ID) AS TOTAL_RECORDS, 
	SUM(IIF(C.CLASS_CD = 3, 1, 0)) AS CLASSIFIED, SUM(IIF(C.CLASS_CD = 2, 1, 0)) AS UNCLASSIFIED, SUM(IIF(C.CLASS_CD = 1, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[DISEASE_GROUP] A
LEFT JOIN [STUDY].[DISEASE] B ON A.DISEASE_GRP_ID = B.DISEASE_GRP_ID 
LEFT JOIN [STUDY].[DISEASE_MEM] C ON C.DISEASE_ID = B.DISEASE_ID
GROUP BY A.DISEASE_GRP_ID, A.DISEASE_GRP_NM
)
---labs---
, PRIM_LAB AS (
SELECT LAB_CODE_ID, LAB_CODE_DESC
FROM [STUDY].[LAB_CODES]
WHERE PARENT_LAB_CODE_ID IS NULL OR LAB_CODE_ID IN (12,13)
)
, SEC_LAB AS (
SELECT LAB_CODE_ID, LAB_CODE_DESC, PARENT_LAB_CODE_ID AS PRIM_LAB_GROUP_ID
FROM [STUDY].[LAB_CODES] L
WHERE EXISTS (SELECT LAB_CODE_ID
				FROM PRIM_LAB P
				WHERE P.LAB_CODE_ID = L.PARENT_LAB_CODE_ID)
AND L.LAB_CODE_ID NOT IN (12,13)
)
,THIRD_LAB AS (
SELECT LAB_CODE_ID, LAB_CODE_DESC, PARENT_LAB_CODE_ID AS SEC_LAB_GROUP_ID
FROM [STUDY].[LAB_CODES] L
WHERE EXISTS (SELECT 1
					FROM SEC_LAB S
					WHERE S.LAB_CODE_ID = L.PARENT_LAB_CODE_ID)
)
,FOURTH_LAB AS (
SELECT LAB_CODE_ID, LAB_CODE_DESC, PARENT_LAB_CODE_ID AS THIRD_LAB_GROUP_ID
FROM [STUDY].[LAB_CODES] L
WHERE EXISTS (SELECT 1
				FROM THIRD_LAB T
				WHERE T.LAB_CODE_ID = L.PARENT_LAB_CODE_ID)
)
,FIFTH_LAB AS (
SELECT LAB_CODE_ID, LAB_CODE_DESC, PARENT_LAB_CODE_ID AS FOURTH_LAB_GROUP_ID
FROM [STUDY].[LAB_CODES] L
WHERE EXISTS (SELECT 1
				FROM FOURTH_LAB T
				WHERE T.LAB_CODE_ID = L.PARENT_LAB_CODE_ID)
)
, FINAL_LAB AS (
SELECT  L.LAB_CODE_ID, FL.FOURTH_LAB_GROUP_ID, 
	CASE 
		WHEN F.THIRD_LAB_GROUP_ID IS NULL THEN F2.THIRD_LAB_GROUP_ID 
		ELSE F.THIRD_LAB_GROUP_ID 
	END AS THIRD_LAB_GROUP_ID,
	CASE 
		WHEN T.SEC_LAB_GROUP_ID IS NULL AND T2.SEC_LAB_GROUP_ID IS NULL THEN T3.SEC_LAB_GROUP_ID
		WHEN T.SEC_LAB_GROUP_ID IS NULL THEN T2.SEC_LAB_GROUP_ID
		ELSE T.SEC_LAB_GROUP_ID
		END AS SEC_LAB_GROUP_ID,
	CASE 
		WHEN L.LAB_CODE_ID IN (12,13) THEN L.LAB_CODE_ID
		WHEN S3.PRIM_LAB_GROUP_ID IS NULL AND S2.PRIM_LAB_GROUP_ID  IS NULL AND S.PRIM_LAB_GROUP_ID IS NULL THEN  S4.PRIM_LAB_GROUP_ID
		WHEN S2.PRIM_LAB_GROUP_ID IS NULL AND S.PRIM_LAB_GROUP_ID IS NULL THEN  S3.PRIM_LAB_GROUP_ID
		WHEN S.PRIM_LAB_GROUP_ID IS NULL THEN S2.PRIM_LAB_GROUP_ID
		--WHEN L.LAB_CODE_ID IN (12,13) THEN L.LAB_CODE_ID
		ELSE S.PRIM_LAB_GROUP_ID
	END AS PRIM_LAB_GROUP_ID
FROM [STUDY].[LAB_CODES] L
LEFT OUTER JOIN FIFTH_LAB FL ON FL.LAB_CODE_ID = L.LAB_CODE_ID
LEFT OUTER JOIN FOURTH_LAB F ON F.LAB_CODE_ID = L.LAB_CODE_ID
LEFT OUTER JOIN FOURTH_LAB F2 ON F2.LAB_CODE_ID = FL.FOURTH_LAB_GROUP_ID
LEFT OUTER JOIN THIRD_LAB T ON T.LAB_CODE_ID = L.LAB_CODE_ID
LEFT OUTER JOIN THIRD_LAB T2 ON T2.LAB_CODE_ID = F.THIRD_LAB_GROUP_ID
LEFT OUTER JOIN THIRD_LAB T3 ON T3.LAB_CODE_ID = F2.THIRD_LAB_GROUP_ID
LEFT OUTER JOIN SEC_LAB S ON S.LAB_CODE_ID = L.LAB_CODE_ID
LEFT OUTER JOIN SEC_LAB S2 ON S2.LAB_CODE_ID = T.SEC_LAB_GROUP_ID
LEFT OUTER JOIN SEC_LAB S3 ON S3.LAB_CODE_ID = T2.SEC_LAB_GROUP_ID
LEFT OUTER JOIN SEC_LAB S4 ON S4.LAB_CODE_ID = T3.SEC_LAB_GROUP_ID
)
, RESULTS_LAB AS (
SELECT 'LAB' AS CATEGORY,  LC.LAB_CODE_ID, LC.LAB_CODE_DESC,COUNT(*) AS TOTAL_RECORDS, SUM(IIF(LC.LAB_CODE_ID NOT IN (12,13), 1, 0)) AS CLASSIFIED, 
	SUM(IIF(LC.LAB_CODE_ID = 13, 1, 0)) AS UNCLASSIFIED, SUM(IIF(LC.LAB_CODE_ID = 12, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[LAB_TITLES] LT
INNER JOIN [STUDY].[LAB_CODES] LC ON (LT.LAB_CODE_ID = LC.LAB_CODE_ID)
GROUP BY LC.LAB_CODE_ID, LC.LAB_CODE_DESC
)
, TALLY_LAB AS (
SELECT F.LAB_CODE_ID, F.FOURTH_LAB_GROUP_ID, F.THIRD_LAB_GROUP_ID, F.SEC_LAB_GROUP_ID, F.PRIM_LAB_GROUP_ID,  ISNULL(R.TOTAL_RECORDS, 0) AS TOTAL_RECORDS, 
	ISNULL(R.CLASSIFIED, 0) AS CLASSIFIED, ISNULL(R.UNCLASSIFIED, 0) AS UNCLASSIFIED, ISNULL(R.NOTFOLLOWED, 0) AS NOTFOLLOWED
FROM FINAL_LAB F
LEFT OUTER JOIN RESULTS_LAB R ON R.LAB_CODE_ID = F.LAB_CODE_ID
)
, LAB AS (
SELECT 'LAB' AS CATEGORY, P.LAB_CODE_ID, P.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM PRIM_LAB P
LEFT OUTER JOIN TALLY_LAB T ON T.PRIM_LAB_GROUP_ID = P.LAB_CODE_ID
GROUP BY P.LAB_CODE_ID, P.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, S.LAB_CODE_ID, S.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM SEC_LAB S
LEFT OUTER JOIN TALLY_LAB T ON T.SEC_LAB_GROUP_ID = S.LAB_CODE_ID
WHERE T.SEC_LAB_GROUP_ID IS NOT NULL
GROUP BY S.LAB_CODE_ID, S.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, S.LAB_CODE_ID, S.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM SEC_LAB S
LEFT OUTER JOIN TALLY_LAB T ON T.LAB_CODE_ID = S.LAB_CODE_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY_LAB T2 
					WHERE T2.SEC_LAB_GROUP_ID = S.LAB_CODE_ID)
GROUP BY S.LAB_CODE_ID, S.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, TL.LAB_CODE_ID, TL.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM THIRD_LAB TL
LEFT OUTER JOIN TALLY_LAB T ON T.THIRD_LAB_GROUP_ID = TL.LAB_CODE_ID
WHERE T.THIRD_LAB_GROUP_ID IS NOT NULL
GROUP BY TL.LAB_CODE_ID, TL.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, TL.LAB_CODE_ID, TL.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM THIRD_LAB TL
LEFT OUTER JOIN TALLY_LAB T ON T.LAB_CODE_ID = TL.LAB_CODE_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY_LAB T2 
					WHERE T2.THIRD_LAB_GROUP_ID = TL.LAB_CODE_ID)
GROUP BY TL.LAB_CODE_ID, TL.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, F.LAB_CODE_ID, F.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM FOURTH_LAB F
LEFT OUTER JOIN TALLY_LAB T ON T.FOURTH_LAB_GROUP_ID = F.LAB_CODE_ID
WHERE T.FOURTH_LAB_GROUP_ID IS NOT NULL
GROUP BY F.LAB_CODE_ID, F.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, F.LAB_CODE_ID, F.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM FOURTH_LAB F
LEFT OUTER JOIN TALLY_LAB T ON T.LAB_CODE_ID = F.LAB_CODE_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY_LAB T2 
					WHERE T2.FOURTH_LAB_GROUP_ID = F.LAB_CODE_ID)
GROUP BY F.LAB_CODE_ID, F.LAB_CODE_DESC

UNION 

SELECT 'LAB' AS CATEGORY, FL.LAB_CODE_ID, FL.LAB_CODE_DESC, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM FIFTH_LAB FL
LEFT OUTER JOIN TALLY_LAB T ON T.LAB_CODE_ID = FL.LAB_CODE_ID
GROUP BY FL.LAB_CODE_ID, FL.LAB_CODE_DESC
)
, INS AS (
SELECT 'INS' AS CATEGORY, I.INS_GRP_ID, IG.GRP_NAME, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(I.INS_GRP_ID NOT IN (12,13), 1, 0)) AS CLASSIFIED, 
	SUM(IIF(I.INS_GRP_ID = 13, 1, 0)) AS UNCLASSIFIED, SUM(IIF(I.INS_GRP_ID = 12, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[INSURANCE] I
INNER JOIN [STUDY].[INSURANCE_GROUP] IG ON (I.INS_GRP_ID = IG.INS_GRP_ID)
GROUP BY I.INS_GRP_ID, IG.GRP_NAME
)
, VAX AS (
SELECT 'VAX' AS CATEGORY, I.IMMNZTN_GRP_ID,  IG.IMMNZTN_GRP_NM, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(I.IMMNZTN_GRP_ID NOT IN (12,13), 1, 0)) AS CLASSIFIED, 
	SUM(IIF(I.IMMNZTN_GRP_ID = 13, 1, 0)) AS UNCLASSIFIED, SUM(IIF(I.IMMNZTN_GRP_ID = 12, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[IMMUNIZATION] I
INNER JOIN [STUDY].[IMMUNIZATION_GROUP] IG ON (I.IMMNZTN_GRP_ID = IG.IMMNZTN_GRP_ID)
GROUP BY I.IMMNZTN_GRP_ID, IG.IMMNZTN_GRP_NM
)
---history---
, PRIM AS (
SELECT HISTORY_GROUP_ID, HG.GROUP_NM
FROM [STUDY].[HISTORY_GROUP] HG
WHERE PARENT_HISTORY_GROUP_ID IS NULL
)
, SEC AS (
SELECT HISTORY_GROUP_ID, GROUP_NM, PARENT_HISTORY_GROUP_ID AS PRIM_HX_GROUP_ID
FROM [STUDY].[HISTORY_GROUP] HG
WHERE EXISTS (SELECT HISTORY_GROUP_ID
				FROM PRIM P
				WHERE P.HISTORY_GROUP_ID = HG.PARENT_HISTORY_GROUP_ID)
)
,THIRD AS (
SELECT HISTORY_GROUP_ID, GROUP_NM, PARENT_HISTORY_GROUP_ID AS SEC_HX_GROUP_ID
FROM [STUDY].[HISTORY_GROUP] HG
WHERE EXISTS (SELECT 1
					FROM SEC S
					WHERE S.HISTORY_GROUP_ID = HG.PARENT_HISTORY_GROUP_ID)
)
, FOURTH AS (
SELECT HISTORY_GROUP_ID, GROUP_NM, PARENT_HISTORY_GROUP_ID AS THIRD_HX_GROUP_ID
FROM [STUDY].[HISTORY_GROUP] HG
WHERE EXISTS (SELECT 1
				FROM THIRD T
				WHERE T.HISTORY_GROUP_ID = HG.PARENT_HISTORY_GROUP_ID)
)
, FINAL AS (
SELECT HG.HISTORY_GROUP_ID, F.THIRD_HX_GROUP_ID, 
	CASE
		WHEN T.SEC_HX_GROUP_ID IS NULL THEN T2.SEC_HX_GROUP_ID
		ELSE T.SEC_HX_GROUP_ID
	END AS SEC_HX_GROUP_ID,
	CASE 
		WHEN HG.HISTORY_GROUP_ID IN (12,13) THEN HG.HISTORY_GROUP_ID
		WHEN S2.PRIM_HX_GROUP_ID IS NULL AND S.PRIM_HX_GROUP_ID IS NULL THEN S3.PRIM_HX_GROUP_ID
		WHEN S.PRIM_HX_GROUP_ID IS NULL THEN S2.PRIM_HX_GROUP_ID
		ELSE S.PRIM_HX_GROUP_ID
	END AS PRIM_HX_GROUP_ID
FROM [STUDY].[HISTORY_GROUP] HG
LEFT OUTER JOIN FOURTH F ON F.HISTORY_GROUP_ID = HG.HISTORY_GROUP_ID
LEFT OUTER JOIN THIRD T ON T.HISTORY_GROUP_ID = HG.HISTORY_GROUP_ID
LEFT OUTER JOIN THIRD T2 ON T2.HISTORY_GROUP_ID = F.THIRD_HX_GROUP_ID
LEFT OUTER JOIN SEC S ON S.HISTORY_GROUP_ID = HG.HISTORY_GROUP_ID
LEFT OUTER JOIN SEC S2 ON S2.HISTORY_GROUP_ID = T.SEC_HX_GROUP_ID
LEFT OUTER JOIN SEC S3 ON S3.HISTORY_GROUP_ID = T2.SEC_HX_GROUP_ID
)
, RESULTS AS (
SELECT 'HX' AS CATEGORY, HGH.HISTORY_GROUP_ID, HG.GROUP_NM, COUNT(*) AS TOTAL_RECORDS, SUM(IIF(HGH.HISTORY_GROUP_ID NOT IN (12,13), 1, 0)) AS CLASSIFIED, 
	SUM(IIF(HGH.HISTORY_GROUP_ID = 13, 1, 0)) AS UNCLASSIFIED, SUM(IIF(HGH.HISTORY_GROUP_ID = 12, 1, 0)) AS NOTFOLLOWED
FROM [STUDY].[HISTORY_GROUP_HISTORY_ASC] HGH
LEFT OUTER JOIN [STUDY].[HISTORY_GROUP] HG ON (HGH.HISTORY_GROUP_ID = HG.HISTORY_GROUP_ID)
GROUP BY HGH.HISTORY_GROUP_ID,HG.GROUP_NM
)
,TALLY AS (
SELECT F.HISTORY_GROUP_ID, F.THIRD_HX_GROUP_ID, F.SEC_HX_GROUP_ID, F.PRIM_HX_GROUP_ID, ISNULL(R.TOTAL_RECORDS, 0) AS TOTAL_RECORDS, 
	ISNULL(R.CLASSIFIED, 0) AS CLASSIFIED, ISNULL(R.UNCLASSIFIED, 0) AS UNCLASSIFIED, ISNULL(R.NOTFOLLOWED, 0) AS NOTFOLLOWED
FROM FINAL F 
LEFT OUTER JOIN RESULTS R ON R.HISTORY_GROUP_ID = F.HISTORY_GROUP_ID
)
, HX AS (
SELECT 'HX' AS CATEGORY, P.HISTORY_GROUP_ID AS HISTORY_GROUP_ID, P.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM PRIM P
LEFT OUTER JOIN TALLY T ON T.PRIM_HX_GROUP_ID = P.HISTORY_GROUP_ID
WHERE T.PRIM_HX_GROUP_ID IS NOT NULL
GROUP BY P.HISTORY_GROUP_ID, P.GROUP_NM

UNION

SELECT 'HX' AS CATEGORY, T.HISTORY_GROUP_ID AS HISTORY_GROUP_ID, P.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM PRIM P
LEFT OUTER JOIN TALLY T ON T.HISTORY_GROUP_ID= P.HISTORY_GROUP_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY T2 
					WHERE T2.PRIM_HX_GROUP_ID = P.HISTORY_GROUP_ID)
GROUP BY T.HISTORY_GROUP_ID, P.GROUP_NM

UNION 

SELECT 'HX' AS CATEGORY, T.SEC_HX_GROUP_ID AS HISTORY_GROUP_ID, S.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM SEC S
LEFT OUTER JOIN TALLY T ON T.SEC_HX_GROUP_ID = S.HISTORY_GROUP_ID
WHERE T.SEC_HX_GROUP_ID IS NOT NULL
GROUP BY T.SEC_HX_GROUP_ID, S.GROUP_NM

UNION

SELECT 'HX' AS CATEGORY, T.HISTORY_GROUP_ID, S.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM SEC S
LEFT OUTER JOIN TALLY T ON T.HISTORY_GROUP_ID = S.HISTORY_GROUP_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY T2
					WHERE T2.SEC_HX_GROUP_ID = S.HISTORY_GROUP_ID)
GROUP BY T.HISTORY_GROUP_ID, S.GROUP_NM

UNION 

SELECT 'HX' AS CATEGORY, TH.HISTORY_GROUP_ID, TH.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED, 
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM THIRD TH
LEFT OUTER JOIN TALLY T ON T.THIRD_HX_GROUP_ID = TH.HISTORY_GROUP_ID
WHERE T.THIRD_HX_GROUP_ID IS NOT NULL
GROUP BY TH.HISTORY_GROUP_ID, TH.GROUP_NM

UNION 

SELECT 'HX' AS CATEGORY, T.HISTORY_GROUP_ID, TH.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED,
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM THIRD TH
LEFT OUTER JOIN TALLY T ON T.HISTORY_GROUP_ID = TH.HISTORY_GROUP_ID
WHERE NOT EXISTS (SELECT 1
					FROM TALLY T2
					WHERE T2.THIRD_HX_GROUP_ID = TH.HISTORY_GROUP_ID)
GROUP BY T.HISTORY_GROUP_ID, TH.GROUP_NM

UNION 

SELECT 'HX' AS CATEGORY, T.HISTORY_GROUP_ID, F.GROUP_NM, SUM(T.TOTAL_RECORDS) AS TOTAL_RECORDS, SUM(T.CLASSIFIED) AS CLASSIFIED,
	SUM(T.UNCLASSIFIED) AS UNCLASSIFIED, SUM(T.NOTFOLLOWED) AS NOTFOLLOWED
FROM FOURTH F
LEFT OUTER JOIN TALLY T ON T.HISTORY_GROUP_ID = F.HISTORY_GROUP_ID
GROUP BY T.HISTORY_GROUP_ID, F.GROUP_NM
)

SELECT A.CONCEPT_NM, A.CONCEPT_ID, A.CONCEPT_DESC,
	A.MED AS MED_CLASS_ID, B.BRND_NM AS MED_GROUP_NM, B.TOTAL_RECORDS AS MED_T, B.CLASSIFIED AS MED_C, B.UNCLASSIFIED AS MED_UC, B.NOTFOLLOWED AS MED_NF,
	A.[PROC] AS PROC_CLASS_ID, C.SERVICE_GRP_NAME AS PROC_GROUP_NM, C.TOTAL_RECORDS AS PROC_T, C.CLASSIFIED AS PROC_C, C.UNCLASSIFIED AS PROC_UC, C.NOTFOLLOWED AS PROC_NF,
	A.DX AS DX_CLASS_ID, D.DISEASE_NM AS DX_GROUP_NM, D.TOTAL_RECORDS AS DX_T, D.CLASSIFIED AS DX_C, D.UNCLASSIFIED AS DX_UC, D.NOTFOLLOWED AS DX_NF,
	A.LAB AS LAB_CLASS_ID, E.LAB_CODE_DESC AS LAB_GROUP_NM, E.TOTAL_RECORDS AS LAB_T, E.CLASSIFIED AS LAB_C, E.UNCLASSIFIED AS LAB_UC, E.NOTFOLLOWED AS LAB_NF,
	A.INS AS INS_CLASS_ID, F.GRP_NAME AS INS_GROUP_NM, F.TOTAL_RECORDS AS INS_T, F.CLASSIFIED AS INS_C, F.UNCLASSIFIED AS INS_UC, F.NOTFOLLOWED AS INS_NF,
	A.VAX AS VAX_CLASS_ID, G.IMMNZTN_GRP_NM AS VAX_GROUP_NM, G.TOTAL_RECORDS AS VAX_T, G.CLASSIFIED AS VAX_C, G.UNCLASSIFIED AS VAX_UC, G.NOTFOLLOWED AS VAX_NF,
	A.HX AS HX_CLASS_ID, H.GROUP_NM AS HX_GROUP_NM, H.TOTAL_RECORDS AS HX_T, H.CLASSIFIED AS HX_C, H.UNCLASSIFIED AS HX_UC, H.NOTFOLLOWED AS HX_NF
FROM BASE A
LEFT OUTER JOIN MED B ON B.BRND_ID = A.MED
LEFT OUTER JOIN [PROC] C ON C.SERVICE_GRP_ID = A.[PROC]
LEFT OUTER JOIN DX D ON D.DISEASE_ID = A.DX
LEFT OUTER JOIN LAB E ON E.LAB_CODE_ID = A.LAB
LEFT OUTER JOIN INS F ON F.INS_GRP_ID = A.INS
LEFT OUTER JOIN VAX G ON G.IMMNZTN_GRP_ID = A.VAX
LEFT OUTER JOIN HX H ON H.HISTORY_GROUP_ID = A.HX
ORDER BY A.CONCEPT_NM</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="CONCEPT_NM">
          <DataField>CONCEPT_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CONCEPT_ID">
          <DataField>CONCEPT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CONCEPT_DESC">
          <DataField>CONCEPT_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MED_CLASS_ID">
          <DataField>MED_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_GROUP_NM">
          <DataField>MED_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MED_T">
          <DataField>MED_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_C">
          <DataField>MED_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_UC">
          <DataField>MED_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_NF">
          <DataField>MED_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_CLASS_ID">
          <DataField>PROC_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_T">
          <DataField>PROC_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_GROUP_NM">
          <DataField>PROC_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PROC_C">
          <DataField>PROC_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_UC">
          <DataField>PROC_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_NF">
          <DataField>PROC_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_CLASS_ID">
          <DataField>DX_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_T">
          <DataField>DX_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_C">
          <DataField>DX_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_GROUP_NM">
          <DataField>DX_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DX_UC">
          <DataField>DX_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_NF">
          <DataField>DX_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_CLASS_ID">
          <DataField>LAB_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_T">
          <DataField>LAB_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_C">
          <DataField>LAB_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_UC">
          <DataField>LAB_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_NF">
          <DataField>LAB_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_GROUP_NM">
          <DataField>LAB_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INS_CLASS_ID">
          <DataField>INS_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_T">
          <DataField>INS_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_C">
          <DataField>INS_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_UC">
          <DataField>INS_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_NF">
          <DataField>INS_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_GROUP_NM">
          <DataField>INS_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="VAX_CLASS_ID">
          <DataField>VAX_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_T">
          <DataField>VAX_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_C">
          <DataField>VAX_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_UC">
          <DataField>VAX_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_NF">
          <DataField>VAX_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_CLASS_ID">
          <DataField>HX_CLASS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_T">
          <DataField>HX_T</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_GROUP_NM">
          <DataField>VAX_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="HX_C">
          <DataField>HX_C</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_UC">
          <DataField>HX_UC</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_NF">
          <DataField>HX_NF</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_GROUP_NM">
          <DataField>HX_GROUP_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="MED_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS MED_NUM_RECORDS
FROM [STUDY].[MEDICATION_PACKAGE]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="MED_NUM_RECORDS">
          <DataField>MED_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SER_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS SER_NUM_RECORDS
FROM [STUDY].[SERVICE]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="SER_NUM_RECORDS">
          <DataField>SER_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DX_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS DX_NUM_RECORDS
FROM [STUDY].[DISEASE_MEM]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="DX_NUM_RECORDS">
          <DataField>DX_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="LAB_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS LAB_NUM_RECORDS
FROM [STUDY].[LAB_TITLES]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="LAB_NUM_RECORDS">
          <DataField>LAB_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="INS_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS INS_NUM_RECORDS
FROM [STUDY].[INSURANCE]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="INS_NUM_RECORDS">
          <DataField>INS_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="VAX_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS VAX_NUM_RECORDS
FROM [STUDY].[IMMUNIZATION]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="VAX_NUM_RECORDS">
          <DataField>VAX_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="HX_COUNT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT COUNT (*) AS HX_NUM_RECORDS
FROM [STUDY].[HISTORY_GROUP_HISTORY_ASC]</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="HX_NUM_RECORDS">
          <DataField>HX_NUM_RECORDS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="ADDL_CLASSIFIED_TABLE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>WITH [PROC] AS (
SELECT CASE
			WHEN SERVICE_GRP_ID = 12 THEN 1
			WHEN SERVICE_GRP_ID = 13 THEN 2
			ELSE 3
		END AS CLASS_CD, 
		CASE 
			WHEN SERVICE_GRP_ID = 12 THEN 'NOT_FOLLOWED'
			WHEN SERVICE_GRP_ID = 13 THEN 'UNCLASSIFIED'
			ELSE 'CLASSIFIED'
		END AS CLASS
FROM [STUDY].[SERVICE]
)
, LAB AS (
SELECT CASE
			WHEN LAB_CODE_ID = 12 THEN 1
			WHEN LAB_CODE_ID = 13 THEN 2
			ELSE 3
		END AS CLASS_CD, 
		CASE 
			WHEN LAB_CODE_ID = 12 THEN 'NOT_FOLLOWED'
			WHEN LAB_CODE_ID = 13 THEN 'UNCLASSIFIED'
			ELSE 'CLASSIFIED'
		END AS CLASS
FROM [STUDY].[LAB_TITLES]
)
, INS AS (
SELECT CASE
			WHEN INS_GRP_ID = 12 THEN 1
			WHEN INS_GRP_ID = 13 THEN 2
			ELSE 3
		END AS CLASS_CD, 
		CASE 
			WHEN INS_GRP_ID = 12 THEN 'NOT_FOLLOWED'
			WHEN INS_GRP_ID = 13 THEN 'UNCLASSIFIED'
			ELSE 'CLASSIFIED'
		END AS CLASS
FROM [STUDY].[INSURANCE]
)
, VAX AS (
SELECT CASE
			WHEN IMMNZTN_GRP_ID = 12 THEN 1
			WHEN IMMNZTN_GRP_ID = 13 THEN 2
			ELSE 3
		END AS CLASS_CD, 
		CASE 
			WHEN IMMNZTN_GRP_ID = 12 THEN 'NOT_FOLLOWED'
			WHEN IMMNZTN_GRP_ID = 13 THEN 'UNCLASSIFIED'
			ELSE 'CLASSIFIED'
		END AS CLASS
FROM [STUDY].[IMMUNIZATION]
)
, HX AS (
SELECT CASE
			WHEN HISTORY_GROUP_ID = 12 THEN 1
			WHEN HISTORY_GROUP_ID = 13 THEN 2
			ELSE 3
		END AS CLASS_CD, 
		CASE 
			WHEN HISTORY_GROUP_ID = 12 THEN 'NOT_FOLLOWED'
			WHEN HISTORY_GROUP_ID = 13 THEN 'UNCLASSIFIED'
			ELSE 'CLASSIFIED'
		END AS CLASS
FROM [STUDY].[HISTORY_GROUP_HISTORY_ASC]
) 
, COMBINED AS (
SELECT 'MED' AS TYPE_RECORDS, CLASS_CD, 
	CASE 
		WHEN CLASS_CD = 1 THEN 'NOT_FOLLOWED'
		WHEN CLASS_CD = 2 THEN 'UNCLASSIFIED'
		WHEN CLASS_CD = 3 THEN 'CLASSIFIED'
	END AS CLASS,
	COUNT (*) AS TOTAL_COUNT
FROM [STUDY].[MEDICATION_PACKAGE]
GROUP BY CLASS_CD

UNION 

SELECT 'DX' AS TYPE_RECORDS, CLASS_CD, 
	CASE 
		WHEN CLASS_CD = 1 THEN 'NOT_FOLLOWED'
		WHEN CLASS_CD = 2 THEN 'UNCLASSIFIED'
		WHEN CLASS_CD = 3 THEN 'CLASSIFIED'
	END AS CLASS,
	COUNT (*) AS TOTAL_COUNT
FROM [STUDY].[DISEASE_MEM]
GROUP BY CLASS_CD

UNION 

SELECT 'PROC' AS TYPE_RECORDS, CLASS_CD, CLASS, COUNT(*) AS TOTAL_COUNT
FROM [PROC]
GROUP BY CLASS_CD, CLASS

UNION  

SELECT 'LAB' AS TYPE_RECORDS, CLASS_CD, CLASS, COUNT(*) AS TOTAL_COUNT
FROM LAB
GROUP BY CLASS_CD, CLASS

UNION 

SELECT 'INS' AS TYPE_RECORDS, CLASS_CD, CLASS, COUNT(*) AS TOTAL_COUNT
FROM INS
GROUP BY CLASS_CD, CLASS

UNION 

SELECT 'VAX' AS TYPE_RECORDS, CLASS_CD, CLASS, COUNT(*) AS TOTAL_COUNT
FROM VAX
GROUP BY CLASS_CD, CLASS

UNION 

SELECT 'HX' AS TYPE_RECORDS, CLASS_CD, CLASS, COUNT(*) AS TOTAL_COUNT
FROM HX
GROUP BY CLASS_CD, CLASS
)

SELECT 
	ISNULL([DX_CLASSIFIED],0) AS [DX_CLASSIFIED],
	ISNULL ([DX_UNCLASSIFIED],0) AS [DX_UNCLASSIFIED],
	ISNULL ([DX_NOT_FOLLOWED],0) AS [DX_NOT_FOLLOWED],
	ISNULL ([HX_CLASSIFIED],0) AS [HX_CLASSIFIED],
	ISNULL ([HX_UNCLASSIFIED],0) AS [HX_UNCLASSIFIED],
	ISNULL ([HX_NOT_FOLLOWED],0) AS [HX_NOT_FOLLOWED],
	ISNULL ([INS_CLASSIFIED],0) AS [INS_CLASSIFIED],
	ISNULL ([INS_UNCLASSIFIED],0) AS [INS_UNCLASSIFIED],
	ISNULL ([INS_NOT_FOLLOWED],0) AS [INS_NOT_FOLLOWED],
	ISNULL ([LAB_CLASSIFIED],0) AS [LAB_CLASSIFIED],
	ISNULL ([LAB_UNCLASSIFIED],0) AS [LAB_UNCLASSIFIED],
	ISNULL ([LAB_NOT_FOLLOWED],0) AS [LAB_NOT_FOLLOWED],
	ISNULL ([MED_CLASSIFIED],0) AS [MED_CLASSIFIED],
	ISNULL ([MED_UNCLASSIFIED],0) AS [MED_UNCLASSIFIED],
	ISNULL ([MED_NOT_FOLLOWED],0) AS [MED_NOT_FOLLOWED],
	ISNULL ([PROC_CLASSIFIED],0) AS [PROC_CLASSIFIED],
	ISNULL ([PROC_UNCLASSIFIED],0) AS [PROC_UNCLASSIFIED],
	ISNULL ([PROC_NOT_FOLLOWED],0) AS [PROC_NOT_FOLLOWED],
	ISNULL ([VAX_CLASSIFIED],0) AS [VAX_CLASSIFIED],
	ISNULL ([VAX_UNCLASSIFIED],0) AS [VAX_UNCLASSIFIED],
	ISNULL ([VAX_NOT_FOLLOWED],0) AS [VAX_NOT_FOLLOWED]
FROM (
SELECT TYPE_RECORDS + '_' + CLASS AS COLUMN_NM, TOTAL_COUNT
FROM COMBINED) SOURCE
PIVOT
(MAX(TOTAL_COUNT) FOR COLUMN_NM IN ([DX_CLASSIFIED], 
		[DX_UNCLASSIFIED],
		[DX_NOT_FOLLOWED],
		[HX_CLASSIFIED],
		[HX_UNCLASSIFIED],
		[HX_NOT_FOLLOWED],
		[INS_CLASSIFIED],
		[INS_UNCLASSIFIED],
		[INS_NOT_FOLLOWED],
		[LAB_CLASSIFIED],
		[LAB_UNCLASSIFIED],
		[LAB_NOT_FOLLOWED],
		[MED_CLASSIFIED],
		[MED_UNCLASSIFIED],
		[MED_NOT_FOLLOWED],
		[PROC_CLASSIFIED],
		[PROC_UNCLASSIFIED],
		[PROC_NOT_FOLLOWED],
		[VAX_CLASSIFIED],
		[VAX_UNCLASSIFIED],
		[VAX_NOT_FOLLOWED])) PVT</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="DX_CLASSIFIED">
          <DataField>DX_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_UNCLASSIFIED">
          <DataField>DX_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DX_NOT_FOLLOWED">
          <DataField>DX_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_CLASSIFIED">
          <DataField>HX_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_UNCLASSIFIED">
          <DataField>HX_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="HX_NOT_FOLLOWED">
          <DataField>HX_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_CLASSIFIED">
          <DataField>INS_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_UNCLASSIFIED">
          <DataField>INS_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INS_NOT_FOLLOWED">
          <DataField>INS_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_CLASSIFIED">
          <DataField>LAB_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_UNCLASSIFIED">
          <DataField>LAB_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LAB_NOT_FOLLOWED">
          <DataField>LAB_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_CLASSIFIED">
          <DataField>MED_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_UNCLASSIFIED">
          <DataField>MED_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MED_NOT_FOLLOWED">
          <DataField>MED_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_CLASSIFIED">
          <DataField>PROC_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_UNCLASSIFIED">
          <DataField>PROC_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PROC_NOT_FOLLOWED">
          <DataField>PROC_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_CLASSIFIED">
          <DataField>VAX_CLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_UNCLASSIFIED">
          <DataField>VAX_UNCLASSIFIED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="VAX_NOT_FOLLOWED">
          <DataField>VAX_NOT_FOLLOWED</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>eb88709e-1cbd-482c-a5c8-6e973044183c</rd:ReportID>
</Report>