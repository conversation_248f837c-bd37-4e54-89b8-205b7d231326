import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Rule, RuleType } from '../../../models/rule.model';
import { RulesEngineService } from '../../../services/rules-engine.service';
import { LayoutService } from '../../../../shared-modules/layouts/services/layout/layout.service';

@Component({
  selector: 'app-rules-list',
  templateUrl: './rules-list.component.html',
  styleUrls: ['./rules-list.component.scss']
})
export class RulesListComponent implements OnInit {
  @Input() siteId: string = '';
  @Input() rules: Rule[] = [];
  @Input() ruleTypes: RuleType[] = [];
  @Input() selectedRuleType: number | null = null;

  @Output() ruleSelected = new EventEmitter<Rule>();
  @Output() ruleDeleted = new EventEmitter<Rule>();
  @Output() ruleTypeChanged = new EventEmitter<number | null>();
  @Output() refreshRules = new EventEmitter<void>();
  @Output() createRule = new EventEmitter<void>();

  displayedColumns: string[] = ['name', 'type', 'version', 'createdDate', 'modifiedDate', 'actions'];

  constructor(
    private rulesService: RulesEngineService,
    private layoutService: LayoutService
  ) { }

  ngOnInit(): void {
  }

  onRuleTypeChange(ruleTypeId: number | null): void {
    this.ruleTypeChanged.emit(ruleTypeId);
  }

  onViewRule(rule: Rule): void {
    this.ruleSelected.emit(rule);
  }

  onDeleteRule(rule: Rule): void {
    if (confirm(`Are you sure you want to delete the rule "${rule.name}"?`)) {
      this.layoutService.showSpinner();
      this.rulesService.deleteRule(rule.ruleID)
        .subscribe({
          next: () => {
            this.layoutService.hideSpinner();
            this.ruleDeleted.emit(rule);
          },
          error: (error) => {
            this.layoutService.hideSpinner();
            console.error('Error deleting rule:', error);
            alert('Failed to delete rule. Please try again later.');
          }
        });
    }
  }

  onRefreshRules(): void {
    this.refreshRules.emit();
  }

  onCreateRule(): void {
    this.createRule.emit();
  }
}
