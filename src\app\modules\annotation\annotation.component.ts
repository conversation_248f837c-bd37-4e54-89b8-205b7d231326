import { ChangeDetectorRef, Component, OnInit, isDevMode } from "@angular/core";
import { AnnotationService } from "./annotation.service";
import { AnnotationDetails, AnnotationRequest, ControlType, PatientMeasure } from "./annotation-models";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { DialogResponseComponent } from "../shared-modules/layouts/dialogs/dialog-response/dialog-response.component";
import { UserContext } from "src/app/shared-services/user-context/user-context.service";
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'annotation',
  templateUrl: './annotation.component.html',
  styleUrls: ['./annotation.component.scss'],
})


export class AnnotationComponent implements OnInit {

  public annotationRequest: AnnotationRequest = new AnnotationRequest();
  public annotationDetails: AnnotationDetails = new AnnotationDetails();
  public optionFreeTextTitle: string = '';
  public optionTextValue: string = '';
  public isSubmitButtonEnabled: boolean = false;
  public slectedOption: number = 0;
  public isTextVisible: boolean = false;

 

  public get controlTypes(): typeof ControlType {
    return ControlType;
  }
  dialogRef: MatDialogRef<AnnotationComponent, any> = {} as MatDialogRef<AnnotationComponent, any>
  openDialog: MatDialogRef<DialogResponseComponent, any> = {} as MatDialogRef<DialogResponseComponent, any>

  constructor(private annotationService: AnnotationService,
              private userContext: UserContext,
              private ref: ChangeDetectorRef,
              private dialogRef2: MatDialogRef<AnnotationComponent>,
              private dialog: MatDialog,
             ) {}

  ngOnInit(): void {

    this.CreateRequestObj(this.annotationService.annotationUrl);
        this.annotationService.GetAnnotationDetails(this.annotationRequest).subscribe(res => {
          if (res) {
            if (this.isHttpErrorResponse(res)) {
              
               let payload = "";
                if (isDevMode())
                {
                   payload = res.message + ' ' +  this.annotationService.annotationUrl + ' ' + String(this.annotationRequest)
                }
                window.alert('An error occurred retrieving the annotation details. ' +
                ' Please click Ctrl-F5 and try again. If the error persists, contact <EMAIL> \n  '  + payload );
            } else
            {
              this.annotationDetails = res as AnnotationDetails;
              if (this.annotationDetails.patientMeasure != null) {
                this.optionTextValue = this.annotationDetails.patientMeasure.annotateFreetext;
                this.slectedOption = this.annotationDetails.patientMeasure.annotateOptionId;
  
                //set checked status
                this.annotationDetails.optionsByMeasure.forEach(x => {
                  if (x.annotateOptionId == this.annotationDetails.patientMeasure.annotateOptionId) {
                    x.checked = true;
                    this.isTextVisible = true;
                  }
                });
              } else {
                let pMeasure = new PatientMeasure();
                pMeasure.providerId = this.annotationRequest.ProviderId;
                pMeasure.patientId = this.annotationRequest.DemographicId;
                pMeasure.measureId = this.annotationRequest.MeasureId;
                pMeasure.reportingPeriod = this.annotationRequest.ReportingYear;
                pMeasure.measureAnnotateId = this.annotationRequest.MeasureAnnotateId;
                pMeasure.intervalDesc = this.annotationRequest.IntervalDesc == "none" || this.annotationRequest.IntervalDesc == "undefined" ? null : this.annotationRequest.IntervalDesc;
                pMeasure.measureDt = this.annotationRequest.VisitDate != '0' ? new Date(this.annotationRequest.VisitDate) : null;
                this.annotationDetails.patientMeasure = pMeasure;
              }
              this.ref.detectChanges();
            }
        
          }
        });
  }

  submitClick() {
    this.annotationDetails.patientMeasure.annotateFreetext = this.optionTextValue;
    this.annotationDetails.patientMeasure.openFlg = true;
    this.annotationDetails.patientMeasure.validFromDt = new Date();
    this.annotationDetails.patientMeasure.modifiedUser=this.userContext.GetUserName();
    this.annotationDetails.patientMeasure.annotateTypeId = this.annotationDetails.annotate?.controlTypeId;

    let obj = { ...this.annotationDetails };
    obj.patientMeasure.intervalDesc = obj.patientMeasure.intervalDesc == "undefined" || obj.patientMeasure.intervalDesc == null
      || obj.patientMeasure.intervalDesc.toLowerCase() == "none" ? null : obj.patientMeasure.intervalDesc;
    obj.optionsByMeasure = this.annotationDetails.optionsByMeasure.filter(x => x.checked);

    this.annotationService.SaveMeasureResponse(obj).subscribe(res => {
        if (res == true) {
        this.openResponseDialog("Updated Successfully");
        this.closePopup();       
      }
    })
  }


  isHttpErrorResponse(response: any): response is HttpErrorResponse {
    return response instanceof HttpErrorResponse;
  }

  optionClick(annotateOptionId: any) {
    this.slectedOption = annotateOptionId;
    let optionData = this.annotationDetails.optionsByMeasure.find(x => x.annotateOptionId == annotateOptionId)
    this.optionFreeTextTitle = optionData?.optionFreetextTitle;
    this.isTextVisible =  true;
    this.isSubmitButtonEnabled = (optionData?.optionalFlg || (!optionData?.optionalFlg && this.optionTextValue.length > 0)) ? true : false;

  }

  optionTextChange(event) {
    if (this.slectedOption > 0) {
      let optionData = this.annotationDetails.optionsByMeasure.find(x => x.annotateOptionId == this.slectedOption)
      this.isSubmitButtonEnabled = (optionData?.optionalFlg || (!optionData?.optionalFlg && this.optionTextValue.length > 0)) ? true : false;

    }
  }

  changeGender(e) {
    if (ControlType.RadioButton == this.annotationDetails.annotate?.controlTypeId) {
      this.annotationDetails.optionsByMeasure.forEach(x => {
        if (x.annotateOptionId == e.target.value) {
          x.checked = true;
        } else {
          x.checked = false;
        }
      });
    }

    if (ControlType.CheckBox == this.annotationDetails.annotate?.controlTypeId) {
      this.annotationDetails.optionsByMeasure.forEach(x => {
        if (x.annotateOptionId == e.target.value) {
          x.checked = e.target.checked;
        }
      });
    }
  }

  closePopup() {
    this.dialogRef2.close();
  }

  public openResponseDialog(data: any) {
    this.openDialog = this.dialog.open(DialogResponseComponent, {
      data: data, disableClose: true
    });
  }

  CreateRequestObj(url: string): void {
    const query = url.slice(url.indexOf('?') + 1).split('&').reduce((acc, query) => {
      const parts = query.split('=');
      acc[parts[0]] = parts[1];
      return acc;
    }, {});

    this.annotationRequest = query as AnnotationRequest;
  }

}
