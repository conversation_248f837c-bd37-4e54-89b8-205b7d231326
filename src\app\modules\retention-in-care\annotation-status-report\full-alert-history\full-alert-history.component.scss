.OutreachDeatilDiv {
    float: left;
    width: 100%;
    padding: 2px 10px 10px 10px;
    color: black;    
    font-weight:normal;
}

.OutReachDetailInnerDiv label {
    font-size: smaller;
    font-weight: 500;
}

.OutReachDetailInnerDiv label.labelText{
    font-size: smaller;
    padding: 0px 10px 0px 5px;
    font-weight: normal;
}

.OutReachDetailInnerDiv label.labelText.italicText{
    font-weight: 500;
    padding: 0px 10px 0px 5px;
    font-style: italic;
}

.OutReachDetailInnerDiv label.labelText.boldText{
    font-weight: 500;
    padding: 0px 10px 0px 5px;
}

.OutReachDetailInnerDiv label.labelText.redText{
    font-weight: 500;
    color: red;
}

.OutReachDetailInnerDiv label.labelWithUnderline{
    font-size: smaller;
    font-weight: 500;
    text-decoration: underline;
}

.OutReachDetailInnerDiv {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
}

.OutReachDetailInnerDiv a {
    cursor: pointer; 
    font-size: smaller;
    font-weight: 500;
    outline: none;
    color: #4b8cd4;
    text-decoration: none;
    cursor:pointer;
}

.detailsDiv {
    font-weight: 500;
    float: left; 
    width: 100%; 
    height: 300px;
    overflow-y: scroll; 
    font-size: smaller;
}

.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    text-decoration: none;
    float: left;
    font-size: 14px;
    height: 25px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

.CMbtn:hover {
    background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}

.imgIcons {
    background-color: transparent; 
    cursor: pointer; 
    float: right; 
    padding-right: 20px; 
    position: relative;
    width: 47px;
    height: 27px;
}


.historyDiv {
    font-size: smaller;
    font-weight: 500;
    float: left; 
    width: 100%; 
    height: 300px;
    overflow-y: scroll; 
}

.historyDiv label.table-header{
    font-size: 14px;
    font-weight: 500;
    text-decoration: underline;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}
