<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					DECLARE @SITE_ID INT
					SET                @SITE_ID =
					(SELECT        SITE_ID
					FROM            CLEAN.SITE)
					SELECT TOP 1        SITE_NM, CITY_TXT, STATE_TXT,IIF(OS.SITE_ID IS NULL ,0,1)IS_OPERA
					FROM            [CHORUS].[ADMIN].[SITE] CS LEFT OUTER JOIN   OPERA.CLEAN.SITE OS ON CS.SITE_ID = OS.SITE_ID
					WHERE        (STATUS_CD = 'A') AND (CS.SITE_ID = @SITE_ID)
				</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IS_OPERA">
          <DataField>IS_OPERA</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
					SELECT EXTRACT_DT,SITE_ID
					FROM CLEAN.SITE
				</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SITE_ID">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_PQRS_MEASURES">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@REPORTING_YEAR">
            <Value>=Parameters!REPORTING_YEAR.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ROLLING_WEEK">
            <Value>=Parameters!rollingWeek.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>
					SELECT 	REPORTING_PERIOD,
					ROLLING_WEEK,
					COHORT_ID,
					LOCATION,
					PRIMARY_PROVIDER,
					TOT_CNT,
					TOT_PTNT_CNT,
					PREV_TOT_CNT,
					PREV_TOT_PTNT_CNT,
					MEASURE_CD,
					MEASURE_NM,
					INVERTED_FLG,
					REPORT_LEVEL_DESC_1,
					REPORT_LEVEL_DESC_2,
					REPORT_LEVEL_DESC_3,
					MEETING_DESC,
					NOT_MEETING_DESC,
					TOT_MEASURE_CNT,
					MEASURE_CNT,
					PREV_TOT_MEASURE_CNT,
					PREV_MEASURE_CNT,
					PQRS_25TH_PCTL,
					PQRS_75TH_PCTL,
					PQRS_100TH_PCTL,
					PQRS_AVG
					FROM [REPORT].[GET_PQRS_MEASURES](@USER_ID, @REPORTING_YEAR, @locationCd, @providerCd, @ROLLING_WEEK)
				</CommandText>
      </Query>
      <Fields>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ROLLING_WEEK">
          <DataField>ROLLING_WEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>LOCATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER">
          <DataField>PRIMARY_PROVIDER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOT_CNT">
          <DataField>TOT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOT_PTNT_CNT">
          <DataField>TOT_PTNT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PREV_TOT_CNT">
          <DataField>PREV_TOT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PREV_TOT_PTNT_CNT">
          <DataField>PREV_TOT_PTNT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_NM">
          <DataField>MEASURE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INVERTED_FLG">
          <DataField>INVERTED_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_1">
          <DataField>REPORT_LEVEL_DESC_1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_2">
          <DataField>REPORT_LEVEL_DESC_2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_3">
          <DataField>REPORT_LEVEL_DESC_3</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEETING_DESC">
          <DataField>MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOT_MEETING_DESC">
          <DataField>NOT_MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOT_MEASURE_CNT">
          <DataField>TOT_MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CNT">
          <DataField>MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PQRS_25TH_PCTL">
          <DataField>PQRS_25TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PQRS_75TH_PCTL">
          <DataField>PQRS_75TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PREV_TOT_MEASURE_CNT">
          <DataField>PREV_TOT_MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PQRS_100TH_PCTL">
          <DataField>PQRS_100TH_PCTL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PREV_MEASURE_CNT">
          <DataField>PREV_MEASURE_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PQRS_AVG">
          <DataField>PQRS_AVG</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="ROLLING_WEEK">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@REPORTING_YEAR">
            <Value>=Parameters!REPORTING_YEAR.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>
					SELECT MAX(ROLLING_WEEK) AS MAX_ROLLING_WEEK
					FROM [REPORT].[MEASURES_DTL] M
					INNER JOIN [REPORT].[REPORT_MEASURE_ASC] R ON (M.MEASURE_ID = R.MEASURE_ID AND REPORT_ID = 1)
					WHERE M.REPORTING_PERIOD = @REPORTING_YEAR
				</CommandText>
      </Query>
      <Fields>
        <Field Name="MAX_ROLLING_WEEK">
          <DataField>MAX_ROLLING_WEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix5">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.1cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.88691cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.95309cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.1cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.26013cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.60929cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#1f4e78</Color>
                              <Style>None</Style>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                          </Style>
                        </Textbox>
                        <ColSpan>7</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>All</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#1f4e78</Color>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.66146cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox3</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#1f4e78</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>7</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>OPERA®</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#1f4e78</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Top</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.635cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TOT_COHORT_CNT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TOT_COHORT_CNT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox673">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox673</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox680">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox680</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox251">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>-</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox251</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#0b6c9f</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.60001cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox461">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox461</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox674">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox674</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox681">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox681</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox462">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox462</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox73">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox73</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox464">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox464</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>#585858</Color>
                              <Style>Solid</Style>
                              <Width>1.25pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.635cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox114">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>#</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox114</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox675">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox675</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox119">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Trend</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox119</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox121">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>AVG</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox121</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>Silver</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.127cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox523">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox523</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox588">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox588</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox676">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox676</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox683">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox683</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox589">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox589</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox590">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox590</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox591">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox591</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox593">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>4pt</FontSize>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox593</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Black</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.57cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CD4_LAB_COHORT_PCNG3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>
																		=IIF(Fields!INVERTED_FLG.Value=true,
																		SUM(Fields!TOT_MEASURE_CNT.Value)  - SUM(Fields!MEASURE_CNT.Value),
																		SUM(Fields!MEASURE_CNT.Value)

																		)
																	</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_CD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 0,1)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!NOT_MEETING_DESC.Value, Fields!MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_MEASURE_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=iif(inscope("LOCATION"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=true,
SUM(Fields!TOT_MEASURE_CNT.Value) - SUM(Fields!MEASURE_CNT.Value),
SUM(Fields!MEASURE_CNT.Value))</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>#1f4e78</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox109">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>(</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>n0</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>
																		=IIF(Fields!INVERTED_FLG.Value=true, SUM(Fields!MEASURE_CNT.Value) ,
																		SUM(Fields!TOT_MEASURE_CNT.Value)  - SUM(Fields!MEASURE_CNT.Value))
																	</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>n0</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>n0</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_CD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 1,0)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_MEASURE_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=true,
SUM(Fields!MEASURE_CNT.Value),
SUM(Fields!TOT_MEASURE_CNT.Value) - SUM(Fields!MEASURE_CNT.Value)
)</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=iif(inscope("LOCATION"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Color>#1f4e78</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox677">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>
																		=IIF(Fields!INVERTED_FLG.Value=true,
																		IIF(Format(Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)).contains(".50"),
																		Floor(Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)), Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)),
																		IIF(Format(Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)).contains(".50"), Floor(Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)), Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100))

																		)&amp;"%"
																	</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox677</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_CD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 0,1)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!NOT_MEETING_DESC.Value, Fields!MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_MEASURE_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=true,
SUM(Fields!TOT_MEASURE_CNT.Value) - SUM(Fields!MEASURE_CNT.Value),
SUM(Fields!MEASURE_CNT.Value)
)</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=iif(inscope("LOCATION"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox684">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>
																		=IIF(Fields!INVERTED_FLG.Value=true,
																		IIF(Format(Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)).contains(".50"), Ceiling(Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)), Round((CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)),
																		IIF(Format(Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)).contains(".50"), Ceiling(Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100)), Round((CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))*100))

																		)&amp;"%"
																	</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox684</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_CD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 1,0)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_MEASURE_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=true,
SUM(Fields!MEASURE_CNT.Value),
SUM(Fields!TOT_MEASURE_CNT.Value) - SUM(Fields!MEASURE_CNT.Value)
)</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=iif(inscope("LOCATION"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Color>#1f4e78</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <rd:Selected>true</rd:Selected>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Chart Name="DataBar7">
                          <ChartCategoryHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Label />
                              </ChartMember>
                            </ChartMembers>
                          </ChartCategoryHierarchy>
                          <ChartSeriesHierarchy>
                            <ChartMembers>
                              <ChartMember>
                                <Label />
                              </ChartMember>
                              <ChartMember>
                                <Label />
                              </ChartMember>
                            </ChartMembers>
                          </ChartSeriesHierarchy>
                          <ChartData>
                            <ChartSeriesCollection>
                              <ChartSeries Name="Series">
                                <ChartDataPoints>
                                  <ChartDataPoint>
                                    <ChartDataPointValues>
                                      <Y>
																						=IIF(Fields!INVERTED_FLG.Value=TRUE,CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value),
																						CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)

																						)
																					</Y>
                                    </ChartDataPointValues>
                                    <ChartDataLabel>
                                      <Style />
                                    </ChartDataLabel>
                                    <Style>
                                      <Color>CornflowerBlue</Color>
                                    </Style>
                                    <ChartMarker>
                                      <Style />
                                    </ChartMarker>
                                    <DataElementOutput>Output</DataElementOutput>
                                  </ChartDataPoint>
                                </ChartDataPoints>
                                <Type>Bar</Type>
                                <Subtype>PercentStacked</Subtype>
                                <Style />
                                <ChartEmptyPoints>
                                  <Style />
                                  <ChartMarker>
                                    <Style />
                                  </ChartMarker>
                                  <ChartDataLabel>
                                    <Style />
                                  </ChartDataLabel>
                                </ChartEmptyPoints>
                                <CustomProperties>
                                  <CustomProperty>
                                    <Name>PointWidth</Name>
                                    <Value>0.75</Value>
                                  </CustomProperty>
                                  <CustomProperty>
                                    <Name>BarLabelStyle</Name>
                                    <Value>Outside</Value>
                                  </CustomProperty>
                                </CustomProperties>
                                <ValueAxisName>Primary</ValueAxisName>
                                <CategoryAxisName>Primary</CategoryAxisName>
                                <ChartSmartLabel>
                                  <CalloutLineColor>Black</CalloutLineColor>
                                  <MinMovingDistance>0pt</MinMovingDistance>
                                </ChartSmartLabel>
                              </ChartSeries>
                              <ChartSeries Name="Series1">
                                <ChartDataPoints>
                                  <ChartDataPoint>
                                    <ChartDataPointValues>
                                      <Y>
																						=IIF( Fields!INVERTED_FLG.Value=TRUE,
																						CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value),
																						CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value))
																					</Y>
                                    </ChartDataPointValues>
                                    <ChartDataLabel>
                                      <Style />
                                    </ChartDataLabel>
                                    <Style>
                                      <Color>Red</Color>
                                    </Style>
                                    <ChartMarker>
                                      <Style />
                                    </ChartMarker>
                                    <DataElementOutput>Output</DataElementOutput>
                                  </ChartDataPoint>
                                </ChartDataPoints>
                                <Type>Bar</Type>
                                <Subtype>PercentStacked</Subtype>
                                <Style />
                                <ChartEmptyPoints>
                                  <Style />
                                  <ChartMarker>
                                    <Style />
                                  </ChartMarker>
                                  <ChartDataLabel>
                                    <Style />
                                  </ChartDataLabel>
                                </ChartEmptyPoints>
                                <CustomProperties>
                                  <CustomProperty>
                                    <Name>PointWidth</Name>
                                    <Value>0.75</Value>
                                  </CustomProperty>
                                </CustomProperties>
                                <ValueAxisName>Primary</ValueAxisName>
                                <CategoryAxisName>Primary</CategoryAxisName>
                                <ChartSmartLabel>
                                  <CalloutLineColor>Black</CalloutLineColor>
                                  <MinMovingDistance>0pt</MinMovingDistance>
                                </ChartSmartLabel>
                              </ChartSeries>
                            </ChartSeriesCollection>
                          </ChartData>
                          <ChartAreas>
                            <ChartArea Name="Default">
                              <ChartCategoryAxes>
                                <ChartAxis Name="Primary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <Margin>False</Margin>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <Margin>False</Margin>
                                  <ChartMajorGridLines>
                                    <Enabled>False</Enabled>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartCategoryAxes>
                              <ChartValueAxes>
                                <ChartAxis Name="Primary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Minimum>0</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                  <rd:SyncScope>Tablix5</rd:SyncScope>
                                  <rd:SyncMaximum>true</rd:SyncMaximum>
                                </ChartAxis>
                                <ChartAxis Name="Secondary">
                                  <Visible>False</Visible>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                  <ChartAxisTitle>
                                    <Caption>Axis Title</Caption>
                                    <Style>
                                      <FontSize>8pt</FontSize>
                                    </Style>
                                  </ChartAxisTitle>
                                  <ChartMajorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                      </Border>
                                    </Style>
                                  </ChartMajorGridLines>
                                  <ChartMinorGridLines>
                                    <Style>
                                      <Border>
                                        <Color>Gainsboro</Color>
                                        <Style>Dotted</Style>
                                      </Border>
                                    </Style>
                                  </ChartMinorGridLines>
                                  <ChartMinorTickMarks>
                                    <Length>0.5</Length>
                                  </ChartMinorTickMarks>
                                  <CrossAt>NaN</CrossAt>
                                  <Location>Opposite</Location>
                                  <Minimum>NaN</Minimum>
                                  <Maximum>NaN</Maximum>
                                  <ChartAxisScaleBreak>
                                    <Style />
                                  </ChartAxisScaleBreak>
                                </ChartAxis>
                              </ChartValueAxes>
                              <Style>
                                <BackgroundColor>#00ffffff</BackgroundColor>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                              <ChartElementPosition>
                                <Left>3</Left>
                                <Height>90</Height>
                                <Width>96</Width>
                              </ChartElementPosition>
                            </ChartArea>
                          </ChartAreas>
                          <Palette>BrightPastel</Palette>
                          <ChartBorderSkin>
                            <Style>
                              <BackgroundColor>Gray</BackgroundColor>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <Color>White</Color>
                            </Style>
                          </ChartBorderSkin>
                          <ChartNoDataMessage Name="NoDataMessage">
                            <Caption>No Data Available</Caption>
                            <Style>
                              <BackgroundGradientType>None</BackgroundGradientType>
                              <TextAlign>General</TextAlign>
                              <VerticalAlign>Top</VerticalAlign>
                            </Style>
                          </ChartNoDataMessage>
                          <rd:DesignerMode>DataBar</rd:DesignerMode>
                          <DataSetName>DS_PQRS_MEASURES</DataSetName>
                          <ToolTip>=" "</ToolTip>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <BackgroundGradientType>None</BackgroundGradientType>
                          </Style>
                        </Chart>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE,
IIF(ROUND(1000*(CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &gt; ROUND(1000*(CInt(Sum(Fields!PREV_TOT_MEASURE_CNT.Value) - Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),ChrW(8593),
IIF(ROUND(1000*(CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &lt; ROUND(1000*(CInt(Sum(Fields!PREV_TOT_MEASURE_CNT.Value) - Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),ChrW(8595),ChrW(8594))),
IIF(ROUND(1000*(CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &gt; ROUND(1000*(CInt(Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),ChrW(8593),
IIF(ROUND(1000*(CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &lt; ROUND(1000*(CInt(Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),ChrW(8595),ChrW(8594)))
)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>14pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=IIF(Fields!INVERTED_FLG.Value=TRUE,
IIF(ROUND(1000*(CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &gt; ROUND(1000*(CInt(Sum(Fields!PREV_TOT_MEASURE_CNT.Value) - Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),"Green",
IIF(ROUND(1000*(CInt(Sum(Fields!TOT_MEASURE_CNT.Value) - Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &lt; ROUND(1000*(CInt(Sum(Fields!PREV_TOT_MEASURE_CNT.Value) - Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),"Red","CornflowerBlue")),
IIF(ROUND(1000*(CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &gt; ROUND(1000*(CInt(Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),"Green",
IIF(ROUND(1000*(CInt(Sum(Fields!MEASURE_CNT.Value))/Sum(Fields!TOT_MEASURE_CNT.Value)),0) &lt; ROUND(1000*(CInt(Sum(Fields!PREV_MEASURE_CNT.Value))/Sum(Fields!PREV_TOT_MEASURE_CNT.Value)),0),"Red","CornflowerBlue"))
)</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Trend</ReportName>
                                  <Parameters>
                                    <Parameter Name="extractDt">
                                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="USER_ID">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="measure_cd">
                                      <Value>=Fields!MEASURE_CD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="REPORTING_YEAR">
                                      <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Top</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PQRS_AVG">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Sum(Fields!IS_OPERA.Value, "SITE_INFO") = 1, IIF(Fields!INVERTED_FLG.Value=TRUE, 1-Fields!PQRS_AVG.Value, Fields!PQRS_AVG.Value), "")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Normal</FontWeight>
                                    <Format>0%</Format>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PQRS_AVG</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.635cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox213">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>#</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox209</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox678">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox678</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox685">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox685</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox83">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox71</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox86">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Trend</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox73</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox92">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>AVG</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox75</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.5cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox64">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox64</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox65">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox65</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox679">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox679</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox686">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox686</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox585">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox585</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Wingdings</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>None</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>0%</Format>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>18.32604cm</Size>
                    <CellContents>
                      <Textbox Name="Textbox148">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>="Through Week " + Str(IIF(IsNothing(Parameters!rollingWeek.Value),(IIF(IsNothing(Fields!ROLLING_WEEK.Value),Sum(Fields!MAX_ROLLING_WEEK.Value, "ROLLING_WEEK"),First(Fields!ROLLING_WEEK.Value))),Parameters!rollingWeek.Value)) + ", " + Str(IIF(IsNothing(Fields!REPORTING_PERIOD.Value),Parameters!REPORTING_YEAR.Value,Fields!REPORTING_PERIOD.Value))</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Right</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox148</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>#1f4e78</Color>
                            <Style>Solid</Style>
                          </BottomBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <KeepWithGroup>After</KeepWithGroup>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <KeepWithGroup>After</KeepWithGroup>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>18.32604cm</Size>
                    <CellContents>
                      <Textbox Name="Textbox246">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>="Total Medicare Part B patients in care with one visit during the reporting period: " &amp; FormatNumber(SUM(Fields!TOT_CNT.Value),0)</Value>
                                <MarkupType>HTML</MarkupType>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>9pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox246</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>#0b6c9f</Color>
                            <Style>Solid</Style>
                          </BottomBorder>
                          <BackgroundColor>Black</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>8pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <KeepWithGroup>After</KeepWithGroup>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>18.32604cm</Size>
                    <CellContents>
                      <Textbox Name="Textbox151">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Individual Measures</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Left</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox151</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>#585858</Color>
                            <Style>Solid</Style>
                            <Width>1.25pt</Width>
                          </BottomBorder>
                          <LeftBorder>
                            <Color>#585858</Color>
                            <Style>Solid</Style>
                            <Width>1.25pt</Width>
                          </LeftBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <FixedData>true</FixedData>
                      <KeepWithGroup>After</KeepWithGroup>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.032cm</Size>
                    <CellContents>
                      <Textbox Name="Textbox105">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>#</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox105</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <RightBorder>
                            <Color>#0b6c9f</Color>
                          </RightBorder>
                          <BackgroundColor>Silver</BackgroundColor>
                          <VerticalAlign>Bottom</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>6.26447cm</Size>
                        <CellContents>
                          <Textbox Name="Textbox106">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>Title</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>11pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox106</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <RightBorder>
                                <Color>#0b6c9f</Color>
                              </RightBorder>
                              <BackgroundColor>Silver</BackgroundColor>
                              <VerticalAlign>Bottom</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.89cm</Size>
                            <CellContents>
                              <Textbox Name="Textbox107">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>NQS</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>11pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Left</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox107</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>Silver</BackgroundColor>
                                  <VerticalAlign>Bottom</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>0.93957cm</Size>
                                <CellContents>
                                  <Textbox Name="Textbox1">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>CCM</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>11pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Left</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox1</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Style>None</Style>
                                      </Border>
                                      <RightBorder>
                                        <Style>Solid</Style>
                                      </RightBorder>
                                      <BackgroundColor>Silver</BackgroundColor>
                                      <VerticalAlign>Bottom</VerticalAlign>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>4.1cm</Size>
                                    <CellContents>
                                      <Textbox Name="Textbox181">
                                        <CanGrow>true</CanGrow>
                                        <CanShrink>true</CanShrink>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>Location</Value>
                                                <Style>
                                                  <FontFamily>Calibri</FontFamily>
                                                  <FontSize>11pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox177</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <RightBorder>
                                            <Style>Solid</Style>
                                          </RightBorder>
                                          <BackgroundColor>Silver</BackgroundColor>
                                          <VerticalAlign>Middle</VerticalAlign>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>4.1cm</Size>
                                        <CellContents>
                                          <Textbox Name="Textbox204">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>Provider</Value>
                                                    <Style>
                                                      <FontFamily>Calibri</FontFamily>
                                                      <FontSize>11pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox200</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <RightBorder>
                                                <Style>Double</Style>
                                                <Width>1.5pt</Width>
                                              </RightBorder>
                                              <BackgroundColor>Silver</BackgroundColor>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <FixedData>true</FixedData>
                                          <KeepWithGroup>After</KeepWithGroup>
                                          <RepeatOnNewPage>true</RepeatOnNewPage>
                                        </TablixMember>
                                      </TablixMembers>
                                      <FixedData>true</FixedData>
                                      <KeepWithGroup>After</KeepWithGroup>
                                      <RepeatOnNewPage>true</RepeatOnNewPage>
                                    </TablixMember>
                                  </TablixMembers>
                                  <FixedData>true</FixedData>
                                  <KeepWithGroup>After</KeepWithGroup>
                                  <RepeatOnNewPage>true</RepeatOnNewPage>
                                  <KeepTogether>true</KeepTogether>
                                </TablixMember>
                              </TablixMembers>
                              <FixedData>true</FixedData>
                              <KeepWithGroup>After</KeepWithGroup>
                              <RepeatOnNewPage>true</RepeatOnNewPage>
                            </TablixMember>
                          </TablixMembers>
                          <FixedData>true</FixedData>
                          <KeepWithGroup>After</KeepWithGroup>
                          <RepeatOnNewPage>true</RepeatOnNewPage>
                        </TablixMember>
                      </TablixMembers>
                      <FixedData>true</FixedData>
                      <KeepWithGroup>After</KeepWithGroup>
                      <RepeatOnNewPage>true</RepeatOnNewPage>
                    </TablixMember>
                  </TablixMembers>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <Group Name="ALL">
                    <GroupExpressions>
                      <GroupExpression>1</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>1</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="MEASURE_NM">
                        <GroupExpressions>
                          <GroupExpression>=Fields!MEASURE_NM.Value</GroupExpression>
                          <GroupExpression>=Fields!MEASURE_CD.Value</GroupExpression>
                          <GroupExpression>=Fields!REPORT_LEVEL_DESC_2.Value</GroupExpression>
                        </GroupExpressions>
                        <Filters>
                          <Filter>
                            <FilterExpression>=Fields!REPORT_LEVEL_DESC_1.Value</FilterExpression>
                            <Operator>Equal</Operator>
                            <FilterValues>
                              <FilterValue>Individual</FilterValue>
                            </FilterValues>
                          </Filter>
                        </Filters>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!MEASURE_CD.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>18.32604cm</Size>
                            <CellContents>
                              <Textbox Name="Textbox532">
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>9pt</FontSize>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox532</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>Black</BackgroundColor>
                                  <VerticalAlign>Top</VerticalAlign>
                                  <PaddingLeft>6pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>1pt</PaddingTop>
                                  <PaddingBottom>1pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                        <TablixMember>
                          <TablixHeader>
                            <Size>2.032cm</Size>
                            <CellContents>
                              <Textbox Name="MEASURE_NM">
                                <CanGrow>true</CanGrow>
                                <ToggleImage>
                                  <InitialState>=iif(CountDistinct(Fields!LOCATION.Value,"MEASURE_NM") = 1, true,false)</InitialState>
                                </ToggleImage>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!MEASURE_CD.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>8.5pt</FontSize>
                                          <FontWeight>Bold</FontWeight>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>MEASURE_NM</rd:DefaultName>
                                <ActionInfo>
                                  <Actions>
                                    <Action>
                                      <Hyperlink>="javascript:void(window.open(' https://qpp.cms.gov/ '))"</Hyperlink>
                                    </Action>
                                  </Actions>
                                </ActionInfo>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>Lavender</BackgroundColor>
                                  <VerticalAlign>Top</VerticalAlign>
                                  <PaddingLeft>6pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>1pt</PaddingTop>
                                  <PaddingBottom>1pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>6.26447cm</Size>
                                <CellContents>
                                  <Textbox Name="Textbox88">
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>= Fields!MEASURE_NM.Value</Value>
                                            <ToolTip>=IIF(Fields!INVERTED_FLG.Value=TRUE, "Inverse Measure: " + Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</ToolTip>
                                            <MarkupType>HTML</MarkupType>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>8.5pt</FontSize>
                                              <FontWeight>Bold</FontWeight>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style />
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox88</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Style>None</Style>
                                      </Border>
                                      <BackgroundColor>Lavender</BackgroundColor>
                                      <VerticalAlign>Top</VerticalAlign>
                                      <PaddingLeft>1pt</PaddingLeft>
                                      <PaddingRight>1pt</PaddingRight>
                                      <PaddingTop>1pt</PaddingTop>
                                      <PaddingBottom>1pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.89cm</Size>
                                    <CellContents>
                                      <Textbox Name="REPORT_LEVEL_DESC_2">
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>
																											= Switch(Fields!REPORT_LEVEL_DESC_2.Value = "Effective Clinical Care", "ECC",
																											Fields!REPORT_LEVEL_DESC_2.Value = "Communication and Care Coordination", "CCC",
																											Fields!REPORT_LEVEL_DESC_2.Value = "Community/Population Health", "CPH",
																											Fields!REPORT_LEVEL_DESC_2.Value = "Efficiency and Cost Reduction", "ECR",
																											Fields!REPORT_LEVEL_DESC_2.Value = "Patient Safety", "PS",
																											Fields!REPORT_LEVEL_DESC_2.Value = "Person and Caregiver-Centered Experience and Outcomes", "PCCEO")
																										</Value>
                                                <Style>
                                                  <FontFamily>Calibri</FontFamily>
                                                  <FontSize>8.5pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>REPORT_LEVEL_DESC_2</rd:DefaultName>
                                        <ToolTip>=Fields!REPORT_LEVEL_DESC_2.Value</ToolTip>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <BackgroundColor>Lavender</BackgroundColor>
                                          <VerticalAlign>Top</VerticalAlign>
                                          <PaddingLeft>6pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>1pt</PaddingTop>
                                          <PaddingBottom>1pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>0.93957cm</Size>
                                        <CellContents>
                                          <Textbox Name="REPORT_LEVEL_DESC_4">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=IIF(Fields!REPORT_LEVEL_DESC_3.Value = "CCM","Y","N")</Value>
                                                    <Style>
                                                      <FontFamily>Calibri</FontFamily>
                                                      <FontSize>8.5pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Left</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BackgroundColor>Lavender</BackgroundColor>
                                              <VerticalAlign>Top</VerticalAlign>
                                              <PaddingLeft>8pt</PaddingLeft>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <Group Name="LOCATION">
                                            <GroupExpressions>
                                              <GroupExpression>=Fields!LOCATION.Value</GroupExpression>
                                            </GroupExpressions>
                                          </Group>
                                          <SortExpressions>
                                            <SortExpression>
                                              <Value>=Fields!LOCATION.Value</Value>
                                            </SortExpression>
                                          </SortExpressions>
                                          <TablixHeader>
                                            <Size>4.1cm</Size>
                                            <CellContents>
                                              <Textbox Name="LOCATION">
                                                <CanGrow>true</CanGrow>
                                                <CanShrink>true</CanShrink>
                                                <KeepTogether>true</KeepTogether>
                                                <Paragraphs>
                                                  <Paragraph>
                                                    <TextRuns>
                                                      <TextRun>
                                                        <Value>=Fields!LOCATION.Value &amp; " (" &amp; FormatNumber(SUM(Fields!TOT_MEASURE_CNT.Value), 0) &amp; ")"</Value>
                                                        <Style>
                                                          <FontFamily>Calibri</FontFamily>
                                                          <FontSize>8pt</FontSize>
                                                          <FontWeight>Bold</FontWeight>
                                                        </Style>
                                                      </TextRun>
                                                    </TextRuns>
                                                    <Style />
                                                  </Paragraph>
                                                </Paragraphs>
                                                <rd:DefaultName>LOCATION</rd:DefaultName>
                                                <ActionInfo>
                                                  <Actions>
                                                    <Action>
                                                      <Drillthrough>
                                                        <ReportName>Quality_Measures</ReportName>
                                                        <Parameters>
                                                          <Parameter Name="extractDt">
                                                            <Value>=Parameters!extractDt.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="locationCd">
                                                            <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="providerCd">
                                                            <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="drillthroughLocationCd">
                                                            <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="drillthroughProviderCd">
                                                            <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="USER_ID">
                                                            <Value>=Parameters!USER_ID.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="COHORT_ID">
                                                            <Value>=Fields!COHORT_ID.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="REPORTING_YEAR">
                                                            <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="rollingWeek">
                                                            <Value>=Parameters!rollingWeek.Value</Value>
                                                          </Parameter>
                                                        </Parameters>
                                                      </Drillthrough>
                                                    </Action>
                                                  </Actions>
                                                </ActionInfo>
                                                <Style>
                                                  <Border>
                                                    <Style>None</Style>
                                                  </Border>
                                                  <BottomBorder>
                                                    <Color>Gray</Color>
                                                    <Style>Solid</Style>
                                                  </BottomBorder>
                                                  <BackgroundColor>Lavender</BackgroundColor>
                                                  <VerticalAlign>Top</VerticalAlign>
                                                  <PaddingLeft>6pt</PaddingLeft>
                                                  <PaddingRight>2pt</PaddingRight>
                                                  <PaddingTop>1pt</PaddingTop>
                                                  <PaddingBottom>1pt</PaddingBottom>
                                                </Style>
                                              </Textbox>
                                            </CellContents>
                                          </TablixHeader>
                                          <TablixMembers>
                                            <TablixMember>
                                              <Group Name="PRIMARY_PROVIDER">
                                                <GroupExpressions>
                                                  <GroupExpression>=Fields!PRIMARY_PROVIDER.Value</GroupExpression>
                                                </GroupExpressions>
                                              </Group>
                                              <SortExpressions>
                                                <SortExpression>
                                                  <Value>=Fields!PRIMARY_PROVIDER.Value</Value>
                                                </SortExpression>
                                              </SortExpressions>
                                              <TablixHeader>
                                                <Size>4.1cm</Size>
                                                <CellContents>
                                                  <Textbox Name="PRIMARY_PROVIDER">
                                                    <CanGrow>true</CanGrow>
                                                    <KeepTogether>true</KeepTogether>
                                                    <Paragraphs>
                                                      <Paragraph>
                                                        <TextRuns>
                                                          <TextRun>
                                                            <Value>=Fields!PRIMARY_PROVIDER.Value &amp; " (" &amp; FormatNumber(Fields!TOT_MEASURE_CNT.Value, 0) &amp; ")"</Value>
                                                            <Style>
                                                              <FontFamily>Calibri</FontFamily>
                                                              <FontSize>8.5pt</FontSize>
                                                              <FontWeight>Bold</FontWeight>
                                                            </Style>
                                                          </TextRun>
                                                        </TextRuns>
                                                        <Style />
                                                      </Paragraph>
                                                    </Paragraphs>
                                                    <rd:DefaultName>PRIMARY_PROVIDER</rd:DefaultName>
                                                    <ActionInfo>
                                                      <Actions>
                                                        <Action>
                                                          <Drillthrough>
                                                            <ReportName>Quality_Measures</ReportName>
                                                            <Parameters>
                                                              <Parameter Name="locationCd">
                                                                <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                                              </Parameter>
                                                              <Parameter Name="providerCd">
                                                                <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                              </Parameter>
                                                              <Parameter Name="extractDt">
                                                                <Value>=Parameters!extractDt.Value</Value>
                                                              </Parameter>
                                                              <Parameter Name="drillthroughLocationCd">
                                                                <Value>=iif(inscope("LOCATION"),Fields!LOCATION.Value,"")</Value>
                                                              </Parameter>
                                                              <Parameter Name="drillthroughProviderCd">
                                                                <Value>=iif(inscope("PRIMARY_PROVIDER"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                              </Parameter>
                                                              <Parameter Name="USER_ID">
                                                                <Value>=Parameters!USER_ID.Value</Value>
                                                              </Parameter>
                                                              <Parameter Name="COHORT_ID">
                                                                <Value>=Fields!COHORT_ID.Value</Value>
                                                              </Parameter>
                                                              <Parameter Name="REPORTING_YEAR">
                                                                <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                                              </Parameter>
                                                              <Parameter Name="rollingWeek">
                                                                <Value>=Parameters!rollingWeek.Value</Value>
                                                              </Parameter>
                                                            </Parameters>
                                                          </Drillthrough>
                                                        </Action>
                                                      </Actions>
                                                    </ActionInfo>
                                                    <Style>
                                                      <Border>
                                                        <Style>None</Style>
                                                      </Border>
                                                      <BottomBorder>
                                                        <Color>Gray</Color>
                                                        <Style>Solid</Style>
                                                        <Width>0.5pt</Width>
                                                      </BottomBorder>
                                                      <BackgroundColor>Lavender</BackgroundColor>
                                                      <VerticalAlign>Top</VerticalAlign>
                                                      <PaddingLeft>6pt</PaddingLeft>
                                                      <PaddingRight>2pt</PaddingRight>
                                                      <PaddingTop>1pt</PaddingTop>
                                                      <PaddingBottom>1pt</PaddingBottom>
                                                    </Style>
                                                  </Textbox>
                                                </CellContents>
                                              </TablixHeader>
                                              <TablixMembers>
                                                <TablixMember />
                                              </TablixMembers>
                                              <Visibility>
                                                <Hidden>=iif(CountDistinct(Fields!PRIMARY_PROVIDER.Value,"MEASURE_NM")=1,False,True)</Hidden>
                                                <ToggleItem>LOCATION</ToggleItem>
                                              </Visibility>
                                            </TablixMember>
                                          </TablixMembers>
                                          <Visibility>
                                            <Hidden>=iif(CountDistinct(Fields!LOCATION.Value,"MEASURE_NM") = 1, False,true)</Hidden>
                                            <ToggleItem>MEASURE_NM</ToggleItem>
                                          </Visibility>
                                        </TablixMember>
                                      </TablixMembers>
                                    </TablixMember>
                                  </TablixMembers>
                                </TablixMember>
                              </TablixMembers>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <Group Name="MEASURE_NM1">
                    <GroupExpressions>
                      <GroupExpression>=Fields!MEASURE_NM.Value</GroupExpression>
                    </GroupExpressions>
                    <Filters>
                      <Filter>
                        <FilterExpression>=Fields!REPORT_LEVEL_DESC_1.Value</FilterExpression>
                        <Operator>Equal</Operator>
                        <FilterValues>
                          <FilterValue>Group</FilterValue>
                        </FilterValues>
                      </Filter>
                    </Filters>
                  </Group>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>18.32604cm</Size>
                        <CellContents>
                          <Textbox Name="Textbox19">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>Group Measures</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Left</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox18</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BottomBorder>
                                <Color>Silver</Color>
                                <Style>Solid</Style>
                              </BottomBorder>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <VerticalAlign>Top</VerticalAlign>
                              <PaddingLeft>6pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>1pt</PaddingTop>
                              <PaddingBottom>1pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>2.032cm</Size>
                        <CellContents>
                          <Textbox Name="MEASURE_NM1">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!MEASURE_CD.Value &amp; " - " &amp; Fields!MEASURE_NM.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>9pt</FontSize>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>MEASURE_NM1</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BottomBorder>
                                <Color>Silver</Color>
                                <Style>Solid</Style>
                                <Width>1pt</Width>
                              </BottomBorder>
                              <BackgroundColor>Lavender</BackgroundColor>
                              <VerticalAlign>Top</VerticalAlign>
                              <PaddingLeft>6pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>1pt</PaddingTop>
                              <PaddingBottom>1pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>6.26447cm</Size>
                            <CellContents>
                              <Textbox Name="Textbox90">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>9.5pt</FontSize>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox90</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BottomBorder>
                                    <Color>Silver</Color>
                                    <Style>Solid</Style>
                                    <Width>1pt</Width>
                                  </BottomBorder>
                                  <BackgroundColor>Lavender</BackgroundColor>
                                  <VerticalAlign>Top</VerticalAlign>
                                  <PaddingLeft>6pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>1pt</PaddingTop>
                                  <PaddingBottom>1pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>0.89cm</Size>
                                <CellContents>
                                  <Textbox Name="REPORT_LEVEL_DESC_3">
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>=Switch(Fields!REPORT_LEVEL_DESC_2.Value = "Effective Clinical Care", "ECC", Fields!REPORT_LEVEL_DESC_2.Value = "Communication and Care Coordination", "CCC", Fields!REPORT_LEVEL_DESC_2.Value = "Community/Population Health", "CPH", Fields!REPORT_LEVEL_DESC_2.Value = "Efficiency and Cost Reduction", "ECR", Fields!REPORT_LEVEL_DESC_2.Value = "Patient Safety", "PS", Fields!REPORT_LEVEL_DESC_2.Value = "Person and Caregiver-Centered Experience and Outcomes", "PCCEO")</Value>
                                            <Style>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>9.5pt</FontSize>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Left</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>REPORT_LEVEL_DESC_2</rd:DefaultName>
                                    <ToolTip>=Fields!REPORT_LEVEL_DESC_2.Value</ToolTip>
                                    <Style>
                                      <Border>
                                        <Style>None</Style>
                                      </Border>
                                      <BottomBorder>
                                        <Color>Silver</Color>
                                        <Style>Solid</Style>
                                      </BottomBorder>
                                      <BackgroundColor>Lavender</BackgroundColor>
                                      <VerticalAlign>Top</VerticalAlign>
                                      <PaddingLeft>6pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>1pt</PaddingTop>
                                      <PaddingBottom>1pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.93957cm</Size>
                                    <CellContents>
                                      <Textbox Name="Textbox5">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Calibri</FontFamily>
                                                  <FontSize>9.5pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Left</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox5</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <BottomBorder>
                                            <Color>Silver</Color>
                                            <Style>Solid</Style>
                                          </BottomBorder>
                                          <BackgroundColor>Lavender</BackgroundColor>
                                          <VerticalAlign>Top</VerticalAlign>
                                          <PaddingLeft>6pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>1pt</PaddingTop>
                                          <PaddingBottom>1pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <Group Name="LOCATION1">
                                        <GroupExpressions>
                                          <GroupExpression>=Fields!LOCATION.Value</GroupExpression>
                                        </GroupExpressions>
                                      </Group>
                                      <TablixHeader>
                                        <Size>4.1cm</Size>
                                        <CellContents>
                                          <Textbox Name="LOCATION1">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LOCATION.Value &amp; " (" &amp; FORMATNUMBER(sum(Fields!TOT_MEASURE_CNT.Value),0) &amp; ")"</Value>
                                                    <Style>
                                                      <FontFamily>Calibri</FontFamily>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LOCATION1</rd:DefaultName>
                                            <ActionInfo>
                                              <Actions>
                                                <Action>
                                                  <Drillthrough>
                                                    <ReportName>Quality_Measures</ReportName>
                                                    <Parameters>
                                                      <Parameter Name="extractDt">
                                                        <Value>=Parameters!extractDt.Value</Value>
                                                      </Parameter>
                                                      <Parameter Name="locationCd">
                                                        <Value>=iif(inscope("LOCATION1"),Fields!LOCATION.Value,"")</Value>
                                                      </Parameter>
                                                      <Parameter Name="providerCd">
                                                        <Value>=iif(inscope("PRIMARY_PROVIDER1"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                      </Parameter>
                                                      <Parameter Name="drillthroughLocationCd">
                                                        <Value>=iif(inscope("LOCATION1"),Fields!LOCATION.Value,"")</Value>
                                                      </Parameter>
                                                      <Parameter Name="drillthroughProviderCd">
                                                        <Value>=iif(inscope("PRIMARY_PROVIDER1"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                      </Parameter>
                                                      <Parameter Name="USER_ID">
                                                        <Value>=Parameters!USER_ID.Value</Value>
                                                      </Parameter>
                                                      <Parameter Name="COHORT_ID">
                                                        <Value>=Fields!COHORT_ID.Value</Value>
                                                      </Parameter>
                                                      <Parameter Name="REPORTING_YEAR">
                                                        <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                                      </Parameter>
                                                      <Parameter Name="rollingWeek">
                                                        <Value>=Parameters!rollingWeek.Value</Value>
                                                      </Parameter>
                                                    </Parameters>
                                                  </Drillthrough>
                                                </Action>
                                              </Actions>
                                            </ActionInfo>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Silver</Color>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <BackgroundColor>Lavender</BackgroundColor>
                                              <VerticalAlign>Top</VerticalAlign>
                                              <PaddingLeft>6pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>1pt</PaddingTop>
                                              <PaddingBottom>1pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <Group Name="PRIMARY_PROVIDER1">
                                            <GroupExpressions>
                                              <GroupExpression>=Fields!PRIMARY_PROVIDER.Value</GroupExpression>
                                            </GroupExpressions>
                                          </Group>
                                          <TablixHeader>
                                            <Size>4.1cm</Size>
                                            <CellContents>
                                              <Textbox Name="PRIMARY_PROVIDER1">
                                                <CanGrow>true</CanGrow>
                                                <KeepTogether>true</KeepTogether>
                                                <Paragraphs>
                                                  <Paragraph>
                                                    <TextRuns>
                                                      <TextRun>
                                                        <Value>=Fields!PRIMARY_PROVIDER.Value &amp; " (" &amp; FORMATNUMBER(sum(Fields!TOT_MEASURE_CNT.Value),0) &amp; ")"</Value>
                                                        <Style>
                                                          <FontFamily>Calibri</FontFamily>
                                                        </Style>
                                                      </TextRun>
                                                    </TextRuns>
                                                    <Style />
                                                  </Paragraph>
                                                </Paragraphs>
                                                <rd:DefaultName>PRIMARY_PROVIDER1</rd:DefaultName>
                                                <ActionInfo>
                                                  <Actions>
                                                    <Action>
                                                      <Drillthrough>
                                                        <ReportName>Quality_Measures</ReportName>
                                                        <Parameters>
                                                          <Parameter Name="extractDt">
                                                            <Value>=Parameters!extractDt.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="locationCd">
                                                            <Value>=iif(inscope("LOCATION1"),Fields!LOCATION.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="providerCd">
                                                            <Value>=iif(inscope("PRIMARY_PROVIDER1"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="drillthroughLocationCd">
                                                            <Value>=iif(inscope("LOCATION1"),Fields!LOCATION.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="drillthroughProviderCd">
                                                            <Value>=iif(inscope("PRIMARY_PROVIDER1"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                                          </Parameter>
                                                          <Parameter Name="USER_ID">
                                                            <Value>=Parameters!USER_ID.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="COHORT_ID">
                                                            <Value>=Fields!COHORT_ID.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="REPORTING_YEAR">
                                                            <Value>=Parameters!REPORTING_YEAR.Value</Value>
                                                          </Parameter>
                                                          <Parameter Name="rollingWeek">
                                                            <Value>=Parameters!rollingWeek.Value</Value>
                                                          </Parameter>
                                                        </Parameters>
                                                      </Drillthrough>
                                                    </Action>
                                                  </Actions>
                                                </ActionInfo>
                                                <Style>
                                                  <Border>
                                                    <Style>None</Style>
                                                  </Border>
                                                  <BottomBorder>
                                                    <Color>Silver</Color>
                                                    <Style>Solid</Style>
                                                  </BottomBorder>
                                                  <RightBorder>
                                                    <Color>DimGray</Color>
                                                    <Style>Solid</Style>
                                                  </RightBorder>
                                                  <BackgroundColor>Lavender</BackgroundColor>
                                                  <VerticalAlign>Top</VerticalAlign>
                                                  <PaddingLeft>6pt</PaddingLeft>
                                                  <PaddingRight>2pt</PaddingRight>
                                                  <PaddingTop>1pt</PaddingTop>
                                                  <PaddingBottom>1pt</PaddingBottom>
                                                </Style>
                                              </Textbox>
                                            </CellContents>
                                          </TablixHeader>
                                          <TablixMembers>
                                            <TablixMember />
                                          </TablixMembers>
                                          <Visibility>
                                            <Hidden>true</Hidden>
                                            <ToggleItem>LOCATION1</ToggleItem>
                                          </Visibility>
                                        </TablixMember>
                                      </TablixMembers>
                                      <Visibility>
                                        <Hidden>true</Hidden>
                                        <ToggleItem>MEASURE_NM1</ToggleItem>
                                      </Visibility>
                                    </TablixMember>
                                  </TablixMembers>
                                </TablixMember>
                              </TablixMembers>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <KeepTogether>true</KeepTogether>
            <DataSetName>DS_PQRS_MEASURES</DataSetName>
            <SortExpressions>
              <SortExpression>
                <Value>=Fields!ROLLING_WEEK.Value</Value>
                <Direction>Descending</Direction>
              </SortExpression>
            </SortExpressions>
            <Top>0.01389in</Top>
            <Height>4.97276cm</Height>
            <Width>26.52617cm</Width>
            <Style>
              <Border>
                <Color>#0b6c9f</Color>
                <Style>Solid</Style>
                <Width>0.25pt</Width>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>1.97167in</Height>
        <Style />
      </Body>
      <Width>10.46421in</Width>
      <Page>
        <PageHeader>
          <Height>0.65257in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox13">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIf(Parameters!REPORTING_YEAR.Value = 2016, Parameters!REPORTING_YEAR.Value &amp; " - PQRS Measures", Parameters!REPORTING_YEAR.Value &amp; " - MIPS Measures")</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Top>0.33365cm</Top>
              <Left>9.40062cm</Left>
              <Height>0.83087cm</Height>
              <Width>7.84933cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Height>0.97931cm</Height>
              <Width>9.19396cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.00289in</Top>
              <Left>8.11344in</Left>
              <Height>0.38267in</Height>
              <Width>2.30867in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox14">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Normal</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox14</rd:DefaultName>
              <Top>1.41146cm</Top>
              <Height>0.14023cm</Height>
              <Width>26.47217cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Color>Purple</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </TopBorder>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>0.73264in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox52">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>DATA NOTES</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=IIF(Parameters!REPORTING_YEAR.Value=2016,"The Physician Quality Reporting System (PQRS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All 2016 PQRS measures can be found at:  ",  "The Merit-based Incentive Payment System (MIPS) is a CMS quality reporting program for covered Physician Fee Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All " +CStr(Parameters!REPORTING_YEAR.Value)+" MIPS &amp; eCQM measures can be found at:  ")</Value>
                      <MarkupType>HTML</MarkupType>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=iif(Parameters!REPORTING_YEAR.Value=2016,"https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp", "https://qpp.cms.gov/")</Value>
                      <ActionInfo>
                        <Actions>
                          <Action>
                            <Hyperlink>=iif(Parameters!REPORTING_YEAR.Value=2016,"javascript:void(window.open('https://www.cms.gov/Medicare/Quality-Initiatives-Patient-Assessment-Instruments/PQRS/index.html?redirect=/PQRS/15_measurescodes.Asp'))","javascript:void(window.open(' https://qpp.cms.gov/ '))")</Hyperlink>
                          </Action>
                        </Actions>
                      </ActionInfo>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <Color>#0000ff</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>The All OPERA Average comparison data is only available to sites participating in OPERA</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <TextDecoration>None</TextDecoration>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListStyle>Bulleted</ListStyle>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListLevel>1</ListLevel>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox52</rd:DefaultName>
              <Top>0.14111cm</Top>
              <Left>0.10431in</Left>
              <Height>0.61459in</Height>
              <Width>26.20722cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>8.5in</PageHeight>
        <PageWidth>11in</PageWidth>
        <LeftMargin>0.3in</LeftMargin>
        <RightMargin>0.2in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <ColumnSpacing>0.05118in</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="extractDt">
      <DataType>DateTime</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <DataSetReference>
          <DataSetName>Extract_Date</DataSetName>
          <ValueField>EXTRACT_DT</ValueField>
        </DataSetReference>
      </DefaultValue>
      <Prompt>extractDt</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>locationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>providerCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughProviderCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughProviderCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughLocationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughLocationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>9f779edd-0c47-4f8a-b859-df282a6706eb</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="rollingWeek">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>rollingWeek</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="REPORTING_YEAR">
      <DataType>String</DataType>
      <Prompt>REPORTING YEAR</Prompt>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <Prompt>COHORT_ID</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>5</NumberOfColumns>
      <NumberOfRows>3</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>extractDt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>drillthroughProviderCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>drillthroughLocationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>rollingWeek</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>REPORTING_YEAR</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="ID1480625394_StockIndexUp">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADNUlEQVRIiaWW32scVRTHP/fcu1OlFNOfq1aK1aSg0DJGKP7CZpNYUBD/hQgGU1ta6Vuf+tYn6ZsPK0r0D1ApFqUldQql9kWbtJSKiArJ2iRN22zNSrPZmXt9uDO7m2R3SeKFw70MzPd7vud77pnBOceaV5FRijiKjK79pXWAv/HNm+7z8S/d01/tXjOJXit4IT8wdKznY0qVaXq3HuRudTacOzz7LOc59/8Iioz25fuHjvQcY3zuJuP3bhIEAQd3vcL80nw4M3inI0lnghR8pOcol0s/8fvCn8SScK96n0fJIq/ueo1yrRzODk6HvMsFzrO4EkI551BKrQTuAr7ty/f3jew7SlS6yuS/JRISEhw4C8qx8/GddHft5bupr7nx4PoEjgIjlDsTePCoL98fftA9woXJiLml+8QuxjqLdRaHA+VQKDYHm9m//QV+mDrHjbnrEyQUON4gkRaZR4X8QDjcfWQZeOIsiUtSFXF6jqnUFrj14Dbv7HmPcFtvSELEJ3StVtAEPvTcMBenfmS+9pCaS8FcgiXBOkvVLRHbGqIUIoJWwpZgCy/tOMDYH98z/vcvE1gKnKLsCT5TXUDUnx8M339+mIuTl5mPy8QuZubRXSq1Cgu1ClVb9Z0hghad7o0IdMDLO0J+/usav925PYGlYFIl0cCTg+FH+04wWSmxd9sechWDxXJl+hoO50G0xogPf/ZEbz1TQJRCKYUoxeEX30ashL9O3YoygvDSzBiXZsaWWXKm9yxbH3uCh0v/NMCX7QajBaM1n0ZnweIjITuHnuBDVvQpUMT5jDRaGqA5bTCS7jp7piEGTq/GMauAs2VBKZWWRuqlMaLJGVMn8LvxWbdY7QliUKiGoVpjtAduFcTrJUi8AhHB1EM3lcUQZEpEtyWQ1o+9AlEKrQRRXkVmaHPmgcl5BW1K1JEgU9Do+WYfUi+M92L9
ChLvgagmH+pe+BbNaUOgcxskiAEFosSPhIyoXq6mEL3BEpHdTkEk86IR2X3QGzI5AaW8D9kIkJRM14ka84g2/w4d7wEoDj31OlW7WL9cWef4Fs2xyeQQ1T7P1l80gJO4+mxpFa0y/mL1qPgP7488BMECRR8AAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrow_bottom">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAB60lEQVRIidWVv24TQRDGf2ODREOTAuVJIChBQUBDQRGJhhKJJ4jJA0SUEUpJkyIPgNKARIQsJLoUJiCEUqQgUoJCCuQHIDvzUfhs39m+P0Y0THG3tzsz38433+4ZNabV5eck35IEnrAICIECpXRo8MS+Hh2XxbfqAIDrGRQ2sWDoGnClKrgJQJas+excANJ0MmUPzfCfG8AmNqkhlA3edSBzVJAB5pGoJ6myQTCoII9hU4Nqa9jkgYImc/6THgDYX2uoAUVQQlFD+/8pqgTQ3TutGp8WNTSPZf3o4Qoe93AHRRt3x+MqigckXyK77IhAEZgEKfUV2rPwc9yRwi3Uxv0n8NpOzn7l0HUD2Bx9AZYdAlGkw8ZneMHg2Yz5feBtgSJ7s7+H1BFcTCcssfwxH4wTcIDZUzs5+1EAyGzXpG3goiznoLphYhsrySwBXaBj309H8QUAe/e+D+wYbOUlOCnHkapsXJ2kHvCCVuug6Fu2y/ur6/LYMMUiyck3efhH4/ISRCK8B6zZ0fFU5eUSFLsG24Lz6aXhVWoJowvWmZW8EsA+fOyDdoCXZRSJjJZ2kZaibwPT8tK6IjYsfDGjKBHRg1izz98qBdH0LsrT9VvQxejUJW9cAYBu31xQ+GMibiG9MnFon75EXdwfWr3pc8rHjZUAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrow_bottom1">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABiklEQVQ4jaWUv0tcQRSFv6MiFkFSpLSyWFKK4N+RKo1V2lQWSZci1RLSS/DPSG2stxRsLF5AUllYqcQlhLlzUrz9Matv3hq9MDDMvPvNuefOPFEJb2+/IucfROw4AiZDEYmc3+rm5ntX3koNONlb9/2D7DVgrS+pN1QMAEk9X/cA7VZbh8KnAUslrqz/F3AGKKD9qCXArpL7i22j2i1JYD9KVRlLFS6sPQc4Nd+PBC0F0tqxUpZczNerQgC8u7tJzl+JeM38mW2Q0o4jNlh8ehDROKVLIlDOkFLC/iwYTZvyB/gJ7AObD058GAPBgNbnDBwDl7OSdXr6F/sIewhc99gALPiaDMdIB0i/ZkAAnZ2Nsb8BQ+YJ1YYIQBpJ+qCcG01uRWdFHgw+OudPSuklEXju3XQkUhoBbzQeL1RU6/IR9hfgtlQ6mWfgBOn9fVgVqKb5LfsQeyi4LX5hbQOkA93dnXfl1i/2xcUYOKT19JqyAaurTTWvtjENb229IOIdOe9hD3V1VYUB/AOBBcVBBpO3rgAAAABJRU5ErkJggg==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrow_bottom2">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABiklEQVQ4jaWUv0tcQRSFv6MiFkFSpLSyWFKK4N+RKo1V2lQWSZci1RLSS/DPSG2stxRsLF5AUllYqcQlhLlzUrz9Matv3hq9MDDMvPvNuefOPFEJb2+/IucfROw4AiZDEYmc3+rm5ntX3koNONlb9/2D7DVgrS+pN1QMAEk9X/cA7VZbh8KnAUslrqz/F3AGKKD9qCXArpL7i22j2i1JYD9KVRlLFS6sPQc4Nd+PBC0F0tqxUpZczNerQgC8u7tJzl+JeM38mW2Q0o4jNlh8ehDROKVLIlDOkFLC/iwYTZvyB/gJ7AObD058GAPBgNbnDBwDl7OSdXr6F/sIewhc99gALPiaDMdIB0i/ZkAAnZ2Nsb8BQ+YJ1YYIQBpJ+qCcG01uRWdFHgw+OudPSuklEXju3XQkUhoBbzQeL1RU6/IR9hfgtlQ6mWfgBOn9fVgVqKb5LfsQeyi4LX5hbQOkA93dnXfl1i/2xcUYOKT19JqyAaurTTWvtjENb229IOIdOe9hD3V1VYUB/AOBBcVBBpO3rgAAAABJRU5ErkJggg==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrowdown">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAA4AAAAQCAYAAAAmlE46AAAAwElEQVQokaXRsW0CQRAF0HfIOhE4IqIMAiIXQQFYckeUYdEEpG6C6EpA5wgJPoEt+3zcokMeaYLVztsZ7dCJsAptSC/bCyuluLAdQAm5sO3WTnr2qfhq764PR8f/YVAxLRVWTNM9hze8hrriBXXBnsJHxQnvVWjx/OCknxPsHkSwE2bhUNrfQB7CDIRFOI5Ax7D40zusw/kOOof1zdD5ys0duMmN+sV12A+gfcpr+sHz0HRQE+aj/josv0ETlkM1V8r3nME4mtfmAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrowdown1">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABE0lEQVRIie2TMUoDQRSGv5GwhBSeQKxSps4NbIJ4APEMKT2ABwl4ghxAcghLsUqVQmyDW+T9FtmQx7KTnZmgVX54MCzvff8/bxm46FwJxgL5suN53Dd/VWBIyOjPNsiBAwxSmvTXBiXgg5JWVAqH/16RYOihDaw6Aa78jAvxEzN4DnDngYJhLLXg1cMavQEvnQMGtwZLQd1+XH1lUBssDW4iefZrEUwMVj2wrvNKMFGU7kxsb7LuAkZqbSnwltFU8G6wO3GTXdMzzWEfDAaCmeAztiKDD4OZZTzYtkkleLDun14L7lUKbxk9GWzsmH4jeDwb7AxGgrnBl8G3YC4YpcwmXS/AVrAIcN18WgTYFie+yOsX2ILLQkUKyfYAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrowup">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABWElEQVRIie3UMWtUURAF4G9MCGKhIGsnQpYIFhYiqSTVA0GwEfMDBCGtpZBisXjFlurvsLaxyOtFbNQq4oKlZbAIERyLjcm+dd/uxW33VJc7Z86cOXAvKyxAFDPr6jKeySS8Mmh+lrStFYpfwlPyuYh7xJFq85Nm9GtR64WiAZmPpX2ih6vYx6OS1vkR1dUGHuANuTFFP8Eu3hkcnHRJdEdUV+u4j5fktRle1qRt4VC1OdKMfs+SmRfRXXKIfueiYQvDMbeL8q9zuI235I1zSs6kn5a+Cw/JzwZNq9SO6Fz8NXmnSHxs8wpu4aOq/0MzOitNR3QdNXbagkXPZYeoTzXOsN6iZO6hJ+L9hPOL2O4Q/SAdTyzaE/bwYvaAMCSGbefZJ75MGSGCzCcivo2NGN/9PU8ozEddbeGwo3rT4ODrvPayl7wE/m9A5mLOUgOi/BNeRbTC8vgD9ktSvKIGbGgAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrowright">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABZ0lEQVRIie2UsUrDUBSGv18kdPABnJx0i07iAwg6+BrqKAqFFAdnMQ4ijkLBFxARddDV2aXByUk6+AQiRfwd2oakNkmrHf2H3EO453zn3HPPhX9VSCNvjFs1rAjRBLcdLY7kNzVWLvIa+AxrSXEyaYC7ELSBfAKEEwakoAC0in2ruLWiOJmeEED5VZoDnQPrOkqCIq8cXXFrvhugfxz9FYAAqOW8TYg4RdSB67K0eoDE1ZVk4f0Q7oA2gUtH4Xt29y96kD2q1A6AY2BLcTLzR0ChZoEDYK8c4PQzXHaJ7alB3/wVS3taMuBSZiSUvQdvwCGoWQxI4+ZuzxDID7uDVAeuBps8OCQL+QhZkAPMBbCMev/sL6QXoO4ovBmWSw7gKHwpTDpOaoiPHFdKwPuY+yK/0jH/qV7kbgGvmG3Qkxvh54QAAtNBPAI7boTPVR7jz4G4A3aByuAwdgU8YJqItqORXut/VesbkkZpUkorTwQAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="arrowdown11">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAk0lEQVQ4jd2QPQqDYBBEn8EyZQq7tB7AE3gEz2CRO+QiFrlcaksFK3lpFL7IpxgCIbiwMMvOzP7AX4aQCS4yi3FP3w47gEE6A+EGXKfyHOHehX7CzwSat66QC23k+8tshTy6jlAI3Ya4E4rNm4RSGCLiQSh3PUaohDEQj0K1SxyY1IFBvcZL1xoJPITLjD+a/tN4AYXNWgxWBb6gAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportServerUrl>http://ord-devsql01/ReportServer</rd:ReportServerUrl>
  <rd:ReportID>89b38ba8-54d5-4ac9-bad1-50c5d0e92c91</rd:ReportID>
</Report>