# Chorus Portal

Chorus Portal is a comprehensive healthcare data management and analysis application built with Angular and .NET Core. This document provides a complete overview of the application's architecture, modules, features, and technical implementation.

---

## Table of Contents

1. [Application Architecture](#application-architecture)
2. [Technology Stack](#technology-stack)
3. [Authentication & Security](#authentication--security)
4. [Core Modules](#core-modules)
5. [Shared Services & Infrastructure](#shared-services--infrastructure)
6. [Error Handling & Logging](#error-handling--logging)
7. [Development Setup](#development-setup)
8. [Environment Configuration](#environment-configuration)
9. [Session Management](#session-management)
10. [Reporting System](#reporting-system)
11. [API Integration](#api-integration)
12. [Deployment](#deployment)

---

## Application Architecture

### Overview

Chorus Portal follows a modular Angular architecture with clear separation of concerns:

- **Frontend**: Angular 17.3.8 with TypeScript
- **Backend**: .NET Core API
- **Reporting**: Bold Reports integration
- **Authentication**: JWT-based with refresh token mechanism
- **State Management**: RxJS Observables and BehaviorSubjects
- **UI Framework**: Angular Material with custom styling

### Module Structure

```
src/app/
├── modules/
│   ├── dashboard/          # Main entry point and patient preview
│   ├── admin/              # Administrative tools and rules engine
│   ├── documents/          # Document management system
│   ├── login/              # Authentication and user management
│   ├── retention-in-care/  # Outreach and patient retention
│   ├── shared-modules/     # Shared UI components and layouts
│   └── utility/            # Common utilities and helpers
├── shared-services/        # Core application services
├── shared/                 # Shared constants and models
└── pipes/                  # Custom Angular pipes
```

---

## Technology Stack

### Frontend Dependencies

- **Angular**: 17.3.8 (Core framework)
- **Angular Material**: 16.2.8 (UI components)
- **RxJS**: 7.4.0 (Reactive programming)
- **Bold Reports**: 6.3.27 (Reporting engine)
- **Bootstrap**: 5.2.3 (CSS framework)
- **Moment.js**: 2.29.4 (Date manipulation)
- **jsPDF**: 2.5.1 (PDF generation)
- **ngx-toastr**: 17.0.2 (Toast notifications)
- **ngx-spinner**: 16.0.0 (Loading indicators)

### Development Tools

- **TypeScript**: Type-safe JavaScript
- **SCSS**: Enhanced CSS with variables and mixins
- **Angular CLI**: 17.3.7 (Build and development tools)
- **Karma/Jasmine**: Testing framework

### Build Configuration

- **Output**: `dist/chorus-portal`
- **Service Worker**: Enabled for production
- **Compression**: Gzipper for optimized builds
- **Assets**: Favicon, manifest, and static resources

---

## Authentication & Security

### JWT-Based Authentication

The application uses a comprehensive JWT-based authentication system with the following components:

#### AuthInterceptor
- **Location**: `src/app/shared-services/auth-interceptor/`
- **Purpose**: Handles HTTP request/response interception for authentication
- **Features**:
  - Automatic token attachment to API requests
  - 401/403 error handling with route caching
  - Graceful logout on authentication failures
  - Feature access endpoint exception handling

#### Session Management
- **JWT Tokens**: Access tokens with configurable expiration
- **Refresh Tokens**: Long-lived tokens for seamless session renewal
- **Session Timeout**: 20 minutes of inactivity (configurable)
- **Auto-refresh**: Transparent token renewal when user is active

#### Security Features
- **Route Guards**: `EpividianGuardService` protects authenticated routes
- **Route Caching**: Stores intended destination for post-login redirect
- **Session Validation**: Real-time session validity checking
- **Secure Storage**: Optional base64 encoding for browser objects
- **Activity Tracking**: Mouse, keyboard, and scroll event monitoring

#### User Activity Detection
The application monitors user activity through:
- Window scroll events
- Keyboard interactions
- Mouse clicks and movements
- Automatic session extension on activity

### Two-Factor Authentication (TFA)
- **SMS Verification**: Phone-based TFA support
- **TOTP Support**: Time-based one-time passwords
- **Device Registration**: New device verification workflow
- **QR Code Generation**: For authenticator app setup

---

## Core Modules

### Dashboard Module
**Location**: `src/app/modules/dashboard/`

The dashboard serves as the main entry point and provides:

#### Patient Preview
- **Appointment Management**: Daily appointment scheduling view
- **Provider Selection**: Filterable provider dropdown with autocomplete
- **Date Navigation**: Yesterday/Tomorrow buttons with date picker
- **Quality Gaps**: Visual indicators for actionable patient gaps
- **Flow Sheets**: HIV/HCV diagnosis status indicators

#### Key Components
- `DashboardComponent`: Main patient preview interface
- `ReportComponent`: Bold Reports integration
- `DynamicFormsComponent`: PDF form filling system
- `CustomQueryComponent`: Advanced patient queries
- `HuddleComponent`: Team collaboration features

#### Services
- `DashboardService`: Appointment and provider data management
- `PanelService`: Dynamic report panel creation
- API endpoints for appointments, providers, and form URLs

### Admin Module
**Location**: `src/app/modules/admin/`

Administrative tools for site administrators:

#### Rules Engine Management
- **Workflow Configuration**: Create and manage business rule workflows
- **Rule Types**: Support for multiple rule types (API, Form, PDF, etc.)
- **Execution History**: Track workflow execution and results
- **JSON Editor**: Advanced rule configuration with validation
- **Dependency Management**: Rule sequence and dependency configuration

#### PDF Filler Manager
- **Template Management**: Upload and configure PDF templates
- **Field Mapping**: Connect PDF fields to data sources
- **Iframe Integration**: Secure PDF editing environment
- **Site-specific Configuration**: Per-site PDF management

#### Features
- Role-based access control (site admin required)
- Export functionality for workflow configurations
- Real-time execution monitoring
- Comprehensive error handling and logging

### Documents Module
**Location**: `src/app/modules/documents/`

Comprehensive document management system:

#### Document Library
- **Hierarchical Navigation**: Folder-based organization with breadcrumbs
- **File Operations**: Upload, download, rename, delete, and move
- **Sorting**: Configurable column sorting (name, size, date, etc.)
- **File Type Support**: Icons and handling for various file types
- **Context Menus**: Right-click operations for files and folders

#### Features
- **Secure Downloads**: Base64-encoded file transfer
- **Folder Management**: Create, rename, and organize directories
- **File Validation**: Size and type restrictions
- **Audit Trail**: Track file modifications and access
- **Responsive Design**: Mobile-friendly interface

### Retention-in-Care Module
**Location**: `src/app/modules/retention-in-care/`

Patient outreach and retention management:

#### Outreach Management
- **Call Tracking**: Record and manage patient outreach calls
- **Administrative Actions**: Handle scheduling and follow-up tasks
- **Annotation Status**: Track patient communication status
- **Result Recording**: Document outreach outcomes

#### Features
- **Patient Lists**: Awaiting scheduling and wrong number lists
- **Communication History**: Complete patient interaction timeline
- **Action Items**: Administrative tasks and follow-ups
- **Reporting**: Comprehensive outreach analytics

---

## Shared Services & Infrastructure

### UserContext Service
**Location**: `src/app/shared-services/user-context/`

Central user state management:
- **User Information**: Profile, sites, and security access
- **Session Management**: JWT token handling and validation
- **Site Selection**: Multi-site user support
- **Feature Access**: Role-based feature availability
- **Observable Patterns**: Reactive state updates

### ApiHandler Service
**Location**: `src/app/shared-services/ep-api-handler/`

HTTP communication layer:
- **RESTful Operations**: GET, POST, PUT, DELETE methods
- **Error Handling**: Standardized error processing
- **Token Management**: Automatic bearer token attachment
- **Environment Routing**: Dynamic API endpoint resolution
- **Request Options**: Configurable headers, parameters, and credentials

### ConfigurationService
**Location**: `src/app/shared-services/configuration.service.ts`

Environment and configuration management:
- **Multi-Environment Support**: Development, Test, Production, Local
- **Dynamic Loading**: Runtime configuration from JSON files
- **API Endpoints**: Environment-specific URL configuration
- **Feature Flags**: Environment-based feature toggles
- **Session Settings**: Configurable timeout and security options

### LayoutService
**Location**: `src/app/modules/shared-modules/layouts/services/layout/`

UI layout and navigation management:
- **Navigation State**: Sidebar expansion and menu management
- **Spinner Control**: Global loading indicator management
- **Data Status**: Real-time data loading status display
- **Menu Generation**: Dynamic navigation menu creation
- **Site Context**: Site-specific layout adjustments

### TimeoutSessionService
**Location**: `src/app/shared-services/timeout-session-service.ts`

Session timeout and activity monitoring:
- **Activity Tracking**: User interaction monitoring
- **Session Dialogs**: Timeout warning and extension prompts
- **Automatic Logout**: Inactivity-based session termination
- **Token Refresh**: Seamless session extension
- **Background Processing**: Non-blocking session management

### EpividianCommon Utility
**Location**: `src/app/modules/utility/EpividianCommon.ts`

Common utility functions:
- **Session Validation**: JWT and refresh token validation
- **Storage Management**: Secure browser storage operations
- **Date Utilities**: UTC conversion and manipulation
- **Token Refresh**: Automatic JWT renewal
- **Data Serialization**: JSON parsing and encoding

---

## Setting Up the Development Environment

### Prerequisites

- **Node.js**: Version 18+ recommended
- **npm**: Latest version
- **Angular CLI**: Version 17.3.7 (`npm install -g @angular/cli`)
- **Git**: For version control

### Cloning the Repository

```bash
git clone https://epividian.visualstudio.com/Epividian%20Services/_git/Chorus_Portal
cd Chorus_Portal
```

### Installing Dependencies

```bash
npm install
```

### Running the Application

```bash
ng serve
```

The application will be available at `http://localhost:4200/`

### Build Commands

```bash
# Development build
ng build

# Production build
ng build --configuration production

# Run tests
ng test

# Update Bold Reports packages
npm run update-boldreport-packages
```

---

## Error Handling & Logging

### Current Error Handling

The application implements several layers of error handling:

#### HTTP Error Handling
- **ApiHandler Service**: Centralized HTTP error processing
- **Error Transformation**: Server errors converted to user-friendly messages
- **Status Code Handling**: Specific handling for 401, 403, 404, 500 errors
- **Retry Logic**: Automatic retry for certain error types

#### Authentication Errors
- **AuthInterceptor**: Handles authentication failures gracefully
- **Route Preservation**: Caches intended routes during auth errors
- **Automatic Logout**: Clears session on persistent auth failures
- **User Feedback**: Clear error messages for login issues

#### Component-Level Error Handling
- **Try-Catch Blocks**: Local error handling in critical operations
- **Observable Error Handling**: RxJS catchError operators
- **User Notifications**: Toast messages for operation feedback
- **Graceful Degradation**: Fallback behavior for failed operations

### Audit and Logging

#### AuditService
**Location**: `src/app/shared-services/audit.service.ts`

- **Page Tracking**: Records user navigation and page visits
- **Parameter Auditing**: Tracks user interactions and form submissions
- **Session Storage**: Maintains audit trail in browser session
- **GUID Generation**: Unique identifiers for audit entries

#### Console Logging
- **Development Logging**: Extensive console.log statements for debugging
- **Error Logging**: console.error for exception tracking
- **Authentication Logging**: Route caching and session events
- **Configuration Logging**: Environment and config loading events

### Areas for Improvement

#### Centralized Error Logging
**Current Gap**: No centralized error logging to database or external service

**Recommendations**:
1. **Error Logging Service**: Create centralized error collection
2. **Database Integration**: Store errors in backend database
3. **Error Categorization**: Classify errors by type and severity
4. **User Context**: Include user and session information in error logs
5. **Error Reporting**: Dashboard for error monitoring and analysis

#### Enhanced User Experience
**Current Gap**: Limited user-friendly error messages

**Recommendations**:
1. **Error Message Service**: Standardized user-facing error messages
2. **Retry Mechanisms**: Automatic retry with user feedback
3. **Offline Support**: Handle network connectivity issues
4. **Error Recovery**: Guided recovery steps for common errors

#### Monitoring and Alerting
**Current Gap**: No real-time error monitoring

**Recommendations**:
1. **Application Insights**: Integrate with Azure Application Insights
2. **Real-time Alerts**: Immediate notification of critical errors
3. **Performance Monitoring**: Track application performance metrics
4. **Health Checks**: Automated system health monitoring

---

## Reporting System

### Bold Reports Integration

The application uses Bold Reports for comprehensive reporting capabilities:

#### Report Viewer Component
**Location**: `src/app/modules/dashboard/report/`

- **Dynamic Loading**: Reports loaded based on user selection
- **Parameter Panels**: Dynamic UI for report parameters
- **Export Options**: PDF, Excel, Word, and other formats
- **Print Support**: Direct printing from browser
- **Responsive Design**: Mobile-friendly report viewing

#### Report Panel System
**Location**: `src/app/modules/dashboard/panels/`

Dynamic panel components for report parameters:
- `PnlPatientSearchComponent`: Patient search criteria
- `PnlFromToDateComponent`: Date range selection
- `PnlCohortsComponent`: Patient cohort selection
- `PnlReportingYearComponent`: Reporting year selection
- `PnlCustomQueryComponent`: Advanced query builder
- `PnlLocationComponent`: Location-based filtering

#### Panel Service
**Location**: `src/app/modules/dashboard/panels/PanelService.ts`

- **Dynamic Panel Creation**: Runtime panel component loading
- **Data Management**: Report parameter data handling
- **API Integration**: Backend communication for panel data
- **Caching**: Efficient data caching for performance

### Custom Query System

#### Query Builder
- **Drag-and-Drop Interface**: Visual query construction
- **Regimen Builder**: Medication regimen query tool
- **Advanced Filters**: Complex filtering capabilities
- **Save/Load Queries**: Persistent query storage
- **Export Results**: Multiple export formats

#### Features
- **Real-time Preview**: Live query result preview
- **Validation**: Query syntax and logic validation
- **Performance Optimization**: Efficient query execution
- **User Permissions**: Role-based query access

### Export Capabilities

#### EHI Export
**Location**: `src/app/modules/dashboard/ehi-export/`
- **Electronic Health Information**: Standardized health data export
- **FHIR Compliance**: Healthcare data standard compliance
- **Batch Processing**: Large dataset export support

#### QRDA Export
**Location**: `src/app/modules/dashboard/qrda-export/`
- **Quality Reporting**: Clinical quality measure reporting
- **CMS Compliance**: Centers for Medicare & Medicaid Services standards
- **Automated Generation**: Scheduled report generation

---

## API Integration

### API Handler Architecture

The application uses a centralized API handling system:

#### ApiHandler Service
**Location**: `src/app/shared-services/ep-api-handler/ep-api-handler.ts`

Core HTTP communication features:
- **RESTful Methods**: GET, POST, PUT, DELETE operations
- **Bearer Token Management**: Automatic JWT token attachment
- **Error Handling**: Standardized error processing and transformation
- **Environment Routing**: Dynamic API endpoint resolution
- **Request Configuration**: Flexible headers, parameters, and options

#### API Routes Configuration
**Location**: `src/app/shared-services/ep-api-handler/api-option-enums.ts`

Centralized route definitions:
- **Authentication Routes**: Login, logout, password management, TFA
- **User Management**: Profile, sites, security access, device management
- **Dashboard APIs**: Appointments, providers, forms, reports
- **Document APIs**: Upload, download, folder management
- **Admin APIs**: Rules engine, workflows, PDF management
- **Reporting APIs**: Report data, parameters, export functions

#### Environment Configuration

Multiple environment support:
- **Development**: `https://api-dev.epividian.com`
- **Test**: `https://api-test.epividian.com`
- **Production**: `https://api-prod.epividian.com`
- **Local**: `http://localhost` for development

### API Communication Patterns

#### Observable-Based Requests
- **RxJS Integration**: All API calls return Observables
- **Error Handling**: Consistent error processing across all requests
- **Retry Logic**: Automatic retry for transient failures
- **Caching**: Strategic caching for performance optimization

#### Request/Response Handling
- **Type Safety**: TypeScript interfaces for all API models
- **Data Transformation**: Automatic JSON serialization/deserialization
- **Validation**: Client-side validation before API calls
- **Loading States**: Integrated spinner and loading indicators

---

## Dynamic Forms System

### Overview

The Dynamic Forms system provides sophisticated PDF form filling capabilities with workflow integration:

**Location**: `src/app/modules/dashboard/dynamic-forms/`

### Key Features

#### PDF Form Integration
- **PSPDFKit Integration**: Advanced PDF viewing and editing
- **Field Mapping**: Dynamic mapping of form fields to data sources
- **Real-time Validation**: Immediate feedback on form completion
- **Multi-step Workflow**: Fill, Sign, and Review process

#### Workflow Steps
1. **Fill Step**: Complete form fields with patient data
2. **Sign Step**: Digital signature collection
3. **Review Step**: Final review and submission (History mode: Forms, Review)

#### Advanced Validation System

The system includes sophisticated conditional validation:

##### DependentObject Validation
**Format**: `ValidationCondition: "DependentObject=[dependent_field_name], [value1], [value2], [value3]"`

**How It Works**:
1. **Parent Field**: Field with DependentObject validation condition
2. **Dependent Field**: Field whose requirement status changes
3. **Trigger Values**: Comma-separated values that make dependent field required

**Example**:
```json
{
  "FieldName": "PrescriptionType",
  "FieldType": "dropdown",
  "ValidationCondition": "DependentObject=QuantityWrittenLoadingDose, Initial Therapy, Maintenance Therapy, Other"
}
```

#### Implementation Details

- **Location**: `src/app/modules/dashboard/dynamic-forms/services/dynamicForms/custom-validator.ts`
- **Function**: `updateDependency()` handles the logic for updating dependent field requirements
- **Real-time Updates**: Changes are applied immediately when the parent field value changes
- **Form Validation**: The form control's validation state is automatically refreshed using `updateValueAndValidity()`
- **Checklist Integration**: The required fields checklist automatically updates to reflect dynamic requirements

#### Supported Validation Types

- **DependentObject**: Conditional field requirements
- **RegEx**: Regular expression pattern matching
- **Date Validation**: Custom date range and format validation
- **Checkbox/Radio**: Multi-selection and single-selection validation
- **Custom Conditions**: JavaScript expression evaluation with field value substitution

#### Navigation and State Management

- **Backward Navigation**: Reset required flags when navigating from Sign to Fill step
- **Section Management**: Next Section button saves current section individually
- **Continue Button**: Saves all unsaved sections at once
- **History Mode**: Next Section only navigates without submitting PDFs
- **PDF Loading**: Handles 'PdfLoaded' and 'PdfDownloading' messages from PDF Filler iframe

#### Data Handling

Special handling for table data:
- **Selected Rows Only**: Include only selected rows when sending data
- **JSON Serialization**: Return serialized JSON objects (not stringified JSON)
- **SelectedValue Format**: Proper JSON array formatting
- **Unescaped JSON**: Use unescaped JSON objects
- **Field Type Exclusion**: Don't process fields with FieldType 'table' using JSON.stringify
- **Data Cleanup**: Remove 'select' field before sending to API

---

## Session Management

### Last Active Flag Mechanism

The application employs a sophisticated session management system centered around user activity tracking:

#### Overview

The `lastActive` flag represents the last time the user interacted with the application and is crucial for session management and security.

#### Interaction Monitoring

The application monitors various user actions to update the `lastActive` timestamp:
- **Mouse Events**: Clicking anywhere in the application window
- **Scroll Events**: Scrolling within the application
- **Keyboard Events**: Typing or entering text in input fields

#### Session Timeout and JWT Lifecycle

The session timeout is primarily dictated by the JWT lifecycle but is closely tied to the `lastActive` flag:

1. **Initial Login**: Upon successful authentication, the user is issued a JWT
2. **Activity Tracking**: User interactions continuously update the `lastActive` flag
3. **Inactivity Check**: Application tracks time elapsed since `lastActive` timestamp
4. **Session Timeout Evaluation**:
   - If elapsed time < session timeout threshold (20 minutes): session remains active
   - If elapsed time > session timeout threshold: application prepares to terminate session

#### Auto-refresh Mechanism

Seamless user experience through intelligent token management:
- **Token Expiry and Active Session**: If JWT expires but `lastActive` time is within session timeout period, access token is automatically refreshed
- **Session Continuation**: As long as user remains active, session persists with transparent token refreshes
- **Background Processing**: Token refresh happens without user intervention

#### Automatic Sign-out

Security through automatic session termination:
- **Inactivity Timeout**: If user inactivity exceeds session timeout threshold (20 minutes), automatic sign-out occurs
- **Security Protection**: Protects against unauthorized access if user forgets to log out
- **Clean Session Termination**: Proper cleanup of session data and tokens

#### Security Considerations

**Current Implementation**: The AuthProvider does not remove refresh token links upon logout

**Recommendation**: Implement feature to clear user's current or all refresh tokens upon logout to enhance security

---

## Environment Configuration

### Configuration System

The application uses a dynamic configuration system for environment management:

#### Configuration File Setup

1. **Locate Template**: Find `config_example.json` in the `assets/configs/` folder
2. **Create Configuration**: Rename `config_example.json` to `config.json`
3. **Set Environment**: Configure the environment value in the JSON file

```json
{
    "environment": "Development"
}
```

#### Supported Environments

- **Local**: Development with local backend (`http://localhost`)
- **Development**: Development environment (`https://api-dev.epividian.com`)
- **Test**: Testing environment (`https://api-test.epividian.com`)
- **Production**: Production environment (`https://api-prod.epividian.com`)

#### ConfigurationService

**Location**: `src/app/shared-services/configuration.service.ts`

Features:
- **Dynamic Loading**: Runtime configuration loading from JSON files
- **Environment Validation**: Validates environment type before loading
- **Fallback Handling**: Defaults to Development if configuration fails
- **Variable Injection**: Injects environment variables into application

#### Environment-Specific Settings

Each environment includes:
- **API Endpoints**: V1 and V2 API base URLs
- **Authentication Provider**: AuthProvider service URL
- **PDF Filler**: PDF management service URL
- **Session Configuration**: Timeout and security settings
- **Feature Flags**: Environment-specific feature toggles
- **App Store Links**: Mobile app download links


---

## Deployment

### Build Process

The application uses Angular CLI for building and deployment:

#### Development Build
```bash
ng build
```

#### Production Build
```bash
ng build --configuration production
```

#### Build Features
- **Service Worker**: Enabled for production builds
- **Compression**: Gzipper for optimized file sizes
- **Asset Optimization**: Minification and bundling
- **Source Maps**: Available for debugging

### Deployment Targets

#### Azure DevOps Pipeline
**Location**: `yml/Portal_Pipeline.yml`

Features:
- **Automated Builds**: Triggered on code commits
- **Environment Deployment**: Automatic deployment to target environments
- **Compression**: Built-in file compression
- **Health Checks**: Post-deployment validation

#### Static File Hosting
- **Output Directory**: `dist/chorus-portal`
- **Web Server Configuration**: `web.config` for IIS deployment
- **Asset Management**: Proper handling of static assets and routing

### Configuration Management

#### Environment-Specific Builds
- **Development**: Debug mode with source maps
- **Test**: Optimized build with testing configurations
- **Production**: Fully optimized with security enhancements

#### Feature Flags
- **Service Worker**: Enabled/disabled based on environment
- **Mock Data**: Development-only mock API integration
- **Debug Logging**: Environment-specific logging levels

---

## Conclusion

Chorus Portal is a comprehensive healthcare data management and analysis application built with modern web technologies. The application provides:

### Key Strengths
- **Modular Architecture**: Well-organized, maintainable codebase
- **Comprehensive Security**: JWT-based authentication with session management
- **Advanced Reporting**: Bold Reports integration with dynamic panels
- **Document Management**: Full-featured document library system
- **Dynamic Forms**: Sophisticated PDF form filling with validation
- **Administrative Tools**: Rules engine and workflow management
- **Responsive Design**: Mobile-friendly interface

### Technical Highlights
- **Angular 17.3.8**: Modern frontend framework
- **TypeScript**: Type-safe development
- **RxJS**: Reactive programming patterns
- **Material Design**: Consistent UI components
- **Service-Oriented Architecture**: Modular, reusable services

### Areas for Enhancement
- **Centralized Error Logging**: Database-backed error tracking
- **Performance Monitoring**: Real-time application insights
- **Enhanced Security**: Improved token management and audit trails
- **Offline Support**: Progressive Web App capabilities
- **Automated Testing**: Comprehensive test coverage

For issues, contributions, or support, please refer to the project repository and documentation.




