import { AuthRedirectOption } from "src/app/modules/login/models/tfaMethod-enum";

export interface IChorusResponseToken
    {
        access_token: string;
        token_type: string;
        expires_in: number;
        refresh_token: string;
        hasVerifiedEmail: boolean;
        tfaEnabled: boolean;
        issued: string;
        expires: string;
        refreshToken: string;
        resetToken: string;
        tfaToken: string;
        deviceCode: string;
        last4: string;
        tfaMethod: number;
        callback_redirect: AuthRedirectOption;
        userName: string;
        csUserCode: number;
    }
