<?xml version="1.0" encoding="utf-8"?>
<Report MustUnderstand="df" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:df="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition/defaultfontfamily">
  <df:DefaultFontFamily>Segoe UI</df:DefaultFontFamily>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsChorus">
      <DataSourceReference>dsChorus</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b0aef8aa-fd8d-4f64-8a55-f736d8df74ff</rd:DataSourceID>
    </DataSource>
    <DataSource Name="dsAuthProvider">
      <DataSourceReference>dsAuthProvider</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>0f46c5b7-148b-4301-8133-12c19a77ad9c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="FirstTimeUsers">
      <Query>
        <DataSourceName>dsChorus</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@LOGINMONTHFIRSTDAY">
            <Value>=Parameters!LOGINMONTHFIRSTDAY.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@SITE_ID">
            <Value>=Parameters!SITE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@TYPE">
            <Value>=Parameters!TYPE.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>WITH LOGINS AS (
-- App Logins
SELECT DISTINCT User_NM AS USERNAME,
	    COUNT(Login_ID) AS LOGIN_COUNT
		,LOGIN_DT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,CASE WHEN sg.SITE_ID = 117 THEN '117 (AHF)'
	  WHEN sg.SITE_ID = 107 THEN '107 (WFH)'
	  WHEN sg.SITE_ID = 109 THEN '109 (Sinai)'
	  WHEN sg.SITE_ID = 110 THEN '110 (Mills)'
	  WHEN sg.SITE_ID = 116 THEN '116 (FIGHT)'
	  WHEN sg.SITE_ID = 123 THEN '123 (Amity)'
	  WHEN sg.SITE_ID = 124 THEN '124 (Rosedale)'
	  WHEN sg.SITE_ID = 125 THEN '125 (Neighborhood)'
	  WHEN sg.SITE_ID = 126 THEN '126 (CAN)'
	  WHEN sg.SITE_ID = 127 THEN '127 (Fairgrove)'
	  WHEN sg.SITE_ID = 131 THEN '131 (Ingham)'
	  END AS SITE_NAME,
	  SG.SITE_ID
	  ,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal') AS TYPE
	  ,ASSOCIATION_NM
  FROM [CHORUS].[CHORUS].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USER_NM
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
  LEFT OUTER JOIN (
  
  SELECT
		PA.SITE_ID,
        PA.USER_ID,
		AT.ASSOCIATION_NM
    FROM CHORUS.[ADMIN].SITE_PROVIDER_ASC PA
    LEFT JOIN CHORUS.ADMIN.ASSOCIATION_TYPE AS AT ON PA.ASSOCIATION_TYPE_ID = AT.ASSOCIATION_TYPE_ID
	WHERE ASSOCIATION_NM LIKE 'SELF'
    GROUP BY PA.USER_ID, ASSOCIATION_NM, site_id
							) S ON (SG.SITE_ID = S.SITE_ID AND ud.USER_ID = s.USER_ID)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND EMAIL_TXT not like '%@epividian.com'
	 --and LOGIN_DT BETWEEN @START_DT AND @STOP_DT
  GROUP BY LOGIN_DT, CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USER_NM,Iif(len(WEB_URL)&lt;=3,'Mobile App','Web Portal'), ASSOCIATION_NM
 UNION
  -- Web Portal Logins
SELECT DISTINCT Username,	
	    COUNT(Login_ID) AS LOGIN_COUNT
		, LOGIN_DT
 	   ,CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE) As LoginMonthFirstDay
	  ,CASE WHEN sg.SITE_ID = 117 THEN '117 (AHF)'
	  WHEN sg.SITE_ID = 107 THEN '107 (WFH)'
	  WHEN sg.SITE_ID = 109 THEN '109 (Sinai)'
	  WHEN sg.SITE_ID = 110 THEN '110 (Mills)'
	  WHEN sg.SITE_ID = 116 THEN '116 (FIGHT)'
	  WHEN sg.SITE_ID = 123 THEN '123 (Amity)'
	  WHEN sg.SITE_ID = 124 THEN '124 (Rosedale)'
	  WHEN sg.SITE_ID = 125 THEN '125 (Neighborhood)'
	  WHEN sg.SITE_ID = 126 THEN '126 (CAN)'
	  WHEN sg.SITE_ID = 127 THEN '127 (Fairgrove)'
	  WHEN sg.SITE_ID = 131 THEN '131 (Ingham)'
	  END AS SITE_NAME,
	  SG.SITE_ID
	  ,'Web Portal' AS TYPE
	  ,ASSOCIATION_NM
  FROM [Authprovider].[dbo].[LOGIN_LOG] LL
  LEFT JOIN [CHORUS].[CHORUS].[LOGIN_DISPO_CODE] LDC ON LDC.CODE_ID = LL.CODE_ID
  LEFT JOIN [CHORUS].[CHORUS].[USER_DETAIL] UD ON UD.EMAIL_TXT = LL.USERNAME
  LEFT JOIN [CHORUS].[GROUP].[USER_GROUP] UG ON UG.[USER_ID] = UD.[USER_ID]
  LEFT JOIN [CHORUS].[GROUP].[SITE_GROUP] SG ON UG.[SITE_GRP_ID] = SG.[SITE_GRP_ID]
  LEFT OUTER JOIN (
  SELECT
		PA.SITE_ID,
        PA.USER_ID,
		AT.ASSOCIATION_NM
    FROM CHORUS.[ADMIN].SITE_PROVIDER_ASC PA
    LEFT JOIN CHORUS.ADMIN.ASSOCIATION_TYPE AS AT ON PA.ASSOCIATION_TYPE_ID = AT.ASSOCIATION_TYPE_ID
	WHERE ASSOCIATION_NM LIKE 'SELF'
    GROUP BY PA.USER_ID, ASSOCIATION_NM, site_id


							) S ON (SG.SITE_ID = S.SITE_ID AND ud.USER_ID = s.USER_ID)
  WHERE LDC.CODE_ID IN(1,11,16,19,21)
     AND DEFAULT_SITE = '1' 
	 AND USERNAME not like '%@epividian.com'
	 --and LOGIN_DT BETWEEN @START_DT AND @STOP_DT
  GROUP BY LOGIN_DT, CAST((Login_DT-Day(LOGIN_DT)+1) AS DATE), SG.SITE_ID, USERNAME, ASSOCIATION_NM
  )
  
SELECT USERNAME, 
LOGIN_COUNT, 
LoginMonthFirstDay, 
SITE_NAME,
SITE_ID, 
TYPE, 
ASSOCIATION_NM
FROM 
(
SELECT USERNAME, 
LOGIN_COUNT, 
LoginMonthFirstDay, 
LOGIN_DT,
SITE_NAME,
SITE_ID, 
TYPE,
ASSOCIATION_NM,
ROW_NUMBER() OVER (PARTITION BY USERNAME ORDER BY LOGIN_DT ASC) ROWNUM
FROM LOGINS
) L
WHERE L.ROWNUM = 1
AND Login_DT &gt;= @LOGINMONTHFIRSTDAY
AND LoginMonthFirstDay = @LOGINMONTHFIRSTDAY
AND SITE_ID = @SITE_ID
AND TYPE = @TYPE</CommandText>
      </Query>
      <Fields>
        <Field Name="USERNAME">
          <DataField>USERNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LOGIN_COUNT">
          <DataField>LOGIN_COUNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="LoginMonthFirstDay">
          <DataField>LoginMonthFirstDay</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SITE_NAME">
          <DataField>SITE_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SITE_ID">
          <DataField>SITE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TYPE">
          <DataField>TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ASSOCIATION_NM">
          <DataField>ASSOCIATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>0.0308cm</Top>
            <Left>0cm</Left>
            <Height>1.67993cm</Height>
            <Width>5.312cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="HIVFlowsheet">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>First Time Users </Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>#0097c4</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <Top>0.01213in</Top>
            <Left>2.09134in</Left>
            <Height>0.66221in</Height>
            <Width>6.00213in</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>1pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox47">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Epividian® CHORUS</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>™</Value>
                    <Style>
                      <FontStyle>Normal</FontStyle>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextDecoration>None</TextDecoration>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> Report</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <Color>DimGray</Color>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox46</rd:DefaultName>
            <Left>8.09347in</Left>
            <Height>0.38267in</Height>
            <Width>2.16862in</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.44792in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.36458in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.96875in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.90625in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.82611in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>User</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value># logins per month</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Web Portal or App</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Provider</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!USERNAME.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LoginMonthFirstDay.Value</Value>
                                  <Style>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LOGIN_COUNT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TYPE.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ASSOCIATION_NM.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>FirstTimeUsers</DataSetName>
            <Top>0.72989in</Top>
            <Height>0.5in</Height>
            <Width>9.51361in</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Through: </Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Today()</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>11pt</FontSize>
                      <Format>yyyy-MM-dd</Format>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>0.43822in</Top>
            <Left>8.09347in</Left>
            <Height>0.25in</Height>
            <Width>2.16862in</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>2.01213in</Height>
        <Style />
      </Body>
      <Width>10.26209in</Width>
      <Page>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="LOGINMONTHFIRSTDAY">
      <DataType>DateTime</DataType>
      <Prompt>LOGINMONTHFIRSTDAY</Prompt>
    </ReportParameter>
    <ReportParameter Name="SITE_ID">
      <DataType>String</DataType>
      <Prompt>SITE ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="TYPE">
      <DataType>String</DataType>
      <Prompt>TYPE</Prompt>
    </ReportParameter>
    <ReportParameter Name="START_DT">
      <DataType>DateTime</DataType>
      <Prompt>START DT</Prompt>
    </ReportParameter>
    <ReportParameter Name="STOP_DT">
      <DataType>DateTime</DataType>
      <Prompt>STOP DT</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>LOGINMONTHFIRSTDAY</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>SITE_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>TYPE</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>START_DT</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>STOP_DT</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>1d8fe623-c0ae-457a-a9c1-df1a8b930619</rd:ReportID>
</Report>