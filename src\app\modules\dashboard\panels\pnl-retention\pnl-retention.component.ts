import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { Subject } from 'rxjs';
import { IData, IReportParamData, IParamGroup } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-retention',
  templateUrl: './pnl-retention.component.html',
  styleUrls: ['./pnl-retention.component.scss']
})
export class PnlRetentionComponent implements OnInit {
  rptbtnColor: string;
  @Input() rptpanRetention: FormControl;
  retentionData: IReportParamData = {} as IReportParamData;
  groupedRetentionData: Subject<IReportParamData[]> = new Subject<IReportParamData[]>();
  noSearchPan: boolean = false;


  constructor(
    public panelService: PanelService
  ) {
    this.rptpanRetention = new FormControl;
    this.rptbtnColor = this.rptbtnColor= btnColor.btnSecondColor;
  }

  ngOnInit(): void {

    this.panelService.GetPanelData(ReportPanelTypes.pnlRetention).subscribe(s =>
      {
        this.retentionData=s[0];
      });
  }


  /*
  getGroupName(groupId: number): string
  {
    var groupName = "";
    var groupObj = this.retentionData.groups.find(f=>f.id==groupId);
    if (groupObj)
    {
      groupName = groupObj.name;
    }
    return groupName;
  }
  */

  getGroupData(groupId: number): IData[]
  {
    return this.retentionData.data.filter(f=>f.groupId==groupId);
  }

  readyToRun(): void{
      this.rptbtnColor= btnColor.btnPrimaryColor;
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
