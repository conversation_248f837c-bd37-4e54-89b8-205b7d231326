<div class="cover-container container-fluid">
  <!-- BEGIN: Chorus login header section -->
  <div class="header-row-1 row g-0 d-flex justify-content-xxl-start justify-content-xl-start justify-content-lg-start justify-content-md-start justify-content-sm-center justify-content-center">
      <div class="logo-container col-lg-3 col-md-3 col-sm-12">
      </div>
  </div>
  <!-- END: Chorus login header section -->

  <!-- BEGIN: Chorus login main / dynamic section -->
  <div class="header-row-2 row g-0 d-flex justify-content-xxl-end justify-content-xl-end justify-content-lg-end justify-content-md-end justify-content-sm-start justify-content-start">
      <div class="col-lg-9 col-md-9 col-sm-12 mt-5 mt-sm-5 mt-md-0">
          <div class="login-content row g-0 mb-5">
              <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="container-fluid eplogin-container-wrapper">
                  <!-- BEGIN: Loading Spinner section -->
                  <div class="spinner-container row g-0 mx-2">
                      <div class="col-lg-9 col-md-9 col-sm-12 d-flex justify-content-center">
                          <div class="spinner-wrapper" role="status">
                              <img *ngIf="showSpinner" alt="Loading..." class="img-spinner" src="../../../../assets/images/loading.gif" />
                          </div>
                      </div>
                  </div>
                  <!-- END: Loading Spinner section -->

                  <!-- Showing error response return by API request when user's login attempts not over-->
                  <div class="row g-0 mx-2 mb-2" *ngIf="errorMsg && !isLoginAttemptsOver">
                      <div class="col-lg-9 col-md-9 col-sm-12">
                          <span class="login-error-message">
                              {{errorMsg}}
                          </span>
                      </div>
                  </div>
                  <!-- Showing error response return by API request when user's login attempts are over-->
                  <div class="row g-0 mx-2 mb-2 d-flex justify-content-center" *ngIf="errorMsg && isLoginAttemptsOver">
                      <div class="col-lg-9 col-md-9 col-sm-12">
                          <span class="login-error-message">
                              {{errorMsg}}
                          </span>
                      </div>
                  </div>

                  <!-- BEGIN: Login Form section -->
                  <!-- Login form will disappear when user's login attempts are over-->
                  <form class="login-form" *ngIf="!isLoginAttemptsOver" [formGroup]="epSignInForm" (ngSubmit)="onEpSignIn(epSignInForm)">
                      <!-- Enter Email form field -->
                      <div class="row g-0 mx-2 mb-2">
                          <div class="col-lg-9 col-md-9 col-sm-12">
                              <input id="email" type="email" tabindex="1" formControlName="epEmail" (blur)="loadUsersDeviceCode()" 
                               autofocus
                              class="form-control" name="email" placeholder="username" appEpElementAutoFocus />
                          </div>
                      </div>
                      <!-- Enter Password form field with form submission button -->
                      <div class="row g-0 mx-2">
                        <div class="col-lg-9 col-md-9 col-sm-12">
                            <div class="input-group">
                                <input id="password" [type]="hide ? 'password' : 'text'" tabindex="2" formControlName="epPassword" class="form-control" name="password" placeholder="password" (keyup.enter)="submitForm()" />
                                <mat-icon class="passShowHide" [inline]="true" (click)="passwordShowHide()"  tabindex="3" (keyup.enter)="submitForm()"    [hidden]="epSignInForm.invalid">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                                <mat-icon class="capsLockIcon" [hidden]="!capsLockOn">keyboard_capslock</mat-icon>
                                <button class="btn btn-light" tabindex="4" type="submit"></button>
                            </div>
                        </div>
                    </div>
                    


                      <!-- Forgor Password Navigation section -->
                      <div class="row g-0 mx-2">
                        <div class="col-lg-9 col-md-9 col-sm-12 d-flex">
                            <div [hidden]="!capsLockOn" class="capsLockAlert">CAPS lock is on</div>
                            <a class="forgot-password" tabindex="5" [routerLink]="['/Auth/ForgotPassword']">forgot password</a>
                        </div>
                      </div>
                  </form>
                  <!-- END: Login Form section -->
              </div>
              </div>
          </div>
      </div>
  </div>
  <!-- END: Chorus login main / dynamic section -->

  <!-- BEGIN: Chorus login footer section -->
    <div class="footer-row row g-0 justify-content-evenly">
        <div class="col-lg-10 col-md-10 col-sm-12 d-flex flex-column align-items-center mb-3">	
            <div class="certified-version">
                <p class="p-bottom-override">Epividian CHORUS 29.1</p>
            </div>
            <a class="contact-support" href="mailto:<EMAIL>" tabindex="99">
                contact support - Version <span class="version">{{AppVersion}}</span>
            </a>
        </div>
    </div>
  <!-- END: Chorus login footer section -->
</div>



