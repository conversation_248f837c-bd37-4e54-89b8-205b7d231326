// .myTable  table, th, td {
//     border: solid 1px #ddd;
//     border-collapse: collapse;
//     padding: 2px 3px;
//     text-align: center;
//     -webkit-print-color-adjust :exact !important;
//     border-collapse:collapse;
// }
// .myTable {

// border-collapse:collapse !important;
// /*width:*/
// }

// td {
//     font-size:12px;
//     font-weight:normal;
// }
// .myTable th {
//     font-weight:bold;
//      font-size:12px;
//     background:#2779aa;
//       -webkit-print-color-adjust :exact !important;
//       color:white;
// }

// th:nth-child(1){
// display:none;
// }
// td:nth-child(1){
// display:none;

// }
// .myTable th:nth-child(2){
// width:204px;
// }
// .myTable  td:nth-child(2){
//  width:204px;

// }
// .myTable    th:nth-child(3){
// width:76px
// }
// .myTable  td:nth-child(3){
//  width:76px;

// }
// .myTable  th:nth-child(4){
// width:84px;
// }
// .myTable   td:nth-child(4){
//  width:84px;

// }
// .myTable  th:nth-child(5){
// width:84px
// }
// .myTable   td:nth-child(5){
//  width:84px;

// }
// .myTable    th:nth-child(6){
// width:130px;
// }
// .myTable   td:nth-child(6){
//  width:130px;

// }
// .myTable th:nth-child(7){
//         width:233px;
// /*display:none;*/
// }
// .myTable td:nth-child(7){
//         width:233px;
//  /*display:none;*/

// }
// .myTable th:nth-child(8){
// width:148px;
// }
// .myTable  td:nth-child(8){
//    width:148px;

// }


// #Pname {
// border: 1px solid #aed0ea;
// background: #deedf7 url(images/ui-bg_highlight-soft_100_deedf7_1x100.png) 50% 50% repeat-x;
// color: #222;
// font-weight: bold;
// display: block !important;
// margin: auto;
// padding: .3em .3em .3em .3em;
// position: relative;
// font-size: 12px;
// -webkit-print-color-adjust :exact !important;
// }
// #showPrintData
// {
// margin-top: -17px;
// }
// .ui-widget-header
// {border:1px solid #aed0ea;background:#deedf7 url("images/ui-bg_highlight-soft_100_deedf7_1x100.png") 50% 50% repeat-x;color:#222 !important;
//           font-weight:bold}

// .ui-state-default, .ui-jqgrid-hdiv {
// background: none repeat scroll 0 0 #2779aa !important;
// color: white !important;
// margin-left: -1px !important;
// margin-top: -1px !important;
// }

// .ui-state-default, .ui-jqgrid-hdiv {
// color: white !important;
// }


.provider-view {
    float: left; 
    width: 100%; 
    padding: 5px;
}

.table-title {
    padding: .3em .3em .3em .3em;
    position: relative;
    font-size: 13px;
    border-left: 0 none;
    border-right: 0 none;
    border-top: 0 none;
    text-align: center;
    font-weight:500;
    border: 1px solid #aed0ea;
    background: #deedf7; 
    color: #222;
    display: block !important;
}

CMbtnDisabled {
    background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 40px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

.CMbtn {
    background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    height: 25px;
    padding: 1px 10px;
    text-align: center;
    width: 150px;
    font-weight: 500;
    font-size: smaller;
    text-decoration: none;
}

.CMbtn:hover {
    background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
    background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
    background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}

.imgIcons {
    background-color: transparent; 
    cursor: pointer; 
    float: right; 
    padding-right: 20px; 
    position: relative;
    width: 47px;
    height: 27px;
}

.ui-widget-content{
    background:#f2f5f7 50% top repeat-x;
    color:#362b36;
    width: 165px;
}

.ui-jqgrid-view {
    position: relative;
    float: left;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    left: 0;
    top: 0;
    padding: 0;
    font-size: 11px;
}

.ui-state-default, .ui-jqgrid-hdiv {
    background: none repeat scroll 0 0 #2779aa !important;
    color: white !important;
    margin-left: -1px !important;
    margin-top: -1px !important;
	border-top-left-radius: 8px;
	border-top-right-radius: 5px;
}

/* no need of this */
.ui-widget-header {
    border: 1px solid #aed0ea;
    background: #deedf7; /*url(images/ui-bg_highlight-soft_100_deedf7_1x100.png) 50% 50% repeat-x; */
    color: #222;
    font-weight: bold;
    display: block !important;
}

.ColorLocationRCProviderlevel {
    background-color: #dcdcdc !important;
    font-weight:bold !important;
    cursor:pointer;    
    padding-left: 5px; 
    text-decoration: none;    
    color: #222;   
}

.searchButton {
    color: #362b36;
    width: 160px;
    text-align: center;
}

.providerLevelData {
    cursor:pointer;    
    padding-left: 5px; 
}

table.ui-jqgrid-htable {
    min-width: 100%;
}

table.ui-jqgrid-htable th {
    border: 1px solid #FFF;
}
table.ui-jqgrid-htable td {
    border: 1px solid #CCC;
}


  /*Grid*/
  .ui-jqgrid {position: relative;}
  .ui-jqgrid .ui-jqgrid-view {position: relative;left:0; top: 0; padding: 0; font-size:11px;}
  /* caption*/
  .ui-jqgrid .ui-jqgrid-titlebar {padding: .3em .2em .2em .3em; position: relative; font-size: 12px; border-left: 0 none;border-right: 0 none; border-top: 0 none;}
  .ui-jqgrid .ui-jqgrid-caption {text-align: left; display:none;}
  .ui-jqgrid .ui-jqgrid-title { margin: .1em 0 .2em; }
  .ui-jqgrid .ui-jqgrid-titlebar-close { position: absolute;top: 50%; width: 19px; margin: -10px 0 0 0; padding: 1px; height:18px; cursor:pointer;}
  .ui-jqgrid .ui-jqgrid-titlebar-close span { display: block; margin: 1px; }
  .ui-jqgrid .ui-jqgrid-titlebar-close:hover { padding: 0; }
  /* header*/
  .ui-jqgrid .ui-jqgrid-hdiv {position: relative; margin: 0;padding: 0; overflow-x: hidden; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
  .ui-jqgrid .ui-jqgrid-hbox {float: left; }
  .ui-jqgrid .ui-jqgrid-htable {table-layout:fixed;margin:0;}
  .ui-jqgrid .ui-jqgrid-htable th {height:22px;padding: 0 2px 0 2px;}
  .ui-jqgrid .ui-jqgrid-htable th div {overflow: visible !important; position:relative; height:17px;}
  .ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {overflow: visible !important;white-space: nowrap;text-align:center;border-top : 0 none;border-bottom : 0 none;}
  .ui-th-ltr, .ui-jqgrid .ui-jqgrid-htable th.ui-th-ltr {border-left : 0 none;}
  .ui-th-rtl, .ui-jqgrid .ui-jqgrid-htable th.ui-th-rtl {border-right : 0 none;}
  .ui-first-th-ltr {border-right: 1px solid; }
  .ui-first-th-rtl {border-left: 1px solid; }
  .ui-jqgrid .ui-th-div-ie {white-space: nowrap; zoom :1; height:17px;}
  .ui-jqgrid .ui-jqgrid-resize {height:20px !important;position: relative; cursor :e-resize;display: inline;overflow: visible !important;}
  .ui-jqgrid .ui-grid-ico-sort {overflow:visible !important;position:absolute;display:inline; cursor: pointer !important;}
  .ui-jqgrid .ui-icon-asc {margin-top:-3px; height:12px;}
  .ui-jqgrid .ui-icon-desc {margin-top:3px;height:12px;}
  .ui-jqgrid .ui-i-asc {margin-top:0;height:16px;}
  .ui-jqgrid .ui-i-desc {margin-top:0;margin-left:13px;height:16px;}
  .ui-jqgrid .ui-jqgrid-sortable {cursor:pointer;}
  .ui-jqgrid tr.ui-search-toolbar th { border-top-width: 1px !important; border-top-color: inherit !important; border-top-style: ridge !important }
  tr.ui-search-toolbar input {margin: 1px 0 0 0}
  tr.ui-search-toolbar select {margin: 1px 0 0 0}
  /* body */ 
  .ui-jqgrid .ui-jqgrid-bdiv {position: relative; margin: 0; padding:0; overflow: visible !important; text-align:left;}
  .ui-jqgrid .ui-jqgrid-btable {table-layout:fixed; margin:0; outline-style: none; }
  .ui-jqgrid tr.jqgrow { outline-style: none; }
  .ui-jqgrid tr.jqgroup { outline-style: none; }
  .ui-jqgrid tr.jqgrow td {font-weight: normal; overflow: visible !important; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
  .ui-jqgrid tr.jqgfirstrow td {padding: 0 2px 0 2px;border-right-width: 1px; border-right-style: solid;}
  .ui-jqgrid tr.jqgroup td {font-weight: normal; overflow: visible !important; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
  .ui-jqgrid tr.jqfoot td {font-weight: bold; overflow: visible !important; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
  .ui-jqgrid tr.ui-row-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
  .ui-jqgrid tr.ui-row-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
  .ui-jqgrid td.jqgrid-rownum { padding: 0 2px 0 2px; margin: 0; border: 0 none;}
  .ui-jqgrid .ui-jqgrid-resize-mark { width:2px; left:0; background-color:#777; cursor: e-resize; cursor: col-resize; position:absolute; top:0; height:100px; overflow:visible !important; display:none; border:0 none; z-index: 99999;}
  /* footer */
  .ui-jqgrid .ui-jqgrid-sdiv {position: relative; margin: 0;padding: 0; overflow: visible !important; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
  .ui-jqgrid .ui-jqgrid-ftable {table-layout:fixed; margin-bottom:0;}
  .ui-jqgrid tr.footrow td {font-weight: bold; overflow: visible !important; white-space:nowrap; height: 21px;padding: 0 2px 0 2px;border-top-width: 1px; border-top-color: inherit; border-top-style: solid;}
  .ui-jqgrid tr.footrow-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
  .ui-jqgrid tr.footrow-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
  /* Pager*/
  .ui-jqgrid .ui-jqgrid-pager { border-left: 0 none !important;border-right: 0 none !important; border-bottom: 0 none !important; margin: 0 !important; padding: 0 !important; position: relative; height: 25px;white-space: nowrap;overflow: visible !important;font-size:11px;}
  .ui-jqgrid .ui-pager-control {position: relative;}
  .ui-jqgrid .ui-pg-table {position: relative; padding-bottom:2px; width:auto; margin: 0;}
  .ui-jqgrid .ui-pg-table td {font-weight:normal; vertical-align:middle; padding:1px;}
  .ui-jqgrid .ui-pg-button  { height:19px !important;}
  .ui-jqgrid .ui-pg-button span { display: block; margin: 1px; float:left;}
  .ui-jqgrid .ui-pg-button:hover { padding: 0; }
  .ui-jqgrid .ui-state-disabled:hover {padding:1px;}
  .ui-jqgrid .ui-pg-input { height:13px;font-size:.8em; margin: 0;}
  .ui-jqgrid .ui-pg-selbox {font-size:.8em; line-height:18px; display:block; height:18px; margin: 0;}
  .ui-jqgrid .ui-separator {height: 18px; border-left: 1px solid #ccc ; border-right: 1px solid #ccc ; margin: 1px; float: right;}
  .ui-jqgrid .ui-paging-info {font-weight: normal;height:19px; margin-top:3px;margin-right:4px;}
  .ui-jqgrid .ui-jqgrid-pager .ui-pg-div {padding:1px 0;float:left;position:relative;}
  .ui-jqgrid .ui-jqgrid-pager .ui-pg-button { cursor:pointer; }
  .ui-jqgrid .ui-jqgrid-pager .ui-pg-div  span.ui-icon {float:left;margin:0 2px;}
  .ui-jqgrid td input, .ui-jqgrid td select .ui-jqgrid td textarea { margin: 0;}
  .ui-jqgrid td textarea {width:auto;height:auto;}
  .ui-jqgrid .ui-jqgrid-toppager {border-left: 0 none !important;border-right: 0 none !important; border-top: 0 none !important; margin: 0 !important; padding: 0 !important; position: relative; height: 25px !important;white-space: nowrap;overflow: visible !important;}
  .ui-jqgrid .ui-jqgrid-toppager .ui-pg-div {padding:1px 0;float:left;position:relative;}
  .ui-jqgrid .ui-jqgrid-toppager .ui-pg-button { cursor:pointer; }
  .ui-jqgrid .ui-jqgrid-toppager .ui-pg-div  span.ui-icon {float:left;margin:0 2px;}
  /*subgrid*/
  .ui-jqgrid .ui-jqgrid-btable .ui-sgcollapsed span {display: block;}
  .ui-jqgrid .ui-subgrid {margin:0;padding:0; width:100%;}
  .ui-jqgrid .ui-subgrid table {table-layout: fixed;}
  .ui-jqgrid .ui-subgrid tr.ui-subtblcell td {height:18px;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
  .ui-jqgrid .ui-subgrid td.subgrid-data {border-top:  0 none !important;}
  .ui-jqgrid .ui-subgrid td.subgrid-cell {border-width: 0 0 1px 0;}
  .ui-jqgrid .ui-th-subgrid {height:20px;}
  /* loading */
  .ui-jqgrid .loading {position: absolute; top: 45%;left: 45%;width: auto;z-index:101;padding: 6px; margin: 5px;text-align: center;font-weight: bold;display: none;border-width: 2px !important; font-size:11px;}
  .ui-jqgrid .jqgrid-overlay {display:none;z-index:100;}
    

