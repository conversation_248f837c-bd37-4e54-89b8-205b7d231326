import { Component, On<PERSON><PERSON>roy, OnInit, Inject } from '@angular/core';
import { Location } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { RecordResultDialogComponent } from '../../record-result-dialog/record-result-dialog.component';
import { RetentionService } from '../../retention.service';
import { ActivatedRoute } from '@angular/router';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Subscription, forkJoin } from 'rxjs';
import { NgxSpinnerService } from 'ngx-spinner';
import { RCOurReachCallVM, RCOutreachModel, RCMeasureDetailVM } from '../../retention-models';

@Component({
  selector: 'patient-details',
  templateUrl: './patient-details.component.html',
  styleUrls: ['./patient-details.component.scss']
})

export class PatientDetailsComponent implements OnInit, OnD<PERSON>roy {
  patientDetails: any = [];
  patientId : any;
  siteId: any;
  private pageSubscriptions: Subscription = new Subscription;
  outreachCallsData: RCOurReachCallVM = new RCOurReachCallVM();
  outReachModel:any = new RCOutreachModel();

  constructor(
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private location: Location,
    public dialog: MatDialog,
    private _Activatedroute:ActivatedRoute,
    public retentionInCare: RetentionService,
    private userContext: UserContext
  ) {}
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.patientId = this._Activatedroute.snapshot.paramMap.get("patientId");

    this.siteId = this.userContext.GetCurrentSiteValue();
    if (this.siteId != 0) {
      this.loadPatientDetails(this.siteId.toString(), this.patientId);
      this.loadOutreachCallsData(this.siteId);
    }

    // Patient Measure Details API call to show Patient details
    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(site => {
        this.loadPatientDetails(site.toString(), this.patientId);
        this.loadOutreachCallsData(this.siteId);
      })
    );
  }

  private loadPatientDetails(site: string, patientId: number) {
    this.retentionInCare.GetPatientMeasureDetail(site, patientId).subscribe(result => {
      if(result){
        this.patientDetails = result;
      }
    })
  }

  loadOutreachCallsData(siteId: string) {
    this.spinnerService.show();
     // Create observables for your API calls
    const outreachCallObservable = this.retentionInCare.GetOutreachCallDetail(siteId);

    // Use forkJoin to combine observables
    forkJoin([outreachCallObservable]).subscribe(([outreachCallResult]) => {
      if (outreachCallResult) {
        this.outreachCallsData = outreachCallResult;
      }

      // Inside this callback, both API calls have completed
      this.spinnerService.hide();
    });
  }


  // Function is used to open Dialog box when user clicks on Record Result list
  openDialog(): void {
   
    if (this.outreachCallsData)
    {
       this.outReachModel = this.outreachCallsData.lstCurrentList.find(item => item.demographicsId == this.patientId);
       if (!this.outReachModel)
       {
         this.outReachModel = this.outreachCallsData.lstCallHistoryList.find(item => item.demographicsId == this.patientId)
       }
    }

    this.retentionInCare.setSelectedPatient(this.outReachModel);

    var dialogData = {
        demographicsId :  (this.patientDetails as RCMeasureDetailVM).demographicS_ID,
        locationId :  (this.patientDetails as RCMeasureDetailVM).locatioN_ID,
        annotationId : (this.patientDetails as RCMeasureDetailVM).measurE_ANNOTATE_ID,
        patientName :  (this.patientDetails as RCMeasureDetailVM).patienT_NM,

    }
    
    const dialogRef = this.dialog.open(RecordResultDialogComponent, {
      width: '430px',
      height: '290px',
      disableClose: true,
      data : dialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      //console.log('The dialog was closed');
    });
  }

  // Function used to go back to previous page
  goBack() {
    this.location.back();
  }
}
