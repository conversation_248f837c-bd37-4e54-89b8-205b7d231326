<H2 class="title"> Patient Preview </H2>
<div class="SSTopDiv">
  <div class="SStablegridbg">
    <div class="SStopstrip grid-top">
        <div class="SStopstripleft">
            <span class="form-field-label">
                <label ID="lblCount" >{{appointmentInfo.length}} Appointments Scheduled For: </label>
            </span>

            <div class="input-wrapper">
                <input type="text" #search placeholder="Select provider" matInput [formControl]="providerControl" [matAutocomplete]="auto"
                (keyup)="comboFilter(search.value)" class="mySelectClass provider-list">
                <mat-icon class="input-icon">arrow_drop_down</mat-icon>
            </div>

            <mat-autocomplete class="dropdownformat" #auto="matAutocomplete" (optionSelected)="providerChange($event)">

                <mat-option class="dropdownformat" value="-1">Please select</mat-option>
                <mat-optgroup class="dropdownformat"  [label]="providerGroupLabels.AssociatesProvider" [style.display]="AssociatesProvider.length==0?'none':'block'">
                    <mat-option class="dropdownformat"  *ngFor="let provider of FilteredAssociatesProviders" [value]="provider.provideR_ID">
                        {{provider.fulL_NM}}
                    </mat-option>
                </mat-optgroup>
                <mat-optgroup class="dropdownformat"  [label]="providerGroupLabels.AppointmentProvider" [style.display]="AppointmentProviders.length==0?'none':'block'">
                    <mat-option class="dropdownformat"  *ngFor="let provider of FilteredAppointmentProviders" [value]="provider.provideR_ID">
                        {{provider.fulL_NM}}
                    </mat-option>
                </mat-optgroup>
            </mat-autocomplete>
            <span class="form-field-label date-picker-label">
                <label ID="lblOn" >on</label>
            </span>

            <mat-form-field appearance="outline" class="datePicker">
                <input matInput [matDatepicker]="picker" class="dateInput" [(ngModel)]="currentSelectedDate" (dateChange)="updateDates()">
                <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>

            <!-- <div id="imgArrow">
                <img id="imgDownArrow" src="../../../../assets/images/arrow.png" alt="#" class="date_picker" />
            </div> -->
        </div>

        <div id="Printer" class="SStopstripright" >
            <button ID="lnkshowQMGap" style="font-weight:bold; font-size: 12px; position: relative; top: -3px;" (click)="setQualityGroup()">{{lblQualityGroup}}</button>
            <img id="imgPrinter" src="../../../../assets/images/printimg6.png" alt="#"
                Style="background-color: transparent; cursor: pointer; height:23px; width:23px; float:right"
                (click)="printSchedule()"  />
        </div>
    </div>
    <div class="printSStopstripleft" id="header" style="display:none">
        <label ID="lblAppointmentHeader">{{appointmentInfo.length}} Appointments Scheduled For </label>
    </div>
  </div>
    <div id="PrintSSinner_table" class="SStablegridbg" style="margin-right: 0px; padding-right: 0px;"  #PrintSSinner_table>
        <div class="SStablewrap">
            <table>
                <tr class="tableHeaderRow">
                    <th style="width: 6vh">
                        <img src="../../../../assets/images/alert.png" alt="#" />
                    </th>
                    <th style="width: 120px">APPOINTMENT TIME</th>
                    <th style="width: 120px">PATIENT NAME</th>
                    <th style="width: 100px">QUALITY GAP</th>
                    <th class="leftBorder" style="width: 30%" [style.display]="showQualityGroup?'':'none'" id="desc"></th>
                    <th style="width: 144px">FLOW SHEETS</th>
                    <th style="width: 300px">PATIENT SUMMARY</th>
                </tr>
                <tbody class="DeviceGridSSinner_table"
                    style="background-color: #fff; word-wrap: break-word; height: 248px;width: 100%;display: contents;">

                    <tr *ngFor="let info of appointmentInfo">
                        <td class={{info.gridRowColor}}>

                        </td>
                        <td>{{info.scheduleTm| date:'h:mm a'}}
                            <span *ngIf="info.statusDesc && info.statusDesc.length>0"><br><br>({{info.statusDesc}})</span>
                            <span *ngIf="info.appointmentType && info.appointmentType.length>0"><br><br>({{info.appointmentType}})</span>
                        </td>
                        <td>{{info.patientSearchDisplay}}</td>

                        <td CssClass="space" HorizontalAlign="center" style="vertical-align:middle;width: 80px">
                            <button *ngIf="info.measureGap" class="GridbackColor" (click)="showRecordsQualityGapReport(info)">
                                {{info.measureGap.measureGap}}
                            </button>
                        </td>

                        <td class="leftBorder" [style.display]="showQualityGroup?'':'none'" id="desc">
                            <div *ngIf="checkIfDescription(info.measureGap)">
                                <li *ngFor="let description of info.measureGap.measureGapDesc" class="measureGapDescLists">
                                    {{description.desc}}
                                    <div *ngIf="description.formLink">
                                            <a *ngIf="!description.formLink.submittedDate; else submitted" class='formButton' href="/Dashboard/Forms/{{siteId}}/{{description.formLink.formURL}}"><img class="launchButton" src='../../../../assets/images/btn_form_launch.svg'></a>
                                        <ng-template #submitted>
                                            <a class="submittedLink" href="/Dashboard/Forms/{{siteId}}/{{description.formLink.formURL}}">Form submitted on: {{description.formLink.submittedDate | date:'MM-dd-yyyy'}}</a>
                                        </ng-template>
                                    </div>
                                </li>
                            </div>
                        </td>

                        <td>
                            <img src={{info.FlowSheetImage}} (click)="showFlowSheetHIVReport(info)" style="cursor: pointer">
                        </td>
                        <td><div [innerHtml]="info.patientAnalysisDisplay"></div> </td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
    <div class="EmptyWarning"  *ngIf="lblWarningText.length>0">
                <div style="margin-top: 8px;">{{lblWarningText}}</div>
    </div>
    <div class="footerDiv">
        <div class="footerDivLeft footerDivBackground">
            <a ID="lnkYesterday" CssClass="SSmainbuttons" (click)="loadYesterdaysData()">
                <img src="../../../../assets/images/arrowleft.png" alt="#"/>
                <label ID="lblYesterday"  class="btn-label">YESTERDAY {{ yesterdayDate }}</label>
            </a>
        </div>
        <div class="footerDivRight footerDivBackground">
            <a ID="lnkTomorrow" CssClass="SSmainbuttons" (click)="loadTomorrowsData()">
                <label ID="lblTomorrow" class="btn-label">TOMORROW {{ tomorrowDate }}</label>
                <img src="../../../../assets/images/arrowright.png" alt="#"/>
            </a>
        </div>
    </div>

</div>
