import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { detailsTableService } from './Services/details-table.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { HuddleDetail } from '../models/huddle-detail.model';
import { Router } from '@angular/router';
import { ILocation } from '../models/location.model';
import { ApiRoutes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { HuddleCalendar } from '../models/huddle-calendar.model';
import { LayoutService } from '../services/layout/layout.service';

@Component({
  selector: 'epividian-details-table',
  templateUrl: './details-table.component.html',
  styleUrl: './details-table.component.scss'
})
export class DetailsTableComponent {

  @Input() selectedFilter: string = "All Appointments"
  @Input() calendarSelection: HuddleCalendar = {} as HuddleCalendar;
  @Output() huddleAppointments: EventEmitter<HuddleDetail[]> = new EventEmitter<HuddleDetail[]>();

  public siteId: string = "";
  public detailData: HuddleDetail[] = [];
  public numberOfAppointments: number = 0;
  public numberOfPatients: number = 0;
  public selectedLocation: ILocation = {} as ILocation;
  public selectedProvider: string = "";
  public selectedDate: Date = new Date();

  constructor(
    public detailsService: detailsTableService,
    private userContext: UserContext,
    private router: Router,
    private layoutService: LayoutService
  ) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.siteId != ""){
      if (changes['calendarSelection']){
        this.selectedFilter = "All Appointments";
        this.selectedDate = this.calendarSelection.date;
        this.selectedLocation = this.calendarSelection.location;
        this.selectedProvider = this.calendarSelection.provider;
        if(this.selectedLocation.id || this.selectedProvider != ""){
          this.getDetails();
        }
      }
      if (changes['selectedFilter']){
        this.getDetails();
      }
    }
  }

  getDetails(){
    this.layoutService.showSpinner();
    this.detailsService.getHuddleDetails(this.siteId,this.selectedDate, this.selectedFilter, this.selectedProvider, this.selectedLocation.id).subscribe(res => {
      this.detailData = res;
      this.detailData.forEach(appointment => {
        appointment.appointmentType = this.getAppointmentType(appointment.appointmentType)
        appointment.FlowSheetImage = this.getFlowSheetImage(appointment.hivFlag, appointment.hcvFlag)
      });
      this.numberOfAppointments = this.detailData.length;
      this.numberOfPatients = this.getUniquePatients();
      // Emit huddle appointments to parent for PDF generation and filter counts
      this.emitHuddleAppointments();
      this.layoutService.hideSpinner();
    });
  }

  getAppointmentsWithFlagSet(flagPosition: number): number {
    return this.detailData.filter(appointment => this.detailsService.isFlagSet(appointment, flagPosition)).length;
  }

  getDetailsList(appointment: HuddleDetail){
    let details: string[] = [];
    details = appointment.details;
    if(!appointment.showAllDetails){
      if (appointment.details.length > 3){
        details = details.slice(0,3);
      }
    }
    return details;
  }

  setShowAlldetails(appointment: HuddleDetail, showAll: boolean){
    appointment.showAllDetails = showAll;
  }

  setShowHideAll(showAll: boolean){
    this.detailData.forEach(appt => appt.showAllDetails = showAll);
  }

  getStatus(status: string, appointmentType: string){
    if (appointmentType == "Telehealth" && !this.isNoShowOrCanceled(status)){
      return "telehealth";
    }
    return status.replace(/[\s&-]+/g, '').toLowerCase();
  }

  isNoShowOrCanceled(statusDesc: string){
    if(statusDesc == 'No Show'){
      return true;
    }
    if (statusDesc.includes("Canceled")){
      return true;
    }

    return false;
  }

  getAppointmentType(appointmentType: string){
    if (appointmentType != null){
      if(appointmentType.length > 22){
        return appointmentType.slice(0,22) + "...";
      }
      else
      {
        return appointmentType;
      }
    }
    else
    {
      return "";
    }
  }

  emitHuddleAppointments() {
    this.huddleAppointments.emit(this.detailData);
  }

  showRecordsQualityGapReport(aptRowData: HuddleDetail){
      let demographicId = `[${aptRowData.demographicsId}]`
      demographicId = encodeURIComponent(demographicId);
      const url = ApiRoutes.QualityGapReport
            .replace('{{siteId}}', this.siteId)
            .replace('{{demographicId}}', demographicId);
      this.router.navigateByUrl(url);
  }

  showFlowSheetHIVReport(aptRowData: HuddleDetail){
      let demographicId = `[${aptRowData.demographicsId}]`
      demographicId = encodeURIComponent(demographicId);
      const url = ApiRoutes.PatientFlowsheetReport
            .replace('{{siteId}}', this.siteId)
            .replace('{{demographicId}}', demographicId);
      this.router.navigateByUrl(url);
  }

  getFlowSheetImage(hiv: number, hcv: number) {
    if (hiv >= 1 && hcv == 0) return '../../../../assets/images/hiv.png';
    else if (hiv == 0 && hcv >= 1) return '../../../../assets/images/hcv.png';
    else if (hiv >= 1 && hcv >= 1)
      return '../../../../assets/images/hivhcv.png';
    //if (hiv == 0 && hcv == 0)
    else return '';
  }

  getUniquePatients(){
    const uniqueDemographicIds = new Set<number>(this.detailData.map(item => item.demographicsId));
    return uniqueDemographicIds.size;
  }
}
