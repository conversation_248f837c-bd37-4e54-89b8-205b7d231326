<div class="object-view-container">
  <h3>JSON Object Structure</h3>

  <!-- Empty state message -->
  <div *ngIf="!jsonData || jsonSchemaService.isEmptyObject(jsonData)" class="empty-json-message">
    <p>No JSON data available. Please add data in Raw JSON view.</p>
  </div>

  <!-- JSON object tree view -->
  <div *ngIf="jsonData && !jsonSchemaService.isEmptyObject(jsonData)" class="json-object-view">
    <div *ngFor="let rootKey of rootKeys; let i = index" class="json-root-node">
      <mat-expansion-panel
        [expanded]="expandedPanels[rootKey]"
        (opened)="expandedPanels[rootKey] = true"
        (closed)="expandedPanels[rootKey] = false">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="json-key">{{ jsonSchemaService.formatLabel(rootKey) }}</span>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Render object content using JsonNodeComponent -->
        <app-json-node
          [node]="jsonData[rootKey]"
          [path]="rootKey"
          [level]="0"
          [nodeKey]="rootKey"
          [isExpanded]="true"
          (nodeChange)="onNodeChange($event)">
        </app-json-node>
      </mat-expansion-panel>
    </div>
  </div>
</div>
