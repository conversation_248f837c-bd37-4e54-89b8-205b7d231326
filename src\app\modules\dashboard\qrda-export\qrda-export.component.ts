import { Component, Inject, OnInit, AfterViewInit, ViewChild, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import { <PERSON><PERSON><PERSON>and<PERSON> } from "src/app/shared-services/ep-api-handler/ep-api-handler";
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, Subscription, finalize } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { PanelService } from '../panels/PanelService';
import { ReportPanelTypes } from '../panels/report-panel-enums';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { PnlQrdaExtractComponent } from '../panels/pnl-qrda-extract/pnl-qrda-extract.component';
import { IChorusAccessViewModel, RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';

@Component({
  selector: 'qrda-export',
  templateUrl: './qrda-export.component.html',
  styleUrl: './qrda-export.component.scss'
})
export class QrdaExportComponent implements OnInit, AfterViewInit {
  site: string = '';
  showAllPatientsOption: boolean = false;
  isSSRSReport: boolean = false;
  private pageSubscriptions: Subscription = new Subscription();

  // Panel state management - mutually exclusive panels
  public reportNavCollapsed: boolean = false;
  public filtersCollapsed: boolean = true;
  public currentSelectedReport: string = "";

  @ViewChild('panelContainerRef', { read: ViewContainerRef }) _panelContainer!: ViewContainerRef;

  constructor(
    private userContext: UserContext,
    public panelService: PanelService,
    public activeRoute: ActivatedRoute,
    private apihandler: ApiHandler,
    public router: Router,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private layoutService: LayoutService,
    private auditService: AuditService
  ) {

    let tmpSite = this.activeRoute.snapshot.paramMap.get('siteId');
    if (tmpSite) {
      this.site = tmpSite;
    }

    this.auditService.setPageAudit(Page.QRDAExport);

    this.userContext.SetCurrentSite(Number(this.site));
  }

  ngOnInit(): void {
    // Session storage integration for panel state persistence
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';

    if (shouldCollapse && !isInitialNavigation) {
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }

    // Clear session storage flags after checking
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }
  }

  // Mutually exclusive panel toggle methods
  toggleReportNav(): void {
    if (this.reportNavCollapsed) {
      this.reportNavCollapsed = false;
      this.filtersCollapsed = true;
    } else {
      this.reportNavCollapsed = true;
      this.filtersCollapsed = false;
    }
  }

  toggleFilters(): void {
    if (this.filtersCollapsed) {
      this.filtersCollapsed = false;
      this.reportNavCollapsed = true;
    } else {
      this.filtersCollapsed = true;
      this.reportNavCollapsed = false;
    }
  }

  // Report selection handling
  onReportSelected(): void {
    this.reportNavCollapsed = true;
    this.filtersCollapsed = false;
  }

  // Current report changed handler
  onCurrentReportChanged(reportName: any): void {
    this.currentSelectedReport = typeof reportName === 'string' ? reportName : reportName?.toString() || '';
  }

  // Dynamic panel title
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }

  ngOnDestroy(): void {
    // Complete the destroyed$ subject to clean up subscriptions
    this.pageSubscriptions.unsubscribe();
  }

  ngAfterViewInit(): void {

    this.pageSubscriptions.add(
      this.userContext.getUserSecurity().subscribe((userAccess: IChorusAccessViewModel[]) => {
        this.exitToLoginIfNoAccessRole(userAccess, RoleTypes.QRDA_Export);
      })
    );

    // Create the panel after the view has been initialized
    this.panelService.CreateReportPanel(ReportPanelTypes.pnlQrdaExtract).then((componentType: any) => {
      if (this._panelContainer) {
        // Clear the container before adding a new component
        this._panelContainer.clear();

        // Dynamically create the component
        const componentRef = this._panelContainer.createComponent<PnlQrdaExtractComponent>(componentType);

        // Set the showAllOption input only when loaded from QRDA-ExportComponent
        componentRef.instance.showOnlyAllOption = this.showAllPatientsOption;
        componentRef.instance.isSSRSReport = this.isSSRSReport;

        // Subscribe to the Run Click event from the child component

        componentRef.instance.runClicked.subscribe((data) => {
          console.log('Run button clicked in child component with data:', data);

          var titleName = data.searchText > 0 ? data.searchText : "All_Patients"

          this.exportData(data).subscribe((res: HttpResponse<string>) => {
            if (res.status === 200) {
              this.downloadFile(res.body, titleName);
            } else {
              alert("Error downloading file: status code" + res.status);
            }
          });

        });

      }
    });
  }

  private exitToLoginIfNoAccessRole(userAccess : IChorusAccessViewModel[], requiredAccess : RoleTypes){
    const siteIdNum = Number(this.site);

    // Find the access entry that matches the current siteId
    const siteAccess = userAccess.find(access => access.siteId === siteIdNum);

    if (siteAccess == null || siteAccess == undefined)
    {
      return;
    }

    // Check if the siteAccess exists and contains the EHI_Export role
    const hasRole = siteAccess?.userRoles.some(r => r.role === requiredAccess) ?? false;

    if (!hasRole)
    {
        this.userContext.ClearSession();
        this.router.navigateByUrl('/');
    }
  }

  exportData(data: any): Observable<any> {
    this.spinnerService.show();
    var patientMRN = data.patientSearchType == 0 ? data.searchText : "";
    var apiUrl = ApiRoutes.getQRDA.replace("{{patientMRN}}", patientMRN)
    apiUrl = apiUrl.replace("{{siteId}}", this.site)
    console.log(apiUrl);
    return this.apihandler.Get<any>(
      ApiTypes.V2,
      apiUrl,
      true,
      true,
      'blob'
    ).pipe(
      finalize(() => {
        // Hide spinner after API call completes (whether successful or failed)
        this.spinnerService.hide();
      })
    );
  }
  // Method to trigger file download
  downloadFile(content: any, fileName: string): void {
    try {
      // Create the blob and object URL
      const blob = new Blob([content], { type: 'application/zip'});
      const a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = fileName;

      // Append, trigger the click, and remove the element
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(a.href); // Revoke the object URL after download
      document.body.removeChild(a);
      alert('The file you requested is in your Downloads folder. Check your email for instructions on accessing it.')
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed: ' + error);
    }
  }
}
