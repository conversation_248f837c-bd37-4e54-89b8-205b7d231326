<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.8.34330.188</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.8.34330.188</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-02-01T21:28:38.4302272Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="EXTRACT_DATE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT CAST(EXTRACT_DT AS DATE) AS EXTRACT_DT FROM [CLEAN].[SITE]</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsPatientDemographic">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>REPORT.GET_PATIENT_DEMOGRAPHICS</CommandText>
      </Query>
      <Fields>
        <Field Name="PATIENT_HEADER">
          <DataField>PATIENT_HEADER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DATE_RANGE">
          <DataField>DATE_RANGE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="QUALITY_GAP_DETAIL">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DEMOGRAPHICS_ID">
            <Value>=Parameters!DEMOGRAPHICS_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>
					SELECT [SOURCE], DEMOGRAPHICS_ID, MEASURE_ID, MEASURE, MEETS, [DATE], ANNOTATE_DATE, MEASURE_DT, ISNULL([DESCRIPTION], 'Missing') AS [DESCRIPTION], REPORTING_PERIOD,
					PRIMARY_PROVIDER_ID, OPEN_FLG, INTERVAL_DESC, ANNOTATE_INTERVAL_DESC, MEASURE_CD, INVERTED_FLG, MEETING_DESC, NOT_MEETING_DESC,
					AM_INTERVAL_DESC, MEASURE_ANNOTATE_ID, ISNULL(ACTION_TXT, 'None') AS ACTION_TXT
					FROM [REPORT].[GET_QUALITY_GAP_DTL](@DEMOGRAPHICS_ID)
					ORDER BY CASE WHEN [MEETS] = 0	THEN 1
					WHEN [MEETS] = 1	THEN 2
					END, [SOURCE], MEASURE
				</CommandText>
      </Query>
      <Fields>
        <Field Name="SOURCE">
          <DataField>SOURCE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DEMOGRAPHICS_ID">
          <DataField>DEMOGRAPHICS_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE">
          <DataField>MEASURE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEETS">
          <DataField>MEETS</DataField>
          <rd:TypeName>System.Int16</rd:TypeName>
        </Field>
        <Field Name="DATE">
          <DataField>DATE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ANNOTATE_DATE">
          <DataField>ANNOTATE_DATE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="DESCRIPTION">
          <DataField>DESCRIPTION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_DT">
          <DataField>MEASURE_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="OPEN_FLG">
          <DataField>OPEN_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER_ID">
          <DataField>PRIMARY_PROVIDER_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INTERVAL_DESC">
          <DataField>INTERVAL_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ANNOTATE_INTERVAL_DESC">
          <DataField>ANNOTATE_INTERVAL_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEETING_DESC">
          <DataField>MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INVERTED_FLG">
          <DataField>INVERTED_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="NOT_MEETING_DESC">
          <DataField>NOT_MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ANNOTATE_ID">
          <DataField>MEASURE_ANNOTATE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="AM_INTERVAL_DESC">
          <DataField>AM_INTERVAL_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ACTION_TXT">
          <DataField>ACTION_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.56403in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.1228in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.66943in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.97917in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.35417in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.21042in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.26216in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.28749in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.46759in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Source</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Measure</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Meets</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Recommended Action</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Description</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Comments</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.18634in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <UserSort>
                            <SortExpression>=Fields!SOURCE.Value</SortExpression>
                            <SortTarget>QUALITY_GAP_DETAIL</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox32">
                          <UserSort>
                            <SortExpression>=Fields!MEASURE.Value</SortExpression>
                            <SortTarget>QUALITY_GAP_DETAIL</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox32</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox31">
                          <UserSort>
                            <SortExpression>=Fields!MEETS.Value</SortExpression>
                            <SortTarget>QUALITY_GAP_DETAIL</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox31</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!ACTION_TXT.Value</SortExpression>
                            <SortTarget>QUALITY_GAP_DETAIL</SortTarget>
                          </UserSort>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <BackgroundColor>#2c3539</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25926in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SOURCE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!SOURCE.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>SOURCE</rd:DefaultName>
                          <ToolTip>=Fields!SOURCE.Value</ToolTip>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MEASURE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MEASURE.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MEASURE</rd:DefaultName>
                          <ToolTip>=IIf((Fields!MEASURE_CD.Value="001" OR Fields!MEASURE_CD.Value="AHF011") AND Fields!MEETS.Value=1, Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</ToolTip>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MEETS">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Fields!MEETS.Value = 1,"Yes","No")</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MEETS</rd:DefaultName>
                          <ToolTip>=IIF(Fields!MEETS.Value = 1,"Yes","No")</ToolTip>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIF(Fields!MEETS.Value = 0, "#ffff00", IIF(Fields!MEETS.Value = 0 And InStr(Fields!ACTION_TXT.Value, "No longer actionable") &lt; 0, "#f2cb1d", IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <rd:Selected>true</rd:Selected>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DATE.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>dd-MM-yyyy</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ACTION_TXT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ACTION_TXT.Value</Value>
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ACTION_TXT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox14</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DESCRIPTION">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>
																		=Switch((Fields!DESCRIPTION.Value) = "Missing", "Missing" ,
																		(Fields!DESCRIPTION.Value) = "LTF 180", "LTF 180",
																		(Fields!DESCRIPTION.Value)= "LTF 365", "LTF 365",
																		true,Fields!DESCRIPTION.Value.ToString())
																	</Value>
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>
																			=Switch(Fields!DESCRIPTION.Value &gt;0 = "Missing", "Red" ,
																			Fields!DESCRIPTION.Value &gt;0 = "LTF 180", "Red",
																			Fields!DESCRIPTION.Value &gt;0 = "LTF 365", "Red" )
																		</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DESCRIPTION</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.2pt</PaddingLeft>
                            <PaddingRight>0.2pt</PaddingRight>
                            <PaddingTop>0.2pt</PaddingTop>
                            <PaddingBottom>0.2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value EvaluationMode="Constant">Annotate</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>= IIf(isNothing(Fields!OPEN_FLG.Value),"Blue",IIF(Fields!MEETS.Value=0,"Red","Green"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Hyperlink>= "javascript:var a=  OpenModalbox('../_layouts/15/Chorus/Member/Anotation.aspx?DemographicId="+Parameters!DEMOGRAPHICS_ID.Value.ToString()+"&amp;ProviderId="+Fields!PRIMARY_PROVIDER_ID.Value.ToString()+"&amp;MeasureId="+Fields!MEASURE_ID.Value.ToString()+"&amp;ReportingYear="+Fields!REPORTING_PERIOD.Value.ToString()+"&amp;MeasureAnnotateId="+Fields!MEASURE_ANNOTATE_ID.Value.ToString()+"&amp;VisitDate="+IIf(isNothing(Fields!ANNOTATE_DATE.Value), "0", Fields!ANNOTATE_DATE.Value)+"&amp;IntervalDesc="+ IIF(IsNothing(Fields!ANNOTATE_INTERVAL_DESC.Value), "None", Fields!ANNOTATE_INTERVAL_DESC.Value)+"', '500px', '730px');"</Hyperlink>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>= IIf(RowNumber(Nothing) Mod 2 = 0, "Silver", "White")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.2pt</PaddingLeft>
                            <PaddingRight>0.2pt</PaddingRight>
                            <PaddingTop>0.2pt</PaddingTop>
                            <PaddingBottom>0.2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <FixedData>true</FixedData>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>QUALITY_GAP_DETAIL</DataSetName>
            <Top>0.025in</Top>
            <Height>0.91319in</Height>
            <Width>10.44967in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <LineHeight>1pt</LineHeight>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.93819in</Height>
        <Style />
      </Body>
      <Width>10.44967in</Width>
      <Page>
        <PageHeader>
          <Height>0.72708in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "EXTRACT_DATE")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Left>8.04496in</Left>
              <Height>0.72708in</Height>
              <Width>2.19639in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PATIENT_HEADER.Value, "dsPatientDemographic")</Value>
                      <MarkupType>HTML</MarkupType>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <MarkupType>HTML</MarkupType>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox4</rd:DefaultName>
              <Left>0.0125in</Left>
              <Height>0.72708in</Height>
              <Width>2.42947in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Textbox>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Quality Measures Gap</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox3</rd:DefaultName>
              <Left>2.51141in</Left>
              <Height>0.72708in</Height>
              <Width>5.4641in</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BottomBorder>
              <Color>Purple</Color>
              <Style>Solid</Style>
              <Width>1.25pt</Width>
            </BottomBorder>
          </Style>
        </PageHeader>
        <PageHeight>8.5in</PageHeight>
        <PageWidth>11in</PageWidth>
        <InteractiveHeight>0in</InteractiveHeight>
        <InteractiveWidth>11in</InteractiveWidth>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.5in</TopMargin>
        <BottomMargin>0.5in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="DEMOGRAPHICS_ID">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>9669</Value>
        </Values>
      </DefaultValue>
      <Prompt>DEMOGRAPHICS ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="EXTRACT_DT">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <DataSetReference>
          <DataSetName>EXTRACT_DATE</DataSetName>
          <ValueField>EXTRACT_DT</ValueField>
        </DataSetReference>
      </DefaultValue>
      <Prompt>EXTRACT DT</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>1</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>DEMOGRAPHICS_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>EXTRACT_DT</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>3fa71471-34fa-4abd-b801-1d1c12d07a2e</rd:ReportID>
</Report>
