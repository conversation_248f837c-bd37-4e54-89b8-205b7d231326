.dashboardWrapper{
  height: 100vh;
  display: flex;
  font-family: MuseoSans-300;
}

.dashboardSideNav{
  background-color: #0071BC !important;
  border: none !important;
}

.expandedSideNav{
  width: 340px;
  height: 97vh;
}

.expandedSideNav::before{
  content: ' ';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  z-index: -1;
  background-image: url("../../../../../assets/images/Pattern.svg");
}

.collapsedSideNav{
  width: 98px;
  height: 97vh;
}

.collapsedSideNav::before{
  content: ' ';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  z-index: -1;
  background-image: url("../../../../../assets/images/Pattern.svg");
}

.logoContainer{
  margin-top: 16px;
  background-image: url("../../../../../assets/images/Logo2024.svg");
  background-position: top left;
  background-repeat: no-repeat;
  padding-top: 32.5%;
}

.logoContainerCollapsed{
  margin-top: 16px;
  background-image: url("../../../../../assets/images/Chorus\ Logo_icon-hoz.svg");
  background-position: -26px 0px;
  background-repeat: no-repeat;
  padding-top: 122%;
}

.sidenavSubContainer{
 display: flex;
 flex-direction: column;
 height: calc(100% - 110px); /* Full height minus logo space */
 min-height: 600px; /* Ensure minimum height for consistent layout */
}

.dashboardTabs{
  display: flex;
  flex-direction: column;
  padding-top: 40px; /* Reduced from 88px to move menu up */
  padding-left: 15px;
  color: white;
  flex-shrink: 0; /* Prevent menu from shrinking */
}

.dashboardOption {
  display: flex;
  height: 55px !important;
  margin-bottom: 5px;
  cursor: pointer;
}

.dashboardTabItem{
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Museo500-Regular';
  font-size: 20px;
  font-weight: 500;
}

.collapsedItem{
  display: flex;
  flex-direction: column;
  text-align: center;
  justify-content: center;
  font-size: 12px;
  line-height: 12px;
  font-family: 'Museo500-Regular';
  font-weight: 500;
}

.uncollapsedItem{
  justify-content: flex-start;
  text-align: left;
  margin-top: -3px;
  padding-left: 15px;
  align-items: center;
  gap: 10px;
}

.itemIcon{
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.itemIcon img {
  width: 24px;
  height: 24px;
}

.itemIcon mat-icon {
  width: 24px;
  height: 24px;
  font-size: 24px;
}

.collapsedIcon{
  padding: 0 0 1px 0;
}

.wrapText{
  text-wrap: wrap;
}

.dashboardOption[aria-selected="true"] {
  background-color: white;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  position: relative;
  z-index: 1;
}

.collapsedSideNav .dashboardOption{
    justify-content: center;
    text-align: center;
}

.dashboardOption[aria-selected="true"] .dashboardTabItem {
  color: #0071BC !important;
}

.dashboardOption[aria-selected="true"] .dashboardTabItemCollapsed {
  color: #0071BC !important;
}

.dashboardOption[aria-selected="true"] .itemIcon {
  filter: invert(40%) sepia(81%) saturate(2337%) hue-rotate(187deg) brightness(68%) contrast(106%);
}

.dashboardOption:hover {
  background-color: #eff1f6;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #0071BC !important;
  position: relative;
  z-index: 1;
}

.dashboardOption:hover .itemIcon{
  filter: invert(40%) sepia(81%) saturate(2337%) hue-rotate(187deg) brightness(68%) contrast(106%);
}

.dashboardOption:focus {
  background-color: #eff1f6;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #0071BC !important;
  position: relative;
  z-index: 1;
}

.isActive {
  background-color: #eff1f6;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #0071BC !important;
  position: relative;
  z-index: 1;
}

.isActive .itemIcon {
  filter: invert(40%) sepia(81%) saturate(2337%) hue-rotate(187deg) brightness(68%) contrast(106%);
}

#huddleIcon{
  height: 34px;
  width: 25px;
  padding-bottom: 3px;
}

.userSection{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto; /* Push entire user section to bottom */
  flex-shrink: 0; /* Prevent user section from shrinking */
  min-height: 200px; /* Minimum height to ensure proper layout */
  padding-bottom: 0; /* Remove space from bottom edge */
}

.userInfoContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0; /* Prevent shrinking of user info */
}

.bottomInfoContainer{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  margin-top: 35px; /* Space between user info and bottom text */
  padding: 0 15px 0px 15px; /* Add small bottom padding to align text properly */
}

.collapsedUserSection{
padding-bottom: 100px;
}

.userButton{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: none;
  background: none;
  height: 100%;
  color: white !important;
  z-index: 1;
  cursor: pointer;
  position: relative; /* Ensure proper positioning context for menu */
}

.avatar {
  background-color: #80a9c8;
  color: #fff;
  padding: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius:100%;
  width:60px;
  height: 60px;
  font-weight: 600;
  font-size: 27px;
}

.avatarIcon{
  color: white !important;
  padding-top: 2px;
  text-align: center;
  letter-spacing: 3px;
  font-size: 27px;
}

.userInfo{
  display: flex;
  justify-content: center;
}

.userEmail{
  font-size: 12px;
}

.collapseIcon{
  position: absolute;
  top: 85vh; /* Moved down slightly to account for reduced menu padding */
  left: 0px;
}

.collapseButtonExpanded{
  height: 38px;
  width: 25px;
  background: #eff1f6;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-left: 315px;
  padding: 6px 0 0;
  border: 0;
  color: #b7b7b7;
  cursor: pointer;
}

.collapseButtonCollapsed{
  height: 38px;
  width: 25px;
  background: #eff1f6;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-left: 73px;
  padding: 6px 0 0;
  border: 0;
  color: #b7b7b7;
  cursor: pointer;
}

.updateIcon{
  margin-right: 5px;
  margin-top: 14px;
  height: 19px;
  width: 25px;
  background-image: url("../../../../../assets/images/update_FILL0_wght400_GRAD0_opsz24.svg");
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.siteButton{
  display: flex;
  justify-content: center;
  border-radius: 10px;
}

.siteName{
  color: white;
}

.signOutButton{
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #66a9d7;
  border: 1px solid #66a9d7;
  border-radius: 6px;
  color: white;
  padding: 3px 16px;
  margin-top: 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.signOutButton:hover{
  background-color: #5a9bc9;
}

.signOutButton mat-icon{
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.dateLastLoaded{
  display: flex;
  align-items: center;
  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));
  font-size: 12px;
  color: aliceblue;
  text-align: left;
  margin-bottom: -7px;
}

.chorusVersion{
  display: flex;
  justify-content: flex-start;
  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));
  font-size: 12px;
  color: aliceblue;
  padding-left: 30px; /* Align with date text after icon */
  margin-bottom: 0; /* Ensure no bottom margin */
  line-height: 1; /* Tight line height to minimize space */
}


.dateText{
  font-family: 'Museo500-Regular';
  font-weight: 500;
}

.menuDivider{
  margin: 1px;
}

.menu-separator {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 10px 15px;
  width: calc(100% - 30px);
}

.bonus-measures-icon {
  color: white !important;
}

.isActive .bonus-measures-icon {
  color: white !important;
}

.dashboardOption:hover .bonus-measures-icon {
  color: white !important;
}



.contentWrapperExpanded{
  background-color: #eff1f6;
  margin-left: 340px !important;
  width: 100%;
}

.contentWrapperCollapsed{
  background-color: #eff1f6;
  margin-left: 98px !important;
  width: 100%;
}

.contentLayout{
  padding-left: 10px;
  padding-right: 10px;
  background-color: #eff1f6;
  height: 100vh;
  overflow-y: auto;
  flex-direction: column_reverse;
  justify-content: space-between;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.linkSpacerRight {
  padding-right: 20px;
}

.linkSpacerLeft {
  padding-left: 20px;
}



