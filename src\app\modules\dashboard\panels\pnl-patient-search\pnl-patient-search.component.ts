import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject, debounceTime, switchMap, filter, lastValueFrom, firstValueFrom } from 'rxjs';
import { IData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';
import { CriteriaSummaryResults } from 'src/app/modules/dashboard/panels/models/custom-query-model';

@Component({
  selector: 'pnl-patient-search',
  templateUrl: './pnl-patient-search.component.html',
  styleUrls: ['./pnl-patient-search.component.scss']
})
export class PnlPatientSearchComponent implements OnInit, OnDestroy {
 @Output() runClicked: EventEmitter<any> = new EventEmitter<any>();

  @Input() rptpanTxtSearch: FormControl;
  @Input() patientSearch: FormControl;
  @Input() patientSearchType: number = 0;
  @Input() reportViewer_Control_viewReportClick;
  @Input() showOnlyAllOption: boolean = false;
  @Input() isSSRSReport: boolean = true;

  reportPath: string='';
  paneldata: IData[] = [] as IData[]
  autoCompleteItems: Subject<IData[]> = new Subject<IData[]>();
  currentSite: string = '0';
  rptbtnColor: string;
  noSearchPan: boolean = false;
  searchSubject = new Subject<string>();
  selectedKey: string = '';
  isRunDisabled: boolean = true;
  criteriaSummarycolumn1: string = "";
  criteriaSummarycolumn2: string = "";
  showCustomCriteria = false;
  patientIsSelected = false;

  constructor(
    public panelService: PanelService,
    private userContext: UserContext,
  ) {
    this.rptpanTxtSearch = new FormControl();
    this.patientSearch = new FormControl();
    this.rptbtnColor = btnColor.btnSecondColor;
  }

  ngOnInit(): void {
    this.currentSite = this.userContext.GetCurrentSiteValue().toString();
    
    this.panelService.GetPanelData(ReportPanelTypes.pnlPatientSearch).subscribe(s =>
      {
          this.paneldata=s[0].data;
      });

      this.searchSubject.pipe(
        filter(searchStr => searchStr.length >= 1), 
        debounceTime(1200),  // Wait for 2 seconds of inactivity
        switchMap(searchStr => {
          return this.panelService.PatientAutoComplete(
            searchStr,
            this.patientSearchType.toString(),
            this.currentSite
          );
        })
      ).subscribe(s => {
        if (this.showOnlyAllOption)
          {
            s[0].data.unshift(this.addAllOnlyToList());
          }

        this.autoCompleteItems.next(s[0].data);
      });
      
      const jsonString = sessionStorage.getItem('criteriaSummaryResults');
      if (jsonString) {
        const  criteriaSummary : CriteriaSummaryResults = JSON.parse(jsonString);
          
        if (criteriaSummary)
        {
          this.criteriaSummarycolumn1 = criteriaSummary.column1;
          this.criteriaSummarycolumn2 = criteriaSummary.column2;
          this.showCustomCriteria = true;
        }
      }

  }

  onPatientSelected(value: string)
  {
    let valueId = Number(value)
    this.patientSearchType = valueId;
    if (this.showOnlyAllOption)
    {
     
        this.isRunDisabled = !(valueId==1) && 
        (this.rptpanTxtSearch.value == 0 ||
        this.rptpanTxtSearch.value === null ||
        this.rptpanTxtSearch.value === undefined 
        );

        this.setRunColor();
    }
  }


  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

    // Function to create payload to search
    autoComplete(searchStr: string): void {
      this.searchSubject.next(searchStr);
      this.checkTextLength(searchStr);
    }

    displayFn(option: {key: string, value: string}): string {
      if (option)
      {
        return option.value ? option.value : option.key;
      }
      return '';
    }

    async onOptionSelected(event) {
      const isTextInList = event.option.value;
      this.rptpanTxtSearch.setValue(isTextInList.key);
      this.setButtonColor(isTextInList);
    }

    runReady(value: any)
    {
      this.setButtonColor(value)
    }

    async checkTextLength(text: string) {
      if (text.length < 2)
      {
        this.rptpanTxtSearch.setValue(null)
        this.setButtonColor(undefined);
        return;
      }
  
      let itemList: IData[] = await firstValueFrom(this.autoCompleteItems); // Adjusting to the correct data type
      // Check if the text matches any 'value' in the itemList
      const isTextInList = itemList.find(item => item.value === text);
      this.setButtonColor(isTextInList);
   
    }


    setButtonColor(isSelected: IData |undefined)
    {

        var patientNotSelected = (
          this.rptpanTxtSearch.value == 0 ||
          this.rptpanTxtSearch.value === null ||
          this.rptpanTxtSearch.value === undefined || 
          !isSelected
        )

         this.isRunDisabled =
         (
          !this.showOnlyAllOption && patientNotSelected
         )
         ||
         ( 
          this.showOnlyAllOption && patientNotSelected && this.patientSearchType == 0
         )
         this.setRunColor();
    }

    private setRunColor()
    {
       if (this.isRunDisabled)
        {
          this.rptbtnColor = btnColor.btnSecondColor;
        }
        else
        {
          this.rptbtnColor= btnColor.btnPrimaryColor;
        }
    }

    private addAllOnlyToList() {
      
      const allOption: IData = {
        groupId: 0,
        key: -1,
        value: 'All',
        subtext: ''
      };
     
      return allOption;
    }

    // Method triggered when Run button is clicked
  onRunClick(): void {
    const dataToEmit = {
      // Data you want to pass to the parent component (e.g., search criteria)
      patientSearchType: this.patientSearchType,
      searchText: this.rptpanTxtSearch.value
    };
    
    // Emit the event with the necessary data
    this.runClicked.emit(dataToEmit);
  }


    ngOnDestroy(): void {
      sessionStorage.removeItem('criteriaSummaryResults');
    }

}
