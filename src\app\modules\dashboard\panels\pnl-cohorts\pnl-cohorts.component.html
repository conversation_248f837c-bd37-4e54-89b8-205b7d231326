<div class="searchCriteriaDiv">
        <!-- Label Row -->
        <div class="label-row">
            <label class="field-label">Cohort*:</label>
        </div>

        <!-- Cohort Input Row -->
        <div class="input-row">
            <input matInput class="form-select cohort-input" #search [matAutocomplete]="auto" name="rptpanCohorts"
            [formControl]="rptpanCohorts" (keyup)="comboFilter(search.value)" placeholder="Select or search for a cohort">
            <mat-autocomplete #auto="matAutocomplete" (optionSelected)="checkTextLength($event.option.value)">
                <mat-option *ngFor="let option of cohortsData" [value]="option.key">
                    <span class="option-main-text">{{option.key}}</span>
                    <div class="option-sub-text">{{option.subtext}}</div>
                </mat-option>
            </mat-autocomplete>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="panelService.InitBoldReport()" id="reportViewer_Control_viewReportClick"
            [disabled]="isRunDisabled" aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
</div>
