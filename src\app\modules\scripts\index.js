
function printSchedule() {

    printCss = '@page {  size:auto;  margin-top: 0mm;  margin-bottom: 0mm;} @media print { a {display:none;} } @media screen and projection { a {display:inline;} width:100%}';
    
   // printCss += '@media print { table { page-break-after:auto } tr { page-break-inside:avoid; page-break-after:auto } a[href]:after { content: none !important; }} html{background-color: #FFFFFF; margin: 0px; } body{padding : 2mm 2mm 2mm 2mm;margin: 5mm 0mm 0mm 0mm;} .GridRowColorCancelled {border-left-width: 1px !important; box-shadow: inset 6px 0px 0px 0px rgb(204,41,61) !important; .GridRowColornoShow {border-left-width: 1px !important; box-shadow: inset 6px 0px 0px 0px rgb(242, 203, 29) !important;} .leftBorder {border-left-style: hidden !important; -webkit-border-left-style: hidden !important; -moz-border-left-style: hidden !important; padding-left: inherit !important;}  -webkit-print-color-adjust:exact; .GridRowColorCompleted {border-left-width:1px !important; box-shadow: inset 6px 0px 0px 0px rgb(0, 156, 204) !important;} '; //}
    printCss += '* {-webkit-print-color-adjust: exact !important; /* Chrome, Safari 6 – 15.3, Edge */color-adjust: exact !important; /* Firefox 48 – 96 */print-color-adjust: exact !important; /* Firefox 97+, Safari 15.4+ */}';
    printCss += '@media print {  table{width:99.5%;border-collapse:collapse} tr:nth-child(odd){background-color:#fff}  table tr td{background:#fff;border:1px solid #d3d5da;word-wrap:break-word;font-weight:700} table td, table th{padding:8px 14px;text-align:center;font-size:12px;border:1px #6b6b6b solid;border-bottom:none;text-align:left;color:#93959a} table th{height:34px;border:1px #d3d5da solid;border-bottom:none;color:#93959a;text-transform:uppercase;font-size:12px;background:#f9f9fb;background:-moz-linear-gradient(top,rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%);background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(249,249,251,1)),color-stop(2%,rgba(242,243,247,1)),color-stop(92%,rgba(236,238,244,1)),color-stop(98%,rgba(236,238,244,1)),color-stop(100%,rgba(240,241,246,1)));background:-webkit-linear-gradient(top,rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%);background:-o-linear-gradient(top,rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%);background:-ms-linear-gradient(top,rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%);background:linear-gradient(to bottom,rgba(249,249,251,1) 0%,rgba(242,243,247,1) 2%,rgba(236,238,244,1) 92%,rgba(236,238,244,1) 98%,rgba(240,241,246,1) 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#f9f9fb,endColorstr=#f0f1f6,GradientType=0)} table .right{text-align:right} table .center{text-align:center}.GridbackColor {width: 50px; height: 50px; background-color:#fcaf17;background-image: url(./assets/images/circle_50x50_black.png) no-repeat !important;  background-size: 50px 50px;  border: none; cursor: pointer;  font-weight:bold; font-size:14.5px; color:#646456; border-radius:25px;}}'
    
    var divToPrint = document.getElementById('PrintSSinner_table');      
    var divHeader = document.getElementById('header').innerHTML;      
    const spanElement = document.querySelector('.mat-select-min-line');
    const PatientName = spanElement.textContent;
    var Ondate = document.getElementById('divtopstripright').textContent;
    var newStr = [Ondate.slice(0, 2), Ondate.slice(2)].join(' ');
    newWin = window.open("", "printVersion", 'height=400,width=600', "width=1,height=1,top=0,left=0");
    newWin.document.writeln('<!DOCTYPE html>');
    newWin.document.writeln('<html><head> <style type="text/css" media="print">' + printCss + '</style><title></title>');
    newWin.document.writeln("<meta http-equiv=cache-control content=max-age=0 />")
    newWin.document.writeln("<meta http-equiv=cache-control content=no-cache />")
    newWin.document.writeln('<link rel="stylesheet" type="text/css" href="./assets/styles/printstyle.scss" media="print" />');
    newWin.document.writeln('</head><body style="font-weight:bold;" onload="window.print();window.close()> ');
    newWin.document.writeln('<div class="printSStopstripleft" Style="float: left; color: #444444;, font-size: 12px;, font-weight: bold;, width: auto; padding-bottom: 10px"> <span>' + divHeader + ' ' + PatientName + ' ' + newStr + '</span></div>');
    
    newWin.document.write(divToPrint.innerHTML);      
    newWin.document.write('</body></html>');
    newWin.document.close();     
    

    setTimeout(function () {
      newWin.focus();
      newWin.print();
      newWin.close();
    }, 3000);
  }



  //Was having trouble getting this to run from retention module
  //----------------------------------------------------------------
  // Provider List Page
  // pdf export function to print the Providers data
  function printProvidersListView() {
    printCss = `<style>@media screen, print {a link { visibility: hidden; } }</style>`;
    var divToPrintProvider = document.getElementById('providerListView');
    newWindetail = window.open("", "printVersion", 'height=400,width=600', "width=1,height=1,top=0,left=0");
    newWindetail.document.writeln('<!DOCTYPE html>');
    newWindetail.document.writeln('<html><head>' + printCss + '<title>Providers List Details</title>');
    newWindetail.document.writeln("<meta http-equiv=cache-control content=max-age=0 />")
    newWindetail.document.writeln("<meta http-equiv=cache-control content=no-cache />")
    newWindetail.document.writeln('<link rel="stylesheet" type="text/css" href="../../../../assets/styles/providersListPrint.css"/> ');
    newWindetail.document.writeln('</head><body style="font-weight:bold;"> ');
    newWindetail.document.write(divToPrintProvider.innerHTML);
    newWindetail.document.write('</body></html>');
    newWindetail.document.close();

    setTimeout(function () {
      newWindetail.focus();
      newWindetail.print();
      newWindetail.close();
    }, 7000);

  }

  // excel export function to export Providers data
  /*function exportProviderListExcel() {
    //var result = 'data:application/vnd.ms-excel,' + encodeURIComponent($('div[id$=providerListView]').html());
    //var link = document.createElement("a");
    //document.body.appendChild(link);
    //link.download = "Annotation_Status_Report_Provider.xls"; //You need to change file_name here.
    //link.href = result;
    //link.click();
    var wb = XLSX.utils.table_to_book(document.getElementById('providerListView').innerHTML, { sheet: "Sheet1" });
    var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Annotation_Status_Report_Provider.xlsx');

  }*/

  // html export function to show Providers data
  function providerListdownloadInnerHtml() {
    var elHtml = document.getElementById("providerListView").innerHTML;
    var link = document.createElement('a');
    var mimeType = 'text/html';
    var filename = "Annotation_Status_Report_Provider";
    link.setAttribute('download', filename);
    link.setAttribute('href', 'data:' + mimeType + ';charset=utf-8,' + encodeURIComponent(elHtml));
    link.style.backgroundColor = '#000';
    link.click();
  }

  // Provider Patient List Page
  // pdf export function to print Provider Patient List
  function printProviderPatientListView() {
    printCss = `<style>@media screen, print {tr #searchCriteriaTR { visibility: hidden; }}</style>`;
    var divToPrintProvider = document.getElementById('printProviderPatientListView');
    newWindetail = window.open("", "printVersion", 'height=400,width=600', "width=1,height=1,top=0,left=0");
    newWindetail.document.writeln('<!DOCTYPE html>');
    newWindetail.document.writeln('<html><head>' + printCss + '<title>Provider Patient List</title>');
    newWindetail.document.writeln("<meta http-equiv=cache-control content=max-age=0 />")
    newWindetail.document.writeln("<meta http-equiv=cache-control content=no-cache />")
    newWindetail.document.writeln('<link rel="stylesheet" type="text/css" href="../../../../assets/styles/providerPatientsListPrint.css"/> ');
    newWindetail.document.writeln('</head><body style="font-weight:bold;"> ');
    newWindetail.document.write(divToPrintProvider.innerHTML);
    newWindetail.document.write('</body></html>');
    newWindetail.document.close();

    setTimeout(function () {
      newWindetail.focus();
      newWindetail.print();
      newWindetail.close();
    }, 7000);
  }

  // excel export function to export Provider Patient List
  /*function providerPatientListExportExcel() {
    var result = 'data:application/vnd.ms-excel,' + encodeURIComponent($('div[id$=printproviderExcel]').html());
    var link = document.createElement("a");
    document.body.appendChild(link);
    link.download = "Annotation_Status_Report_Patient.xls";
    link.href = result;
    link.click();
  }*/

  // html export function to show Provider Patient List
  function providerPatientListHtml() {
    var elHtml = document.getElementById("printProviderPatientListView").innerHTML;
    var link = document.createElement('a');
    var mimeType = 'text/html';
    var filename = "Annotation_Status_Report_Patient";
    link.setAttribute('download', filename);
    link.setAttribute('href', 'data:' + mimeType + ';charset=utf-8,' + encodeURIComponent(elHtml));
    link.click();
  }

  // Patient Details Page functions
  // html export function to show Patient Details
  function downloadPatientDetailsHtml() {
    $(tableProviderData).find('th:last-child, td:last-child').hide().end().prop('outerHTML');
    var elHtml = document.getElementById("PatientMeasureDetailsView").innerHTML;
    var link = document.createElement('a');
    var mimeType ='text/html';
    var filename = "Details_Report";
    link.setAttribute('download', filename);
    link.setAttribute('href', 'data:' + mimeType + ';charset=utf-8,' + encodeURIComponent(elHtml));
    link.click();
  }

  // pdf export function to print Patient Details
  function printPatientDetailsView() {
    printCss = `<style>@media screen, print {a { visibility: hidden; }}</style>`;
    var divToPrintProvider = document.getElementById('PatientMeasureDetailsView');
    newWindetail = window.open("", "printVersion", 'height=400,width=600', "width=1,height=1,top=0,left=0");
    newWindetail.document.writeln('<!DOCTYPE html>');
    newWindetail.document.writeln('<html><head>' + printCss + '<title>Patient Details</title>');
    newWindetail.document.writeln("<meta http-equiv=cache-control content=max-age=0 />")
    newWindetail.document.writeln("<meta http-equiv=cache-control content=no-cache />")
    newWindetail.document.writeln('<link rel="stylesheet" type="text/css" href="../../../../assets/styles/patientDetailsPrint.css"/> ');
    newWindetail.document.writeln('</head><body style="font-weight:bold;"> ');
    newWindetail.document.write(divToPrintProvider.innerHTML);
    newWindetail.document.write('</body></html>');
    newWindetail.document.close();

    setTimeout(function () {
      newWindetail.focus();
      newWindetail.print();
      newWindetail.close();
    }, 6000);
  }

   function OpenModalbox(action, height, width) {
    window.reportComponentReference.zone.run(() => { window.reportComponentReference.loadAnnotation(action); });
  }