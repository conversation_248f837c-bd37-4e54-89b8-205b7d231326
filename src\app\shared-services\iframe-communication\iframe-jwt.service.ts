import { Injectable } from '@angular/core';
import { UserContext } from '../user-context/user-context.service';
import { EpividianCommon } from '../../modules/utility/EpividianCommon';
import { Observable, Subject } from 'rxjs';

/**
 * Service for handling JWT token communication with iframes
 * Provides methods for securely sending JWT tokens to iframes and handling token refreshes
 */
@Injectable({
  providedIn: 'root'
})
export class IframeJwtService {
  private tokenRefreshed = new Subject<string>();

  constructor(
    private userContext: UserContext,
    private epividianCommon: EpividianCommon
  ) {
    // Subscribe to token changes to notify iframes when token is refreshed
    this.userContext.getUserSession().subscribe(session => {
      if (session && session.access_token) {
        this.tokenRefreshed.next(session.access_token);
      }
    });
  }

  /**
   * Get an observable that emits when the JWT token is refreshed
   * Iframes can subscribe to this to know when to update their tokens
   */
  public getTokenRefreshNotifier(): Observable<string> {
    return this.tokenRefreshed.asObservable();
  }

  /**
   * Send the current JWT token to an iframe
   * @param iframeId The ID of the iframe element
   * @param targetOrigin The target origin for security (default: '*')
   * @returns boolean indicating if the token was successfully sent
   */
  public sendJwtToIframe(iframeId: string, targetOrigin: string = '*'): boolean {
    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
    if (!iframe || !iframe.contentWindow) {
      console.error(`Iframe with ID ${iframeId} not found or not ready`);
      return false;
    }

    // Get the current session from localStorage
    const sessionData = this.epividianCommon.readFromStorage('Session', true);
    if (!sessionData) {
      console.warn('No JWT session available to send to iframe');
      return false;
    }

    try {
      // Send the token to the iframe using postMessage
      iframe.contentWindow.postMessage({
        action: 'JWT_UPDATE',
        key: 'localStorageKey',
        value: sessionData,
        timestamp: Date.now()
      }, targetOrigin);
      
      console.log(`JWT token sent to iframe ${iframeId}`);
      return true;
    } catch (error) {
      console.error('Error sending JWT to iframe:', error);
      return false;
    }
  }

  /**
   * Setup token refresh handling for multiple iframes
   * @param iframeIds Array of iframe IDs to update when token refreshes
   * @param targetOrigin The target origin for security (default: '*')
   */
  public setupTokenRefreshHandling(iframeIds: string[], targetOrigin: string = '*'): void {
    this.getTokenRefreshNotifier().subscribe(() => {
      iframeIds.forEach(id => this.sendJwtToIframe(id, targetOrigin));
    });
  }

  /**
   * Get the URL for an iframe with the JWT token as a query parameter
   * @param baseUrl The base URL for the iframe
   * @param includeToken Whether to include the token in the URL (default: false for security)
   * @returns The URL with the token appended if requested
   */
  public getIframeUrl(baseUrl: string, includeToken: boolean = false): string {
    if (!includeToken) {
      return baseUrl;
    }

    // This method is provided for backward compatibility but is not recommended
    // for security reasons. Use postMessage approach instead.
    console.warn('Including JWT in URL is not recommended for security reasons. Consider using postMessage instead.');
    
    const sessionObj = this.epividianCommon.GetSessionObjFromLocalStorage();
    if (!sessionObj || !sessionObj.access_token) {
      return baseUrl;
    }

    // Add token as query parameter
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}token=${sessionObj.access_token}`;
  }
}
