﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix3">
        <TablixCorner>
          <TablixCornerRows>
            <TablixCornerRow>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox48">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>CLASSIFICATION NAME</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox48</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox50">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>STAT NAME</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox50</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox52">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value> CAT ID</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox52</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox1">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>MTHD CD</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
            </TablixCornerRow>
          </TablixCornerRows>
        </TablixCorner>
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.6875in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.57292in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.76042in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="STAT_VALUE">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Max(Fields!STAT_VALUE.Value)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>#,0;(#,0)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>STAT_VALUE</rd:DefaultName>
                      <Visibility>
                        <Hidden>=Iif(Parameters!ALERT_FLG.Value = True AND (Abs(ReportItems!PERC_CALC.Value) * 100 &lt;= Parameters!Percent_Fill.Value OR 
	IsNothing(ReportItems!PERC_CALC.Value)),True, False)</Hidden>
                      </Visibility>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>=Iif(Abs(ReportItems!PERC_CALC.Value) *100 &gt;= Parameters!Percent_Fill.Value, "Yellow", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PERC_CALC">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIF(Fields!ROW_NUM.Value = 1, Fields!PERC_CALC.Value,0)</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <Format>0%</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PERC_CALC</rd:DefaultName>
                      <Visibility>
                        <Hidden>=Iif(Parameters!ALERT_FLG.Value = True AND (Abs(ReportItems!PERC_CALC.Value) * 100 &lt;= Parameters!Percent_Fill.Value OR 
	IsNothing(ReportItems!PERC_CALC.Value)),True, False)</Hidden>
                      </Visibility>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>=Iif(Abs(ReportItems!PERC_CALC.Value) *100 &gt;= Parameters!Percent_Fill.Value, "Yellow", "White")</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Chart Name="Sparkline1">
                      <ChartCategoryHierarchy>
                        <ChartMembers>
                          <ChartMember>
                            <Group Name="Sparkline1_CategoryGroup">
                              <GroupExpressions>
                                <GroupExpression>=Fields!EXTRACT_DT.Value</GroupExpression>
                              </GroupExpressions>
                            </Group>
                            <SortExpressions>
                              <SortExpression>
                                <Value>=Fields!EXTRACT_DT.Value</Value>
                              </SortExpression>
                            </SortExpressions>
                            <Label>=Fields!EXTRACT_DT.Value</Label>
                          </ChartMember>
                        </ChartMembers>
                      </ChartCategoryHierarchy>
                      <ChartSeriesHierarchy>
                        <ChartMembers>
                          <ChartMember>
                            <Label>STAT VALUE</Label>
                          </ChartMember>
                        </ChartMembers>
                      </ChartSeriesHierarchy>
                      <ChartData>
                        <ChartSeriesCollection>
                          <ChartSeries Name="STAT_VALUE">
                            <ChartDataPoints>
                              <ChartDataPoint>
                                <ChartDataPointValues>
                                  <Y>=Sum(Fields!STAT_VALUE.Value)</Y>
                                </ChartDataPointValues>
                                <ChartDataLabel>
                                  <Style />
                                </ChartDataLabel>
                                <ToolTip>=FormatDateTime(Max(Fields!EXTRACT_DT.Value),DateFormat.ShortDate) &amp; " (" &amp; FormatNumber(Fields!STAT_VALUE.Value,0) &amp; ")"</ToolTip>
                                <Style>
                                  <Color>Black</Color>
                                </Style>
                                <ChartMarker>
                                  <Type>Auto</Type>
                                  <Style />
                                </ChartMarker>
                                <DataElementOutput>Output</DataElementOutput>
                              </ChartDataPoint>
                            </ChartDataPoints>
                            <Type>Line</Type>
                            <Style />
                            <ChartEmptyPoints>
                              <Style />
                              <ChartMarker>
                                <Style />
                              </ChartMarker>
                              <ChartDataLabel>
                                <Style />
                              </ChartDataLabel>
                            </ChartEmptyPoints>
                            <ValueAxisName>Primary</ValueAxisName>
                            <CategoryAxisName>Primary</CategoryAxisName>
                            <ChartSmartLabel>
                              <CalloutLineColor>Black</CalloutLineColor>
                              <MinMovingDistance>0pt</MinMovingDistance>
                            </ChartSmartLabel>
                          </ChartSeries>
                        </ChartSeriesCollection>
                      </ChartData>
                      <ChartAreas>
                        <ChartArea Name="Default">
                          <ChartCategoryAxes>
                            <ChartAxis Name="Primary">
                              <Visible>False</Visible>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                              <ChartAxisTitle>
                                <Caption>Axis Title</Caption>
                                <Style>
                                  <FontSize>8pt</FontSize>
                                </Style>
                              </ChartAxisTitle>
                              <Margin>False</Margin>
                              <ChartMajorGridLines>
                                <Enabled>False</Enabled>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                  </Border>
                                </Style>
                              </ChartMajorGridLines>
                              <ChartMinorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                    <Style>Dotted</Style>
                                  </Border>
                                </Style>
                              </ChartMinorGridLines>
                              <ChartMinorTickMarks>
                                <Length>0.5</Length>
                              </ChartMinorTickMarks>
                              <CrossAt>NaN</CrossAt>
                              <Minimum>NaN</Minimum>
                              <Maximum>NaN</Maximum>
                              <ChartAxisScaleBreak>
                                <Style />
                              </ChartAxisScaleBreak>
                            </ChartAxis>
                            <ChartAxis Name="Secondary">
                              <Visible>False</Visible>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                              <ChartAxisTitle>
                                <Caption>Axis Title</Caption>
                                <Style>
                                  <FontSize>8pt</FontSize>
                                </Style>
                              </ChartAxisTitle>
                              <Margin>False</Margin>
                              <ChartMajorGridLines>
                                <Enabled>False</Enabled>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                  </Border>
                                </Style>
                              </ChartMajorGridLines>
                              <ChartMinorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                    <Style>Dotted</Style>
                                  </Border>
                                </Style>
                              </ChartMinorGridLines>
                              <ChartMinorTickMarks>
                                <Length>0.5</Length>
                              </ChartMinorTickMarks>
                              <CrossAt>NaN</CrossAt>
                              <Location>Opposite</Location>
                              <Minimum>NaN</Minimum>
                              <Maximum>NaN</Maximum>
                              <ChartAxisScaleBreak>
                                <Style />
                              </ChartAxisScaleBreak>
                            </ChartAxis>
                          </ChartCategoryAxes>
                          <ChartValueAxes>
                            <ChartAxis Name="Primary">
                              <Visible>False</Visible>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                              <ChartAxisTitle>
                                <Caption>Axis Title</Caption>
                                <Style>
                                  <FontSize>8pt</FontSize>
                                </Style>
                              </ChartAxisTitle>
                              <ChartMajorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                  </Border>
                                </Style>
                              </ChartMajorGridLines>
                              <ChartMinorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                    <Style>Dotted</Style>
                                  </Border>
                                </Style>
                              </ChartMinorGridLines>
                              <ChartMinorTickMarks>
                                <Length>0.5</Length>
                              </ChartMinorTickMarks>
                              <CrossAt>NaN</CrossAt>
                              <Minimum>NaN</Minimum>
                              <Maximum>NaN</Maximum>
                              <ChartAxisScaleBreak>
                                <Style />
                              </ChartAxisScaleBreak>
                            </ChartAxis>
                            <ChartAxis Name="Secondary">
                              <Visible>False</Visible>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                              <ChartAxisTitle>
                                <Caption>Axis Title</Caption>
                                <Style>
                                  <FontSize>8pt</FontSize>
                                </Style>
                              </ChartAxisTitle>
                              <ChartMajorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                  </Border>
                                </Style>
                              </ChartMajorGridLines>
                              <ChartMinorGridLines>
                                <Style>
                                  <Border>
                                    <Color>Gainsboro</Color>
                                    <Style>Dotted</Style>
                                  </Border>
                                </Style>
                              </ChartMinorGridLines>
                              <ChartMinorTickMarks>
                                <Length>0.5</Length>
                              </ChartMinorTickMarks>
                              <CrossAt>NaN</CrossAt>
                              <Location>Opposite</Location>
                              <Minimum>NaN</Minimum>
                              <Maximum>NaN</Maximum>
                              <ChartAxisScaleBreak>
                                <Style />
                              </ChartAxisScaleBreak>
                            </ChartAxis>
                          </ChartValueAxes>
                          <Style>
                            <BackgroundColor>#00ffffff</BackgroundColor>
                            <BackgroundGradientType>None</BackgroundGradientType>
                          </Style>
                        </ChartArea>
                      </ChartAreas>
                      <Palette>EarthTones</Palette>
                      <ChartBorderSkin>
                        <Style>
                          <BackgroundColor>Gray</BackgroundColor>
                          <BackgroundGradientType>None</BackgroundGradientType>
                          <Color>White</Color>
                        </Style>
                      </ChartBorderSkin>
                      <ChartNoDataMessage Name="NoDataMessage">
                        <Caption>No Data Available</Caption>
                        <Style>
                          <BackgroundGradientType>None</BackgroundGradientType>
                          <TextAlign>General</TextAlign>
                          <VerticalAlign>Top</VerticalAlign>
                        </Style>
                      </ChartNoDataMessage>
                      <rd:DesignerMode>Sparkline</rd:DesignerMode>
                      <DataSetName>Source</DataSetName>
                      <ZIndex>1</ZIndex>
                      <Visibility>
                        <Hidden>=Iif(Parameters!ALERT_FLG.Value = True AND (Abs(ReportItems!PERC_CALC.Value) * 100 &lt;= Parameters!Percent_Fill.Value OR 
	IsNothing(ReportItems!PERC_CALC.Value)),True, False)</Hidden>
                      </Visibility>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>=Iif(Abs(ReportItems!PERC_CALC.Value) *100 &gt;= Parameters!Percent_Fill.Value, "Yellow", "#00ffffff")</BackgroundColor>
                        <BackgroundGradientType>None</BackgroundGradientType>
                      </Style>
                    </Chart>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="EXTRACT_DT">
                <GroupExpressions>
                  <GroupExpression>=Fields!EXTRACT_DT.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!EXTRACT_DT.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>0.25in</Size>
                <CellContents>
                  <Textbox Name="EXTRACT_DT">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Fields!EXTRACT_DT.Value</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Format>d</Format>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>EXTRACT_DT</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <Visibility>
                <Hidden>=Iif(Fields!EXTRACT_DT.Value = First(Fields!SECLAST_EXTRACT_DT.Value, "SECLAST_EXTRACT_DT") or Fields!EXTRACT_DT.Value = First(Fields!LAST_EXTRACT_DT.Value, "LAST_EXTRACT_DT"), False,True)</Hidden>
              </Visibility>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.25in</Size>
                <CellContents>
                  <Textbox Name="Textbox3">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>% Δ</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox3</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.25in</Size>
                <CellContents>
                  <Textbox Name="Textbox38">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>TREND</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox38</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>Gray</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
            </TablixMember>
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="STAT_CLASSIFICATION_NM">
                <GroupExpressions>
                  <GroupExpression>=Fields!STAT_CLASSIFICATION_NM.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!STAT_CLASSIFICATION_NM.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>2.60417in</Size>
                <CellContents>
                  <Textbox Name="STAT_CLASSIFICATION_NM1">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Fields!STAT_CLASSIFICATION_NM.Value</Value>
                            <Style>
                              <FontFamily>Calibri</FontFamily>
                              <FontSize>11pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>STAT_CLASSIFICATION_NM1</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>White</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <Group Name="STAT_NM">
                    <GroupExpressions>
                      <GroupExpression>=Fields!STAT_NM.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!STAT_NM.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>1.72292in</Size>
                    <CellContents>
                      <Textbox Name="STAT_NM1">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Fields!STAT_NM.Value</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>11pt</FontSize>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>STAT_NM1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>White</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="STAT_CLASSIFICATION_CATEGORY_ID">
                        <GroupExpressions>
                          <GroupExpression>=Fields!STAT_CLASSIFICATION_CATEGORY_ID.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!STAT_CLASSIFICATION_CATEGORY_ID.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>0.73125in</Size>
                        <CellContents>
                          <Textbox Name="STAT_CLASSIFICATION_CATEGORY_ID1">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!STAT_CLASSIFICATION_CATEGORY_ID.Value</Value>
                                    <Style>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>11pt</FontSize>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>STAT_CLASSIFICATION_CATEGORY_ID1</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <BackgroundColor>=Iif(Abs(ReportItems!PERC_CALC.Value) *100 &gt;= Parameters!Percent_Fill.Value, "Yellow", "White")</BackgroundColor>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="STAT_MTHD_CD">
                            <GroupExpressions>
                              <GroupExpression>=Fields!STAT_MTHD_CD.Value</GroupExpression>
                            </GroupExpressions>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Fields!STAT_MTHD_CD.Value</Value>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>0.6875in</Size>
                            <CellContents>
                              <Textbox Name="STAT_MTHD_CD">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!STAT_MTHD_CD.Value</Value>
                                        <Style>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>11pt</FontSize>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>STAT_MTHD_CD</rd:DefaultName>
                                <Visibility>
                                  <Hidden>=Iif(Parameters!ALERT_FLG.Value = True AND (Abs(ReportItems!PERC_CALC.Value) * 100 &lt;= Parameters!Percent_Fill.Value OR 
	IsNothing(ReportItems!PERC_CALC.Value)),True, False)</Hidden>
                                </Visibility>
                                <Style>
                                  <Border>
                                    <Color>LightGrey</Color>
                                    <Style>Solid</Style>
                                  </Border>
                                  <BackgroundColor>=Iif(Abs(ReportItems!PERC_CALC.Value) *100 &gt;= Parameters!Percent_Fill.Value, "Yellow", "White")</BackgroundColor>
                                  <VerticalAlign>Middle</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                          <Visibility>
                            <Hidden>=Iif(Parameters!ALERT_FLG.Value = True AND (Abs(ReportItems!PERC_CALC.Value) * 100 &lt;= Parameters!Percent_Fill.Value OR 
	IsNothing(ReportItems!PERC_CALC.Value)),True, False)</Hidden>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <FixedColumnHeaders>true</FixedColumnHeaders>
        <DataSetName>Source</DataSetName>
        <Top>0.36389in</Top>
        <Height>0.5in</Height>
        <Width>8.76667in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox2">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>QUALITY CONTROL ETL STATS REPORT</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>16pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox2</rd:DefaultName>
        <Top>0.01389in</Top>
        <Left>2.84375in</Left>
        <Height>0.35in</Height>
        <Width>3.87084in</Width>
        <ZIndex>1</ZIndex>
        <RepeatWith>Tablix3</RepeatWith>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox5">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>="Last Extract: " &amp; FormatDateTime(First(Fields!LAST_RUN_DT.Value, "Source"),DateFormat.ShortDate)</Value>
                <Style>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox5</rd:DefaultName>
        <Top>0.05833in</Top>
        <Left>7.17292in</Left>
        <Height>0.25in</Height>
        <Width>2.20834in</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
    </ReportItems>
    <Height>0.86389in</Height>
    <Style />
  </Body>
  <Width>9.50626in</Width>
  <Page>
    <InteractiveHeight>0in</InteractiveHeight>
    <InteractiveWidth>8.5in</InteractiveWidth>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>1in</RightMargin>
    <TopMargin>1in</TopMargin>
    <BottomMargin>1in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>Integrated</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="Source">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@SITE_ID">
            <Value>=Parameters!SITE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@FROM_DT">
            <Value>=Parameters!FROM_DT.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
          <QueryParameter Name="@TO_DT">
            <Value>=Parameters!TO_DT.Value</Value>
            <rd:UserDefined>true</rd:UserDefined>
          </QueryParameter>
        </QueryParameters>
        <CommandText>WITH CTE AS (
SELECT A.STAT_CLASSIFICATION_ID, B.STAT_CLASSIFICATION_NM, A.STAT_NAME_ID, C.STAT_NM, 
	A.STAT_CLASSIFICATION_CATEGORY_ID, A.STAT_MTHD_CD , STAT_VALUE,
	CAST(EXTRACT_DT AS DATE) AS EXTRACT_DT, LEAD(STAT_VALUE,1,0) OVER (PARTITION BY A.STAT_CLASSIFICATION_ID, A.STAT_NAME_ID, STAT_CLASSIFICATION_CATEGORY_ID, STAT_MTHD_CD ORDER BY EXTRACT_DT DESC) AS PREVIOUS, 
	ROW_NUMBER () OVER (PARTITION BY A.STAT_CLASSIFICATION_ID, A.STAT_NAME_ID, STAT_CLASSIFICATION_CATEGORY_ID, STAT_MTHD_CD ORDER BY EXTRACT_DT DESC) AS ROW_NUM,
	SUM (STAT_VALUE) OVER (PARTITION BY A.STAT_CLASSIFICATION_ID, A.STAT_NAME_ID, STAT_CLASSIFICATION_CATEGORY_ID, STAT_MTHD_CD ORDER BY EXTRACT_DT DESC) AS CAT_SUM
FROM CHORUS.QC.REPORT_SOURCE_TF () A
INNER JOIN [CHORUS].[QC].[STAT_CLASSIFICATION] B ON B.STAT_CLASSIFICATION_ID = A.STAT_CLASSIFICATION_ID
INNER JOIN [CHORUS].[QC].[STAT_NAME] C ON C.STAT_NAME_ID = A.STAT_NAME_ID
WHERE SITE_ID = @SITE_ID AND CAST (EXTRACT_DT AS DATE) BETWEEN @FROM_DT AND @TO_DT
)

SELECT STAT_CLASSIFICATION_ID, STAT_CLASSIFICATION_NM, STAT_NAME_ID, STAT_NM, STAT_CLASSIFICATION_CATEGORY_ID, STAT_MTHD_CD,
	STAT_VALUE, EXTRACT_DT, CASE 
	                            WHEN ROW_NUM = 1  AND EXTRACT_DT = LAST_RUN_DT AND PREVIOUS &gt; 0 THEN (STAT_VALUE - PREVIOUS)/CAST(PREVIOUS  AS NUMERIC(25,5))
								WHEN ROW_NUM = 1  AND EXTRACT_DT = LAST_RUN_DT AND STAT_VALUE &gt; 0 AND PREVIOUS = 0 THEN 1
							ELSE 0								 
	END AS PERC_CALC, LAST_RUN_DT
	,ROW_NUM,
	CAT_SUM
FROM CTE
CROSS JOIN
(SELECT MAX(CAST(EXTRACT_DT AS DATE)) AS LAST_RUN_DT
FROM CHORUS.QC.REPORT_SOURCE_TF ()
WHERE SITE_ID = @SITE_ID 
AND CAST (EXTRACT_DT AS DATE) BETWEEN @FROM_DT AND @TO_DT) MAX_RUN</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="STAT_CLASSIFICATION_ID">
          <DataField>STAT_CLASSIFICATION_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="STAT_CLASSIFICATION_NM">
          <DataField>STAT_CLASSIFICATION_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STAT_NAME_ID">
          <DataField>STAT_NAME_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="STAT_CLASSIFICATION_CATEGORY_ID">
          <DataField>STAT_CLASSIFICATION_CATEGORY_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="STAT_NM">
          <DataField>STAT_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STAT_MTHD_CD">
          <DataField>STAT_MTHD_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STAT_VALUE">
          <DataField>STAT_VALUE</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="PERC_CALC">
          <DataField>PERC_CALC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="LAST_RUN_DT">
          <DataField>LAST_RUN_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ROW_NUM">
          <DataField>ROW_NUM</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="CAT_SUM">
          <DataField>CAT_SUM</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SECLAST_EXTRACT_DT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@SITE_ID">
            <Value>=Parameters!SITE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@FROM_DT">
            <Value>=Parameters!FROM_DT.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@TO_DT">
            <Value>=Parameters!TO_DT.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT TOP (1) CAST(EXTRACT_DT AS date) AS SECLAST_EXTRACT_DT
FROM CHORUS.QC.STAT_RUN
WHERE EXTRACT_DT  &lt; (SELECT MAX(EXTRACT_DT)
					FROM CHORUS.QC.STAT_RUN
					WHERE SITE_ID = @SITE_ID AND CAST (EXTRACT_DT AS DATE) BETWEEN @FROM_DT AND @TO_DT)
ORDER BY EXTRACT_DT DESC</CommandText>
      </Query>
      <Fields>
        <Field Name="SECLAST_EXTRACT_DT">
          <DataField>SECLAST_EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="LAST_EXTRACT_DT">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@SITE_ID">
            <Value>=Parameters!SITE_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@FROM_DT">
            <Value>=Parameters!FROM_DT.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@TO_DT">
            <Value>=Parameters!TO_DT.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT MAX(CAST(EXTRACT_DT AS DATE)) AS LAST_EXTRACT_DT
FROM CHORUS.QC.STAT_RUN
WHERE SITE_ID = @SITE_ID AND CAST (EXTRACT_DT AS DATE) BETWEEN @FROM_DT AND @TO_DT</CommandText>
      </Query>
      <Fields>
        <Field Name="LAST_EXTRACT_DT">
          <DataField>LAST_EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="FROM_DT">
      <DataType>DateTime</DataType>
      <Prompt>From_Dt</Prompt>
    </ReportParameter>
    <ReportParameter Name="TO_DT">
      <DataType>DateTime</DataType>
      <Prompt>To_Dt</Prompt>
    </ReportParameter>
    <ReportParameter Name="SITE_ID">
      <DataType>String</DataType>
      <Prompt>SITE_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="Percent_Fill">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>10</Value>
        </Values>
      </DefaultValue>
      <Prompt>Enter N% Delta To Highlight in Yellow</Prompt>
    </ReportParameter>
    <ReportParameter Name="ALERT_FLG">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>false</Value>
        </Values>
      </DefaultValue>
      <Prompt>Alert:</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Code>Function GetColor(Value As Decimal) As String
	If (Value &gt;= .10) Then Return "Blue"
	If (Value &lt; 0) Then Return "Red"
	Return "Green"
End Function </Code>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>e2cfbd89-9d50-469f-9822-4e8be04aab81</rd:ReportID>
</Report>