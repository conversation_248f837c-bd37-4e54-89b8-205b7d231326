<div class="searchCriteriaDiv">
        <div class="elementDiv">
            High/At Risk LFU for*:
        </div>
        <div class="elementDiv">
            <input mat-form-field class="form-select form-select-sm" [matAutocomplete]="auto" name="rptpanRetention" [formControl]="rptpanRetention">
            <mat-autocomplete #auto="matAutocomplete">
              <mat-optgroup *ngFor="let retgroup of retentionData.groups" [label]="retgroup.name" >
                <mat-option *ngFor="let retData of getGroupData(retgroup.id)" [value]="retData.key">{{retData.value}}</mat-option>
              </mat-optgroup>
            </mat-autocomplete>
        </div>
        <div class="elementDiv">
            <button type="button" (click)="panelService.InitBoldReport()" id="reportViewer_Control_viewReportClick"  aria-describedby="reportViewer_Control_viewReportClick" [class]="rptbtnColor">Run</button>
        </div>
</div>
