import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';

import { QualityMeasureFiltersComponent } from './quality-measure-filters.component';
import { QualityMeasuresService } from '../services/quality-measures.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

describe('QualityMeasureFiltersComponent', () => {
  let component: QualityMeasureFiltersComponent;
  let fixture: ComponentFixture<QualityMeasureFiltersComponent>;
  let mockQualityMeasuresService: jasmine.SpyObj<QualityMeasuresService>;
  let mockUserContext: jasmine.SpyObj<UserContext>;

  beforeEach(async () => {
    // Create spy objects for the services
    mockQualityMeasuresService = jasmine.createSpyObj('QualityMeasuresService', ['getLocations']);
    mockUserContext = jasmine.createSpyObj('UserContext', ['GetCurrentSiteValue']);

    // Set up default return values
    mockUserContext.GetCurrentSiteValue.and.returnValue(123);
    mockQualityMeasuresService.getLocations.and.returnValue(of([
      { value: 'test-location', label: 'Test Location', group: 'Test Group' }
    ]));

    await TestBed.configureTestingModule({
      imports: [QualityMeasureFiltersComponent],
      providers: [
        { provide: QualityMeasuresService, useValue: mockQualityMeasuresService },
        { provide: UserContext, useValue: mockUserContext }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QualityMeasureFiltersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get current site ID from UserContext', () => {
    expect(mockUserContext.GetCurrentSiteValue).toHaveBeenCalled();
  });

  it('should call getLocations API when searching locations', () => {
    // Trigger location search
    component.onLocationSearch('test');

    // Verify that the API was called with the correct siteId
    expect(mockQualityMeasuresService.getLocations).toHaveBeenCalledWith('123');
  });
});
