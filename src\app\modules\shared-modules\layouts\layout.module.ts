import { CommonModule, DatePipe } from '@angular/common';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { NavComponent } from './nav/nav.component';
import { ReportNavComponent } from './report-nav/report-nav.component';
import { HeaderComponent } from './header/header.component';
import { RouterModule } from '@angular/router';
import { MainLayoutComponent } from './main-layout/main-layout.component';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatTableModule } from '@angular/material/table';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatExpansionModule } from '@angular/material/expansion';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
// BrowserModule and BrowserAnimationsModule should only be imported in the root module
// import { BrowserModule } from '@angular/platform-browser';
// import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LayoutWithoutNavComponent } from './layout-without-nav/layout-without-nav.component';
import { MatDialogModule } from '@angular/material/dialog'
import { NgxSpinnerModule } from 'ngx-spinner';
import { ChangePasswordDialogComponent } from './dialogs/change-password-dialog/change-password-dialog.component';
import { DialogResponseComponent } from './dialogs/dialog-response/dialog-response.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatInputModule } from '@angular/material/input';
import { MatSortModule } from '@angular/material/sort';
import { DialogSessionTimeoutComponent } from './dialogs/session-timeout-dialog/dialog-session-timeout.component';
import { HelpScreenComponent } from './help-screen/help-screen.component';
import { MatTreeModule } from '@angular/material/tree';
import { SupportDetailsDialogComponent } from './dialogs/support-details-dialog/support-details-dialog.component';
import { FileSelectorDialogComponent } from './dialogs/file-selector-dialog/file-selector-dialog.component';
import { ScheduleDetailsCalendarComponent } from './schedule-details-calendar/schedule-details-calendar.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DetailsTableComponent } from './details-table/details-table.component';
import { HuddleFilterComponent } from './huddle-filter/huddle-filter.component';


@NgModule({
  declarations: [NavComponent, ReportNavComponent, HeaderComponent, MainLayoutComponent, LayoutWithoutNavComponent, ChangePasswordDialogComponent, DialogResponseComponent, DialogSessionTimeoutComponent, HelpScreenComponent, SupportDetailsDialogComponent, FileSelectorDialogComponent, ScheduleDetailsCalendarComponent, DetailsTableComponent, HuddleFilterComponent],
  exports: [MainLayoutComponent, LayoutWithoutNavComponent, ReportNavComponent, ScheduleDetailsCalendarComponent, DetailsTableComponent, HuddleFilterComponent],
  imports: [CommonModule,
            DatePipe,
            RouterModule,
            FormsModule,
            ReactiveFormsModule,
            // BrowserModule and BrowserAnimationsModule removed - should only be in root module
            MatTableModule,
            MatIconModule,
            MatMenuModule,
            MatButtonModule,
            MatListModule,
            MatTableModule,
            MatPaginatorModule,
            MatInputModule,
            MatSortModule,
            MatToolbarModule,
            MatSidenavModule,
            MatExpansionModule,
            MatTabsModule,
            NgxSpinnerModule,
            MatDialogModule,
            MatAutocompleteModule,
            MatTreeModule,
            MatDatepickerModule,
            MatNativeDateModule
            ],
  providers: [],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class LayoutModule {

  constructor(
  ) {}

}
