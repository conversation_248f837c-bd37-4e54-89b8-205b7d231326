import { Component, Injectable, OnInit } from '@angular/core';

@Injectable()
export class mockDocuments implements OnInit {

  jsonObject: JSON;

  arrayObj: any = [
    {
        "name": "Test Folder here",
        "fileType": "",
        "isFile": "false",
        "ext": null,
        "fileSize": null,
        "modified": "June 05, 2022",
        "modifiedBy": "Test User",
    },
    {
      "name": "first document",
      "ext": "doc",
      "fileType": "Word",
      "isFile": "false",
      "fileSize": "50Kb",
      "modified": "June 05, 2022",
      "modifiedBy": "Test User",
    },
    {
      "name": "second document",
      "ext": "exl",
      "fileType": "Word",
      "isFile": "false",
      "fileSize": "50Kb",
      "modified": "June 05, 2022",
      "modifiedBy": "Test User",
    }
]

   constructor() {
    this.jsonObject = <JSON>this.arrayObj;

  }

  public isGet = true;
  public isPost = false;

  ngOnInit(): void {}
}
