.searchCriteriaDiv {
    position: relative;
    margin: 2px;
    padding: 15px;
    width: 100%;
    z-index: 1;
    background-color: white;
}

// Warning Message
.warning-message {
    margin-bottom: 10px;
}

.lblWarningMsg {
    font-size: smaller;
    padding-bottom: 5px;
    margin: 0;
    color: #666;
}

// Label Row
.label-row {
    margin-bottom: 10px;
}

.field-label {
    color: black;
    font-weight: 500;
    margin: 0;
}

// Location Input Row
.input-row {
    margin-bottom: 15px;
}

.location-input {
    width: 100%;
    max-width: 100%;
}

// Run Button Row
.button-row {
    margin-top: 15px;
    padding: 0 16px;
}

.run-button {
    width: 100% !important;
    padding: 8px 16px !important;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
    font-size: 14px;
    height: auto;
    min-height: 35px;
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}
