import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class DocumentsService {

  private static BreakCrumbs: string[] = ['Documents'];

  constructor() { }

  // Get the current break crumb path
  GetBreakCrumbList(reset: boolean = false): string[] {
    if (reset) {
      let docList: string[]=['Documents']
      DocumentsService.BreakCrumbs = docList;
    }

    return DocumentsService.BreakCrumbs;
  }

  // Set the current break crumb path
  AddBreakCrumbPath(path: string): void {
    DocumentsService.BreakCrumbs.push(path);
  }


  //loops through DocumentsService.BreakCrumbs and returns a concatentated string by "/" and skips the first element
  GetBreakCrumbPath(): string {
    let path = "root";
    for (let i = 1; i < DocumentsService.BreakCrumbs.length; i++) {
      path += "/" + DocumentsService.BreakCrumbs[i];
    }
    return path;
  }

  //remove breadcrumb path back to selected id
  RemoveBreakCrumbById(goBackHowManyLevels: number): void {
      for (let i = 1; i <= goBackHowManyLevels; i++)
      {
        DocumentsService.BreakCrumbs.pop();
      }
  }

  RemoveCurrentCrumb()
  {
    DocumentsService.BreakCrumbs.pop();
  }

  RouteToDirectory(dirName : string, router : Router )  : string
  {
    if (dirName != "Documents" || this.GetBreakCrumbList().length > 1) {
      this.AddBreakCrumbPath(dirName);
    }
    dirName = this.GetBreakCrumbPath();
    dirName==="root" ? dirName+= "/" : dirName;

    //let path = btoa(`${this.dirPath}${dirName}`);
    let path = btoa(`${dirName}`);
    let dirPath = path;
    path = `/Documents/${path}`;
    router.navigate([path]);
    return dirPath;
  }

  BreadCrumbNavigate(crumbPath: string, router : Router)
  {
    let crumbs = this.GetBreakCrumbList();

    if (crumbs.length > 1) {
      let pathId = crumbs.findIndex(crumbs => crumbs === crumbPath)
      pathId = crumbs.length - pathId;
      pathId == crumbs.length ? pathId-= 1 : pathId = pathId;
      this.RemoveBreakCrumbById(pathId);
    }

    this.RouteToDirectory(crumbPath, router);
  }


}
