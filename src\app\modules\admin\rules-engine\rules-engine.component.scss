.rules-engine-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.rules-engine-header {
  margin-bottom: 30px;

  h1 {
    margin-bottom: 10px;
    color: #333;
    font-size: 28px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

.rules-engine-content {
  flex: 1;

  ::ng-deep .mat-tab-body-wrapper {
    flex: 1;
  }
}

.tab-content {
  padding: 20px 0;
}

.rule-type-selector {
  margin-bottom: 20px;

  .mat-form-field {
    width: 300px;
  }

  .rule-type-description {
    margin-top: 8px;
    color: #666;
    font-style: italic;
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .search-container {
    flex: 0 0 300px;
  }
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.no-selection-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #666;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #999;
  }

  p {
    font-size: 18px;
  }
}

// History tab styles
.history-filters {
  margin-bottom: 20px;

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;

    mat-form-field {
      flex: 1;
      min-width: 200px;
      max-width: 300px;
    }

    button {
      height: 56px;
    }
  }
}

.truncated-id {
  font-family: monospace;
  margin-right: 4px;
}

.process-id-container {
  display: flex;
  align-items: center;

  .copy-button {
    margin-left: 4px;
  }
}

.guid-display {
  font-family: monospace;
  font-size: 0.9em;
  font-weight: 500;
  color: #333;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.guid-full {
  font-family: monospace;
  font-size: 0.8em;
  color: #666;
  margin-left: 4px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;

  &.success {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  &.error {
    background-color: #ffebee;
    color: #c62828;
  }

  &.running {
    background-color: #e3f2fd;
    color: #1565c0;
  }
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #999;
  }

  p {
    font-size: 16px;
    margin: 4px 0;

    &.hint {
      font-size: 14px;
      color: #999;
      font-style: italic;
    }
  }
}

// Process details dialog styles
.process-details-header {
  margin-bottom: 20px;

  .process-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .info-row {
      display: flex;
      align-items: center;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 100px;
      }

      .value {
        flex: 1;
      }
    }
  }
}

.process-details-content {
  margin-top: 20px;

  h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #66A9D7;
    padding-bottom: 8px;
  }

  .rule-header {
    display: flex;
    align-items: center;

    .rule-order {
      margin-right: 8px;
      font-weight: 500;
    }

    .rule-name {
      flex: 1;
      font-weight: 500;
    }

    mat-icon {
      margin-left: 8px;

      &.success {
        color: #2e7d32;
      }

      &.error {
        color: #c62828;
      }
    }
  }

  .rule-error {
    background-color: #ffebee;
    padding: 12px;
    border-radius: 4px;
    margin: 12px 0;

    p {
      margin: 0;
      color: #c62828;
    }
  }

  .rule-data {
    margin: 16px 0;

    h4 {
      margin-bottom: 8px;
      color: #333;
    }

    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
      font-family: monospace;
      font-size: 12px;
    }
  }

  .rule-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
  }

  .no-results-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
  }
}

// Workflow Status Dialog styles
.workflow-status-container {
  padding: 16px 0;

  .status-header {
    margin-bottom: 20px;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 100px;
      }

      .value {
        flex: 1;
      }
    }
  }

  .status-progress {
    margin: 20px 0;

    .progress-text {
      margin-top: 8px;
      font-weight: 500;
    }
  }

  .status-details {
    margin: 20px 0;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .info-row {
      display: flex;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
        min-width: 120px;
      }
    }
  }

  h3 {
    margin-top: 24px;
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #66A9D7;
    padding-bottom: 8px;
  }

  .no-results-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .rule-header {
    display: flex;
    align-items: center;

    .rule-order {
      margin-right: 8px;
      font-weight: 500;
    }

    .rule-name {
      flex: 1;
    }

    mat-icon {
      margin-left: 8px;

      &.success {
        color: #4caf50;
      }

      &.error {
        color: #f44336;
      }
    }
  }

  .rule-error {
    background-color: #ffebee;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;

    p {
      margin: 0;
      color: #d32f2f;
    }
  }

  .rule-data {
    margin-bottom: 16px;

    h4 {
      margin-bottom: 8px;
      color: #555;
    }

    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  .rule-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .no-status-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      color: #999;
    }

    p {
      font-size: 16px;
    }
  }
}

.table-container {
  overflow-x: auto;

  table {
    width: 100%;
  }

  .mat-column-actions {
    width: 80px;
    text-align: center;
  }
}

.placeholder-text {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

// Dialog styles
.full-width {
  width: 100%;
  margin-bottom: 15px;
}

.workflow-form {
  padding: 16px;
}

.rule-sequence-tab {
  padding: 12px;
  min-height: 350px;

  ::ng-deep {
    .workflow-details-container {
      padding: 0;
      font-size: 0.85em;

      h3 {
        font-size: 1.1em;
      }

      .mat-form-field {
        font-size: 0.9em;
      }

      .rule-order {
        width: 20px !important;
        height: 20px !important;
        font-size: 11px !important;
      }

      button.mat-icon-button {
        transform: scale(0.85);
      }
    }
  }
}

// JSON Editor styles
.json-editor-section {
  margin-top: 20px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #66A9D7;
    padding-bottom: 10px;
  }

  .loading-definition {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;

    span {
      color: #666;
      font-size: 14px;
    }
  }
}

// Workflow rules dialog
.workflow-rules-container {
  display: flex;
  gap: 20px;

  .assigned-rules, .available-rules {
    flex: 1;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 18px;
      border-bottom: 2px solid #66A9D7;
      padding-bottom: 10px;
    }
  }

  .rule-list {
    border: 1px solid #eee;
    border-radius: 4px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;

    .rule-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin-bottom: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #eee;

      &:last-child {
        margin-bottom: 0;
      }

      .rule-content {
        display: flex;
        flex-direction: column;

        .rule-name {
          font-weight: bold;
          margin-bottom: 5px;
        }

        .rule-type {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .empty-list {
      padding: 20px;
      text-align: center;
      color: #999;
      font-style: italic;
    }
  }
}

// Execution dialog
.execution-result {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;

  &.success {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  &.error {
    background-color: #ffebee;
    color: #c62828;
  }

  mat-icon {
    margin-right: 10px;
  }
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  border-radius: 4px;
  margin-bottom: 20px;

  p {
    margin: 0;
    color: #c62828;
  }
}

.execution-details {
  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #66A9D7;
    padding-bottom: 10px;
  }

  .rule-execution-result {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 15px;

    .rule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .rule-name {
        font-weight: bold;
      }

      mat-icon {
        &.success {
          color: #2e7d32;
        }

        &.error {
          color: #c62828;
        }
      }
    }

    .rule-execution-time {
      font-size: 12px;
      color: #666;
      margin-bottom: 10px;
    }

    .rule-error {
      padding: 10px;
      background-color: #ffebee;
      border-radius: 4px;

      p {
        margin: 0;
        color: #c62828;
      }
    }
  }
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  font-weight: bold;
}

// Workflow rules dialog
::ng-deep .workflow-dialog {
  .mat-dialog-container {
    padding: 0;
    overflow: hidden;

    h2.mat-dialog-title {
      margin: 0;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
    }

    .mat-dialog-content {
      max-height: none;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;

      .mat-tab-group {
        flex: 1;

        .mat-tab-body-wrapper {
          flex: 1;
        }
      }

      app-workflow-details {
        .workflow-details-container {
          font-size: 0.85em;
          height: 100%;

          .workflow-details-content {
            height: 100%;

            .workflow-rules-container {
              height: 100%;

              .assigned-rules, .available-rules {
                display: flex;
                flex-direction: column;

                .rule-list {
                  flex: 1;
                  overflow-y: auto;
                  max-height: 350px;
                }
              }
            }
          }

          h3 {
            font-size: 1.1em;
          }

          .mat-form-field {
            font-size: 0.9em;
          }

          .rule-order {
            width: 20px !important;
            height: 20px !important;
            font-size: 11px !important;
          }

          button.mat-icon-button {
            transform: scale(0.85);
          }
        }
      }
    }

    .mat-dialog-actions {
      margin: 0;
      padding: 12px 24px;
      border-top: 1px solid #e0e0e0;
      background-color: #f5f5f5;
    }
  }
}
