import { Injectable, Injector } from '@angular/core';
import { HttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { mockChorusLogin, mocktest, mockReport, mockDocuments } from '../../../../mockapi';

@Injectable()
export class HttpMockRequestInterceptor implements HttpInterceptor {
    mockDataNames;
    constructor(private injector: Injector) {
      this.mockDataNames = [mockChorusLogin,mocktest,mockReport,mockDocuments]
    }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        for (let mockendpoint of this.mockDataNames) {
          let endpoint = "";
          let methodType = "";
          console.log(mockendpoint.name);
          endpoint = mockendpoint.name;
          endpoint = endpoint.replace("mock","").replace("Mock","");
          console.log(endpoint);

            if (request.url.endsWith(endpoint)) {
                console.log('Loaded from json : ' + request.url);
                let tmproute = new mockendpoint;
                if (tmproute.isPost)
                {
                  methodType = "POST";
                }
                else if(tmproute.isGet)
                {
                  methodType = "GET";
                }

                if(request.method == methodType)
                {
                  //let jsondata: string = tmproute.jsonObject;
                  //let res: Observable<HttpResponse<any>> = of(new HttpResponse({ status: 200, body: tmproute.jsonObject }));
                  //,{observe: 'response', headers: {'Content-Type': 'application/json'}}}
                  return of(new HttpResponse({ status: 200, body: tmproute.jsonObject }));
                }

                /*
                this.httpClient.post(this.apiURL+'V2/ChorusLogin', JSON.stringify(body), {observe: 'response',
                  headers: { 'Content-Type': 'application/json'},
                  });
                */

            }

        }
        console.log('Loaded from http call :' + request.url);
        return next.handle(request);
    }

    public newInstance <T>(tmpObjName: string) : T {
      var instance = new (<any>window)[tmpObjName]();
      return <T> instance;
    }


}
