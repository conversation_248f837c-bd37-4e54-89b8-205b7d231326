import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatMenuModule } from '@angular/material/menu';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonToggleModule } from '@angular/material/button-toggle';

import { RulesListComponent } from './components/rules-list/rules-list.component';
import { WorkflowsListComponent } from './components/workflows-list/workflows-list.component';
import { ExecutionHistoryComponent } from './components/execution-history/execution-history.component';
import { WorkflowStatusComponent } from './components/workflow-status/workflow-status.component';
import { ExecutionDetailsComponent } from './components/execution-details/execution-details.component';
import { JsonEditorComponent } from './components/json-editor/json-editor.component';
import { BooleanFieldComponent } from './components/json-editor/boolean-field/boolean-field.component';
import { JsonToolbarComponent } from './components/json-editor/json-toolbar/json-toolbar.component';
import { RawJsonViewComponent } from './components/json-editor/raw-json-view/raw-json-view.component';
import { ObjectViewComponent } from './components/json-editor/object-view/object-view.component';
import { JsonNodeComponent } from './components/json-editor/json-node/json-node.component';
import { WorkflowDetailsComponent } from './components/workflow-details/workflow-details.component';

@NgModule({
  declarations: [
    RulesListComponent,
    WorkflowsListComponent,
    ExecutionHistoryComponent,
    WorkflowStatusComponent,
    ExecutionDetailsComponent,
    JsonEditorComponent,
    BooleanFieldComponent,
    JsonToolbarComponent,
    RawJsonViewComponent,
    ObjectViewComponent,
    JsonNodeComponent,
    WorkflowDetailsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatTooltipModule,
    ClipboardModule,
    DragDropModule,
    MatMenuModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatButtonToggleModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    RulesListComponent,
    WorkflowsListComponent,
    ExecutionHistoryComponent,
    WorkflowStatusComponent,
    ExecutionDetailsComponent,
    JsonEditorComponent,
    WorkflowDetailsComponent
  ]
})
export class RulesEngineComponentsModule { }
