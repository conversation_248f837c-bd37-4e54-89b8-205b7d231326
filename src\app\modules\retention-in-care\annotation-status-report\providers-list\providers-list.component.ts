import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result, RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result_Group } from '../../retention-models';
import { RetentionService } from '../../retention.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Subscription } from 'rxjs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable'
import { formatDate } from '@angular/common';

@Component({
  selector: 'providers-list',
  templateUrl: './providers-list.component.html',
  styleUrls: ['./providers-list.component.scss']
})
export class ProvidersListComponent implements OnInit, OnD<PERSON>roy {

  retentionProvidersList: RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result[] = [];
  retentionProvidersListGrouped: RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result[][] = [];
  retentionTableGroup: RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result_Group[] = [];
  isActive = true;
  isHighlighted = false;
  active = true;
  inactive = false;
  siteId: number = 0;

  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private retentionInCare: RetentionService,
    private router: Router,
    public userContext: UserContext
  ) { }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit(): void {

    this.siteId = this.userContext.GetCurrentSiteValue()
    if (this.siteId != 0) {
      this.LoadData(this.siteId.toString());
      this.toggleGroup();
    }

    //Bind the site context to the page.
    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(siteId => {
        // Annotation Status Report API call to show Patients list
        this.LoadData(siteId.toString());
        this.toggleGroup();
      })
    );
   
    //this.LoadData(UserContext.GetCurrentSite());
    
   }

   toggleGroup() {    
    this.isActive = !this.isActive;
    this.isHighlighted = !this.isHighlighted;
    this.active = !this.active;
    this.inactive = !this.inactive;
  }

  LoadData(siteId: string)
  {
    this.spinnerService.show();
      // Clear the table before fetching new data
    this.retentionTableGroup = [];
    this.retentionInCare.GetRetentionData(siteId.toString()).subscribe(result => {
      if(result) {
        this.retentionProvidersList = result;
      }
      
      // Manipulate the data to get it into RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result_Group format.
      // Ideally this should be refactor the API to send the data in a better format.
      this.retentionProvidersListGrouped =  Object.values(
        this.retentionProvidersList.reduce((acc, obj) => ({ ...acc, [obj.locatioN_ID]: [...(acc[obj.locatioN_ID] || []), obj ]}), {})
      );
      this.retentionProvidersListGrouped.forEach(e => {
        this.retentionTableGroup.push({providerList: e, expanded: true});
      });
      this.spinnerService.hide();
      })
  }

 
  LoadPatientsByProviderData(retentionProvider:RETENTION_STUDY_PROVIDER_SUMMARY_DTL_Result,paramerName :any,parameterval: any): void {
    
    if (paramerName == "Total_Patient")
    {
        this.retentionInCare.setPatientsByProviderData(retentionProvider);
        this.router.navigate(['OutreachAndRetention/AnnotationStatusReport/PatientsByProvider/'+retentionProvider.provideR_ID+'/'+retentionProvider.locatioN_ID],{});
    }   
    else
    {
      if (parameterval !=0)
      {
      this.retentionInCare.setPatientsByProviderData(retentionProvider);
      var columnName =  paramerName.toLocaleLowerCase() ;
      this.router.navigate(['OutreachAndRetention/AnnotationStatusReport/PatientsByProvider/'+retentionProvider.provideR_ID+'/'+retentionProvider.locatioN_ID], { queryParams: { columnName } });
      }      
    }
  }

  //Prints the report to PDF
  printPDF()
  {
    let doc = new jsPDF('l', 'pt', 'legal');
    let currentDate = formatDate(new Date(), 'ddMMyyyy', 'en-US');
    //let formattedDate = `${currentDate.getMonth() + 1}/${currentDate.getDate()}/${currentDate.getFullYear()}`;
    const tableRows = document.querySelectorAll('#providerListViewTable tr'); // Select all table rows
    const rowCount = tableRows.length - 1; // Get the total row count
    let captionText = `Annotation Status Report\nRun Date: ${currentDate}`;
    
    doc.text(captionText, doc.internal.pageSize.getWidth() / 2, 30, { align: 'center' });
    let totalWidth = doc.internal.pageSize.getWidth() - 50;
    
    autoTable(doc, {
        html: '#providerListViewTable',
      
        startY: 80,

        headStyles: {
            fontSize: 6,
        },
        styles: {
            fontSize: 8, 
        },
        tableWidth: 'auto'
    });
    doc.save('AnnotationStatusReport' + currentDate + '.pdf');
  }

  // Prints the report to a csv file.
  printCSV()
  {
    var csvStr = this.ConvertToCSV(this.retentionProvidersList);
    var currentDate = formatDate(new Date(), 'ddMMyyyy', 'en-US');
    this.saveFile('csv', csvStr, ('CustomQuery' + currentDate));
  }

  ConvertToCSV(objArray) {
    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
    var headers = "Provider by Location, Total Patients, New Alerts / Reattempt Call, Agreed to Visit / ReqtApptChange, Scheduled Visit/ Confirmed Visit, Already Received-Injection, No Voicemail /Remind Later, Left Voicemail, Bad Number / No Service, Refused Visit, Moved, Transferred, Deceased, Incarcerated, Staff Declined to Contact, Phone Number Corrected, Cannot Be Reached, Custom, Others, No Phone Number";
    var str = headers + '\r\n';
      this.retentionProvidersList.forEach(element => {
        var line = '';
        if(element.provideR_ID == 0)
        {
          line = element.locatioN_NM + ", ";
        }
        else
        {
          line = element.provideR_NM + ", ";
        }
          line +=  
            element.retentioN_PARTICIPATION_TOTAL_PATIENTS + ", "
            + element.neW_ALERTS + ", "
            + element.agreeD_TO_VISIT + ", "
            + element.scheduleD_VISIT + ", "
            + element.alreadY_RECEIVED_INJECTION + ", "
            + element.nO_VOICEMAIL + ", "
            + element.lefT_VOICEMAIL + ", "
            + element.wronG_NUMBER + ", "
            + element.refuseD_VISIT + ", "
            + element.moved + ", "
            + element.transferred + ", "
            + element.deceased + ", "
            + element.incarcerated + ", "
            + element.stafF_DECLINED_TO_CONTACT + ", "
            + element.phonE_NUMBER_CORRECTED + ", "
            + element.cannoT_BE_REACHED + ", "
            + element.custom + ", "
            + element.otheR_ANNOTATION_PATIENTS + ", "
            + element.nO_PHONE_NUMBER;

          str += line + '\r\n';
      });
    return str.replaceAll("null", "");
  }

  saveFile(fileType: string, content: any, fileName: string ){
    const blob = new Blob([content], { type: 'text/csv'});
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = fileName;
    a.click();
  }
}
