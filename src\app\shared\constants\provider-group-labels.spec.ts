import { PROVIDER_GROUP_LABELS, getProviderGroupDisplayLabel, ProviderGroupKey } from './provider-group-labels';

describe('Provider Group Labels', () => {
  describe('PROVIDER_GROUP_LABELS constant', () => {
    it('should contain the correct mappings', () => {
      expect(PROVIDER_GROUP_LABELS.AssociatesProvider).toBe('My Associated Providers');
      expect(PROVIDER_GROUP_LABELS.AppointmentProvider).toBe('Providers With Appointments');
    });

    it('should be readonly', () => {
      // This test ensures the 'as const' assertion is working
      const keys = Object.keys(PROVIDER_GROUP_LABELS) as ProviderGroupKey[];
      expect(keys).toContain('AssociatesProvider');
      expect(keys).toContain('AppointmentProvider');
    });
  });

  describe('getProviderGroupDisplayLabel function', () => {
    it('should return correct display label for known group keys', () => {
      expect(getProviderGroupDisplayLabel('AssociatesProvider')).toBe('My Associated Providers');
      expect(getProviderGroupDisplayLabel('AppointmentProvider')).toBe('Providers With Appointments');
    });

    it('should return the original key for unknown group keys', () => {
      expect(getProviderGroupDisplayLabel('UnknownProvider')).toBe('UnknownProvider');
      expect(getProviderGroupDisplayLabel('SomeOtherGroup')).toBe('SomeOtherGroup');
    });

    it('should handle empty string', () => {
      expect(getProviderGroupDisplayLabel('')).toBe('');
    });

    it('should handle null and undefined gracefully', () => {
      expect(getProviderGroupDisplayLabel(null as any)).toBeNull();
      expect(getProviderGroupDisplayLabel(undefined as any)).toBeUndefined();
    });
  });
});
