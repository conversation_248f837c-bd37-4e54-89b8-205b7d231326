import { Component, HostListener, OnInit } from '@angular/core';
import { LoginService  } from 'src/app/modules/login/services/login/login.service';
import { NgxSpinnerService } from "ngx-spinner";
import { Router, RouterModule, Routes } from '@angular/router';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { AuditService, Page } from 'src/app/shared-services/audit.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit {
  
  error_description: string = "";
  errorMsg: String = "";
  emailValue: string = "";
  keyupeventfired: boolean = false;

  constructor(private spinnerService: NgxSpinnerService,
              private router: Router,
              private apiHandler: ApiHandler,
              private userContext: UserContext,
              private auditService: AuditService,
              ) {
                this.auditService.setPageAudit(Page.ForgotPassword);
              }

  ngOnInit(): void {
    this.errorMsg = "Enter your email address to reset your password";
  }


  @HostListener('document:keyup.enter')
  handleEnterKey() {

    this.onForgotPassword(this.emailValue, false);
   
  }

  onForgotPassword(username: string, buttonclicked: boolean)
  {
    if (this.validateEmail(this.emailValue)) {
      this.userContext.SetUsername(username)
      this.spinnerService.show();
      
      const userEmail: {email: string} = {email: username};
  
      if (!this.keyupeventfired)
      {
        this.keyupeventfired = !buttonclicked;
        this.apiHandler.Post(ApiTypes.AuthProvider, ApiRoutes.ForgotPassword, JSON.stringify(userEmail),null,true).subscribe(s =>
          {
            this.spinnerService.hide();
            this.errorMsg = "Check your email for password reset link."
          
            setTimeout(() => {
              this.router.navigate(['/']);
            },3500);
    
          });
      }
    } else {
      this.errorMsg = "Please enter a valid email address.";
      this.keyupeventfired = false;
   
    }
  }

  public validateEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    return emailRegex.test(email);
  }

}
