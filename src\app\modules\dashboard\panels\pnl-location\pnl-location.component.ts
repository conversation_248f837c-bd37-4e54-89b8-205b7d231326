import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-location',
  templateUrl: './pnl-location.component.html',
  styleUrl: './pnl-location.component.scss'
})
export class PnlLocationComponent implements OnInit {

  rptbtnColor: string;
  @Input() rptpanLocation: FormControl;
  @Input() locationSearch: FormControl;

  optionAll = {} as IData;
  locationData: IReportParamData[] =[] as IReportParamData[];
  filteredLocations: IReportParamData[] = [] as IReportParamData[];
  isRunButtonEnable: boolean = false;
  lblWarningMsg: string = 'To run your report, please make a selection for location.';

  constructor(public panelService: PanelService) {
    this.rptpanLocation = new FormControl;
    this.locationSearch = new FormControl;
    this.rptbtnColor= btnColor.btnSecondColor;
    this.optionAll.key=-1;
    this.optionAll.value='All';
  }

  ngOnInit(): void {
    this.panelService.GetPanelData(ReportPanelTypes.pnlLocation).subscribe(s =>
      {
        this.locationData = s;
        this.getDefaults();
        this.comboFilter("All");
      });
  }

  onSelectionChange(event): void {
    this.rptpanLocation.setValue(event.option.value.key);
    this.setButtonColor();
  }

  getDefaults(){
    if (this.locationData && this.locationData[0]?.data) {
      const defaultLocation = this.locationData[0].data.find(item => item.value === this.locationData[0].default);
     
      // Ensure rptpanLocation is updated so should the user not make any selection changes after load, the current value is passed to the API.
      if (defaultLocation) {
        this.locationSearch.setValue(defaultLocation);
        this.rptpanLocation.setValue(defaultLocation.key); 
      } else {
        this.locationSearch.setValue(this.optionAll);
        this.rptpanLocation.setValue(this.optionAll.key);
      }
    }
  
    this.setButtonColor();
  }

  displayFn(option):any {
    if (option)
    {
      return option.value ? option.value : option.key;
    }
      return '';
  }

  comboFilter(searchString :string)
  {
    this.filteredLocations = this.panelService.ComboAutoComplete(searchString, 0, this.locationData, false);  
  }

  setButtonColor(): void {
    if (!this.locationSearch.value || !this.locationSearch.value.value) {
      this.rptbtnColor = btnColor.btnSecondColor;
      this.isRunButtonEnable = false;
    } else {
      this.rptbtnColor = btnColor.btnPrimaryColor;
      this.isRunButtonEnable = true;
    }
  }
}
