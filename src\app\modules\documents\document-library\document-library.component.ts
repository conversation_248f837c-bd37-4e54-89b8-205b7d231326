import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Component, OnInit, Output, EventEmitter, OnDestroy, HostListener } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, Subscription } from 'rxjs';
import {
  IDocumentItem,
  IDocumentManagerRename,
} from 'src/app/shared-services/ep-api-handler/models/documents.model';
import {
  ApiRoutes,
  ApiTypes,
} from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { DocIconTypes, PopUpNames, IconList } from './constants/DocIconTypes';
import { ActivatedRoute, Router } from '@angular/router';
import { DocumentsService } from '../documents.service';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';


//import { HttpClient } from '@angular/common/http';
//import { protectedResources } from '../../auth-config';

@Component({
  selector: 'app-document-library',
  templateUrl: './document-library.component.html',
  styleUrls: ['./document-library.component.scss'],
})

export class DocumentLibraryComponent implements OnInit, OnDestroy {
  private rootSharePath = 'root/';
  public dirPath: string = '';
  public progress: number = 0;
  public message: string = '';
  public errorMessage: string = '';
  public userName: string = '';
  //public documentLibrary: Subject<IDocumentItem[]> = new Subject<IDocumentItem[]>();
  public documentLibrary: IDocumentItem[] = [] as IDocumentItem[];
  public test: IDocumentItem[] = [];
  public fileData: string = '';
  public fileName: string = '';
  public popUpInput: string = '';
  public docIconTypes = DocIconTypes;
  public breadcrumbs: string[] = ["Documents"];
  public siteId: string = '';
  //@Input() public fileUpload: FormControl;
  @Output() public onUploadFinished = new EventEmitter();

  //modal models
  // public modelUpload: PopUpNames = PopUpNames.Upload;
  public popUpNames = PopUpNames;
  public currentPopUp;
  public modelTitle: string = '';
  //@ViewChild('modalInput', { static: true })
  //ModalInput: ElementRef<HTMLInputElement>;

  public file: HTMLInputElement | null = null;
  // public ModalInput: HTMLInputElement;
  public displayStyle: string = 'none';
  private pageSubscriptions: Subscription = new Subscription;
  private lastClickTime:number = 0; // for double click issues
 public  sortIsAscending : boolean = true;
 public lastSortColumn : string = 'Name';

  constructor(
    private userContext: UserContext,
    private layoutService: LayoutService,
    private route: ActivatedRoute,
    private router: Router,
    private documentsService: DocumentsService,
    private auditService: AuditService
  ) {
    this.auditService.setPageAudit(Page.Documents);

    // this.ModalInput  =fileInput
    this.userName = userContext.DisplayName();  // Sets the current user's name
    //this.fileUpload = new FormControl(); // Initializes the file upload control
    this.getdocumentLibrary(); // Gets the document list and stores it


  }

  @HostListener('window:popstate', ['$event'])
  onBrowserBackBtn(event: Event) {
   
    // Display an alert when the back button is pressed
    this.BreadCrumbGoBack()

  }

  private extractPathFromUrl(url: string): string {
    // Split the URL by '?' to remove query parameters
    const urlWithoutQueryParams = url.split('?')[0];
  
    // Split the remaining URL by '/' to get the path segments
    const pathSegments = urlWithoutQueryParams.split('/');
  
    // Remove empty segments (e.g., leading/trailing slashes)
    const nonEmptySegments = pathSegments.filter(segment => segment.trim() !== '');
  
    // Join the non-empty segments back into a path
    const extractedPath = nonEmptySegments.join('/');
  
    return extractedPath;
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  ngOnInit() {
    this.breadcrumbs = this.documentsService.GetBreakCrumbList(true);
    
    this.pageSubscriptions.add(this.userContext.getCurrentSite().subscribe(site => {
      this.siteId = site.toString();
      
      this.route.paramMap.subscribe((params) => {
        this.dirPath = String(params.get('dirPath'));
        if (this.dirPath!="") 
        {
            this.dirPath =  atob(decodeURIComponent(this.dirPath));
            // update breadcrumb on refresh
            if(this.documentsService.GetBreakCrumbList().length === 1 && this.dirPath.split('/').length > 1)
            {
              const components: string[] = this.dirPath.split('/');
              for(let comp = 1; comp < components.length;comp++ )
              {
                if(components[comp] !== '')
                {
                this.documentsService.AddBreakCrumbPath(components[comp]);
                }
              }           
            }
            this.getdocumentLibrary();
        }
  
      });
    }));
    //move to function
    //this.dirPath = this.route.snapshot.paramMap.get('dirPath');

  }

  // Used to navigate back to selected directory
  BreadCrumbNavigate(crumbPath: string)
  {
    this.documentsService.BreadCrumbNavigate(crumbPath, this.router);
  }

  BreadCrumbGoBack()
  {
   
    let crumbs = this.documentsService.GetBreakCrumbList();

    if (crumbs.length > 1) {
      let pathId = crumbs.length-1;
      if (pathId > 0)
      {
        this.documentsService.RemoveCurrentCrumb();
        const crumbPath = this.documentsService.GetBreakCrumbPath();
        this.dirPath = crumbPath;
      //  path = `/Documents/${path}`;
        this.router.navigate([crumbPath]);
      }
    
    }

  }

  thisDirectoryExists(name: string): boolean {
    
    var exists = this.documentLibrary
    .filter(item => !item.isFile && item.name === name)
    .length > 0
    if (exists)
      {
          window.alert('A directory with the name ' + exists + ' already exists.');
      }
    return exists;
  }


    // Used to get the document list from the backend
    getdocumentLibrary() {
        this.getdocumentLibrarySorted('Name', true);
    }

    getdocumentLibrarySorted(columnName, ascending)
    {
       let path = `${btoa(this.dirPath)}`;

        if (path!='')
        {
        let documentGetList = ApiRoutes.DocumentGetListSorted.replace(
          '{{siteId}}',
          this.siteId)
          .replace('{{path}}', path)
          .replace('{{sortColumn}}', columnName)
          .replace('{{ascend}}', ascending.toString())
        ;
        this.userContext.apihandler
          .Get<IDocumentItem[]>(ApiTypes.V2, documentGetList)
          .subscribe((res) => {
            this.documentLibrary = res;
            this.layoutService.hideSpinner();
          });
        }

    }

    // opens dialog box to upload a file or create a new folder
  openPopup(popupName: PopUpNames, isFile?: boolean, fileOrDirname?: string) {
    this.popUpInput = '';
    this.message='';
    this.errorMessage='';
    if (fileOrDirname) {
      this.fileName = fileOrDirname;
    }
    this.currentPopUp = popupName;
    this.modalName(popupName, isFile);
    this.displayStyle = 'block';
  }

  closePopup() {
    this.displayStyle = 'none';
  }

  public modalName(popupName: PopUpNames, isFile?: boolean): string {
    switch (popupName) {
      case PopUpNames.CreateFolder:
        return (this.modelTitle = PopUpNames.CreateFolder);

      case PopUpNames.RenameFileOrDirectory:
        if (isFile) {
          this.modelTitle = PopUpNames.RenameFileOrDirectory.replace(
            '{{fileordir}}',
            'File'
          );
        } else {
          this.modelTitle = PopUpNames.RenameFileOrDirectory.replace(
            '{{fileordir}}',
            'Directory'
          );
        }
        return this.modelTitle;

      default:
      case PopUpNames.Upload:
        return (this.modelTitle = PopUpNames.Upload);
    }
  }

  // Handles all Success/Ok response from dialog Modal
  public dialogSuccess = (newFileName, file) => {
    this.layoutService.showSpinner();
    this.displayStyle = 'none';
    this.message = '';
    this.errorMessage = '';
    switch (this.currentPopUp) {
      case PopUpNames.CreateFolder:
       
        this.layoutService.hideSpinner();
        try{
        if(this.validateFolderName(newFileName))
        {
        this.CreateNewFolder(newFileName);
        }
        } catch(error)
        {
          this.errorMessage= error.message;
        }
        this.fileName = '';
        
        break;

      case PopUpNames.RenameFileOrDirectory:
        this.layoutService.hideSpinner();
        // Check for file extension and append if missing
        const originalExtension = this.fileName.split('.').pop();
        if (originalExtension && !newFileName.includes('.')) {
          newFileName = `${newFileName}.${originalExtension}`;
        }

        this.Rename(newFileName);
        this.fileName = '';
        break;

      default:
        let fileToUpload = file.files[0];
        const formData = new FormData();
        formData.append(fileToUpload.name, this.fileData);

        // Generates the path for the document and base 64 encodes it for the url
        let path = this.rootSharePath;
        let breadcrumbs = this.documentsService.GetBreakCrumbList();
        for(let crumbId=1; crumbId < this.breadcrumbs.length; crumbId++)
        {
          path += breadcrumbs[crumbId] + '/';
        }
        path += this.fileName;
        path = btoa(path);

        var docUploadUri = ApiRoutes.DocumentUpload.replace(
          '{{siteId}}',
          this.siteId
        ).replace('{{FileNamePath}}', path);

        this.userContext.apihandler
          .Post(ApiTypes.V2, docUploadUri, JSON.stringify(this.fileData), true)
          .subscribe((event) => {
            this.layoutService.hideSpinner()
            // this.progress = Math.round(100 * event.loaded / event.total);
            this.message = 'Upload success.';

            // Refreshes the document list
            this.getdocumentLibrary();
            return;
          });
    }
  };

  // api call to delete a document from the backend
  delete(fileName: string) {
    let path = `${btoa(this.documentsService.GetBreakCrumbPath() +"/"+ fileName)}`;
    let documentDelete = ApiRoutes.DocumentDelete.replace(
      '{{siteId}}',
      this.siteId
    ).replace('{{FileNamePath}}', path);

    this.userContext.apihandler
      .Post(ApiTypes.V2, documentDelete, '', false)
      .subscribe((s) => {
        if (s) {
          let fileOrDir = '';
          if (fileName.includes('.')) {
            fileOrDir = 'File';
          } else {
            fileOrDir = 'Directory';
          }

          this.message = `${fileOrDir} Deleted`;
          this.getdocumentLibrary();
        }
      });
  }



  // Used to download a document
  download(fileName: string) {
    let fileNameAndPath = fileName;
    this.layoutService.showSpinner()
    this.getDownloadUrl(fileName).subscribe((event) => {
      this.layoutService.hideSpinner();
      this.message = 'Download success.';
      this.downloadFile(event, fileNameAndPath);
    });
  }

  // This method takes in a file name as input and returns an observable
  // of the download URL for the file.
  getDownloadUrl(fileName: string): Observable<any> {
    let path = this.documentsService.GetBreakCrumbPath() + "/"+ fileName;
    path = btoa(path);
    var DownloadUri = ApiRoutes.DocumentDownload.replace(
      '{{siteId}}',
      this.siteId
    ).replace('{{FileNamePath}}', path);
    return this.userContext.apihandler.Get(ApiTypes.V2, DownloadUri, true);
  }

  //download the file
  private downloadFile = (data: any, fileName: string) => {
    let downloadedFile: Blob = new Blob();


    let file = fileName.substring(fileName.lastIndexOf("/")+1);
    let extension = file.split('.')[1].toLowerCase();
    if (data.data) {
      const byteString = atob(data.data.fileContents); // Decode binary data from the server
      const arrayBuffer = new ArrayBuffer(byteString.length);
      const int8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < byteString.length; i++) {
        int8Array[i] = byteString.charCodeAt(i);
      }
      downloadedFile = new Blob([int8Array]); // Create blob object with binary data
      //downloadedFile = new Blob([int8Array], { type: this.createFileType(extension) });
    }
    const a = document.createElement('a');
    a.setAttribute('style', 'display:none;');
    document.body.appendChild(a);
    a.download = file;
    a.href = URL.createObjectURL(downloadedFile);
    a.target = '_blank';
    a.click();
    document.body.removeChild(a);
  };

  //reads the file and stores its contents and name
  onSelect(event: Event) {
    let fileElement: HTMLInputElement;
    let fileBlob: Blob = new Blob();
    if (event.target) {
      fileElement = event.target as HTMLInputElement;
      if (fileElement.files) {
        fileBlob = fileElement.files[0];
      }
    }
    const reader = new FileReader();
    reader.onload = () => {
      if (reader.result) {
        this.popUpInput = '';
        this.fileData = reader.result.toString();
        this.fileName = fileElement.value.replace('C:\\fakepath\\', '');
        this.file = fileElement;
      }
    };

    reader.readAsDataURL(fileBlob);
  }

  onDialogTxtChange(event: any) {
    this.popUpInput = event.target.value;
  }

  public Rename(newName: string) {
    if (this.thisDirectoryExists(newName)) {
      return;
    }
  
    // Extract current extension
    const currentExtension = this.fileName.includes('.')
      ? this.fileName.split('.').pop()
      : '';
  
    // Check if the new name includes an extension; if not, append the original extension
    if (currentExtension && !newName.includes('.')) {
      newName = `${newName}.${currentExtension}`;
    }
  
    let documentRename = ApiRoutes.DocumentRename.replace(
      '{{siteId}}',
      this.siteId
    );
    let path = `${this.documentsService.GetBreakCrumbPath()}/${this.fileName}`;
    let reNameFileDir: IDocumentManagerRename = {
      pathandname: path,
      newname: newName,
    };
  
    this.userContext.apihandler
      .Post(ApiTypes.V2, documentRename, JSON.stringify(reNameFileDir), true)
      .subscribe((s) => {
        if (s) {
          this.message = 'Renamed';
          this.getdocumentLibrary();
        }
      });
  }  

  public CreateNewFolder(newName) {

    if (this.thisDirectoryExists(newName))
    {
      return;
    }

    let rootAndDirName = `${btoa(this.documentsService.GetBreakCrumbPath() +"/"+ newName)}`;
    let documentCreateUri = ApiRoutes.DocumentCreate.replace(
      '{{siteId}}',
      this.siteId
    ).replace('{{dirName}}', rootAndDirName);
    /*
      let createDir: IDocumentManagerRename = {
        pathandname: rootAndDirName,
        newname: newName,
      };
   */
    this.userContext.apihandler
      .Post(ApiTypes.V2, documentCreateUri, '', true)
      .subscribe((s) => {
        if (s) {
          this.message = 'Folder Created';
          this.getdocumentLibrary();
        }
      });
    this.layoutService.hideSpinner();
  }

  createFileType(e): string {
    let fileType: string = '';
    if (e == 'pdf' || e == 'csv') {
      fileType = `application/${e}`;
    } else if (
      e == 'jpeg' ||
      e == 'jpg' ||
      e == 'png' ||
      e == 'png' ||
      e == 'gif'
    ) {
      fileType = `image/${e}`;
    } else if (e == 'txt') {
      fileType = 'text/plain';
    } else if (e == 'ppt' || e == 'pot' || e == 'pps' || e == 'ppa') {
      fileType = 'application/vnd.ms-powerpoint';
    } else if (e == 'pptx') {
      fileType =
        'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    } else if (e == 'doc' || e == 'dot') {
      fileType = 'application/msword';
    } else if (e == 'docx') {
      fileType =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (e == 'xls' || e == 'xlt' || e == 'xla') {
      fileType = 'application/vnd.ms-excel';
    } else if (e == 'xlsx') {
      fileType =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }

    return fileType;
  }

  public saveInput(newName: string) {
    this.Rename(newName);
  }

  public cancelInput() {}


  public routeToDirectory(dirName: string) {
    this.dirPath =  this.documentsService.RouteToDirectory(dirName, this.router)
  }

  //this function routes to eitherrouteToDirectory or Download function
  public routeFileOrDirectory(dirName: string, isFile: boolean) {
    this.message = '';
    this.errorMessage ='';
    if(isFile) {
      this.download(dirName);
    } else {
      // handle double click as single click
      const currentTime = new Date().getTime();
      if(currentTime - this.lastClickTime > 500 || this.lastClickTime === 0)
      {
      this.routeToDirectory(dirName);
      }
      this.lastClickTime = currentTime;
    }
  }

  //Move to Generic Help Utility Class
  private ensureTrailingSlash(s: string): string {
    return s.endsWith('\\') ? s : s + '\\';
  }

  public handleHeaderClick(caption) {

    if (caption == this.lastSortColumn)
    {
      this.sortIsAscending = !this.sortIsAscending;
    }
    else
    {
      this.sortIsAscending = true;
      this.lastSortColumn = caption;
    }

    this.getdocumentLibrarySorted(caption,  this.sortIsAscending);
 
  }

  getFileExtension(filename: string): string {
    const ext = filename.split('.').pop();
    var obj = IconList.find(row => row.type === ext);

    if (!obj)
      {
        obj =    IconList.find(row => row.type === 'blank') ;
      }

    return obj ? obj.icon : ''; // Return the icon class or an empty string if not found
  }

  getFolder(): string {
    
    const obj = IconList.find(row => row.type === 'folder');
    return obj ? obj.icon : ''; // Return the icon class or an empty string if not found
  }

  getBlank() : string {
    const obj =    IconList.find(row => row.type === 'blank') ;
   return obj ? obj.icon : ''; 
  }
  // validate folder names
  validateFolderName(folderName: string): boolean {
    
      let reservedNames: string[] = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'LPT1'];
      const trimmedFolderName = folderName.trim();
      if (!trimmedFolderName) {
        throw new Error('Folder name cannot be empty.');
      }
      const invalidChars = /[<>:"\/\\|?*]/;
      if (invalidChars.test(trimmedFolderName)) {
        throw new Error('Folder name contains invalid characters. /[<>:"\/\\|?*]/');
      }
      if (reservedNames.includes(trimmedFolderName.toUpperCase())) {
        throw new Error(`Folder name cannot be a reserved name like "${trimmedFolderName}".`);
      }
      return true;

    } 

}
