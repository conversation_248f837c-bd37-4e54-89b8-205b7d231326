
<div class="helpHeader" style="background:#005391; height: 50px;">
  <img class="images" style="height: 50px;" src="../../../assets/images/logo.png" />
  <div class="helpTitle">CHORUS Help Portal</div>
</div>

<table class="table-layout">
    <tr>
        <td class="sidebar">
            <mat-tree [dataSource]="helpDataSource" [treeControl]="helpTree" class="help-tree">
                <!-- This is the tree node template for leaf nodes -->
                <!-- There is inline padding applied to this node using styles.
                  This padding value depends on the mat-icon-button width. -->
                <mat-tree-node *matTreeNodeDef="let node" matTreeNodeToggle>
                    <div class="leaf" (click)="getHelpContent(node,siteId)">{{node.name}}</div>
                </mat-tree-node>
                <!-- This is the tree node template for expandable nodes -->
                <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
                    <div class="mat-tree-node">
                      <button mat-icon-button matTreeNodeToggle
                              [attr.aria-label]="'Toggle ' + node.name">
                        <mat-icon class="mat-icon-rtl-mirror">
                          {{helpTree.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
                        </mat-icon>
                      </button>
                        <div style="white-space: nowrap;">{{node.name}}</div>
                    </div>
                    <!-- There is inline padding applied to this div using styles.
                        This padding value depends on the mat-icon-button width.  -->
                    <div [class.tree-invisible]="!helpTree.isExpanded(node)"
                        role="group">
                      <ng-container matTreeNodeOutlet></ng-container>
                  </div>
                </mat-nested-tree-node>
            </mat-tree>
        </td>
        <td class="contentlayout">
            <div class="content" [innerHtml]="helpContent"></div>
        </td>
    </tr>
</table>