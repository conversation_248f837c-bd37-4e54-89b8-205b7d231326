
<head>
  <link rel="stylesheet" href="https://unpkg.com/file-icon-vectors@1.0.0/dist/file-icon-vectors.min.css" />
</head>
<div>
<h2 class="document-header">Documents</h2>
<div>
<hr class="header-divider">
<div class="main-container">


  <div class="toolbar-container">
    <div class="toolbar-spacer">
      <a class="clickable-link" (click)="openPopup(popUpNames.Upload,undefined)"></a>
    </div>
    <div class="toolbar-buttons">
      <!-- Create Folder -->
      <button class="btn btn-primary btn-sm toolbar-button" (click)="openPopup(popUpNames.CreateFolder,undefined)" >
        <mat-icon class="toolbar-button-icon">control_point</mat-icon>
        <div class="toolbar-button-text">&nbsp;Folder</div>
      </button>
      <!-- Upload File -->
      <button class="btn btn-primary btn-sm toolbar-button" (click)="openPopup(popUpNames.Upload,undefined)" >
        <mat-icon class="toolbar-button-icon">control_point</mat-icon>
        <div class="toolbar-button-text">&nbsp;File</div>
      </button>
      <div class="toolbar-divider">&nbsp;
      </div>
      <div class="breadcrumb-container">
        <mat-icon class="breadcrumb-icon">{{docIconTypes.FolderClosed}}</mat-icon>
        <div class="breadcrumb-text">
          <div class="breadcrumb-item" *ngFor="let crumb of breadcrumbs; index as i;">&nbsp;<a (click)="BreadCrumbNavigate(crumb)" class="breadcrumb-item">{{crumb}}</a>
            <div class="breadcrumb-separator" *ngIf="i<breadcrumbs.length-1">&nbsp;>&nbsp;</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="col-md-4">
    <span class="upload" *ngIf="progress > 0">
      {{progress}}%
    </span>
    <span class="upload" *ngIf="message">
      {{message}}
    </span>
    <span class="upload upload-error" *ngIf="errorMessage">
      {{errorMessage}}
    </span>
  </div>

<div class="table-container">
    <table class="styled-table document-table">
        <thead>
          <tr >
            <th class="table-header-icon" (click)="handleHeaderClick('Type')" ><mat-icon >{{docIconTypes.UnknownFile}}</mat-icon>
              <span *ngIf="sortIsAscending && lastSortColumn=='Type'"><mat-icon>{{docIconTypes.AscendingSort}}</mat-icon></span>
              <span *ngIf="!sortIsAscending && lastSortColumn=='Type'"><mat-icon>{{docIconTypes.DescendingSort}}</mat-icon></span>
            </th>
            <th class="table-header-name" (click)="handleHeaderClick('Name')">Name
              <span *ngIf="sortIsAscending && lastSortColumn=='Name'"><mat-icon>{{docIconTypes.AscendingSort}}</mat-icon></span>
              <span *ngIf="!sortIsAscending && lastSortColumn=='Name'"><mat-icon>{{docIconTypes.DescendingSort}}</mat-icon></span>
            </th>
            <th class="table-header-size">Size</th>
            <th class="table-header-modified" (click)="handleHeaderClick('Modified')">Modified
              <span *ngIf="sortIsAscending && lastSortColumn=='Modified'"><mat-icon>{{docIconTypes.AscendingSort}}</mat-icon></span>
              <span *ngIf="!sortIsAscending && lastSortColumn=='Modified'"><mat-icon>{{docIconTypes.DescendingSort}}</mat-icon></span>
            </th>
            <th class="table-header-modified-by" (click)="handleHeaderClick('ModifiedBy')">ModifiedBy
              <span *ngIf="sortIsAscending && lastSortColumn=='ModifiedBy'"><mat-icon>{{docIconTypes.AscendingSort}}</mat-icon></span>
              <span *ngIf="!sortIsAscending && lastSortColumn=='ModifiedBy'"><mat-icon>{{docIconTypes.DescendingSort}}</mat-icon></span>
            </th>
            <th class="table-header-actions"></th>
          </tr>
        </thead>
        <tbody >
          <tr *ngFor='let _documentLibrary of (documentLibrary)'>

            <td class="docIconHeader">
              <ng-container   *ngIf="_documentLibrary.isFile==false"><span class="docIcon" [ngClass]="getFolder()"></span></ng-container>
              <ng-container *ngIf="_documentLibrary.isFile==true"><span  class="docIcon" [ngClass]="getFileExtension(_documentLibrary.name)"></span></ng-container>
            </td>
            <td>
              <div *ngIf="_documentLibrary.isFile==true" class="file-name-cell"  (click)="routeFileOrDirectory(_documentLibrary.name,_documentLibrary.isFile)">&nbsp;{{_documentLibrary.name}}</div>
              <div *ngIf="_documentLibrary.isFile==false" class="file-name-cell" (click)="routeFileOrDirectory(_documentLibrary.name,_documentLibrary.isFile)">&nbsp;{{_documentLibrary.name}}</div>
            </td>
            <td (click)="routeFileOrDirectory(_documentLibrary.name,_documentLibrary.isFile)" class="table-cell-clickable">{{_documentLibrary.size}}</td>
            <td (click)="routeFileOrDirectory(_documentLibrary.name,_documentLibrary.isFile)" class="table-cell-clickable">{{_documentLibrary.modified}}</td>
            <td (click)="routeFileOrDirectory(_documentLibrary.name,_documentLibrary.isFile)" class="table-cell-clickable">{{_documentLibrary.modifiedBy}}</td>
            <td class="table-actions-cell">
               <button class="action-button" mat-button [matMenuTriggerFor]="menu"><i class="material-icons">more_vert</i></button>
              <mat-menu #menu="matMenu">
                <button mat-menu-item *ngIf="_documentLibrary.isFile==true" (click)="download(_documentLibrary.name)">Download</button>
                <button mat-menu-item *ngIf="_documentLibrary.isFile==true" (click)="openPopup(popUpNames.RenameFileOrDirectory,true, _documentLibrary.name)">Rename</button>
                <button mat-menu-item *ngIf="_documentLibrary.isFile==false" (click)="openPopup(popUpNames.RenameFileOrDirectory,false, _documentLibrary.name)">Rename</button>
                <button mat-menu-item (click)="delete(_documentLibrary.name)">Delete</button>
              </mat-menu>
          </td>
          </tr>
        </tbody>
      </table>
</div>
</div>


<div class="modal" tabindex="-1" role="dialog" [ngStyle]="{'display':displayStyle}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{modelTitle}}</h4>
      </div>
      <div class="modal-body">

        <div class="row modal-row">
            <div class="col-md-3">
              <ng-container *ngIf="modelTitle==popUpNames.Upload">
              <input mat-form-field type="file" placeholder="Choose file" (change)="onSelect($event)">
              </ng-container>
              <ng-container *ngIf="modelTitle!=popUpNames.Upload">
                <input mat-form-field type="text" [value]="popUpInput" (change)="onDialogTxtChange($event)">
                </ng-container>
            </div>
            <!-- <div class="col-md-4">
              <span class="upload" *ngIf="uploadProgress > 0">
                {{uploadUrogress}}%
              </span>
              <span class="upload" *ngIf="uploadMessage">
                {{uploadMessage}}
              </span>
            </div> -->
          </div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn-btn btn-primary btn-sm btnWidth" (click)="dialogSuccess(popUpInput, file)">Save</button>
        <button type="button" class="btn-btn btn-primary btn-sm btnWidth" (click)="closePopup()">Cancel</button>
      </div>
    </div>
  </div>
</div>
</div>
