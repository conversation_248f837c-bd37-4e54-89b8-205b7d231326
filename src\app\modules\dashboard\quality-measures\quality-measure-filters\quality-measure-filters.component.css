.filter-container {
  background-color: transparent; /* Let parent control background */
  font-family: MuseoSans-300;
  font-size: 10px;
  padding: 16px; /* Add padding inside the container */
  height: 100%;
  display: flex;
  flex-direction: column;
}

h2 {
  color: #0071bc;
  font-family: MuseoSans-700;
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 500;
  text-align: left;
  display: none; /* Hide since panel header shows the title */
}

label {
  font-size: 0.8rem;
  color: #333;
  font-weight: 400;
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}

.form-select-sm {
  font-size: 0.9rem;
  color: #666;
  background-position: right 10px center;
  background-size: 10px;
  text-overflow: ellipsis;
  border-radius: 5px;
  height: 35px;
  background-color: #fafafa;
}

.form-select-sm:focus {
  border-color: #0071bc;
  box-shadow: 0 0 0 0.25rem rgba(0, 113, 188, 0.25);
}

.run-button {
  background-color: #0071bc;
  color: white;
  border: 2px solid #66a9d7;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
  height: 38px;
}

.run-button:hover {
  background-color: #005a95;
}

.run-button:active {
  background-color: #004b7c;
}

.filterBorder {
  border-radius: 0; /* Remove border radius to fit panel */
  border: none; /* Remove border since panel provides it */
  padding: 0; /* Remove padding since container provides it */
  display: grid;
  grid-template-columns: 1fr;
  flex: 1; /* Take available space */
  gap: 12px; /* Add gap between filter rows */
}

.separator {
  margin-left: 5px;
  margin-right: 5px;
}

select, option {
  font-family: MuseoSans-300, sans-serif;
}

/* ng-select styling is now handled globally in src/assets/styles/ng-select.scss */
