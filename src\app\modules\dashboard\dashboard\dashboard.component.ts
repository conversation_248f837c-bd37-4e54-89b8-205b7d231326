import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, Inject, <PERSON><PERSON><PERSON>roy, OnInit,  ViewChild, ElementRef } from '@angular/core';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import {
  AppointmentInfo,
  AppointmentProviders,
  FormLinkModel,
  MeasureGapDescModel,
  PatientScheduleStatus,
  QualityGapModel,
} from '../models/Appointment-Info-model';
import { DatePipe } from '@angular/common';

import { IChorusAccessViewModel } from 'src/app/shared-services/user-context/models/user-security-model';
import { NgxSpinnerService } from 'ngx-spinner';
import { MatDialog } from '@angular/material/dialog';
import { DashboardService } from './services/chorus/dashboard.service';
import { Subscription } from 'rxjs';
import { IChorusUserInfo } from 'src/app/shared-services/ep-api-handler/models/chorus-user-Info.model';
import { AuditService, Page } from 'src/app/shared-services/audit.service';
import { LookupValue } from '../models/lookup-value-model';
import { workflowRuleResult } from '../models/workflow-rule-results-model';
import { PROVIDER_GROUP_LABELS } from 'src/app/shared/constants/provider-group-labels';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit, OnDestroy {

  siteId: Number = 0;
  userName: string = '';
  loginDisplay = false;
  loadedForSite: Number = -1;
  appointmentInfo: AppointmentInfo[] = [];
  AllProviders: AppointmentProviders[] = [];
  AppointmentProviders: AppointmentProviders[] = [];
  AssociatesProvider: AppointmentProviders[] = [];
  FilteredAppointmentProviders: AppointmentProviders[] = [];
  FilteredAssociatesProviders:  AppointmentProviders[] = [];
  selectedprovider: any = "-1";
  showQualityGroup: boolean = true;
  selectedDate: any;
  lblNoAppointments: string = 'There are no appointments scheduled for the selected provider on ';
  lblNoProviderSelect: string = 'Please select provider from the provider list';
  lblWarningText: string = this.lblNoProviderSelect;
  lblQualityGroup:string='Hide Actionable Gaps';
  currentSelectedDate = new Date();
  formList: LookupValue[] = [];
  formStatusList: workflowRuleResult[] = [];
  // Cache for form statuses to avoid multiple API calls
  formStatusCache: { [key: string]: workflowRuleResult[] } = {};
  // Variables used for Patient Appointment List filter
  yesterdayDate: string | undefined;
  tomorrowDate: string | undefined;
  datepipe: DatePipe = new DatePipe('en-US');
  site: string = '';
  chorusSecurity: IChorusUserInfo = {} as IChorusUserInfo;
  @ViewChild('PrintSSinner_table') printSSInnerTable!: ElementRef;
  providerControl = new FormControl();
  private pageSubscriptions: Subscription = new Subscription();

  // Expose provider group labels for template use
  readonly providerGroupLabels = PROVIDER_GROUP_LABELS;

  constructor(
    private userContext: UserContext,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private router: Router,
    private ref: ChangeDetectorRef,
    public dialog: MatDialog,
    private dashboardService: DashboardService,
    private auditService: AuditService
  ) {
    this.auditService.setPageAudit(Page.Schedule);
  }

  ngOnInit() {

    this.currentSelectedDate = this.dashboardService.GetAppointmentDateSelect() !="" ? new Date(this.dashboardService.GetAppointmentDateSelect()) : new Date();

    this.pageSubscriptions.add(
      this.userContext.getUserSession().subscribe((s) => {

      this.pageSubscriptions.add(
        this.userContext.getUserSecurity().subscribe((s: IChorusAccessViewModel[]) => {

            this.pageSubscriptions.add(this.userContext.getChorusUserInfo().subscribe((s) => {
              this.chorusSecurity = s;
              this.userName = s.firstNm + ' ' + s.lastNm;

              if (s.isAgree!=true) {
                this.userContext.GetUserInfoSub(true).subscribe(s => {
                  this.chorusSecurity = s;

                  //this is just until we get the TFA Flow Implemented
                  if (this.chorusSecurity.isAgree!==true) {
                    this.router.navigate(['/TermsConditions']);
                  }
                  else
                  {
                      this.router.navigate(['/Dashboard']);
                  }

                });
              }
            }));


          //Bind the site context to the page.
          // Note: This subscription is nested inside other subscriptions and may not fire on fresh app loads
          this.pageSubscriptions.add(
            this.userContext.getCurrentSite().subscribe(site => {
              this.site = site.toString();
              this.siteId = site;

              // Only load data if we have a valid siteId and haven't loaded for this site yet
              if (this.siteId != 0 && this.loadedForSite != this.siteId)
              {
                // Set date header BEFORE loading providers data to ensure selectedDate is initialized
                this.SetDateHeader(this.dashboardService.GetAppointmentDateSelect());

                this.loadProvidersData(true);
                this.loadedForSite = this.siteId;

                // Load form list now that we have a valid siteId
                this.getFormList();
              } else {
                // Always update date header even if not loading new data
                this.SetDateHeader(this.dashboardService.GetAppointmentDateSelect());
              }
            }
          ));

      }));
    }));


    this.SetDateHeader(this.dashboardService.GetAppointmentDateSelect());

    // Fallback mechanism for same module redirects where subscriptions may not fire
    // This handles cases where user navigates within Dashboard module and subscriptions don't retrigger
    if (this.loadedForSite == -1 && this.siteId ==0 && this.userContext.GetCurrentSiteValue() != 0)
    {
      // Site context is available immediately, load data now
      this.loadedForSite = this.userContext.GetCurrentSiteValue();
      this.siteId = this.userContext.GetCurrentSiteValue();
      this.site = this.userContext.GetCurrentSiteValue().toString();

      // Set date header BEFORE loading providers data to ensure selectedDate is initialized
      this.SetDateHeader(this.dashboardService.GetAppointmentDateSelect());

      this.loadProvidersData(true);

      // Load form list now that we have a valid siteId
      this.getFormList();
    }

    // Direct subscription to getCurrentSite to handle fresh app loads
    // This ensures data loading happens even if nested subscriptions don't fire in time
    // Critical for initial app load when site context becomes available after component init
    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe(site => {
        // Only proceed if we haven't loaded data for this site yet and it's a valid site
        if (site != 0 && this.loadedForSite != site) {
          this.site = site.toString();
          this.siteId = site;

          // Set date header BEFORE loading providers data to ensure selectedDate is initialized
          this.SetDateHeader(this.dashboardService.GetAppointmentDateSelect());

          this.loadProvidersData(true);
          this.loadedForSite = this.siteId;

          // Load form list now that we have a valid siteId
          this.getFormList();
        }
      })
    );

    // Load form status cache from sessionStorage if available
    this.loadFormStatusCache();

    // Only call getFormList if we already have a valid siteId
    // Otherwise it will be called when the site context is loaded
    if (this.siteId != 0) {
      this.getFormList();
    }
  }

  SetDateHeader(savedDate: string = "")
  {
        // Changes for showing yesterday and tomorrow date when page is loaded
        let yDate = new Date();
        let tDate = new Date();

        if (savedDate) {
            this.selectedDate = this.datepipe.transform(new Date(savedDate), 'MMMM d, y')?.toString();
        } else {
            this.selectedDate = this.datepipe.transform(new Date(), 'MMMM d, y')?.toString();
        }

        yDate.setDate(this.currentSelectedDate.getDate() - 1);
        this.yesterdayDate = this.datepipe
          .transform(yDate, 'MMMM d, y')
          ?.toString();

        tDate.setDate(this.currentSelectedDate.getDate() + 1);
        this.tomorrowDate = this.datepipe.transform(tDate, 'MMMM d, y')?.toString();
  }

  setLoginDisplay() {
    this.loginDisplay = true;
  }
  loadProvidersData(loadFirstProvider: boolean = false) {
    // Format the selected date for the API call (MM-dd-yyyy format required)
    let selecteddate = this.datepipe
      .transform(this.selectedDate, 'MM-dd-yyyy')
      ?.toString();

    // Call GetAppointmentProviders API - this loads the provider dropdown and appointment data
    // API endpoint: /V2/api/Appointment/GetProviders/{siteId}/{date}
    this.pageSubscriptions.add(
      this.dashboardService.GetAppointmentProviders(this.siteId.toString(),selecteddate)
        .subscribe((res) => {
          if (res) {
            this.AllProviders = res?.length != 0 ? res : this.AllProviders;
            this.AppointmentProviders = res.filter(
              (x) => x.group == 'AppointmentProvider'
            );
            this.AssociatesProvider = res.filter(
              (x) => x.group == 'AssociatesProvider'
            );
            if ((res?.length != 0) && (this.selectedprovider == "-1" || loadFirstProvider == true)) {
              let providers = this.AssociatesProvider.concat(
                this.AppointmentProviders
              );
              let defaultProvider = providers.filter(
                (x) => x.primarY_FLG == true
              );
              if (this.dashboardService.GetAppointmentSelect() != "")
                this.selectedprovider = this.dashboardService.GetAppointmentSelect();
              else
                if (defaultProvider.length == 1)
                  this.selectedprovider = defaultProvider[0].provideR_ID;
                else
                  this.selectedprovider = providers[0].provideR_ID;
              this.loadAppointmentsData(this.selectedprovider);

            }
            else
              if (res?.length == 0 && this.selectedprovider != "-1") {
                this.appointmentInfo = [];
                this.lblWarningText = this.lblNoAppointments + this.selectedDate;
              } else {
                this.selectedprovider = this.dashboardService.GetAppointmentSelect()!="" ? this.dashboardService.GetAppointmentSelect() : this.selectedprovider
                this.loadAppointmentsData(this.selectedprovider);
            }
            if (res?.length == 0) {
              this.selectedprovider = this.dashboardService.GetAppointmentSelect()!="" ? this.dashboardService.GetAppointmentSelect() : "-1";
            }
            this.comboFilter('All');
            var sel = this.AllProviders.find(x => x.provideR_ID == this.selectedprovider);
            if (sel) {
                this.setProviderControl(sel.fulL_NM);
            }
          }
        })
      );
  }

  loadAppointmentsData(providerId: number) {
    this.spinnerService.show();
    if (providerId == -1) {
      this.lblWarningText = this.lblNoProviderSelect;
      this.appointmentInfo = [];
      this.spinnerService.hide();
      return;
    }
    this.lblWarningText = '';
    let selecteddate = this.datepipe.transform(this.selectedDate, 'MM-dd-yyyy')?.toString();
    this.pageSubscriptions.add(this.dashboardService.GetAppointment(this.siteId.toString(),providerId.toString(),selecteddate)
    .subscribe((res) => {
      this.spinnerService.hide();
      if (res) {
        this.appointmentInfo = res;
        if (this.appointmentInfo?.length == 0) {
          this.lblWarningText = this.lblNoAppointments + this.selectedDate;
          return;
        }
        this.appointmentInfo.forEach((item) => {
          item.gridRowColor = this.getRowColor(item.statusCd);
          item.FlowSheetImage = this.getFlowSheetImage(
            item.hivDiagFlg,
            item.hcvDiagFlg
          );
          if (item.measureGap){
            item.measureGap = this.createMeasureGapDesc(item.measureGap);
          }
        });
        this.ref.detectChanges();
      }
    }));
  }
  providerChange(evt) {

    var value = evt.option.value.toString();
    this.spinnerService.show();
    if (value == "-1") {
      this.lblWarningText = this.lblNoProviderSelect;
      this.appointmentInfo = [];
      this.spinnerService.hide()
      return;
    }
    this.dashboardService.SetAppointmentSelect(value);
    this.loadAppointmentsData(value);
    var sel = this.AllProviders.filter(x=>x.provideR_ID === evt.option.value)[0];
    this.setProviderControl(sel.fulL_NM);
  }

  setProviderControl(txt: string)
  {
    this.providerControl.setValue(txt);
  }

  getRowColor(status: string) {
    switch (status) {
      case PatientScheduleStatus.Completed:
        return 'GridRowColorCompleted';
      case PatientScheduleStatus.Cancelled:
        return 'GridRowColorCancelled';
      case PatientScheduleStatus.NoShow:
        return 'GridRowColornoShow';
      default:
        return '';
    }
  }

  createMeasureGapDesc(qualityGap: QualityGapModel): QualityGapModel
  {
    let excludeSourceList: string[] = ["KPI","HHS","Other","ECQM", "CC"]
    qualityGap.measureGapDesc.forEach( item => {
      if(excludeSourceList.includes(item.source))
      {
        item.desc = item.measureCd + ": " + item.reportLevel1Desc
      }
      else
      {
        item.desc = item.source + item.measureCd + ': ' + item.reportLevel1Desc
      }
      item.formLink = this.getFormLink(item, qualityGap.patientId);
    });
    return qualityGap;
  }

  getFormList()
  {
    this.dashboardService.GetFormUrls(this.siteId.toString()).subscribe((res) =>
    {
      if (res)
      {
        this.formList = res;
        // After getting form URLs, prefetch all form statuses
        this.prefetchFormStatuses();
      }
    });
  }

  // Prefetch all form statuses to avoid multiple API calls
  prefetchFormStatuses() {
    // Don't clear the cache if we already have data from sessionStorage
    if (Object.keys(this.formStatusCache).length === 0) {
      // Process each form URL to get its workflow ID and rule ID
      for (const form of this.formList) {
        if (form.ordinal) {
          // Extract workflowId from the replacement URL (before the query string)
          const url = form.replacement.replace(/{patientId}/gi, "0"); // Use a dummy patient ID
          const workflowId = url.split('?')[0];
          const ruleId = form.ordinal.toString();

          // Create a cache key using workflowId and ruleId
          const cacheKey = `${workflowId}_${ruleId}`;

          // Only fetch if we haven't already fetched this combination
          if (!this.formStatusCache[cacheKey]) {
            this.dashboardService.GetFormStatuses(this.siteId.toString(), workflowId, ruleId).subscribe((statuses) => {
              if (statuses) {
                this.formStatusCache[cacheKey] = statuses;
                // Save the cache after each successful fetch
                this.saveFormStatusCache();
              }
            });
          }
        }
      }
    }
  }

  checkIfDescription(patientQualityGap: QualityGapModel): boolean
  {
    if(patientQualityGap)
    {
      if(patientQualityGap.measureGap && patientQualityGap.measureGapDesc)
      {
        return true
      }
    }
    return false
  }

  getFormLink(measureGapDesc: MeasureGapDescModel, patientId: number)
  {
    let formLink: FormLinkModel = {} as FormLinkModel;
    let lookupValue = this.formList.find(x => x.value === measureGapDesc.measureCd);

    if (!lookupValue) {
      // Mips separates the measure and the category so we can handle this pattern
      // by appending them together and checking again if nothing was found first.
      const combinedLookup = measureGapDesc.source + measureGapDesc.measureCd;
      lookupValue = this.formList.find(x => x.value === combinedLookup);
    }

    if (lookupValue)
    {
        const url = lookupValue.replacement.replace(/{patientId}/gi, patientId.toString());
        const workflowId = url.split('?')[0];
        const ruleId = lookupValue.ordinal.toString();

        // Set the base URL
        formLink.formURL = url;

        // Create a cache key using workflowId and ruleId
        const cacheKey = `${workflowId}_${ruleId}`;

        // Check if we have cached form statuses for this workflow and rule
        if (this.formStatusCache[cacheKey]) {
          // Use the cached data instead of making a new API call
          const cachedStatuses = this.formStatusCache[cacheKey];

          // Process the cached statuses
          for (const formStatus of cachedStatuses) {
            if (formStatus.demographicId === patientId.toString()) {
              // Convert string dates to Date objects for comparison
              const statusDate = new Date(formStatus.endExecution);
              if (!formLink.submittedDate || statusDate > formLink.submittedDate) {
                formLink.submittedDate = statusDate;
                formLink.formURL = `${url}&WorkFlowId=${formStatus.workFlowProcessId}&IsHistory=1`;
              }
            }
          }
        } else {
          // If not cached yet, make the API call and cache the result
          this.dashboardService.GetFormStatuses(this.siteId.toString(), workflowId, ruleId).subscribe((statuses) => {
            if (statuses) {
              // Cache the result for future use
              this.formStatusCache[cacheKey] = statuses;
              // Save the cache to sessionStorage
              this.saveFormStatusCache();

              // Process the statuses
              for (const formStatus of statuses) {
                if (formStatus.demographicId === patientId.toString()) {
                  // Convert string dates to Date objects for comparison
                const statusDate = new Date(formStatus.endExecution);
                if (!formLink.submittedDate || statusDate > formLink.submittedDate) {
                    formLink.submittedDate = statusDate;
                    formLink.formURL = `${url}&WorkFlowId=${formStatus.workFlowProcessId}&IsHistory=1`;
                  }
                }
              }
            }
          });
        }

        return formLink;
    }
    return undefined;
  }

  getFlowSheetImage(hiv: number, hcv: number) {
    if (hiv >= 1 && hcv === 0) return '../../../../assets/images/hiv.png';
    if (hiv === 0 && hcv >= 1) return '../../../../assets/images/hcv.png';
    if (hiv >= 1 && hcv >= 1) return '../../../../assets/images/hivhcv.png';
    return ''; // Default case (hiv === 0 && hcv === 0)
  }

  setQualityGroup() {
    this.showQualityGroup = !this.showQualityGroup;
    this.lblQualityGroup=this.showQualityGroup?'Hide Actionable Gaps':'Show Actionable Gaps';
    this.ref.detectChanges();
  }

  showRecordsQualityGapReport(aptRowData: AppointmentInfo){
    let demographicId = `[${aptRowData.patientId}]`
    demographicId = encodeURIComponent(demographicId);
    let url = `/Dashboard/Report/${this.siteId}/Patient_Quality_Measures_DTL?DEMOGRAPHICS_ID=${demographicId}`
    this.router.navigateByUrl(url);

    /*Popup could be used however bold report component has an issue on load if its not destroyed each time.
    let dialogRef = this.dialog.open(ReportComponent, {
      data:  url,
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '95%',
      width: '95%'
    });
    */
  }

  showFlowSheetHIVReport(aptRowData: AppointmentInfo){
    let demographicId = `[${aptRowData.patientId}]`
    demographicId = encodeURIComponent(demographicId);
    let url = `/Dashboard/Report/${this.siteId}/PatientFlowsheet?DEMOGRAPHICS_ID=${demographicId}`
    this.router.navigateByUrl(url);
  }


  // Function is used to handle date picket value and change the dates for yesterday and tomorrow button date
  updateDates() {
    this.selectedDate = this.datepipe
      .transform(this.currentSelectedDate, 'MMMM d, y')
      ?.toString();

    this.dashboardService.SetAppointmentDateSelect(this.selectedDate);

    let yDate = new Date(this.selectedDate);
    let tDate = new Date(this.selectedDate);

    yDate.setDate(yDate.getDate() - 1);
    tDate.setDate(tDate.getDate() + 1);

    this.yesterdayDate = this.datepipe
      .transform(yDate, 'MMMM d, y')
      ?.toString();
    this.tomorrowDate = this.datepipe.transform(tDate, 'MMMM d, y')?.toString();
    this.loadProvidersData();
  }

  loadYesterdaysData() {
    let yDate = new Date(this.yesterdayDate ?? new Date());
    let tDate = new Date(this.yesterdayDate ?? new Date());
    let cDate = new Date(this.yesterdayDate ?? new Date());

    yDate.setDate(yDate.getDate() - 1);
    cDate.setDate(tDate.getDate());
    tDate.setDate(cDate.getDate() + 1);

    this.selectedDate =this.datepipe
      .transform(new Date(cDate), 'MMMM d, y')
      ?.toString();
    this.yesterdayDate = this.datepipe
      .transform(yDate, 'MMMM d, y')
      ?.toString();
    this.tomorrowDate = this.datepipe.transform(tDate, 'MMMM d, y')?.toString();
    this.currentSelectedDate = cDate;
    this.loadProvidersData();
  }

  loadTomorrowsData() {
    let yDate = new Date(this.tomorrowDate ?? new Date());
    let tDate = new Date(this.tomorrowDate ?? new Date());
    let cDate = new Date(this.tomorrowDate ?? new Date());

    yDate.setDate(yDate.getDate() - 1);
    cDate.setDate(tDate.getDate());
    tDate.setDate(cDate.getDate() + 1);

    this.selectedDate =this.datepipe
      .transform(new Date(cDate), 'MMMM d, y')
      ?.toString() ;
    this.yesterdayDate = this.datepipe
      .transform(yDate, 'MMMM d, y')
      ?.toString();
    this.tomorrowDate = this.datepipe.transform(tDate, 'MMMM d, y')?.toString();
    this.currentSelectedDate = cDate;
    this.dashboardService.SetAppointmentDateSelect(this.selectedDate);
    this.loadProvidersData();
  }

  printSchedule() {
    const printContents = this.printSSInnerTable.nativeElement.innerHTML;

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.open();
      printWindow.document.write(`
        <html>
        <head>
          <style>
            @page {
              size: landscape;
              margin: 20px;
            }
            @media print {
              body {
                margin: 0;
                padding: 0;
                font-family: 'Museo500', Arial, sans-serif;
                font-size: 10pt;
              }
              .SStablewrap {
                width: 100%;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                table-layout: fixed;
              }
              th, td {
                border: 1px solid #d3d5da;
                padding: 8px;
                vertical-align: top;
                text-align: left;
                word-wrap: break-word;
                font-family: 'Museo500', Arial, sans-serif;
              }
              th {
                font-weight: bold;
                background-color: #f9f9fb;
                color: #93959a;
              }
              .tableHeaderRow th {
                font-size: 11pt;
                padding: 10px 8px;
              }
              .DeviceGridSSinner_table {
                background-color: #fff;
                height: auto;
                display: table-row-group;
              }
              tr {
                page-break-inside: avoid;
              }
              /* Status indicators for rows */
              .GridRowColorCancelled {
                border-left: 6px solid #cc293d !important;
                -webkit-print-color-adjust: exact !important;
                color: #cc293d;
                font-weight: bold;
              }
              .GridRowColornoShow {
                border-left: 6px solid #f2cb1d !important;
                -webkit-print-color-adjust: exact !important;
                color: #f2cb1d;
                font-weight: bold;
              }
              .GridRowColorCompleted {
                border-left: 6px solid #009ccc !important;
                -webkit-print-color-adjust: exact !important;
                color: #009ccc;
                font-weight: bold;
              }
              td.appointment-time {
                width: 80px;
              }
              td.patient-name {
                width: 120px;
              }
              td.quality-gap {
                width: 140px;
              }
              td.flow-sheets {
                width: 100px;
                text-align: center;
              }
              td.patient-summary {
                width: 300px;
              }
            }
          </style>
        </head>
        <body onload="window.print(); window.close();">
          ${printContents}
        </body>
        </html>
      `);
      printWindow.document.close();
    }
  }


  // Load form status cache from sessionStorage
  loadFormStatusCache() {
    const cachedData = sessionStorage.getItem('formStatusCache');
    if (cachedData) {
      try {
        this.formStatusCache = JSON.parse(cachedData);
      } catch (error) {
        console.error('Error parsing form status cache from sessionStorage:', error);
        this.formStatusCache = {};
      }
    }
  }

  // Save form status cache to sessionStorage
  saveFormStatusCache() {
    try {
      sessionStorage.setItem('formStatusCache', JSON.stringify(this.formStatusCache));
    } catch (error) {
      console.error('Error saving form status cache to sessionStorage:', error);
    }
  }

  ngOnDestroy(): void {
    // Save the form status cache before destroying the component
    this.saveFormStatusCache();

    // Complete the destroyed$ subject to clean up subscriptions
    this.pageSubscriptions.unsubscribe();
  }

  comboFilter(searchString : string) {

    const lowerSearchString = searchString.toLowerCase();

    if (searchString !== 'All')
    {
       this.FilteredAppointmentProviders = this.AppointmentProviders.filter(x=>x.fulL_NM.toLowerCase().includes(lowerSearchString));
       this.FilteredAssociatesProviders = this.AssociatesProvider.filter(x=>x.fulL_NM.toLowerCase().includes(lowerSearchString));
    }
    else
    {
       this.FilteredAppointmentProviders = this.AppointmentProviders.map(value=>value);
       this.FilteredAssociatesProviders = this.AssociatesProvider.map(value=> value);
    }
  }
}
