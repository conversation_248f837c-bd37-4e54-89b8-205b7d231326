// environment.service.ts
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  private showMenuItem: boolean = environment.ShowSupportLink;
  public showMenu: Subject<boolean> = new Subject<boolean>();

  constructor() {}

  toggleSupportMenuItem() {
    this.showMenuItem = !this.showMenuItem;
    this.showMenu.next(this.showMenuItem);
    return this.showMenuItem;
  }

  ShowSupportMenuItem(): Observable<boolean> {
    return this.showMenu.asObservable();
  }

}