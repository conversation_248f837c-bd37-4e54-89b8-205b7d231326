import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { JsonSchemaService } from '../../../../services/json-schema.service';

@Component({
  selector: 'app-json-node',
  templateUrl: './json-node.component.html',
  styleUrls: ['./json-node.component.scss']
})
export class JsonNodeComponent implements OnInit, OnChanges {
  @Input() node: any;
  @Input() path = '';
  @Input() level = 0;
  @Input() nodeKey = '';
  @Input() isExpanded = false;
  @Output() nodeChange = new EventEmitter<{ path: string, value: any }>();

  isNodeExpanded = false;
  nodeType = '';
  nodeKeys: string[] = [];
  nodeValue: any;

  // Form controls
  stringControl = new FormControl('');
  numberControl = new FormControl(0);
  booleanControl = new FormControl(false);
  nullControl = new FormControl('');

  // Track expanded state of array items
  expandedArrayItems: { [index: number]: boolean } = {};

  // For array items preview
  arrayItemPreviews: { [index: number]: string } = {};

  constructor(public jsonSchemaService: JsonSchemaService) {}

  ngOnInit(): void {
    console.log(`JsonNodeComponent.ngOnInit: path=${this.path}, nodeKey=${this.nodeKey}, node=`, this.node);
    this.initializeNode();
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log(`JsonNodeComponent.ngOnChanges: path=${this.path}, nodeKey=${this.nodeKey}, changes=`, changes);

    if (changes['node']) {
      console.log(`JsonNodeComponent.ngOnChanges: node changed, new value=`, this.node);
      this.initializeNode();
    }
  }

  /**
   * Initialize the node data
   */
  private initializeNode(): void {
    this.isNodeExpanded = this.isExpanded || this.level === 0;
    this.nodeType = this.jsonSchemaService.getValueType(this.node);

    // Initialize node keys
    this.updateNodeKeys();

    // Set the node value
    this.nodeValue = this.node;
    console.log(`JsonNodeComponent.initializeNode: nodeValue=`, this.nodeValue);

    // Update form controls based on node type
    if (this.nodeType === 'string') {
      this.stringControl.setValue(this.nodeValue || '', { emitEvent: false });
    } else if (this.nodeType === 'number') {
      this.numberControl.setValue(this.nodeValue || 0, { emitEvent: false });
    } else if (this.nodeType === 'boolean') {
      this.booleanControl.setValue(!!this.nodeValue, { emitEvent: false });
    } else if (this.nodeType === 'null') {
      this.nullControl.setValue('', { emitEvent: false });
    }

    // If this is an array, generate previews for items
    if (this.jsonSchemaService.isArray(this.node)) {
      this.generateArrayItemPreviews();

      // Only expand the first item by default
      if (this.node.length > 0) {
        this.expandedArrayItems[0] = true;
      }
    }
  }

  /**
   * Update the node keys based on the node type
   */
  private updateNodeKeys(): void {
    if (this.jsonSchemaService.isObject(this.node)) {
      this.nodeKeys = Object.keys(this.node);
    } else if (this.jsonSchemaService.isArray(this.node)) {
      this.nodeKeys = Array.from({ length: this.node.length }, (_, i) => i.toString());
    }
  }

  /**
   * Generate preview text for array items
   */
  private generateArrayItemPreviews(): void {
    if (!this.jsonSchemaService.isArray(this.node)) return;

    for (let i = 0; i < this.node.length; i++) {
      const item = this.node[i];
      this.arrayItemPreviews[i] = this.generateItemPreview(item);
    }
  }

  /**
   * Generate a preview string for an item
   */
  private generateItemPreview(item: any): string {
    if (item === null || item === undefined) {
      return 'null';
    }

    if (typeof item === 'object') {
      // For objects, try to find a name, id, or label property
      if (item.name) {
        return String(item.name);
      }
      if (item.id) {
        return `ID: ${item.id}`;
      }
      if (item.FieldName) {
        return String(item.FieldName);
      }
      if (item.Label) {
        return String(item.Label);
      }

      // If it's a Fields array item, try to create a more descriptive preview
      if (this.path.endsWith('Fields')) {
        const previewParts: string[] = [];

        if (item.FieldName) {
          previewParts.push(`Name: ${item.FieldName}`);
        }
        if (item.FieldType) {
          previewParts.push(`Type: ${item.FieldType}`);
        }
        if (item.Label) {
          previewParts.push(`Label: ${item.Label}`);
        }

        if (previewParts.length > 0) {
          return previewParts.join(', ');
        }
      }

      // Otherwise, show the object type and number of properties
      const keys = Object.keys(item);
      return `Object (${keys.length} properties)`;
    }

    // For primitive values, convert to string
    return String(item);
  }

  /**
   * Toggle expansion state of the node
   */
  toggleExpansion(): void {
    this.isNodeExpanded = !this.isNodeExpanded;
  }

  /**
   * Toggle expansion state of an array item
   */
  toggleArrayItem(index: number): void {
    this.expandedArrayItems[index] = !this.expandedArrayItems[index];
  }

  /**
   * Check if an array item is expanded
   */
  isArrayItemExpanded(index: number): boolean {
    return !!this.expandedArrayItems[index];
  }

  /**
   * Get the full path to a child node
   * @param key The child key
   * @returns The full path
   */
  getChildPath(key: string): string {
    return this.path ? `${this.path}.${key}` : key;
  }

  /**
   * Handle changes to primitive values
   * @param value The new value
   */
  onPrimitiveValueChange(value: any): void {
    // Convert value to the correct type
    let typedValue: any = value;

    if (this.nodeType === 'number') {
      typedValue = Number(value);
    } else if (this.nodeType === 'boolean') {
      typedValue = value === 'true' || value === true;
    } else if (this.nodeType === 'null') {
      // Convert null to string if value is provided
      typedValue = value ? String(value) : null;
    }

    this.nodeValue = typedValue;
    this.emitChange();
  }

  /**
   * Handle changes to child nodes
   * @param event The change event from a child node
   */
  onChildNodeChange(event: { path: string, value: any }): void {
    // Update the node value at the specified path
    this.updateNodeAtPath(this.node, event.path.replace(`${this.path}.`, ''), event.value);
    this.emitChange();
  }

  /**
   * Update a node at a specific path
   * @param obj The object to update
   * @param path The path to the property
   * @param value The new value
   */
  private updateNodeAtPath(obj: any, path: string, value: any): void {
    const parts = path.split('.');
    const key = parts[0];

    if (parts.length === 1) {
      // Direct property
      if (this.jsonSchemaService.isArray(obj)) {
        obj[parseInt(key)] = value;
      } else {
        obj[key] = value;
      }
    } else {
      // Nested property
      const remainingPath = parts.slice(1).join('.');
      if (this.jsonSchemaService.isArray(obj)) {
        this.updateNodeAtPath(obj[parseInt(key)], remainingPath, value);
      } else {
        this.updateNodeAtPath(obj[key], remainingPath, value);
      }
    }
  }

  /**
   * Emit a change event
   */
  private emitChange(): void {
    this.nodeChange.emit({ path: this.path, value: this.nodeValue });
  }

  /**
   * Add a new item to an array
   */
  addArrayItem(): void {
    if (this.jsonSchemaService.isArray(this.node)) {
      // Determine the type of the first item or default to empty object
      let newItem: any = {};

      if (this.node.length > 0) {
        const firstItem = this.node[0];
        if (typeof firstItem === 'string') {
          newItem = '';
        } else if (typeof firstItem === 'number') {
          newItem = 0;
        } else if (typeof firstItem === 'boolean') {
          newItem = false;
        } else if (this.jsonSchemaService.isArray(firstItem)) {
          newItem = [];
        }
      }

      this.node.push(newItem);
      this.nodeKeys = Array.from({ length: this.node.length }, (_, i) => i.toString());
      this.emitChange();
    }
  }

  /**
   * Remove an item from an array
   * @param index The index to remove
   */
  removeArrayItem(index: number): void {
    if (this.jsonSchemaService.isArray(this.node) && index >= 0 && index < this.node.length) {
      this.node.splice(index, 1);
      this.nodeKeys = Array.from({ length: this.node.length }, (_, i) => i.toString());
      this.emitChange();
    }
  }

  /**
   * Convert a null value to an empty string
   */
  convertNullToEmptyString(): void {
    if (this.nodeType === 'null') {
      this.nodeValue = '';
      this.nodeType = 'string';
      this.stringControl.setValue('', { emitEvent: false });
      this.emitChange();
    }
  }
}
