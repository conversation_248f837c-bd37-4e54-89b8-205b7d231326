<div class="OutreachDeatilDiv" style="height: 83vh;">
    <div class="OutReachList" style="width: 44%;">
        <div style="float: left; width: 100%; font-size: 16px;">
            <label id="lblScheduleListCount" style="padding-top:10px;">
                Awaiting Scheduling 
                [{{this.administrativeActionItems.scheduleListCount}}
                Total Patient<span *ngIf="this.administrativeActionItems.scheduleListCount > 1">s</span>]
            </label>
        </div>
        <div *ngIf="this.administrativeActionItems.scheduleListCount > 0">
            <div class="OutReachDetailInnerDiv" style="float: left; width: 100%; padding-top:10px; height: 500px; ">
                <div style="float: left; width: 100%;">                        
                    <div class="administrativeActionDiv" *ngFor="let item of awaitingSchedulingList;">
                        <div>
                            <a style="cursor: pointer" [innerHTML]="retentionInCare.safeHtml(item.patientNameRiskDate)"></a>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.annotationDescPrName)"></label>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.homePhone)"></label>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.mobilePhone)"></label>
                        </div>
                        <div class="hiddenDiv">
                            <label [innerHTML]="item.annotateId"></label>
                            <label [innerHTML]="item.annotateOptionId"></label>
                            <label [innerHTML]="item.consecutive"></label>
                            <label [innerHTML]="item.demographicsId"></label>
                            <label [innerHTML]="item.doNotCallFlg"></label>
                            <label [innerHTML]="item.locationId"></label>
                        </div>
                        <div class="linkDiv">
                            <a class="link-a" [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', item.demographicsId]">
                                View Report
                            </a> 
                            <a style="cursor: pointer" (click)="openDialog();">Record Result</a>
                        </div>                            
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="ScheduleListDiv" class="OutReachList" style="width: 44%;">
        <div style="float: left; width: 100%; font-size: 16px;">
            <label id="lblScheduleListCount" style="padding-top:10px;">
                Wrong Number/No Service 
                [{{this.administrativeActionItems.wrongNumberListCount}} 
                Total Patient<span *ngIf="this.administrativeActionItems.wrongNumberListCount > 1">s</span>]
            </label>
        </div>        
        <div *ngIf="this.administrativeActionItems.wrongNumberListCount > 0">    
            <div class="OutReachDetailInnerDiv" style="float: left; padding-top:10px; height: 500px; width: 100%; ">
                <div style="float: left; width: 100%;">                        
                    <div class="administrativeActionDiv" *ngFor="let item of awaitingWrongNumberList;">
                        <div>
                            <a style="cursor: pointer" [innerHTML]="retentionInCare.safeHtml(item.patientNameRiskDate)"></a>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.annotationDescPrName)"></label>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.homePhone)"></label>
                        </div>
                        <div>
                            <label [innerHTML]="retentionInCare.safeHtml(item.mobilePhone)"></label>
                        </div>
                        <div class="hiddenDiv">
                            <label [innerHTML]="item.annotateId"></label>
                            <label [innerHTML]="item.annotateOptionId"></label>
                            <label [innerHTML]="item.consecutive"></label>
                            <label [innerHTML]="item.demographicsId"></label>
                            <label [innerHTML]="item.doNotCallFlg"></label>
                            <label [innerHTML]="item.locationId"></label>
                        </div>
                        <div class="linkDiv">
                            <a class="link-a" [routerLink]="['/OutreachAndRetention/AnnotationStatusReport', 'PatientDetails', item.demographicsId]">
                                View Report
                            </a>                        
                            <a style="cursor: pointer" (click)="openDialog();">Record Result</a>
                        </div>                            
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>