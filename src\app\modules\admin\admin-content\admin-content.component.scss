.admin-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-header {
  margin-bottom: 30px;

  h1 {
    margin-bottom: 10px;
    color: #333;
    font-size: 28px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

.admin-content {
  flex: 1;
}

.admin-section {
  margin-bottom: 30px;

  h2 {
    margin-bottom: 20px;
    color: #333;
    font-size: 22px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
}

.admin-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.admin-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .card-icon {
    margin-right: 15px;

    mat-icon {
      font-size: 32px;
      height: 32px;
      width: 32px;
      color: #3f51b5;
    }
  }

  .card-content {
    flex: 1;

    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #333;
      font-size: 18px;
    }

    p {
      color: #666;
      margin-bottom: 0;
    }
  }
}
