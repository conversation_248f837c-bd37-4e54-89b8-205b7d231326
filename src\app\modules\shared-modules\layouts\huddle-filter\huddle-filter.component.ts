import { Component, EventEmitter, Input, input, Output, SimpleChanges, ViewChild  } from '@angular/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import { HuddleDetail } from '../models/huddle-detail.model';
import { HuddleFilterService } from './Services/huddle-filter.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { HuddleFilter } from '../models/huddle-filter.model';

@Component({
  selector: 'epividian-huddle-filter',
  templateUrl: './huddle-filter.component.html',
  styleUrl: './huddle-filter.component.scss'
})
export class HuddleFilterComponent {

  @Output() filterObjectSelected: EventEmitter<HuddleFilter> = new EventEmitter<HuddleFilter>();
  @Input() huddleAppointments: HuddleDetail[] = [];

  public selectedFilter: string = "All Appointments";
  public selectedIndex: number = 0;
  public filters: HuddleFilter[] = [];
  public siteId: string = "";
  public allAppointments: HuddleDetail[] = []; // Store complete dataset for count calculations

  tooltipMessage = 'Additional information goes here.';
  isTooltipDisabled = true;

  constructor(public filterService: HuddleFilterService, private userContext: UserContext) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    this.getFilters(this.siteId);
    this.emitSelectedFilter();
  }

  getFilters(siteId: string){
    this.filterService.GetFilters(siteId).subscribe( res => {
        this.filters = res;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['huddleAppointments']) {
      // Store the complete dataset when we get "All Appointments" data
      if (this.selectedIndex === 0 || this.allAppointments.length === 0) {
        this.allAppointments = [...this.huddleAppointments];
      }

      // Only reset to "All Appointments" if this is the first time we're getting data
      // or if the appointments array was previously empty
      if (!changes['huddleAppointments'].previousValue ||
          changes['huddleAppointments'].previousValue.length === 0) {
        this.selectFilter(0);
      }
      // Otherwise, keep the current filter selection but update the counts
    }
  }

  // Get number of unique patients with appointments that match that filter
  getAppointmentsWithFlagSet(flagPosition: number): number {
    // Always use the complete dataset for count calculations, not the filtered data
    const dataToUse = this.allAppointments.length > 0 ? this.allAppointments : this.huddleAppointments;
    const numberOfUniquePatients = new Set(
      dataToUse
        .filter(appointment => this.isFlagSet(appointment, flagPosition))
        .map(appointment => appointment.demographicsId)
      );
    return numberOfUniquePatients.size;
  }

  isFlagSet(huddleDetail: HuddleDetail, flagPosition: number): boolean {
    // Ensure that the binary string exists and is long enough to check the flag position
    if (huddleDetail.huddleFlags && huddleDetail.huddleFlags.length > flagPosition) {
      return huddleDetail.huddleFlags[flagPosition] === '1';
    }
    return false;
  }

  selectFilter(selectedIndex: number) {
    if (this.filters.length > selectedIndex){
      this.selectedIndex = selectedIndex;
      this.selectedFilter = this.filters[selectedIndex].name;
      this.emitSelectedFilter();
    }
  }

  emitSelectedFilter() {
    if (this.filters.length > this.selectedIndex) {
      this.filterObjectSelected.emit(this.filters[this.selectedIndex]);
    }
  }
}


