<div class="cover-container container-fluid">
  <!-- BEGIN: Chorus login header section -->
  <div class="header-row-1 row g-0 d-flex justify-content-xxl-start justify-content-xl-start justify-content-lg-start justify-content-md-start justify-content-sm-center justify-content-center">
    <div class="logo-container col-lg-3 col-md-3 col-sm-12">
    </div>
</div>
<!-- END: Chorus login header section -->
  <div class="eForgotPasswordContent">
          <label class="eForgotPasswordMessage">{{this.errorMsg}}</label>
          <div class="row g-0 mx-2">
            <div class="col-lg-9 col-md-9 col-sm-12">
                <div class="input-group">
            <input id="email" autofocus [(ngModel)]="emailValue" type="text" tabindex="1" #email class="form-control" name="email" placeholder="<EMAIL>">
            <button class="btn btn-light" tabindex="3" (click)="onForgotPassword(email.value, true)"></button>
                </div>
              </div>
              <div class="row g-0 mx-2">
                <div class="col-lg-9 col-md-9 col-sm-12 d-flex justify-content-end">
              <a class="signIn" tabindex="4" [routerLink]="['/Auth/Login']">return to sign-in</a>
              </div>
              </div>
          </div>
  </div>
</div>
