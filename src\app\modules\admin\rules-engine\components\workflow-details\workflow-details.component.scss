.workflow-details-container {
  padding: 12px;
  font-size: 0.85em;
  display: flex;
  flex-direction: column;
  height: 500px;
  box-sizing: border-box;

  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;

    p {
      margin-top: 12px;
      color: rgba(0, 0, 0, 0.6);
    }
  }

  .workflow-details-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .workflow-rules-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      flex: 1;
      overflow: hidden;

      @media (min-width: 768px) {
        flex-direction: row;
      }

      .assigned-rules, .available-rules {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        h3 {
          margin-bottom: 6px;
          color: #3f51b5;
          font-weight: 500;
          font-size: 1.1em;
        }

        .help-text {
          margin-bottom: 12px;
          color: rgba(0, 0, 0, 0.6);
          font-size: 0.9em;
        }
      }

      .rule-list {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        min-height: 50px;
        background: white;
        overflow-y: auto;
        flex: 1;

        .rule-box {
          padding: 12px;
          border-bottom: 1px solid #f0f0f0;
          background: white;
          cursor: move;

          &:last-child {
            border-bottom: none;
          }

          .rule-content {
            display: flex;
            align-items: center;

            .rule-order {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              background-color: #3f51b5;
              color: white;
              font-size: 11px;
              margin-right: 10px;
            }

            .rule-info {
              flex: 1;
              display: flex;
              flex-direction: column;

              .rule-name {
                font-weight: 500;
                font-size: 0.95em;
              }

              .rule-type {
                font-size: 0.8em;
                color: rgba(0, 0, 0, 0.6);
              }

              .rule-dependencies {
                margin-top: 6px;
                font-size: 0.8em;

                .dependencies-label {
                  color: rgba(0, 0, 0, 0.6);
                  margin-right: 4px;
                }

                .dependency-item {
                  display: inline-block;
                  background-color: #e3f2fd;
                  padding: 1px 5px;
                  border-radius: 3px;
                  margin-right: 4px;
                  margin-bottom: 3px;
                }
              }
            }

            .rule-actions {
              display: flex;
              gap: 2px;

              button {
                transform: scale(0.85);
              }
            }
          }

          &:hover {
            background-color: #f5f5f5;
          }
        }

        .empty-list {
          padding: 12px;
          text-align: center;
          color: rgba(0, 0, 0, 0.6);
          font-size: 0.9em;
        }
      }

      .search-container {
        margin-bottom: 12px;

        .search-field {
          width: 100%;
          font-size: 0.9em;

          ::ng-deep .mat-form-field-wrapper {
            padding-bottom: 0.8em;
          }
        }
      }
    }
  }
}

.dependency-editor {
  font-size: 0.85em;

  .dependency-search {
    margin-bottom: 12px;

    .search-field {
      width: 100%;
      font-size: 0.9em;

      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0.8em;
      }
    }
  }

  .dependency-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;

    .dependency-item {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .disabled-dependency {
        padding: 4px 0 4px 24px;
        color: rgba(0, 0, 0, 0.38);
        display: flex;
        align-items: center;

        .disabled-rule-name {
          font-weight: 500;
          margin-right: 8px;
        }

        .disabled-reason {
          font-size: 0.9em;
          font-style: italic;
        }
      }
    }

    .empty-dependencies {
      padding: 12px;
      text-align: center;
      color: rgba(0, 0, 0, 0.6);
      font-style: italic;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.rule-list.cdk-drop-list-dragging .rule-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
