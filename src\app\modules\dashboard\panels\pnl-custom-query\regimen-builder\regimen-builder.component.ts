import { FlatTreeControl } from '@angular/cdk/tree';
import { Component, OnInit } from '@angular/core';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { PanelService } from '../../PanelService';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Location } from '@angular/common';
import {
  ICreateRegimen,
  MedicationGroupOptions,
  IMedicationsTreeData,
  IMedicationsTreeFlatNode,
  IRegimenPanel,
  RegimentOptions,
  ChainMethodTypesList,
  IPatientsRegimen
} from './regimen-builder.models';
import * as _ from 'lodash';

@Component({
  selector: 'app-regimen-builder',
  templateUrl: './regimen-builder.component.html',
  styleUrls: ['./regimen-builder.component.scss']
})
export class RegimenBuilderComponent implements OnInit {

  visible = true;
  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];


  // This variable is used to show the medications dropdown list
  selectedMedication : string = '';
  draggedElementList: any = [];
  createdRegimensList: any = [];
  createRegimenList: ICreateRegimen;
  txtcurrentLookback: string = "30";

  regimentOptions = RegimentOptions;
  medicationGroupOptions = MedicationGroupOptions;
  chainMethodTypesList = ChainMethodTypesList;
  medicationsGroupsList: any = [];
  availableMedicationsLists: any = [];

  public TREE_DATA: IMedicationsTreeData[] = [];

  regimenPanelsList: any = [];
  tmpTest: any;

  constructor(
    private panelService:  PanelService,
    private location: Location
  ) {
    this.createRegimenList = new ICreateRegimen();
  }

  ngOnInit(): void {
    let availableMedications;
    // Fetching data to show medications groups and available medications under Regimen Builder
    this.panelService.GetMedicationsAndGroups().subscribe(result => {
      if(result){
        this.medicationsGroupsList = result.medicationGroup;
        PanelService.medicationGroups = result.medicationGroup;
        availableMedications = result.availableMedications;
        try {
          // Generating tree structured data to show available medication
          // Ensure therapeuticClassDesc is properly handled for special characters
          this.TREE_DATA = _.map(_.groupBy(availableMedications, (item) => {
            return item.therapeuticClassDesc || 'Other';
          }), (children, brndNM) => ({ children, brndNM }));
          this.dataSource.data = this.TREE_DATA;
        } catch (error) {
          console.error('Error processing medication data:', error);
          // Fallback to simple grouping if lodash fails
          this.TREE_DATA = [];
          this.dataSource.data = this.TREE_DATA;
        }
      }
    });

    if (PanelService.createdRegimenList.length > 0) {
      this.regimenPanelsList = PanelService.createdRegimenList;
    }
  }

  private _transformer = (node:IMedicationsTreeData, level:number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      brndNM: node.brndNM,
      level: level,
      groupId:node.groupId,
      brndId: node.brndId,
      genericNM: node.genericNM
      // groupNM: node.groupNM
    };
  };

  // For checking the levels of tree and its expansion
  treeControl = new FlatTreeControl<IMedicationsTreeFlatNode>(
    node=>node.level,
    node=>node.expandable
  );

  // For the flattening of tree
  treeFlattner= new MatTreeFlattener(
    this._transformer,
    node=>node.level,
    node=>node.expandable,
    node=>node.children
  );

  // For the data source
  dataSource = new MatTreeFlatDataSource(
    this.treeControl,
    this.treeFlattner
  )

  // For the flattening of tree
  hasChild =(_: number, node: IMedicationsTreeFlatNode) => node.expandable;

  // Filter recursively on a text string using property object value
  filterRecursive(filterText: string, array: any[], properties: string[]) {
    let filteredData;
    //Make a copy of the data so we don't mutate the original tree
    function copy(o: any) {
      return Object.assign({}, o);
    }
    if (filterText) {
      try {
        // Normalize Unicode characters and escape special regex characters
        const normalizedFilterText = filterText.toLowerCase().normalize('NFD');
        const escapedFilterText = normalizedFilterText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Copy obj so we don't mutate it and filter
        filteredData = array.map(copy).filter(function x(y) {
          for (const property of properties) {
            if (y[property]) {
              // Normalize the property value for comparison
              const normalizedProperty = y[property].toLowerCase().normalize('NFD');
              if (normalizedProperty.includes(escapedFilterText)) {
                return true;
              }
            }
          }
          // If child match
          if (y.children) {
            return (y.children = y.children.map(copy).filter(x)).length;
          }
        });
      } catch (error) {
        console.error('Error in filterRecursive with text:', filterText, error);
        // Fallback to simple string matching without regex
        filteredData = array.map(copy).filter(function x(y) {
          for (const property of properties) {
            if (y[property] && y[property].toLowerCase().indexOf(filterText.toLowerCase()) !== -1) {
              return true;
            }
          }
          if (y.children) {
            return (y.children = y.children.map(copy).filter(x)).length;
          }
        });
      }
    } else {
      // If filterText is empty then it will return complete list
      filteredData = array;
    }
    return filteredData;
  }

  // Pass input string to recursive function and return data
  filterTree(filterText: string) {
    // Use filter input text, return filtered TREE_DATA, use the 'name' object value
    if(this.TREE_DATA) {
      this.dataSource.data = this.filterRecursive(filterText, this.TREE_DATA, ['brndNM', 'genericNM']);
    }
  }

  // Filter string from mat input filter
  onAvailableMedicationSearch(filterText: string) {
    this.filterTree(filterText);
    // Show / hide based on state of filter string
    if (filterText) {
      this.treeControl.expandAll();
    } else {
      this.treeControl.collapseAll();
    }
  }

  // Function is used to search medications on select of Medications from dropdown
  onMedicationSelect() {
    if('All' === this.selectedMedication) {
      this.onAvailableMedicationSearch('');
    } else {
      this.onAvailableMedicationSearch(this.selectedMedication);
    }
  }

  getMedicationGroupName(code: number) {
    const index = this.medicationsGroupsList.find(i => i.code == code);
    if(index == undefined) {
      return 'Other';
    }
    return index.description;
  }

  // Handle the drop - here we rearrange the data based on the drop event, then rebuild the tree
  drop(event: any, droppedPosition: string, droppedIndex: number) {
    try {
      let newRegimenList: any;
      if(droppedPosition == 'existingRegimen') {
          let tempRegimenPanel = this.regimenPanelsList[droppedIndex];
          tempRegimenPanel.regimenList.forEach( element => {
              if(event.item.data.groupId == element.group.code) {
                  // Use strict equality comparison to handle special characters properly
                  let brandNameExist = element.medicationName.find(i => i.brndNM === event.item.data.brndNM);
                  if( !brandNameExist ) {
                      let newMedication = {
                          brndId: event.item.data.brndId,
                          brndNM: event.item.data.brndNM,
                          groupId: event.item.data.groupId,
                          groupNM: event.item.data.groupNM,
                          therapeuticClassDesc: event.item.data.brndNM
                      }
                      element.medicationName.push(newMedication);
                      newRegimenList = false;
                  }
              }
          });

          if(newRegimenList==undefined) {
            newRegimenList =
              {
                group: {
                  code: event.item.data.groupId,
                  description: event.item.data.groupNM
                },
                selectedOption: 1,
                medicationName: [
                  {
                      brndId: event.item.data.brndId,
                      brndNM: event.item.data.brndNM,
                      groupId: event.item.data.groupId,
                      groupNM: event.item.data.groupNM,
                      therapeuticClassDesc: event.item.data.brndNM
                  }
                ]
              }

              tempRegimenPanel.regimenList.push(newRegimenList);
          }

      } else if( droppedPosition == 'defaultDrop') {
        const regimenPanel: IRegimenPanel = {
          position: this.regimenPanelsList.length,
          regimenList: [
            {
              group: {
                code: event.item.data.groupId,
                description: event.item.data.groupNM
              },
              selectedOption: 2,
              medicationName: [
                {
                    brndId: event.item.data.brndId,
                    brndNM: event.item.data.brndNM,
                    groupId: event.item.data.groupId,
                    groupNM: event.item.data.groupNM,
                    genericNM: event.item.data.genericNM,
                    therapeuticClassDesc: event.item.data.brndNM
                }
              ]
            }
          ],
          levelId: Number(this.regimentOptions[0].value),
          chainMethod: this.chainMethodTypesList[0].value,
          currentlookBack: Number(this.txtcurrentLookback),
          idtoSend: String(this.regimenPanelsList.length+1),
          discontinuedLookback: 0,
          discontinuedRDBtnVal: 0,
        };
        this.regimenPanelsList.push(regimenPanel);
      } else {
        return;
      }
      return;
    } catch (error) {
      console.error('Error in drop operation:', error, 'Event data:', event.item.data);
    }
  }

  removeMedication(medicationName: string, medicationPanelId: number): void {
    try {
      let tempRegimenPanelList = this.regimenPanelsList[medicationPanelId];
      let isRegimenPanelsListEmpty = false;
      tempRegimenPanelList.regimenList.forEach(element => {
        // Use strict equality comparison to handle special characters properly
        const index = element.medicationName.findIndex(i => i.brndNM === medicationName);
        if (index >= 0) {
          element.medicationName.splice(index, 1);
        }
        if((element.medicationName).length == 0 ){
          isRegimenPanelsListEmpty = true;
        }
      })

      if(isRegimenPanelsListEmpty) {
        this.regimenPanelsList.splice(medicationPanelId, 1);
      }
    } catch (error) {
      console.error('Error removing medication:', error, 'Medication name:', medicationName);
    }
  }

  removeAllRegimen() {
    this.regimenPanelsList = [];
    PanelService.createdRegimenList = [];
  }


  includedPatientsRegimen(createdRegimenList) {
    PanelService.createdRegimenList = createdRegimenList.map(m => {m.currentlookBack = Number(this.txtcurrentLookback); return m; });
    // This is used to go back to custom query page
    this.location.back();
  }

  // Function used to go back to previous page - custom query page
  displaySelectionCriteria() {
    this.location.back();
  }

  updateMedGroup(event, regimenPanelId, groupId) {
      this.regimenPanelsList[regimenPanelId].regimenList[groupId].selectedOption = event.target.value;
  }

  updateRegimenOption(event, regimenPanelId) {
      this.regimenPanelsList[regimenPanelId].levelId = event.target.value;
      if (event.target.value == 3 && this.regimenPanelsList[regimenPanelId].discontinuedLookback == 0) {
        this.regimenPanelsList[regimenPanelId].discontinuedLookback=365;
      }
  }

  updateMethod(event, regimenPanelId)
  {
      this.regimenPanelsList[regimenPanelId].chainMethod = event.target.value;
  }

  updateTakenAndDiscontinued(event, regimenPanelId) {
        this.regimenPanelsList[regimenPanelId].discontinuedRDBtnVal = event.target.value;
  }

  updateTakenAndDiscontinuedText(event, regimenPanelId) {
    this.regimenPanelsList[regimenPanelId].discontinuedLookback = event.target.value;
  }

}
