.title{
  color: #0071BC; 
  font-size: 2rem;
  font-weight: normal;
  font-family: Museo500-Regular;
  border-bottom: 1px solid #d3d5da;
}

.grid-top {
  float: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
}

.grid-top .SStopstripleft {
  display: flex;
  margin: 0;
}

.grid-top .SStopstripleft #lblCount,
.grid-top .SStopstripleft #lblOn {
  white-space: nowrap;
  line-height: 17px;
  padding-top: 4px;
}

.grid-top .SStopstripleft .provider-list {
  padding: 0;
  padding-top: 2px;
  margin-left: 10px;
  min-width: 200px;
}

.grid-top #Printer img {
  margin-left: 15px;
}

.grid-top .provider-list {
  border-color: black;
  border-width: 5px;
}

.date-picker-label{
  padding-left: 5px;
  padding-right: 5px;
}

.mat-mdc-form-field{
  min-width: 24%;
}

.datePicker input{
  border: 0;
  color:#4B8CD4; 
  font-size: smaller;
}

::ng-deep .datePicker .mat-mdc-text-field-wrapper {
  max-height: 26px;
}

::ng-deep .datePicker .mat-mdc-form-field {
  max-height: 30px;
}

::ng-deep .datePicker .mat-mdc-form-field-infix {
  padding: 0 !important;
  max-height: 30px;
}

::ng-deep .datePicker .mat-mdc-form-field-flex{
  height: 27px;
}

.date-picker-selector {
  top: 0;
  left: 0;
  opacity: .1;
  width: 165px;
  background-color: #EBEDF3;
}

.SSmainbuttons {
  border: 1px #d3d5da solid;
  padding: 10px 20px 10px 20px;
  color: #93959a;
  float: inherit;
  text-transform: uppercase;
  font-size: 12px;
  background: rgb(249, 249, 251);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* 	 		 	 		Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* IE10+ */
  background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
  /* IE6-9 */
}

.btn-label {
  padding: 10px 20px 10px 20px;
  cursor: pointer;
}

.SStopstripright {
  height: 20px;
  float: right;
  cursor: pointer;
  padding-left: 10px;
  margin: 0;
}

.SStopstripright input:hover {
  color: #005b9f;
}

.button {
  background: linear-gradient(to bottom, #007be7, #005bab) repeat scroll 0 0 transparent;
  border: 1px solid #005bab;
  color: #FFFFFF;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
  cursor: pointer;
  font-size: 10px;
  height: auto;
  line-height: normal;
  margin-right: 5px;
  padding: 5px 10px 6px;
  text-decoration: none;
}

.button:hover {
  background: linear-gradient(to bottom, #005bab, #007be7) repeat scroll 0 0 transparent;
}


.header {
 // width: 980px;
  height: 60px;
}

/*#endregion */

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px auto;
  color: #4e4e53;
  font-weight: bold;
  font-size: 13px;
}

img {
  border: none;
}

a {
  outline: none;
  color: #5591d6;
  font-weight: bold;
  text-decoration: none !important;
}

a:hover {
  color: #4e4e53;
}

p {
  text-align: left;
  line-height: 22px;
}


.errorbg img {
  width: 16px;
  height: 16px;
  margin-right: 20px;
  float: left;
  margin-top: 3px;
}

.SSTopDiv{
  padding-top: 20px;
  height: 500px;
  display: table;
}

/*#region Site Schedule Webpart */
.SSTopDiv img {
  border: none;
}

.SSTopDiv a {
  outline: none;
  color: #4b8cd4;
  font-weight: bold;
  text-decoration: none;
  cursor: pointer;
}

.SSTopDiv a:hover {
  color: #005b9f;
}

.SSTopDiv p {
  text-align: justify;
  line-height: 19px;
}

.SStopstrip {
  height: 30px;
  border: 1px #d3d5da solid;
  padding: 5px 10px 5px 10px;
  background: rgb(249, 249, 251);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* 	 		 	 		Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* IE10+ */
  background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
  /* IE6-9 */
}

.SStopstripleft {
  float: left;
  height: 26px;
  width: auto;
}

.SStopstripleft span {
  color: #444444;
  font-size: 12px;
  font-weight: bold;
  float: left;
}

.SStopstripleft select {
  background: transparent;
  width: 315px;
  font-size: 12px;
  font-weight: bold;
  border: 0;
  border-radius: 0;
  -webkit-appearance: none;
  cursor: pointer;
  text-overflow: '';
  text-indent: 0.01px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.SStopstripleft select option {
  margin-left: 2px;
}

#imgDownArrow {
  float: left;
  margin-top: 5px;
}

.SStablewrap {
  width: 100%;
  margin-bottom: 46px;
  margin-right: 0px; 
  padding-right: 0px;
  max-height: 80vh;
  overflow-y: auto;
}

.SStablewrap table {
  width:99.5%;
  table-layout: fixed;
}

.PrintSSinner_table {
  float: left;
  height: 20px;
  width: 99.5%;

}

.SStablegridbg {
  //width: 99.5%;
  float: left;
  //overflow-y: auto;
  width: 100%;
  //width: calc(99.5%);
}

.SStablegridbg tr:nth-child(odd) {
  background-color: #FFF;
}

.SStablegridbg table {
  width: 100%;
  border-collapse: collapse;


}

.SStablegridbg table tr td {
  background: #FFF;
  border: 1px solid #d3d5da;
  word-wrap: break-word;
  font-weight: bold;
}

.SStablegridbg table td,
.SStablegridbg table th {
  padding: 8px 14px;
  text-align: center;
  font-size: 12px;
  border: 1px #6b6b6b solid;
  border-bottom: none;
  text-align: left;
  color: #93959a;
}

.SStablegridbg table th {
  height: 34px;
  border: 1px #d3d5da solid;
  border-bottom: none;
  color: #93959a;
  text-transform: uppercase;
  font-size: 12px;
  border-top: none;
  background: rgb(249, 249, 251);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(249, 249, 251, 1)), color-stop(2%, rgba(242, 243, 247, 1)), color-stop(92%, rgba(236, 238, 244, 1)), color-stop(98%, rgba(236, 238, 244, 1)), color-stop(100%, rgba(240, 241, 246, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* 	 		 	 		Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(top, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* IE10+ */
  background: linear-gradient(to bottom, rgba(249, 249, 251, 1) 0%, rgba(242, 243, 247, 1) 2%, rgba(236, 238, 244, 1) 92%, rgba(236, 238, 244, 1) 98%, rgba(240, 241, 246, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9fb', endColorstr='#f0f1f6', GradientType=0);
  /* IE6-9 */
}

.SStablegridbg table .right {
  text-align: right;
}

.SStablegridbg table .center {
  text-align: center;
}

.SSfooter {
  width: 99.5%;
  float: left;
}

.SSfooterleft {
  float: left;
}

.SSfooterright {
  float: right;
}

.footerDiv {
  width: 98%;
  float: left;
  position: absolute;
  bottom: 0px;
  padding-left: 10px;
}

.footerDivBackground {
  background-color: white;
  border-width: thin;
  border-color: lightgray;
  border-style: solid;
}

.footerDivLeft {
  float: left;
  border-radius: 0px 15px 0px 0px;
  padding-left: 15px;
  box-shadow: 5px 5px 5px 5px rgb(0 0 0 / 5%);
}

.footerDivRight {
  float: right;
  border-radius: 15px 0px 0px 0px;
  padding-right: 15px;
  margin-right: 3px;
  box-shadow: -5px 5px 5px 5px rgb(0 0 0 / 5%);
}

.GridbackColor {
  width: 50px;
  height: 50px;
  background: url(../../../../assets/images/circle_50x50_black.png) no-repeat !important;
  background-size: 50px 50px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14.5px;
  color: #646456;
  border-radius: 25px;
}

.GridRowColorCancelled {
  border-left-width: 1px !important;
  box-shadow: inset 6px 0px 0px 0px rgb(204, 41, 61) !important;
}

.GridRowColornoShow {
  border-left-width: 1px !important;
  box-shadow: inset 6px 0px 0px 0px rgb(242, 203, 29) !important;
}

.GridRowColorCompleted {
  border-left-width: 1px !important;
  box-shadow: inset 6px 0px 0px 0px rgb(0, 156, 204) !important;
  -webkit-print-color-adjust: exact !important;
}

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  height: 40px;
}

.btn:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

/*.btn:hover, .btn:focus {
         text-decoration: none;
        background-image: -webkit-gradient( linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F) );
        background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
        background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);

    }*/

.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  pointer-events: none;
  opacity: .65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  color: #333;
  background-color: #ebebeb;
  border-color: #adadad;
}

.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  background-image: none;
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #fff;
  border-color: #ccc;
}

.tfadivWithoutAuthCode a {
  text-decoration: underline;
  color: #3782CD;
  cursor: pointer;
}

.textboxcss div {
  position: relative;
  margin-bottom: 20px;
  float: left;
}

.textboxcss img {
  width: 15px;
  height: 16px;
  border: 1px solid #e3e3e3;
  padding: 15px;
  float: left;
  border-right: none;
}

.textboxcss input {
  border: 1px solid #e3e3e3;
  border-radius: 0px;
  padding: 10px 20px 10px 5px;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
  line-height: 0.8rem;
  width: 331px;
  height: 48px;
  border-left: none;
  box-shadow: none;
}

.textboxcss label {
  position: absolute;
  top: 5px;
  left: 51px;
  padding: 10px 10px 10px 0;
  border-radius: 0px;
  transition: 0.2s ease;
  line-height: 0.8rem;
  background-color: #fff;
  color: #b1b1b1;
}

.textboxcss input:focus {
  border-color: #4c8fd5;
  color: #4c8fd5;
}

.textboxcss input:focus~label {
  background-color: #fff;
  padding: 10px 5px 0 5px;
  left: 46px;
  top: -12px;
  color: #4c8fd5;
}

.textboxcss input:focus+img {
  border-color: #4c8fd5;
}

.textboxcss input:valid~label {
  background-color: #fff;
  padding: 10px 5px 0 5px;
  left: 46px;
  top: -12px;
  color: #4c8fd5;
}

/*form .textboxcss input:valid ~ label {
    background-color: #fff;
    padding: 10px 5px 0 5px;
    left: 46px;
    top: -12px;
    color: #4c8fd5;
}*/

.textboxcss .btn {
  color: #FFFFFF;
  font-size: 15px;
  border: none;
  padding: 0px 10px 0px 10px;
  width: 150px;
  text-align: center;
  height: 40px;
  float: left;
  cursor: pointer;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #A4A4A4), color-stop(1, #9F9F9F));
  background-image: -o-linear-gradient(bottom, #A4A4A4 0%, #9F9F9F 100%);
  background-image: -moz-linear-gradient(bottom, #A4A4A4 0%, #9F9F9F 100%);
  background-image: -webkit-linear-gradient(bottom, #A4A4A4 0%, #9F9F9F 100%);
  background-image: -ms-linear-gradient(bottom, #A4A4A4 0%, #9F9F9F 100%);
  background-image: linear-gradient(to bottom, #A4A4A4 0%, #9F9F9F 100%);
}

.buttonDisable {
  background-image: linear-gradient(to bottom, #a4a4a4 0%, #9f9f9f 100%);
  border: medium none;
  color: #ffffff;
  float: left;
  font-size: 15px;
  height: 40px;
  padding: 0 10px;
  text-align: center;
  width: 150px;
  box-shadow: none;
  cursor: not-allowed;
  opacity: 0.65;
  pointer-events: none;
}

.buttonActive {
  color: #FFFFFF;
  font-size: 15px;
  border: none;
  padding: 0px 10px 0px 10px;
  width: 150px;
  text-align: center;
  height: 40px;
  float: left;
  cursor: pointer;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #005F9F), color-stop(0.5, #00518F));
  background-image: -o-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -moz-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -webkit-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -ms-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
}

.buttonActive:hover {
  background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F));
  background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}



.btnActive {
  color: #FFFFFF;
  font-size: 15px;
  border: none;
  padding: 0px 10px 0px 10px;
  width: 150px;
  text-align: center;
  height: 40px;
  float: left;
  cursor: pointer;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #005F9F), color-stop(0.5, #00518F));
  background-image: -o-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -moz-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -webkit-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: -ms-linear-gradient(bottom, #005F9F 0%, #00518F 50%);
  background-image: linear-gradient(to bottom, #005F9F 0%, #00518F 50%);
}

.btnActive:hover {
  background-image: -webkit-gradient(linear, right bottom, right top, color-stop(0, #005F9F), color-stop(0.5, #00518F));
  background-image: -o-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -moz-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -webkit-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: -ms-linear-gradient(top, #005F9F 0%, #00518F 50%);
  background-image: linear-gradient(to top, #005F9F 0%, #00518F 50%);
}


/*#endregion */

/*#region Data Quality Alerts */
.PALtreeviewclass table {
  width: 95%;
}

.PALtreeviewclass td {
  border-bottom: 1px solid #A1A1A1;
}

.PALLeafMenus a {
  text-decoration: none;
  color: #6E6E6E;
  font-size: 13px;
  cursor: pointer;
}

.PALLeafMenus a:hover {
  text-decoration: none;
  font-weight: bold;
  color: #3183BF;
  cursor: pointer;
}

.divDynamicControl label {
  display: block;
  margin: -14px 0px 0px 20px;
  vertical-align: middle;
}

.alert-container img {
  border: none;
}

.alert-container a {
  outline: none;
  color: #4b8cd4;
  font-weight: bold;
  text-decoration: none;
}

.alert-container a:hover {
  color: #005b9f;
}

.alert-search-div span {
  color: #444444;
  font-size: 12px;
  font-weight: bold;
  float: left;
}


.RPTMedReportMenus a {
  text-decoration: none;
  color: #6E6E6E;
  font-size: 12px;
  cursor: pointer;
}

.RPTMedReportMenus span {
  text-decoration: none;
  color: #6E6E6E;
  font-size: 12px;
  font-weight: bold;
  outline: medium none;
}

.medTreeviewclass tr:hover {
  background-color: #FFF;
}

#GrdReportHelpContent td {
  font-size: 12px;
  font-weight: bold;
}


.GridbackColor {
  width: 50px;
  height: 50px;
  background: url(../../../../assets/images/circle_50x50_black.png) no-repeat !important;
  background-size: 50px 50px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14.5px;
  color: #646456;
  border-radius: 25px;
}

.DeviceGridSSinner_table {
  float: left;
  overflow: auto;
  width: 100%;
}

.hover_row {
  background-color: #f5f5f5 !important;
}

.CursorPointerRC {
  cursor: pointer;
}

.tableHeaderRow{
  position: sticky;
  position: -webkit-sticky;
  top: 0;
}

.table-header-rotated th.row-header {
  width: auto;
}

.table-header-rotated td {
  width: 40px;
  border-top: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
  vertical-align: middle;
  text-align: center;
}

.table-header-rotated th.rotate-45 span {
  -ms-transform: skew(45deg, 0deg) rotate(315deg);
  -moz-transform: skew(45deg, 0deg) rotate(315deg);
  -webkit-transform: skew(45deg, 0deg) rotate(315deg);
  -o-transform: skew(45deg, 0deg) rotate(315deg);
  transform: skew(45deg, 0deg) rotate(315deg);
  position: absolute;
  bottom: 30px;
  /* 40 cos(45) = 28 with an additional 2px margin*/
  left: -25px;
  /*Because it looked good, but there is probably a mathematical link here as well*/
  display: inline-block;
  /*// width: 100%;*/
  width: 95px;
  /* 80 / cos(45) - 40 cos (45) = 85 where 80 is the height of the cell, 40 the width of the cell and 45 the transform angle*/
  /*85 to 90*/
  /*90 to 95 16818*/
  text-align: left;
  font-weight: 500;
  /*// white-space: nowrap;*/
  /*whether to display in one line or not*/
}



.table-header-rotated th.rotate-45-90w {
  height: 100px;
  /*80 to 100*/
  width: 74px;
  min-width: 45px;
  max-width: 74px;
  position: relative;
  vertical-align: bottom;
  padding: 0;
  font-size: 12px;
  line-height: 1;
}

.table-header-rotated th.rotate-45-90w>div {
  position: relative;
  top: 0px;
  left: 40px;
  /* 80 * tan(45) / 2 = 40 where 80 is the height on the cell and 45 is the transform angle*/
  //height: 100%;
  -ms-transform: skew(-45deg, 0deg);
  -moz-transform: skew(-45deg, 0deg);
  -webkit-transform: skew(-45deg, 0deg);
  -o-transform: skew(-45deg, 0deg);
  transform: skew(-45deg, 0deg);
  overflow: hidden;
  /*border-left: 1px solid #dddddd;
  border-right: 1px solid #dddddd;*/
  border-top: 1px solid #dddddd;
  background-color: #2779aa;
  color: white;

  -webkit-print-color-adjust: exact !important;
  -moz-color-adjust: exact;
}

.table-header-rotated th.rotate-45-90w span {
  -ms-transform: skew(45deg, 0deg) rotate(315deg);
  -moz-transform: skew(45deg, 0deg) rotate(315deg);
  -webkit-transform: skew(45deg, 0deg) rotate(315deg);
  -o-transform: skew(45deg, 0deg) rotate(315deg);
  transform: skew(45deg, 0deg) rotate(315deg);
  position: absolute;
  bottom: 30px;
  /* 40 cos(45) = 28 with an additional 2px margin*/
  left: -25px;
  /*Because it looked good, but there is probably a mathematical link here as well*/
  display: inline-block;
  /*// width: 100%;*/
  width: 87px;
  /* 80 / cos(45) - 40 cos (45) = 85 where 80 is the height of the cell, 40 the width of the cell and 45 the transform angle*/
  text-align: left;
  font-weight: 500;
  padding-top: 25px;
  /*// white-space: nowrap;*/
  /*whether to display in one line or not*/
}

.leftBorder {
  border-left-style: hidden !important;
  -webkit-border-left-style: hidden !important;
  -moz-border-left-style: hidden !important;
  padding-left: inherit !important
}

.table-header-rotated td:nth-child(1) {
  background-color: #dcdcdc !important;
  -webkit-print-color-adjust: exact !important;
  -moz-color-adjust: exact;
  width: 120px;
}

.mySelectClass{
  padding-left: 5px !important;
  font-weight: bold;
  color: rgb(75, 140, 212)
}
/*#DIVBindMeasureHtml {
    float: left; width: 100%; overflow-y: auto; font-weight: normal;
}*/

.EmptyWarning {
width: 100%;
margin-left: 0px;
background-color: #fff;
text-align: center;
vertical-align: middle;
display: inline-block;
background-color: #fff;
font-size: 12px;
font-weight: bold;
color: #93959a;
height: 35px;
border: 1px #e1e1e1 solid;
}

.input-wrapper {
  position: relative;
  display: inline-block;
  width: 60%;
  min-width: 60%;
}

.provider-list {
  width: 95%;
  border-width: 1px !important;
  background-color: inherit;
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  pointer-events: none;
  color: #000; /* Adjust color as needed */
}


::ng-deep .dropdownformat  {
  color: rgb(75, 140, 212) !important;
  font-weight: bolder !important;
  background-color: #f0f1f6 !important; 

}

.measureGapDescLists {
  display: flex;
  list-style-type: none;
}

.formButton {
  padding-left: 5px;
}

.submittedLink{
  padding-left: 5px;
}

.launchButton{
  height: 1.06rem;
}