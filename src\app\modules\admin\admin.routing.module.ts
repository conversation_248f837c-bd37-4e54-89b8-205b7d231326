import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/shared-modules/layouts/main-layout/main-layout.component';
import { AdminContentComponent } from './admin-content/admin-content.component';
import { RulesEngineComponent } from './rules-engine/rules-engine.component';
import { EpividianGuardService } from 'src/app/shared-services/epividian-guard.service';
import { PdfFillerManagerComponent } from './pdf-filler-manager/pdf-filler-manager.component';

const routes: Routes = [
  {
    path: 'Admin',
    component: MainLayoutComponent,
    canActivate: [EpividianGuardService],
    children: [
      { path: '', component: AdminContentComponent, pathMatch: 'full' },
      { path: 'rules', component: RulesEngineComponent },
      { path: 'pdf-filler-manager/:siteId', component: PdfFillerManagerComponent },
      // These routes will be implemented later
      // { path: 'users', component: UserManagementComponent },
      // { path: 'pulse', component: PulseManagementComponent },
      // { path: 'surveys', component: SurveyManagementComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
