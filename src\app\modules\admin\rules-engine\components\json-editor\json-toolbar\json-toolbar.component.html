<div class="json-toolbar">
  <!-- View toggle buttons -->
  <div class="view-toggle">
    <mat-button-toggle-group [value]="viewMode" (change)="onViewModeChange($event.value)">
      <mat-button-toggle value="object">Object View</mat-button-toggle>
      <mat-button-toggle value="json">Raw JSON</mat-button-toggle>
    </mat-button-toggle-group>
  </div>

  <!-- Action buttons -->
  <div class="action-buttons">
    <!-- Format button (only in JSON view) -->
    <button *ngIf="viewMode === 'json'" 
            type="button" 
            mat-raised-button 
            color="primary" 
            (click)="onFormatJson()">
      <mat-icon>format_align_left</mat-icon> Format JSON
    </button>
    
    <!-- Apply changes button -->
    <button type="button" 
            mat-raised-button 
            color="accent" 
            (click)="onApplyChanges()" 
            [disabled]="hasError">
      <mat-icon>check</mat-icon> Apply Changes
    </button>
  </div>
</div>
