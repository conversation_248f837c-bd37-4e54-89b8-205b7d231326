.searchCriteriaDiv {
    position: relative;
    margin: 2px;
    padding: 15px;
    width: 100%;
    z-index: 1;
    background-color: white;
}

// Label Row
.label-row {
    margin-bottom: 10px;
}

.field-label {
    color: black;
    font-weight: 500;
    margin: 0;
}

// Cohort Input Row
.input-row {
    margin-bottom: 15px;
}

.cohort-input {
    width: 100%;
    max-width: 100%;
}

// Run Button Row
.button-row {
    margin-top: 15px;
    padding: 0 16px;
}

.run-button {
    width: 100% !important;
    padding: 8px 16px !important;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
    font-size: 14px;
    height: auto;
    min-height: 35px;
}

// Option styling
.option-main-text {
    font-size: 14px;
    font-weight: bold;
}

.option-sub-text {
    font-size: 11px;
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}

.btnWidth {
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 30px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

.mat-option {
  white-space: normal !important;
  display: block !important;
  overflow-wrap: break-word !important;
  overflow: visible !important;
  line-height: normal !important;
}

.matHeaderAndSubText {
  height: 85px;
  padding: 5px 0px 5px 5px;
  margin-top: 12px;
}

.element-label {
  padding-top: 0.3%;
}
