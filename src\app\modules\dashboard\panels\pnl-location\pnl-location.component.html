<div class="searchCriteriaDiv">
    <!-- Warning Message -->
    <div class="warning-message" *ngIf="lblWarningMsg">
        <p class="lblWarningMsg">{{lblWarningMsg}}</p>
    </div>

    <!-- Label Row -->
    <div class="label-row">
        <label class="field-label">Location:</label>
    </div>

    <!-- Location Input Row -->
    <div class="input-row">
        <input #location matInput mat-form-field class="form-select form-select-sm location-input"
        [matAutocomplete]="autoLocation" name="rptpanLocation" (keyup)="comboFilter(location.value)"
        [formControl]="locationSearch" placeholder="Select or search for a location">
        <mat-autocomplete #autoLocation="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChange($event)">
          <mat-optgroup *ngFor="let locGroup of locationData[0]?.groups" [label]="locGroup.name" >
            <mat-option *ngFor="let locData of panelService.getGroupData(locGroup.id, filteredLocations[0])" [value]="locData">{{locData.value}}</mat-option>
          </mat-optgroup>
        </mat-autocomplete>
    </div>

    <!-- Run Button Row -->
    <div class="button-row">
        <button type="button" (click)="panelService.InitBoldReport()" [disabled]="!isRunButtonEnable"
        id="reportViewer_Control_viewReportClick" aria-describedby="reportViewer_Control_viewReportClick"
        [ngClass]="rptbtnColor" class="run-button">Run</button>
    </div>
</div>
