import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { IData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelData } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';

@Component({
  selector: 'pnl-reporting-year',
  templateUrl: './pnl-reporting-year.component.html',
  styleUrls: ['./pnl-reporting-year.component.scss']
})
export class PnlReportingYearComponent implements OnInit {
  @Input() rptpanReportingYear: FormControl;
  rptbtnColor: string;
  rptReportYearParam: IReportParamData[] = [] as IReportParamData[];
  rptReportYearData: IData[] = [] as IData[];
  defaultYear: string="";
  noSearchPan: boolean = false;


  constructor(
    public panelService:PanelService,
    private formBuilder: FormBuilder
  ) {
    this.rptpanReportingYear = new FormControl();
    this.rptbtnColor = btnColor.btnSecondColor;
    this.panelService
      .GetPanelData(ReportPanelTypes.pnlReportingYear)
      .subscribe((s) => {
        this.rptReportYearParam = s;
        //the sort was coming back in ascending order.  Including a descending sort here.
        this.rptReportYearData = this.rptReportYearParam[0].data.sort((a, b) => +b.key - +a.key);
        this.rptpanReportingYear = new FormControl(this.rptReportYearData[0].key);
        if (this.rptReportYearParam[0].default != null && this.rptReportYearParam[0].default.toString()!='') {
          this.defaultYear = this.rptReportYearParam[0].default.toString();
          this.rptpanReportingYear.setValue(this.defaultYear);
          
        }
        if (this.rptReportYearData.length > 0)
           this.readyToRun();
      });
  }

  ngOnInit(): void {
  }

  readyToRun(): void{
      this.rptbtnColor= btnColor.btnPrimaryColor;
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

}
