import { Injectable, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  HttpClient,
  JsonpClientBackend,
  HttpHeaders,
  HttpParams,
  HttpResponse,
} from '@angular/common/http';
import {
  IChorusLogin,
  IDeviceDetailsVM,
  IDocumentItem,
  IReportList,
  ITfaModel,
  IapiErrorResponse,
  IUserIP,
} from 'src/app/shared-services/ep-api-handler/models/index';
import { BehaviorSubject, Observable, Subject, Subscription, takeLast } from 'rxjs';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { environment } from 'src/environments/environment';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { IChangePassword } from '../../models/changepassword-model';
import { StringDecoder } from 'string_decoder';
import { Router } from '@angular/router';
import { IChorusAccessViewModel } from 'src/app/shared-services/user-context/models/user-security-model';
import { IChorusUserInfo } from 'src/app/shared-services/ep-api-handler/models/chorus-user-Info.model';
import { EpividianCommon } from 'src/app/modules/utility/EpividianCommon';
import { RouteCacheService } from 'src/app/shared-services/route-cache.service';

@Injectable({
  providedIn: 'root',
})
export class LoginService implements OnInit, OnDestroy {
  _chorusTokenResponse = new Subject<IChorusResponseToken>();
  private _currentIP: any = '';
  _chorusSession: IChorusResponseToken = {} as IChorusResponseToken;

  private dashboardData: any = new BehaviorSubject(null);
  currentDashboardData = this.dashboardData.asObservable();
  data: any;
  chorusSecurity: IChorusUserInfo = {} as IChorusUserInfo;
  isLoadingChorusAccess: boolean = false;
  private pageSubscriptions: Subscription = new Subscription;

  constructor(private userContext: UserContext,
              private router: Router,
              private epividianCommon: EpividianCommon,
              private routeCacheService: RouteCacheService) {
  }

  ngOnInit() {

    this.userContext.getUserSession().subscribe(s => {
      this._chorusSession = s;
    });
    /*
    this.apiHandler.Get(ApiTypes.None, "http://api.ipify.org/",false).subscribe(s => {
      this._currentIP = s.response;

    })
    */
   // this.userContext.LoadUserObjFromLocalStorage();
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  public getIPAddress(): Observable<string> {
    return this.userContext.apihandler.Get<string>(ApiTypes.None, "http://api.ipify.org/",false);

  }

  public IPAddress() {
    return this._currentIP;
  }

  public login<T>(username: string, password: string, deviceCode: string = ""): Observable<IChorusResponseToken|IapiErrorResponse> {

    //base64 encode values if setting for encodeBrowserObjects is true.
    if (environment.encodeBrowserObjects){
      username = btoa(username);
      password = btoa(password)
    }

    const body: IChorusLogin = {
      grant_type: 'password',
      username: username,
      password: password,
      ip: this._currentIP,
      scope: "Chorus Login",
      deviceCode: deviceCode
    };

    //let loginResponse: IapiErrorResponse|IChorusResponseToken = {} as IapiErrorResponse|IChorusResponseToken;
    return this.userContext.apihandler.Post<T>(
      ApiTypes.AuthProvider,
      ApiRoutes.AuthProviderLoginToken,
      JSON.stringify(body),
      false,
      true
    );
  }


  //Used to store the users current token in both the browser and the service itself.
  //Sets the bear token to be used in api Chorus Api Requests
  /*
  public SaveUserResponseToken(userToken: IChorusResponseToken) {
   // this.apiHandler.setbearToken(userToken.access_token);
    this._chorusTokenResponse.next(userToken);
    this.SaveTokenToLocalStorage(userToken);
  }

  //Saves the current user token in the browser.. To Get User object from Browser use userContext Service
  private SaveTokenToLocalStorage(userToken: IChorusResponseToken)
  {
    //depending on app configuration setting to base 64 encode browser objects.
    let jsonString = JSON.stringify(userToken);
    if (environment.encodeBrowserObjects){
      jsonString=btoa(JSON.stringify(userToken))
    }
    localStorage.setItem("Session", jsonString);

    this.userContext.InitializeAuthorizedSession();
  }
  */


  forgotPassword(username: String): any {
    var forgotPassModelJson =
      '{"Username":"' +
      username +
      '","IsAccountAvailable":true,"IsLockedOut":false,"IsApproved":true,"IsTempPwdGenrate":true,"IsEmailSent":true,"IsUpdateTempPwd":false}';
    let forgotPassResponse = {};
    return this.userContext.apihandler.Get(
      ApiTypes.V2,
      ApiRoutes.ForgotPassword,
      true);
  }

  public changePassword(
    oldPassword: string,
    newPassword: string,
    confirmPassword: string
  ): any {

    const body: IChangePassword = {
      oldPassword: oldPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      statusMessage: ""
    };

    return this.userContext.apihandler.Post(ApiTypes.AuthProvider,
                                ApiRoutes.ChangePassword,
                                JSON.stringify(body),
                                "",
                                true);
  }

  getTFA(username: string): any {
    let QRcode = {};
    let userSession = this.userContext.getUserSession();
    let qrCodeUrl = ApiRoutes.QRCode.replace('{{userName}}', username).replace('{{refreshToken}}', this._chorusSession.refreshToken).replace('{{tfaToken}}', this._chorusSession.tfaToken);
    return this.userContext.apihandler.Get(ApiTypes.AuthProvider,
      qrCodeUrl
    );
  }

  sendSMS(mobile: number, username: string): any {
    return this.userContext.apihandler.Get(ApiTypes.V2,
      ApiRoutes.SendTFASMS +
        '?MobileNumber=' +
        encodeURIComponent(mobile.toString()) +
        '&username=' +
        encodeURIComponent(username)
    );
  }

  addTfaPhone(userId: string, refreshToken: string, data: object|null): any {
    userId = encodeURIComponent(btoa((userId)));
    let tfaVerifyUrl = ApiRoutes.AddTfaPhone.replace('{{userId}}', userId).replace('{{refreshToken}}', refreshToken);
    if (data == null) {
      data = { };
    }
    return this.userContext.apihandler.Post(
      ApiTypes.AuthProvider,
      tfaVerifyUrl,
      JSON.stringify(data),
      true,
      true
    );
  }

  verifyTFA(userId: string, refreshToken: string, tfaToken: string, resetTokenQueryString: string=""): any {
    userId = encodeURIComponent(btoa((userId)));
    let tfaVerifyUrl = ApiRoutes.VerifyTFACode.replace('{{userId}}', userId).replace('{{refreshToken}}', refreshToken).replace('{{tfaToken}}', tfaToken);
    if (resetTokenQueryString!="null" && resetTokenQueryString!="")
    {
      tfaVerifyUrl = tfaVerifyUrl + "?ref=" + resetTokenQueryString;
    }

    return this.userContext.apihandler.Post(
      ApiTypes.AuthProvider,
      tfaVerifyUrl,
      "",
      {},
      true
    );
  }

  verifyTOTP(userId: string, refreshToken: string, tfaToken: string, resetTokenQueryString: string=""): any {
    userId = encodeURIComponent(btoa((userId)));
    let tfaVerifyUrl = ApiRoutes.VerifyTOTP.replace('{{userId}}', userId).replace('{{refreshToken}}', refreshToken).replace('{{tfaToken}}', tfaToken);

    if (resetTokenQueryString!="null" && resetTokenQueryString!="")
    {
      tfaVerifyUrl = tfaVerifyUrl + "?ref=" + resetTokenQueryString;
    }

    let jsonResponse = {};
    return this.userContext.apihandler.Post(
      ApiTypes.AuthProvider,
      tfaVerifyUrl,
      "", true, true
    );
  }

  resendCode(userId: string, refreshToken: string, resetTokenQueryString: string=""): any {
    userId = encodeURIComponent(btoa((userId)));
    let tfaResendUrl = ApiRoutes.ResendCode.replace('{{userId}}', userId).replace('{{refreshToken}}', refreshToken);
    if (resetTokenQueryString!="null" && resetTokenQueryString!="")
    {
      tfaResendUrl = tfaResendUrl + "?ref=" + resetTokenQueryString;
    }

    return this.userContext.apihandler.Post(
      ApiTypes.AuthProvider,
      tfaResendUrl,
      "",
      {},
      true
    );
  }

  UpdateUserLoginStatus(doTfa: boolean = false){
    localStorage.removeItem("lastRefreshCheck");
    this.epividianCommon.upsertToStorage("lastActiveTime", new Date(), true, true);
    let routePath = ApiRoutes.UpdateUserLoginStatus.toString();
    if (doTfa==true)
    {
      routePath = routePath + "?tfa=1";
    }
    this.userContext.apihandler.Get(ApiTypes.V2,ApiRoutes.UpdateUserLoginStatus).subscribe();
  }

  /**
   * Handles post-login routing by checking for cached routes and redirecting appropriately
   */
  handlePostLoginRouting(): void {
    const cachedRoute = this.routeCacheService.getValidCachedRoute();

    if (cachedRoute) {
      //console.log('Redirecting to cached route:', cachedRoute);
      this.routeCacheService.clearCachedRoute();
      this.router.navigateByUrl(cachedRoute);
    } else {
      //console.log('No cached route found, using default routing');
      // Use existing default behavior
      this.TermsDashboardRoute();
    }
  }

  TermsDashboardRoute()
  {
    if (!this.isLoadingChorusAccess) {
      this.isLoadingChorusAccess = true;

      this.pageSubscriptions.add(
        this.userContext.getUserSession().subscribe(s => {

          if (Object.keys(s).length !== 0 && s.constructor === Object)
          {
            if (s.access_token != null && this.epividianCommon.isSessionValid(s))
            {
              this.pageSubscriptions.add(
                this.userContext.LoadUsersChorusAccess().subscribe((s: IChorusAccessViewModel[]) => {
                    this.isLoadingChorusAccess = false;
                    let defaultSite = s.find(x => x.isDefault == true);
                    let siteId = Number(defaultSite?.siteId);
                    this.userContext.SetCurrentSite(siteId);
                    this.userContext.SetUserSecurity(s)

                    this.userContext.GetUserInfoSub().pipe(takeLast(1)).subscribe(s => {
                      this.chorusSecurity = s;

                      //this is just until we get the TFA Flow Implemented
                      if (this.chorusSecurity.isAgree!==true) {
                        this.router.navigate(['/TermsConditions']);
                      }
                      else
                      {
                        // Check for cached route before defaulting to Dashboard
                        const cachedRoute = this.routeCacheService.getValidCachedRoute();
                        if (cachedRoute) {
                          this.routeCacheService.clearCachedRoute();
                          this.router.navigateByUrl(cachedRoute);
                        } else {
                          this.router.navigate(['/Dashboard']);
                        }
                      }

                    });
                })
              );
            }
          }
        })
      );
    }
  }
}


