@import 'bootstrap/scss/_functions';
@import 'bootstrap/scss/_variables';
@import 'bootstrap/scss/mixins/_breakpoints';

.cover-container {
    background-color: #0071bc;
    min-height: 100vh;
    overflow: hidden;
    position: relative;

    a:visited {
      color: whitesmoke;
    }
    @include media-breakpoint-up(md) {
        padding-left: 8rem;
    }

    .header-row-1  {
        background-image: url("../../../../assets/images/Bubbles.svg");
        background-position: right -90px;
        background-repeat: no-repeat;
        background-size: contain;
        @include media-breakpoint-down(md) {
            background-position: right top;
            transform: scale(160%) translate(35%, 6%);
        }
        .logo-container {
            background-image: url("../../../../assets/images/Logo.svg");
            background-position: top center;
            background-repeat: no-repeat;
            background-size: contain;
            padding-top: 32.5%;

            @include media-breakpoint-down(md) {
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                padding-top: 90%;
                transform: scale(70%) translate(-50%, -50%);
            }
        }
    }
  }

  ::ng-deep .btn-light {
    background-color: #fff;
    background-image: url("../../../../assets/images/right_arrow.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 10px 15px;
    border: 1px solid #ced4da;
    border-left: none;
    border-radius: 0.7rem;
}

.eTFALoginDetails{
    width: 100%;
}
.eTFAQRCode{
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
/* .eTFASMS{
    position: relative;
    display:block;
    text-align: center;
} */
.eTFALoginInfo{
    display: table;
    position: absolute;
    top: 46%;
    left: 40%;
    right: 10%;
}
.input-group{
    padding-top: 10px;
    text-align:center;
}
.form-control{
    border-radius: 11px !important;
    width: 80%;
}
.eTFAMessage{
    color: white;
    visibility: visible;
    font-family: "MuseoSans-500";
    font-size: 11pt;
    width: 100%;
    word-wrap: break-word;
    padding-bottom: 5px;
    padding-top: 5px;
    text-align: left;
}
.eTFAContinue{
    background-color: white;
    border-style: none;
    font-size: 12px;
    text-align: center;
    border-radius: 10px;
    width: 150px;
 }

 .eTFASMSInput{
    width: 150px;
    border-radius: 10px;
    border-style: none;
    text-align: center;
 }

 .eTFAQRImage{
    border-radius: 10px;
 }

.submit{
    position:relative !important;
    width: 12px;
    height: 13px;
    right: 3px;
    top: 46%;
    cursor: pointer;
}
.input-group{
    padding-top: 10px;
}
.is-invalid
{
  border-color:red;
}
@media only screen and (max-width: 768px) {
    /* For mobile phones: */
    [class*="eTFALoginBubbleContainer"] {
        background-image: url("../../../../assets/images/Bubbles.svg");
        background-repeat: no-repeat;
        background-size: 230%;
        position: relative;
        height: 100vh;
        background-position-x: left;
        top: -14%;
        left: 20%;
    }
    [class*="eTFALoginLogoContainer"] {
        background-image: url("../../../../assets/images/Logo.svg");
        background-repeat: no-repeat;
        background-size: 80%;
        position: absolute;
        top: -24%;
        left: -10%;
        height: 100%;
        width: 100%;
    }
    /* [class*="eTFALoginInfo"] {
        position: absolute;
        top: 50%;
        left: 10%;
        height: 100%;
        width: 100%;
    } */
     [class*="eTFALoginDetails"]{
        width: 100%;
    }
    [class*="eTFAQRCode"]{
        position: relative;
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    [class*="eTFASMS"]{
        position: relative;
        display:block;
        text-align: center;
    }
    [class*="eTFALoginInfo"]{
        display: table;
        width: 100%;
        position: absolute;
        top: 45%;
        left: 0;
    }
    [class*="input-group"]{
        padding-top: 10px;
        width: 70%;
        left: 10%;
    }
    [class*="form-control"]{
        border-radius: 11px !important;
    }
    [class*="eTFAMessage"]{
        color: white;
        visibility: visible;
        font-family: "MuseoSans-500";
        font-size: 11pt;
        width: 100%;
        word-wrap: break-word;
        text-align: left;
    }
    [class*="eTFAContinue"]{
        background-color: white;
        border-style: none;
        font-size: 12px;
        text-align: center;
        border-radius: 10px;
        width: 150px;
    }
    [class*="eTFAQRImage"]{
        border-radius: 10px;
     }
     [class*="eTFASMSInput"]{
        width: 150px;
        border-radius: 10px;
        border-style: none;
        text-align: center;
     }
  }



