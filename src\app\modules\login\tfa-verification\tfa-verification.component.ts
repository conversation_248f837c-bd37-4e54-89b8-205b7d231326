import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from "ngx-spinner";
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { LoginService } from '../services/login/login.service';
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { AuthRedirectOption, TfaMethods } from '../models/tfaMethod-enum';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tfa-verification',
  templateUrl: './tfa-verification.component.html',
  styleUrls: ['./tfa-verification.component.scss'],
})
export class TfaVerificationComponent implements OnInit, OnDestroy {
  TFAMessage: string | null = '';
  mobile: string | null = '';
  userName: string = '';
  tfaMethod: any;
  digits: string = '';
  resetToken: string = '';
  tfaToken: string = '';
  isRedirect: number = 0;
  inputEnabled:boolean=true;

  private pageSubscriptions: Subscription = new Subscription();


  userInfo: IChorusResponseToken = {} as IChorusResponseToken;
  constructor(
    private router: Router,
    private SpinnerService: NgxSpinnerService,
    private _loginService: LoginService,
    private userContext: UserContext
  ) {
    let data: any = this.router.getCurrentNavigation()?.extras.state;

    if (data != null)
    {
      this.tfaMethod = data.tfaMethod;
      this.digits = data.digits || "";
      this.userInfo = data.userInfo || {} as IChorusResponseToken;
      this.resetToken = data.resetToken || this.userInfo.resetToken || "";
      this.isRedirect = data.isRedirect || this.userInfo.callback_redirect || 0;
      let isUserSessionValid = isValidToken(this.userInfo);
      if (isUserSessionValid)
      {
        userContext.SaveTokenToLocalStorage(this.userInfo);
      }

      this.pageSubscriptions.add(
        this.userContext.getUserSession().subscribe(s => {
          this.userInfo = s;
        })
      );
    }

    function isValidToken(token: any): token is IChorusResponseToken {
      // Example validation; adjust as per your interface's needs
      return token && typeof token.refreshToken !== 'undefined';
  }

  }

  ngOnInit(): void {


    this.userContext.LoadUserObjFromLocalStorage();

    //this.mobile = localStorage.getItem("mobile");
    if (this.tfaMethod == TfaMethods.TOTP) {
      this.TFAMessage = 'Enter your generated 6 digit code here.';
    } else {
      this.TFAMessage =
        'We sent code to ***-***-' + this.digits + ', Input it here.';
    }
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }

  resendCode(): void {
    this.inputEnabled = false;
    this.userName = this.userContext.GetUserName();
    this.SpinnerService.show();
    this.pageSubscriptions.add(
      this._loginService
        .resendCode(
          this.userName,
          this.userInfo.refreshToken,
          encodeURIComponent(this.userInfo.resetToken)
        )
        .subscribe((res) => {
          if (res.status == 200 )
          {
            this.TFAMessage = 'We resent code to ***-***-' + this.digits + ', Input it here.';
            setTimeout(() => {
              this.SpinnerService.hide();

            }, 1000);
          }
          else{
            this.SpinnerService.hide();
            this.TFAMessage = 'An error resubmitting the code occurred';
          };
        }));

    this.inputEnabled = true;

  }

  verifyTFA(code: string): any {
    if (code.length == 6) {
      this.inputEnabled = false;
      this.SpinnerService.show();
      this.userName = String(localStorage.getItem('userName'));
      if (this.tfaMethod == TfaMethods.TOTP)
      {
        //Verify TOTP from Authenticator
        this.pageSubscriptions.add(
          this._loginService
            .verifyTOTP(
              this.userName,
              this.userInfo.refreshToken,
              code,
              encodeURIComponent(this.userInfo.resetToken)
            )
            .subscribe((res) => {
              if (res.status==200) {
                this.userContext.SaveTokenToLocalStorage(res.body);
                this.userContext.SetUserDeviceCode(res.body.deviceCode);
                if (res.body.access_token != null)
                {
                  this.userContext.apihandler.setbearToken(res.body.access_token);
                  this._loginService.UpdateUserLoginStatus();
                }
                setTimeout(() => {
                  this.SpinnerService.hide();
                  if (res.body.callback_redirect == AuthRedirectOption.LogIn)
                  {
                    // Use the proper post-login routing that handles cached routes
                    this._loginService.handlePostLoginRouting();
                  } else if (res.body.callback_redirect == AuthRedirectOption.ResetPassword)
                  {
                    if (res.body.tfaToken != null)
                    {
                      this.tfaToken = "&token=" + res.body.tfaToken
                    }
                    let changePasswordPath = '/Auth/ChangePassword?ref=' + encodeURIComponent(this.resetToken) + this.tfaToken;
                    this.router.navigateByUrl(changePasswordPath);
                  }
                }, 1000);

              } else {
                this.SpinnerService.hide();
                this.TFAMessage = 'Please enter correct code';

              }
              (err) => {
                this.SpinnerService.hide();
                this.TFAMessage = 'Please enter correct code';
              };
            })
        );
      } else
      {
        //Verify SMS
        this.pageSubscriptions.add(
          this._loginService
            .verifyTFA(
              this.userName,
              this.userInfo.refreshToken,
              code,
              encodeURIComponent(this.userInfo.resetToken)
            )
            .subscribe((res) => {
              if (res.status == 200 && res.body.access_token != null) {
                this.userContext.SaveTokenToLocalStorage(res.body);
                this.userContext.SetUserDeviceCode(res.body.deviceCode);
                this.userContext.apihandler.setbearToken(res.body.access_token);
                this._loginService.UpdateUserLoginStatus();

                setTimeout(() => {
                  this.SpinnerService.hide();
                  this._loginService.TermsDashboardRoute();
                }, 1000);

              } else if(res.status == 200 && res.body.callback_redirect==AuthRedirectOption.ResetPassword) {
                this.SpinnerService.hide();
                let tokenQueryString = "&token="+code
                let changePasswordPath = '/Auth/ChangePassword?ref=' + encodeURIComponent(this.userInfo.resetToken) + tokenQueryString;
                this.router.navigateByUrl(changePasswordPath);

              } else {
                this.SpinnerService.hide();
                if (res.status == 412)
                {
                  this.TFAMessage = 'This code has expired. Return to login screen and login again to generate new code.';
                }
                else
                {
                   this.TFAMessage = 'Please enter correct code';
                }
              }
              (err) => {
                this.SpinnerService.hide();
                this.TFAMessage = 'Please enter correct code';
              };
            })
          );
      }

    }
    this.inputEnabled = true;
  }

  showspinner() {
    this.SpinnerService.show();
    setTimeout(() => {
      this.SpinnerService.hide();
    }, 5000);
  }
}
