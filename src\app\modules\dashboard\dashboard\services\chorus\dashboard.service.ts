import { Injectable } from '@angular/core';
import { Observable, share } from 'rxjs';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { AppointmentInfo, AppointmentProviders } from '../../../models/Appointment-Info-model';
import { Rule } from '../../../models/dynamic-form-model';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private static _appointmentProviderSelection: string = '';
  private static _appointmentDateSelection: string = '';

  constructor(private userContext: UserContext) { }

  //Handles Getting and Setting the users appointment selection path.
  GetAppointmentSelect(): string
  {
    return DashboardService._appointmentProviderSelection;
  }
  SetAppointmentSelect(aptSelectPath: any)
  {
    DashboardService._appointmentProviderSelection = aptSelectPath;
  }


  //Handles Getting and Setting the users appointment selected Date
  GetAppointmentDateSelect(): string
  {
    return DashboardService._appointmentDateSelection;
  }
  SetAppointmentDateSelect(aptSelectPath: any)
  {
    DashboardService._appointmentDateSelection = aptSelectPath;
  }


  //Gets that Provider's schedule data for the selected date.
  GetAppointment(siteId: string, providerId: string, selecteddate: any): Observable<AppointmentInfo[]>
  {
    return this.userContext.apihandler
      .Get<AppointmentInfo[]>(
        ApiTypes.V2,
        ApiRoutes.GetAppointments +
        '/' +
        siteId +
        '/' +
        providerId +
        '/' +
        selecteddate
      );
  }

  GetAppointmentProviders(siteId: string, selecteddate: any)
  {
    return this.userContext.apihandler
    .Get<AppointmentProviders[]>(
      ApiTypes.V2,
      ApiRoutes.GetProviders +
      '/' +
      siteId +
      '/' +
      selecteddate
    );
  }

  GetFormUrls(siteId: string)
  {
    let url = ApiRoutes.GetFormUrls.toString().replace("{siteId}", siteId);
    return this.userContext.apihandler
    .Get<AppointmentProviders[]>(
      ApiTypes.V2,
      url
    )
  }

  GetFormStatuses(siteId:string, workflowId:string, ruleId:string)
  {
    let url = ApiRoutes.GetWorkflowStatuses.toString().replace("{{siteId}}", siteId)
                                                      .replace("{{workflowId}}", workflowId)
                                                      .replace("{{ruleId}}", ruleId);
    return this.userContext.apihandler
    .Get<Rule[]>(
      ApiTypes.V2,
      url
    )
  }
}
