import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { LoginService  } from 'src/app/modules/login/services/login/login.service';
import { Router, RouterModule, Routes } from '@angular/router';
import { NgxSpinnerService } from "ngx-spinner";
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';

const USphone = /^\D?(\d{3})\D?\D?(\d{3})\D?(\d{4})$/;
const InternationalPhone = /^\+(?:[0-9]●?){6,14}[0-9]$/;
const numberOnly = /[0-9+]/;

@Component({
  selector: 'app-tfa-new-device-link',
  templateUrl: './tfa-new-device-link.component.html',
  styleUrls: ['./tfa-new-device-link.component.scss']
})
export class TfaNewDevicLinkComponent implements OnInit {


  public userName: string = "";
  public qrCodeImageUrl: string = "";
  public manualEntrySetupCode: string = "";
  public mobile: any = "";
  public key: string="";
  userInfo: IChorusResponseToken = {} as IChorusResponseToken;
  public isMobileValid: string = "";
  public hasSubmitted: boolean = false;

  constructor(private service:LoginService, private router: Router, 
              @Inject(NgxSpinnerService) private SpinnerService: NgxSpinnerService,
              private userContext: UserContext) 
    {
    let data: any = this.router.getCurrentNavigation()?.extras.state;

    if (data != null)
    {
      this.userInfo = data.userInfo || {} as IChorusResponseToken;
    }

    if (this.isChorusUserInfoEmpty()==true)
    {
      this.userContext.getUserSession().subscribe(s => {
        this.userInfo = s;
      });
    }
}

public isChorusUserInfoEmpty(): boolean {
  const userInfo = this.userInfo; // get the current value of _chorusUserInfo
  return Object.keys(userInfo).length === 0 && userInfo.constructor === Object;
}

  @HostListener('document:keypress', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.key = event.key;

  }
  ngOnInit(): void {

    this.userContext.LoadUserObjFromLocalStorage();
    this.getTFA();
  }


  getTFA(): any
  {
  this.userName = encodeURIComponent(btoa((String(localStorage.getItem('userName')))));
  //this.userName = this.userContext.GetUserName();
    this.service.getTFA(this.userName).subscribe(res => {
      if (res != null) {
        this.qrCodeImageUrl = "data:image/png;base64," + res.qrCodeImageUrl;
       }
      else{}
      err => {
        var error = err.error;
      }
    });
  }

  linkToSMS(mobile: string)
  {
    this.SpinnerService.show();
    this.hasSubmitted = true;
    if (this.PhoneValidation(mobile) == false)
    {

      this.SpinnerService.hide();
      return;
    }
    let formattedPhoneNumber = mobile.replace(/\D/g, '');
    var data = {"mobile": formattedPhoneNumber}
    var user = this.userInfo;
    this.userName = String(localStorage.getItem('userName'));
    this.service.addTfaPhone(this.userName, user.refreshToken,data).subscribe(res => {
      this.SpinnerService.hide();
      if (res.ok == true) {
        this.userContext.SaveTokenToLocalStorage(res);
        this.router.navigate(['/Auth/VerifyTfa'], {state: { digits:  res.body.last4, userInfo: this.userInfo}});
       }
      else{
        $('.eTFAErrorMessage').text("Unable to send SMS, Please contact");
      }
    });
  }

  linkToAuthenticator()
  {
    this.router.navigate(['/Auth/VerifyTfa'], {state: { tfaMethod:  2, userInfo: this.userInfo }});
  }


  //Validate the phone number on submit before allowing through.
  PhoneValidation (phoneNumber: string) {
    let isValid = false;
    this.isMobileValid ='';

    if (!this.hasSubmitted)
    {
      return true;
    }

    isValid = USphone.test(phoneNumber);
    isValid = InternationalPhone.test(phoneNumber)==true?true:isValid;
    if (isValid == false)
    {
      this.isMobileValid = 'is-invalid';
    }
    return isValid;
  }


  //is used on the Ui directly to format the field as the user enters the sms phone number
  formatPhoneNumber(phone: string) {
    // Remove all non-digit characters
    let formattedPhoneNumber = phone.replace(/\D/g, '');

    // Format the phone number based on the number of digits
    if (formattedPhoneNumber.length === 10) {
      formattedPhoneNumber = formattedPhoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (formattedPhoneNumber.length === 11) {
      formattedPhoneNumber = formattedPhoneNumber.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '$1 ($2) $3-$4');
    }

    // Return the formatted phone number
    return formattedPhoneNumber;
  }

}
