import { HttpError<PERSON><PERSON>po<PERSON>, <PERSON>ttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { catchError, from, lastValueFrom, map, Observable, of, switchMap, throwError } from "rxjs";
import { UserContext } from "../../user-context/user-context.service";
import { ApiHandler } from "../../ep-api-handler/ep-api-handler";
import { TimeoutSessionService } from "../../timeout-session-service";
import { EpividianCommon } from "src/app/modules/utility/EpividianCommon";
import { ApiRoutes, ApiTypes } from "../../ep-api-handler/api-option-enums";
import { IChorusResponseToken } from "../../ep-api-handler/models/login-response.model";
import { environment } from "src/environments/environment";
import { RouteCacheService } from "../../route-cache.service";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(
    private router: Router,
    private userContext: UserContext,
    private timeoutSessionService: TimeoutSessionService,
    private epividianCommon: EpividianCommon,
    private routeCacheService: RouteCacheService
  ) { }

  private handleAuthError(err: HttpErrorResponse): Observable<any> {
    //handle your auth error or rethrow
    if (err.status === 401 || err.status === 403) {
        // Check if the URL is a feature access endpoint
        // If it is, just return the error without logging the user out
        if (err.url && (err.url.includes('/api/User/FeatureAccess') || err.url.includes('FeatureAccess'))) {
            return throwError(err);
        }

        // Cache current route before redirecting to login
        const currentRoute = this.router.url;
        if (currentRoute && currentRoute !== '/' && !currentRoute.includes('/Auth/')) {
          this.routeCacheService.cacheRoute(currentRoute);
          console.log('Auth interceptor cached route due to 401/403:', currentRoute);
        }

        //navigate /delete cookies or whatever
        let logoutRoute = ApiRoutes.Logout.replace('{{fromInactive}}', true.toString());
        let getSession = this.epividianCommon.GetSessionObjFromLocalStorage();
        if (getSession?.access_token)
        {
          this.userContext.apihandler.Post(ApiTypes.AuthProvider, logoutRoute, '', true, true).subscribe((s) => {
            this.userContext.ClearSession();
            this.timeoutSessionService.clearSessionTracker();
            return of(err.message);
          });
        }
        this.router.navigate(['/']);

        // if you've caught / handled the error, you don't want to rethrow it unless you also want downstream consumers to have to handle it as well.
       // or EMPTY may be appropriate here
    }

    return throwError(err);
  }


  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Convert the Promise to an Observable
    // Handle the HTTP request
    if (req.url.includes('Auth/'))
    {
      return next.handle(req);
    }

    return next.handle(req).pipe(
      catchError(x => this.handleAuthError(x)
    ),
    catchError(error => {
      // Handle any other errors
      return this.handleOtherErrors(error);
    }));

  }


  handleOtherErrors(error: any): Observable<any> {
    // Your error handling logic for other errors
    // ...
    return throwError(error);
  }

}
