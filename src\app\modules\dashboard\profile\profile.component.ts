import { Component, Inject, Injectable, OnInit } from '@angular/core';
//import { MsalBroadcastService, MsalService } from '@azure/msal-angular';
//import { EventMessage, EventType, InteractionStatus } from '@azure/msal-browser';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { NgxSpinnerService } from "ngx-spinner";
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';


interface Claim {
  id: number;
  claim: string;
  value: string;
}


Injectable()
@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})

export class ProfileComponent implements OnInit {
  displayedColumns: string[] = ['claim', 'value'];
  dataSource;
  private readonly _destroying$ = new Subject<void>();
  value: string = "";
  //authService: any;

  constructor(@Inject(NgxSpinnerService) private SpinnerService: NgxSpinnerService) {
    this.value = "";
   }

  ngOnInit(): void {
    this.SpinnerService.hide();
    /*this.msalBroadcastService.inProgress$
      .pipe(
        filter((status: InteractionStatus) =>  status === InteractionStatus.None || status === InteractionStatus.HandleRedirect),
        takeUntil(this._destroying$)
      )
      .subscribe(() => {
        this.checkAndSetActiveAccount();
        this.getClaims(this.authService.instance.getActiveAccount()?.idTokenClaims)
      })*/

      let dummyClaims: Claim[] = [
        {
          id: 1,
          claim: "test claim",
          value: "test claim value"
        }
      ];
      this.dataSource  = new MatTableDataSource(dummyClaims);
      this.SpinnerService.hide();
  }

  checkAndSetActiveAccount() {
/*
    let activeAccount = this.authService.instance.getActiveAccount();

    if (!activeAccount && this.authService.instance.getAllAccounts().length > 0) {
      let accounts = this.authService.instance.getAllAccounts();
      this.authService.instance.setActiveAccount(accounts[0]);
    }
    */
  }


  getClaims(claims: any) {

    let list: Claim[]  =  new Array<Claim>();

    Object.keys(claims).forEach(function(k, v){

      let c: Claim = {} as Claim;
      c.id = v;
      c.claim = k;
      c.value =  claims ? claims[k]: null;
      list.push(c);
    });


  }


  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }
}


