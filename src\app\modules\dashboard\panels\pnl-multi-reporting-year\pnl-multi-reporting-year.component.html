<div class="searchCriteriaDiv">
        <!-- Reporting Year Row -->
        <div class="field-row">
            <label class="field-label" for="rptpanMultiReportingYear">Reporting Year*:</label>
            <mat-select mat-form-field [formControl]="rptpanMultiReportingYear" multiple #years
             (valueChange)="readyToRun(years.value)" class="form-select form-select-sm year-select"
             name="rptpanMultiReportingYear" id="rptpanMultiReportingYear" placeholder="Select reporting years">
                <mat-option *ngFor="let option of selectData" [value]="option.key">{{option.value}}</mat-option>
            </mat-select>
        </div>

        <!-- Run Button Row -->
        <div class="button-row">
            <button type="button" (click)="panelService.InitBoldReport()"
            [disabled]="isRunDisabled" id="reportViewer_Control_viewReportClick"
            aria-describedby="reportViewer_Control_viewReportClick" [ngClass]="rptbtnColor" class="run-button">Run</button>
        </div>
</div>
