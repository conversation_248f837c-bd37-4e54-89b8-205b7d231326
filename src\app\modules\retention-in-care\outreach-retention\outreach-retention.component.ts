import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatDate } from '@angular/common';
import { firstValueFrom } from 'rxjs';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { RetentionService } from '../retention.service';
import { RCOurReachCallVM, RCAdministrativeActionVM } from '../retention-models';

@Component({
  selector: 'outreach-retention',
  templateUrl: './outreach-retention.component.html',
  styleUrls: ['./outreach-retention.component.scss']
})
export class OutreachRetentionComponent implements OnInit {

  constructor(
    private router: Router,
    private userContext: UserContext,
    private retentionService: RetentionService
  ) { }

  ngOnInit(): void {
  }

  async printReport() {
    const currentRoute = this.router.url;
    const siteId = this.userContext.GetCurrentSiteValue();

    if (currentRoute.includes('AdministrativeActionItems')) {
      await this.printAdministrativeActionItems(siteId);
    } else {
      // Default to Outreach Calls
      await this.printOutreachCalls(siteId);
    }
  }

  private async printOutreachCalls(siteId: number) {
    try {
      // Get the data
      const outreachData = await firstValueFrom(this.retentionService.GetOutreachCallDetail(siteId.toString()));

      if (!outreachData) {
        console.error('No outreach calls data available');
        return;
      }

      this.generateOutreachCallsPDF(outreachData);
    } catch (error) {
      console.error('Error generating outreach calls PDF:', error);
    }
  }

  private async printAdministrativeActionItems(siteId: number) {
    try {
      // Get the data
      const adminData = await firstValueFrom(this.retentionService.GetAdminActionDetails(siteId.toString()));

      if (!adminData) {
        console.error('No administrative action items data available');
        return;
      }

      this.generateAdministrativeActionItemsPDF(adminData);
    } catch (error) {
      console.error('Error generating administrative action items PDF:', error);
    }
  }

  private generateOutreachCallsPDF(data: RCOurReachCallVM) {
    const doc = new jsPDF('p', 'pt', 'letter');
    const currentDate = new Date();
    const formattedDate = formatDate(currentDate, 'MM/dd/yyyy', 'en-US');
    const formattedTime = formatDate(currentDate, 'HH:mm', 'en-US');

    let yPosition = 50;

    // Header
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Outreach and Retention - Outreach Calls', 50, yPosition);

    yPosition += 40;

    // Summary information
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`New Alerts: ${data.currentListCount}`, 50, yPosition);
    yPosition += 20;
    doc.text(`Reattempts Needed: ${data.tryLaterListCount}`, 50, yPosition);
    yPosition += 20;
    doc.text(`Calls Completed Last Week: ${data.callHistoryListCount}`, 50, yPosition);

    yPosition += 40;

    // Current Patients Section
    if (data.lstCurrentList && data.lstCurrentList.length > 0) {
      this.addPatientSection(doc, 'Current [' + data.currentListCount + ' Total Patient' + (data.currentListCount > 1 ? 's' : '') + ']',
                            data.lstCurrentList, yPosition);
      yPosition = this.getLastYPosition(doc) + 30;
    }

    // Try Later Patients Section
    if (data.lstTryLaterList && data.lstTryLaterList.length > 0) {
      if (yPosition > 650) { // Check if we need a new page
        doc.addPage();
        yPosition = 50;
      }
      this.addPatientSection(doc, 'Try Later [' + data.tryLaterListCount + ' Total Patient' + (data.tryLaterListCount > 1 ? 's' : '') + ']',
                            data.lstTryLaterList, yPosition);
      yPosition = this.getLastYPosition(doc) + 30;
    }

    // Call History Section
    if (data.lstCallHistoryList && data.lstCallHistoryList.length > 0) {
      if (yPosition > 650) { // Check if we need a new page
        doc.addPage();
        yPosition = 50;
      }
      this.addPatientSection(doc, 'Call History', data.lstCallHistoryList, yPosition);
    }

    // Add footer to all pages
    this.addFooter(doc, formattedDate, formattedTime);

    doc.save('OutreachCalls_' + formatDate(currentDate, 'ddMMyyyy', 'en-US') + '.pdf');
  }

  private generateAdministrativeActionItemsPDF(data: RCAdministrativeActionVM) {
    const doc = new jsPDF('p', 'pt', 'letter');
    const currentDate = new Date();
    const formattedDate = formatDate(currentDate, 'MM/dd/yyyy', 'en-US');
    const formattedTime = formatDate(currentDate, 'HH:mm', 'en-US');

    let yPosition = 50;

    // Header
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Outreach and Retention - Administrative Action Items', 50, yPosition);

    yPosition += 40;

    // Summary information
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Patient Awaiting Scheduling: ${data.scheduleListCount}`, 50, yPosition);
    yPosition += 20;
    doc.text(`Wrong Number/No Service: ${data.wrongNumberListCount}`, 50, yPosition);

    yPosition += 40;

    // Awaiting Scheduling Section
    if (data.lstScheduleList && data.lstScheduleList.length > 0) {
      this.addPatientSection(doc, 'Awaiting Scheduling [' + data.scheduleListCount + ' Total Patient' + (data.scheduleListCount > 1 ? 's' : '') + ']',
                            data.lstScheduleList, yPosition);
      yPosition = this.getLastYPosition(doc) + 30;
    }

    // Wrong Number/No Service Section
    if (data.lstWrongNumberList && data.lstWrongNumberList.length > 0) {
      if (yPosition > 650) { // Check if we need a new page
        doc.addPage();
        yPosition = 50;
      }
      this.addPatientSection(doc, 'Wrong Number/No Service [' + data.wrongNumberListCount + ' Total Patient' + (data.wrongNumberListCount > 1 ? 's' : '') + ']',
                            data.lstWrongNumberList, yPosition);
    }

    // Add footer to all pages
    this.addFooter(doc, formattedDate, formattedTime);

    doc.save('AdministrativeActionItems_' + formatDate(currentDate, 'ddMMyyyy', 'en-US') + '.pdf');
  }

  private addPatientSection(doc: jsPDF, sectionTitle: string, patientList: any[], startY: number) {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(sectionTitle, 50, startY);

    const tableData = patientList.map(patient => {
      // Clean HTML tags from the data
      const patientName = this.stripHtmlTags(patient.patientNameRiskDate || '');
      const providerInfo = this.stripHtmlTags(patient.annotationDescPrName || '');
      const homePhone = this.stripHtmlTags(patient.homePhone || '');
      const mobilePhone = this.stripHtmlTags(patient.mobilePhone || '');

      return [
        patientName,
        providerInfo,
        homePhone,
        mobilePhone
      ];
    });

    autoTable(doc, {
      head: [['Patient Name & Risk Date', 'Provider Info', 'Home Phone', 'Mobile Phone']],
      body: tableData,
      startY: startY + 25,
      styles: {
        fontSize: 9,
        cellPadding: 3
      },
      headStyles: {
        fillColor: [0, 113, 188],
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      columnStyles: {
        0: { cellWidth: 150 },
        1: { cellWidth: 150 },
        2: { cellWidth: 100 },
        3: { cellWidth: 100 }
      },
      margin: { left: 50, right: 50 },
      pageBreak: 'auto',
      showHead: 'everyPage'
    });
  }

  private stripHtmlTags(html: string): string {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  }

  private getLastYPosition(doc: jsPDF): number {
    // Get the last Y position from the autoTable
    return (doc as any).lastAutoTable?.finalY || 100;
  }

  private addFooter(doc: jsPDF, date: string, time: string) {
    const pageCount = doc.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);

      // Footer content
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');

      // Left side - Page number
      const pageText = `Page ${i} of ${pageCount}`;
      doc.text(pageText, 50, doc.internal.pageSize.height - 30);

      // Center - CHORUS by Epividian
      const centerX = doc.internal.pageSize.width / 2;
      doc.text('CHORUS™ by Epividian®', centerX, doc.internal.pageSize.height - 30, { align: 'center' });

      // Right side - Date/time printed
      const dateTimeText = `Printed: ${date} ${time}`;
      doc.text(dateTimeText, doc.internal.pageSize.width - 50, doc.internal.pageSize.height - 30, { align: 'right' });
    }
  }
}
