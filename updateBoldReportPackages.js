const { exec } = require('child_process');

const args = process.argv.slice(2);
const version = args[0] ? `@${args[0]}` : '@latest';

const mainPackages = [
    '@boldreports/angular-reporting-components',
    '@boldreports/global'
];

const devPackages = [
    '@boldreports/types'
];

const uninstallCmd = `npm uninstall ${mainPackages.concat(devPackages).join(' ')}`;
const installMainCmd = `npm install ${mainPackages.map(pkg => `${pkg}${version}`).join(' ')}`;
const installDevCmd = `npm install ${devPackages.map(pkg => `${pkg}${version}`).join(' ')} --save-dev`;

exec(uninstallCmd, (uninstallError, uninstallStdout, uninstallStderr) => {
    if (uninstallError) {
        console.error(`Error uninstalling packages: ${uninstallError}`);
        return;
    }
    console.log(`Uninstall Output: ${uninstallStdout}`);
    console.error(`Uninstall Error Output: ${uninstallStderr}`);

    exec(installMainCmd, (installError, installStdout, installStderr) => {
        if (installError) {
            console.error(`Error installing main packages: ${installError}`);
            return;
        }
        console.log(`Main Install Output: ${installStdout}`);
        console.error(`Main Install Error Output: ${installStderr}`);

        exec(installDevCmd, (installDevError, installDevStdout, installDevStderr) => {
            if (installDevError) {
                console.error(`Error installing dev packages: ${installDevError}`);
                return;
            }
            console.log(`Dev Install Output: ${installDevStdout}`);
            console.error(`Dev Install Error Output: ${installDevStderr}`);
        });
    });
});
