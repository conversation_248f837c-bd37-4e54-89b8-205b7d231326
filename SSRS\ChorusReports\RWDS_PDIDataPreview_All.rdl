﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>10.5in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Subreport Name="Subreport1">
                      <ReportName>RWDS_PDIDataPreview_Client</ReportName>
                      <Parameters>
                        <Parameter Name="DEMOGRAPHICS_ID">
                          <Value>=Fields!CLN_PK.Value</Value>
                        </Parameter>
                        <Parameter Name="FromDate">
                          <Value>=Parameters!FromDate.Value</Value>
                        </Parameter>
                        <Parameter Name="ToDate">
                          <Value>=Parameters!ToDate.Value</Value>
                        </Parameter>
                      </Parameters>
                      <KeepTogether>true</KeepTogether>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </Subreport>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details">
                <PageBreak>
                  <BreakLocation>Between</BreakLocation>
                </PageBreak>
              </Group>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>RWDS_PDIDataPreview</DataSetName>
        <Top>0.11334in</Top>
        <Height>0.25in</Height>
        <Width>10.5in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.42708in</Height>
    <Style />
  </Body>
  <Width>10.5in</Width>
  <Page>
    <PageFooter>
      <Height>0.375in</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="Textbox1">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=CStr(Globals!PageNumber) + " of " + CStr(Globals!TotalPages)</Value>
                  <Style />
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox1</rd:DefaultName>
          <Top>0.06944in</Top>
          <Left>0.12375in</Left>
          <Height>0.25in</Height>
          <Width>10.21875in</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageFooter>
    <PageHeight>8.5in</PageHeight>
    <PageWidth>11in</PageWidth>
    <InteractiveHeight>8.5in</InteractiveHeight>
    <InteractiveWidth>11in</InteractiveWidth>
    <LeftMargin>0.25in</LeftMargin>
    <RightMargin>0.25in</RightMargin>
    <TopMargin>0.25in</TopMargin>
    <BottomMargin>0.25in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="RWDS_PDIDataPreview">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@FromDate">
            <Value>=Parameters!FromDate.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ToDate">
            <Value>=Parameters!ToDate.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT DEMOGRAPHICS_ID AS CLN_PK, CLN_LAST_NAME, CLN_FIRST_NAME, CLN_MIDDLE_NAME
FROM [RWDS].[Client_SelectByDateRange](@FromDate, @ToDate)
ORDER BY CLN_LAST_NAME, CLN_FIRST_NAME, CLN_MIDDLE_NAME</CommandText>
      </Query>
      <Fields>
        <Field Name="CLN_PK">
          <DataField>CLN_PK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CLN_LAST_NAME">
          <DataField>CLN_LAST_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CLN_FIRST_NAME">
          <DataField>CLN_FIRST_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CLN_MIDDLE_NAME">
          <DataField>CLN_MIDDLE_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="FromDate">
      <DataType>DateTime</DataType>
      <Prompt>From Date</Prompt>
    </ReportParameter>
    <ReportParameter Name="ToDate">
      <DataType>DateTime</DataType>
      <Prompt>To Date</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>6c44a7ed-a9f3-4441-b461-bcca8b0a3fd0</rd:ReportID>
</Report>