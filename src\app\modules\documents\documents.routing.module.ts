import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/shared-modules/layouts/main-layout/main-layout.component';
import { DocumentLibraryComponent } from './document-library/document-library.component';
import { DownloadComponent } from './download/download.component';
import { EpividianGuardService } from 'src/app/shared-services/epividian-guard.service';

const routes: Routes = [
  {
    path: 'Documents',
    component: MainLayoutComponent,
    canActivate: [EpividianGuardService],
    children: [
      {path: ':dirPath', component: DocumentLibraryComponent}
    ]
  },
  {
    path: 'Download', // Replace 'download' with the initial route segment you want
    component: DownloadComponent,
    children: [
      {
        path: '**',
        component: DownloadComponent
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DocumentsRoutingModule {}
