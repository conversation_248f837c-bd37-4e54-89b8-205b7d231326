import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models';
import { PanelService } from '../PanelService';

@Component({
  selector: 'master-report',
  templateUrl: './master-report.component.html',
  styleUrls: ['./master-report.component.scss']
})
export class MasterReportComponent implements OnInit {

  private unsubscriber: Subject<any> = new Subject();


  public serviceUrl: string = "";
  public reportPath: string = "";
  public serverUrl: string = "";
  public processingMode: string = "";
  public parameters: any = {};
  public routerState: any;
  public reportInfo: IReportList;

  constructor(private panelService: PanelService,
    private ref: ChangeDetectorRef) {
    this.reportInfo = <IReportList>{}
  }

  ngOnInit(): void {
    /*
    this.panelService.currentPnlPatientSearchData
      .pipe(takeUntil(this.unsubscriber))
      .subscribe(res => {

        this.reportPath = res.reportPath
        this.parameters = res.searchData;
        this.ref.detectChanges();
      });
      */
  }

  ngOnDestroy() {
    this.unsubscriber.complete();
  }

}
