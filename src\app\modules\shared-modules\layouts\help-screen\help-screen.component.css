.helpHeader{
    display: flex;
    align-items: center;
    padding-left: 10px;
    color: white;
}

.helpTitle{
    padding-left: 20px;
    font-size: 1.2rem;
}

.title{
    background-color: #fafafa;
    width: 100%;
    height: 30px;
    padding-left: 10px;
    border-bottom: 1px solid;
    display: flex;
    align-items: center;
}

.table-layout{
  position: absolute;
  width: 100%; 
  height: calc(100vh - 50px);
}

.sidebar{
  height: 100%;
  border-right: 3px groove gray; 
  vertical-align: top;
  background-color: white;
}

.help-tree{
  height: 100%;
  overflow-y: auto;
  scroll-margin: 0px;
  scroll-padding: 0px;
}

  .contentlayout {
    max-width: 84vw; 
    width: 90%; 
    padding: 10px;
    vertical-align: top;
    background-color: #eff1f6;
    justify-content: space-between;
  }

  .content{
    height: 100%;
    overflow-y: auto;
  }

.tree-invisible {
    display: none;
  }
  
  .mat-tree-node{
    padding-right: 20px;
  }
  
  /*
   * This padding sets alignment of the nested nodes.
   */
  .help-tree .mat-nested-tree-node div[role=group] {
    padding-left: 30px;
    white-space: nowrap;
  }
  
  /*
   * Padding for leaf nodes.
   * Leaf nodes need to have padding so as to align with other non-leaf nodes
   * under the same parent.
   */
  .help-tree div[role=group] > .mat-tree-node {
    padding-left: 30px;
    padding-right: 40px;
    white-space: nowrap;
  }

  .help-tree div[role=group] > .mat-tree-node:hover {
    background-color:lightgrey;
  }

.leaf{
  cursor: pointer;
}
