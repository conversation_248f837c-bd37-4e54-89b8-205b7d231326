.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.overlay-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  background-color: transparent;;
  color:  #000000 !important;

  .button-container {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;

    button {
      flex: 1;
      background-color: #0074cc;
      color: #fff;
      border: none;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      border-radius: 5px;
      margin: 10px 20px;
    }
  }
}


html, body, material-app, mat-sidenav-container {
  height: 100%;
  width: 100%;
  margin: 0;
  background-color: #eff1f6;
}


:host::ng-deep mat-sidenav, .mat-drawer, mat-sidenav-container, mat-drawer, div{
  background-color: #eff1f6 !important;
  color: #eff1f6 !important;
  }

  :host::ng-deep .mat-drawer{
    background-color: #eff1f6 !important;
    color: #eff1f6 !important;
    }

