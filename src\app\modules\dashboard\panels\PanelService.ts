import { Injectable, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, ReplaySubject, Subject, Subscription } from 'rxjs';
import {
  ApiRoutes,
  ApiTypes,
} from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import {
  IData,
  IReportParamData,
} from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ICustomQueryParamOptions } from './models/custom-query-model';
import { ChainMethodTypesList, IMedicationGroup, IRegimenMedicationsAndGroups, IRegimenPanel, MedicationGroupOptions, RegimentOptions } from './pnl-custom-query/regimen-builder/regimen-builder.models';
import { ReportPanelTypes } from './report-panel-enums';

@Injectable({
  providedIn: 'root',
})
export class PanelService implements OnDestroy {
  bldReportContainerRef: any;
  reportViewerRef: Subject<any>;
  public static createdRegimenList: any[] = [];
  public static medicationGroups: IMedicationGroup[] = [] as IMedicationGroup[];
  ssrsRptParams: ReplaySubject<IReportParamData[]> = new ReplaySubject<IReportParamData[]>(1);
  public RunReportSub: Subject<any> = new Subject<any>();
  public HidePanelSub: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public static FormControlsData: ICustomQueryParamOptions = {} as ICustomQueryParamOptions;
  public static CustQueryFormGroup: FormGroup= {} as FormGroup;
  public static reactiveInput: any= {} as any;
  site: string = '0';

  private pageSubscriptions: Subscription = new Subscription;

  constructor(
    private apiHandler: ApiHandler,
    private userContext: UserContext
  ) {
    //Initialize the root Subject for the Report Viewer Reference
    this.reportViewerRef = new Subject<any>();

    this.pageSubscriptions.add(
      userContext.getCurrentSite().subscribe(site => {
        this.site = site.toString();
    }));
  }
  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }


  public getTextForMedGroupId(value: Number): string {
    const option = PanelService.medicationGroups.find(option => Number(option.code) == value);
    return option ? option.description : 'Other';
  }


  getTextForRegOptions(rs: IRegimenPanel): string {
    let option = RegimentOptions.find(option => option.value == rs.levelId);
    let panelOptionText: string = option ? option.text : '';

    //Handle the have taken and discontinued option
    if (rs.levelId == 1)
    {
      panelOptionText += " (within the last " + rs.currentlookBack + " days)";
    }
    else if (rs.levelId == 3) {
      //using the discontinuedLookback discontinuedRDBtnVal returned relative text
      if (rs.discontinuedRDBtnVal==1) {
        panelOptionText += " (ever)";
      } else {
        panelOptionText += " within " + rs.discontinuedLookback + " days"
      }
    }

    return panelOptionText;
  }

  getTextForChain(value: Number): string {
    const option = ChainMethodTypesList.find(option => option.value == value);
    return option ? option.text : '';
  }

  getTextForMedGroupOptions(value: Number): string {
    const option = MedicationGroupOptions.find(option => Number(option.value) == value);
    return option ? option.text : '';
  }

  isDefinedArt(): boolean {
    const option = PanelService.medicationGroups.find(i => i.description.includes('ART'));
    return PanelService.createdRegimenList.some(i => i.regimenList.find(j => j.group.code == option?.code));
  }

  isDefinedHCV(): boolean {
    const option = PanelService.medicationGroups.find(i => i.description.includes('HCV'));
    return PanelService.createdRegimenList.some(i => i.regimenList.find(j => j.group.code == option?.code));
  }

  //Initialize The Bold Report Component if in use.
  public async InitBoldReport(): Promise<void> {
    const { BldRptViewerComponent } = await import(
          '../report/report-viewer/bld-rpt-viewer/bld-rpt-viewer.component'
      );
        this.reportViewerRef.next(BldRptViewerComponent);
  }

  //adds a event trigger to the run Report Subject
  public RunReportSubTrigger(panData: any)
  {
    this.RunReportSub.next(panData);
  }

  //Configures the api Call for the report panel
  public GetPanelData(panelType: ReportPanelTypes, siteId: string = '0'): Observable<IReportParamData[]> {
    //depending on where entry point to page is you may need to load sitedId from context.
    if  (this.site != '0') {
      siteId = this.site;
    } else {
      siteId = this.userContext.GetCurrentSiteValue().toString();
    }

    var reportParamData = ApiRoutes.ReportParamData.toString()
      .replace('{{siteid}}', siteId)
      .replace('{{panelid}}', panelType.toString());

    return this.apiHandler.Get<IReportParamData[]>(
      ApiTypes.V2,
      reportParamData,
      false
    );
  }


  public ComboAutoComplete(searchString : string,
    comboId : number,
    rawData : IReportParamData[],
    isCohort : boolean
  )
  {
    var filteredData : IReportParamData[] = rawData.map(value=>value);
    var x : IData[] = [];
    if (searchString !== "All") {

    const lowercase = searchString.toLowerCase();

    if (isCohort)
    {
      x = rawData[comboId].data.filter((item) =>item.key.toString().toLowerCase().includes(lowercase));
    }
    else
    {
      x = rawData[comboId].data.filter((item) =>item.value.toLowerCase().includes(lowercase));
    }


      // Create a new filteredLocations array to avoid mutating the original array
    filteredData =  rawData.map((report, index) => {
        if (index === comboId) {
          return {
            ...report,
            data: x
          };
        }
        return filteredData[index];
      });
  }
  return filteredData;
  }

  //Configures the api Call for the Patients Search AutoComplete panel
  public PatientAutoComplete(searchText: string, searchType: string, siteId: string = '0'): Observable<IReportParamData> {

    //depending on where entry point to page is you may need to load sitedId from context.
    if  (this.site != '0') {
      siteId = this.site;
    } else {
      siteId = this.userContext.GetCurrentSiteValue().toString();
    }

    var getAutoCompleteData = ApiRoutes.GetAutoCompleteData.toString()
      .replace('{{siteid}}', siteId)
      .replace('{{searchtype}}', searchType)
      .replace('{{searchtext}}', searchText);

    return this.apiHandler.Get<IReportParamData>(
      ApiTypes.V2,
      getAutoCompleteData,
      false
    );
  }

  //Used on the Retention Panels to get the Group Heads for the dropdown.
  //revisit and map directly into return
  public getGroupName(groupId: number, groups: IReportParamData): string {
    var groupName = '';
    var groupObj = groups.groups.find((f) => f.id == groupId);
    if (groupObj) {
      groupName = groupObj.name;
    }
    return groupName;
  }

  //Used on the Retention and Diseasemanagement Panels to get the Group Heads for the dropdown.
  public getGroupData(groupId: number, panelObj: IReportParamData): IData[] {
    return panelObj.data.filter((f) => f.groupId == groupId);
  }

  //Stores the params directly associated do the ssrs report
  public InitializeReportParamSub(): ReplaySubject<IReportParamData[]> {
    return this.ssrsRptParams;
  }

  //Bring back All the SSRS Report Variable Definitions and Defaults directly from the ReportServer Db
  public GetReportParamsCall(rptName: string): Observable<any> {
    var getSSRSParams = ApiRoutes.GetSSRSParams.replace('{{rptName}}', rptName);
    //checks application terms agreement on entering dashboard.
    return this.apiHandler.Get(ApiTypes.V2, getSSRSParams);
  }

  //Calls the new SSRS Reporting Service to get Parameters for a specific report.
  public GetReportParams(rptName: string) {
    let rptSub = new Subject<IReportParamData[]>();
    var rptParams = [] as IReportParamData[];
    var rptParamData = [] as IData[];
    var getSSRSParams = ApiRoutes.GetSSRSParams.replace('{{rptName}}', rptName);
    //checks application terms agreement on entering dashboard.
    this.apiHandler.Get(ApiTypes.V2, getSSRSParams).subscribe((res) => {
      let paramsFromApi = res.Parameters.Parameter;
      if (paramsFromApi) {
        for (let paramEntry of paramsFromApi) {
          let currentDefaultValue = '';
          if (paramEntry.DefaultValues) {
            currentDefaultValue = paramEntry.DefaultValues.Value;
          }

          var tmpParam = {
            name: paramEntry.Name,
            default: paramEntry.DefaultValues.Value,
          } as IReportParamData;
          rptParams.push(tmpParam);
        }
        this.ssrsRptParams.next(rptParams);
      }
    });
  }

  // API call function to fetch data for available medications and medications group under Regimen Builder
  public GetMedicationsAndGroups(siteId: string = '0'): Observable<IRegimenMedicationsAndGroups> {
    if (siteId == '0') {
      siteId = this.site;
    }
    var url = ApiRoutes.GetMedicationsAndGroups.toString().replace(
      '{{siteid}}',
      siteId
    );
    return this.apiHandler.Get<IRegimenMedicationsAndGroups>(ApiTypes.V2, url);
  }


    // API call function to fetch data for custom query param options
    public GetCustomQueryParamOptions(siteId: string = '0'): Observable<ICustomQueryParamOptions> {
      if (siteId == '0') {
        siteId = this.site;
      }
      var url = ApiRoutes.GetCustomQuery.toString().replace(
        '{{siteid}}',
        siteId
      );
      return this.apiHandler.Get<ICustomQueryParamOptions>(ApiTypes.V2, url);
    }

    HidePanel(){
      this.HidePanelSub.next(true);
    }

    ShowPanel(){
      this.HidePanelSub.next(false);
    }

    SubToPanelVisibility()
    {
      return this.HidePanelSub.asObservable();
    }



    //This Dynamically adds the specific report panel component based on what has been defined in the report definition
  async CreateReportPanel(panel: ReportPanelTypes): Promise<any> {
    switch (panel) {
      case ReportPanelTypes.pnlPatientSearch:
        const { PnlPatientSearchComponent } = await import(
          '../panels/pnl-patient-search/pnl-patient-search.component'
        );
        return PnlPatientSearchComponent;

      case ReportPanelTypes.pnlReportingYear:
        const { PnlReportingYearComponent } = await import(
          '../panels/pnl-reporting-year/pnl-reporting-year.component'
        );
        return PnlReportingYearComponent;

      case ReportPanelTypes.pnlCohorts:
        const { PnlCohortsComponent } = await import(
          '../panels/pnl-cohorts/pnl-cohorts.component'
        );
        return PnlCohortsComponent;

      case ReportPanelTypes.pnlCohortsReportingYear:
        const { PnlCohortsReportingYearComponent } = await import(
          '../panels/pnl-cohorts-reporting-year/pnl-cohorts-reporting-year.component'
        );
        return PnlCohortsReportingYearComponent;

      case ReportPanelTypes.pnlCustomQuery:
        const { PnlCustomQueryComponent } = await import(
          '../panels/pnl-custom-query/pnl-custom-query.component'
        );
        return PnlCustomQueryComponent;

      case ReportPanelTypes.pnlFromToDate:
        const { PnlFromToDateComponent } = await import(
          '../panels/pnl-from-to-date/pnl-from-to-date.component'
        );
        return PnlFromToDateComponent;

      case ReportPanelTypes.pnlLstWeek:
        const { PnlLstWeekComponent } = await import(
          '../panels/pnl-lst-week/pnl-lst-week.component'
        );
        return PnlLstWeekComponent;

      case ReportPanelTypes.pnlMultiReportingYear:
        const { PnlMultiReportingYearComponent } = await import(
          '../panels/pnl-multi-reporting-year/pnl-multi-reporting-year.component'
        );
        return PnlMultiReportingYearComponent;

      case ReportPanelTypes.pnlRetention:
        const { PnlRetentionComponent } = await import(
          '../panels/pnl-retention/pnl-retention.component'
        );
        return PnlRetentionComponent;

      case ReportPanelTypes.pnlDiseaseManagement:
        const { PnlDiseaseManagementComponent } = await import(
          '../panels/pnl-disease-management/pnl-disease-management.component'
        );
        return PnlDiseaseManagementComponent;

      case ReportPanelTypes.pnlQrdaExtract:
        const { PnlQrdaExtractComponent: PnlQrdaExtractComponent } = await import(
          '../panels/pnl-qrda-extract/pnl-qrda-extract.component'
        );
        return PnlQrdaExtractComponent;

      case ReportPanelTypes.pnlLocation:
        const { PnlLocationComponent } = await import(
          '../panels/pnl-location/pnl-location.component'
        );
        return PnlLocationComponent;

      case ReportPanelTypes.pnlHcvLocation:
        const { PnlHcvLocationComponent } = await import(
          '../panels/pnl-hcv-location/pnl-hcv-location.component'
        );
        return PnlHcvLocationComponent;

      case ReportPanelTypes.pnlUsageDates:
        const { PnlUsageDatesComponent } = await import(
          '../panels/pnl-usage-dates/pnl-usage-dates.component'
        );
        return PnlUsageDatesComponent;

      default:
        const { PnlUserIdComponent } = await import(
          '../panels/pnl-user-id/pnl-user-id.component'
        );
        return PnlUserIdComponent;
    }
  }

  // Function to check if response has valid data for the requested control index.
  public HasReportParamData(response: IReportParamData[], index: number): boolean {
    return (
      response &&
      response.length > 0 &&
      response[index] != null &&
      response[index]?.data && // Optional chaining to safely check for data
      Array.isArray(response[index].data) &&
      response[index].data.length > 0
    );
  }

}
