/**
 * Models for rule definition schema
 */

/**
 * Represents the schema definition for a rule type
 */
export interface RuleDefinitionSchema {
  [key: string]: SchemaDefinition;
}

/**
 * Represents a schema definition for a field or object
 */
export interface SchemaDefinition {
  [key: string]: string | SchemaDefinition;
}

/**
 * Represents a field in a rule definition
 */
export interface RuleDefinitionField {
  name: string;
  path: string; // Full path to the field (for nested objects)
  type: string;
  isArray: boolean;
  isObject: boolean;
  isNestedObject?: boolean;
  children?: RuleDefinitionField[];
  value?: any;
}

/**
 * Represents a model in a rule definition
 */
export interface RuleDefinitionModel {
  name: string;
  fields: RuleDefinitionField[];
}

/**
 * Represents a form control configuration for a rule definition field
 */
export interface FieldConfig {
  name: string;
  path: string;
  label: string;
  type: string;
  value?: any;
  options?: any[];
  isArray: boolean;
  isObject: boolean;
  isNestedObject?: boolean;
  children?: FieldConfig[];
  validators?: any[];
}

/**
 * Represents a form field in the FormModel
 */
export interface FormField {
  FieldName: string;
  FieldType: string;
  Label: string;
  DataUrl: string | null;
  SelectedValue: string | null;
  Dimensions: any | null;
  isEditable: boolean;
  isRequired: boolean;
  LayoutOption: string;
  ValidationCondition: string;
  ValidationText: string | null;
  ValidationConstant: any | null;
  Attributes: Record<string, string> | null;
}

/**
 * Represents the layout configuration in the FormModel
 */
export interface FormLayout {
  LayoutType: string;
  LayoutAttributes: Record<string, any>;
}

/**
 * Represents the complete FormModel structure
 */
export interface FormModel {
  FormName: string;
  Fields: FormField[];
  Layout: FormLayout;
  DataUrl: string;
  PdfFillerForm: string;
}
