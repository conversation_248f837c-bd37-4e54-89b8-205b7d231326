<div class="execution-details-container">
  <div class="loading-indicator" *ngIf="loadingProcessDetails">
    <mat-spinner diameter="30"></mat-spinner>
    <p>Loading execution details...</p>
  </div>

  <div *ngIf="!loadingProcessDetails && selectedProcess">
    <div class="process-header">
      <div class="info-row">
        <span class="label">Process ID:</span>
        <span class="value">{{formatGuid(selectedProcess ? (selectedProcess.workflowProcessId || selectedProcess.workFlowProcessId) : '')}} <span class="guid-full">({{selectedProcess ? (selectedProcess.workflowProcessId || selectedProcess.workFlowProcessId) : ''}})</span></span>
        <button type="button" mat-icon-button [cdkCopyToClipboard]="selectedProcess ? (selectedProcess.workflowProcessId || selectedProcess.workFlowProcessId || '') : ''" matTooltip="Copy Full ID">
          <mat-icon>content_copy</mat-icon>
        </button>
      </div>
      <div class="info-row">
        <span class="label">Workflow:</span>
        <span class="value">{{selectedProcess.workflowName}}</span>
      </div>
      <div class="info-row">
        <span class="label">Status:</span>
        <span class="status-badge" [ngClass]="{
          'success': selectedProcess.isComplete && selectedProcess.isSuccess,
          'error': selectedProcess.isComplete && !selectedProcess.isSuccess,
          'running': !selectedProcess.isComplete
        }">
          {{selectedProcess.status}}
        </span>
      </div>
      <div class="info-row">
        <span class="label">Start Time:</span>
        <span class="value">{{selectedProcess.startTime | date:'medium'}}</span>
      </div>
      <div class="info-row" *ngIf="selectedProcess.endTime">
        <span class="label">End Time:</span>
        <span class="value">{{selectedProcess.endTime | date:'medium'}}</span>
      </div>
    </div>

    <h3>Rule Execution Results</h3>

    <div *ngIf="processRuleResults.length === 0" class="no-results-message">
      <p>No rule execution results available</p>
    </div>

    <table mat-table [dataSource]="processRuleResults" class="mat-elevation-z2" *ngIf="processRuleResults.length > 0">
      <!-- Rule Name Column -->
      <ng-container matColumnDef="ruleName">
        <th mat-header-cell *matHeaderCellDef>Rule</th>
        <td mat-cell *matCellDef="let result">{{result.ruleName}}</td>
      </ng-container>

      <!-- Execution Time Column -->
      <ng-container matColumnDef="executionTime">
        <th mat-header-cell *matHeaderCellDef>Execution Time</th>
        <td mat-cell *matCellDef="let result">{{result.executionTime | date:'medium'}}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let result">
          <span class="status-badge" [ngClass]="{
            'success': result.isSuccess,
            'error': !result.isSuccess
          }">
            {{result.isSuccess ? 'Success' : 'Failed'}}
          </span>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <div *ngIf="!loadingProcessDetails && !selectedProcess" class="no-data-message">
    <mat-icon>error_outline</mat-icon>
    <p>No process details available</p>
  </div>
</div>

<div class="dialog-actions">
  <button type="button" mat-button mat-dialog-close>Close</button>
</div>
