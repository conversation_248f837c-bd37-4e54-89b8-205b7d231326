import { Component, OnInit, Input, Output, EventEmitter, ViewChild, TemplateRef } from '@angular/core';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service';
import { RulesEngineService } from '../../../services/rules-engine.service';
import { Rule, Workflow, WorkflowRule } from '../../../models/rule.model';

@Component({
  selector: 'app-workflow-details',
  templateUrl: './workflow-details.component.html',
  styleUrls: ['./workflow-details.component.scss']
})
export class WorkflowDetailsComponent implements OnInit {
  @Input() siteId: string = '';
  @Input() workflow: Workflow | null = null;
  @Input() workflowRules: Rule[] = [];
  @Input() availableRules: Rule[] = [];

  @Output() ruleAdded = new EventEmitter<Rule>();
  @Output() ruleRemoved = new EventEmitter<Rule>();
  @Output() rulesReordered = new EventEmitter<Rule[]>();
  @Output() saveRules = new EventEmitter<void>();

  filteredAvailableRules: Rule[] = [];
  ruleSearchText: string = '';
  loadingRules: boolean = false;

  // Track rule dependencies
  ruleDependencies: Map<number, number[]> = new Map();

  // Form for editing rule dependencies
  dependencyForm: FormGroup;
  selectedRule: Rule | null = null;
  selectedDependencies: number[] = [];

  // Search and filter for dependencies
  dependencySearchText = '';
  filteredWorkflowRules: Rule[] = [];

  // Reference to the dependency dialog template
  @ViewChild('dependencyDialog') dependencyDialog!: TemplateRef<unknown>;

  constructor(
    private rulesService: RulesEngineService,
    private layoutService: LayoutService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private toastr: ToastrService
  ) {
    this.dependencyForm = this.formBuilder.group({
      ruleId: [null, Validators.required],
      dependencyRules: [[]]
    });
  }

  ngOnInit(): void {
    this.updateAvailableRules();
    this.loadRuleDependencies();
  }

  ngOnChanges(): void {
    this.updateAvailableRules();
    this.loadRuleDependencies();
  }

  /**
   * Load rule dependencies for the current workflow
   */
  loadRuleDependencies(): void {
    if (!this.workflow) return;

    this.loadingRules = true;
    this.ruleDependencies.clear();

    this.rulesService.getWorkflowRuleDetails(this.workflow.workflowID).subscribe({
      next: (workflowRules) => {
        // Process the workflow rules to extract dependencies
        for (const wr of workflowRules) {
          if (wr.dependencyRules && wr.dependencyRules.length > 0) {
            this.ruleDependencies.set(wr.ruleID, wr.dependencyRules);
          } else {
            this.ruleDependencies.set(wr.ruleID, []);
          }
        }
        this.loadingRules = false;
      },
      error: (error) => {
        console.error('Error loading workflow rule dependencies:', error);
        this.loadingRules = false;
        this.toastr.error('Failed to load rule dependencies', 'Error');
      }
    });
  }

  /**
   * Update the available rules list by filtering out rules already in the workflow
   */
  updateAvailableRules(): void {
    // Filter out rules that are already in the workflow
    const workflowRuleIds = this.workflowRules.map(r => r.ruleID);
    this.filteredAvailableRules = this.availableRules.filter(r => !workflowRuleIds.includes(r.ruleID));

    // Apply search filter if there is one
    this.filterAvailableRules();
  }

  /**
   * Filter available rules based on search text
   */
  filterAvailableRules(): void {
    if (!this.ruleSearchText) {
      this.filteredAvailableRules = this.availableRules.filter(r =>
        !this.workflowRules.some(wr => wr.ruleID === r.ruleID)
      );
      return;
    }

    const searchText = this.ruleSearchText.toLowerCase();
    this.filteredAvailableRules = this.availableRules.filter(rule =>
      !this.workflowRules.some(wr => wr.ruleID === rule.ruleID) &&
      (rule.name.toLowerCase().includes(searchText) ||
      rule.description?.toLowerCase().includes(searchText))
    );
  }

  /**
   * Add a rule to the workflow
   * @param rule The rule to add
   */
  addRuleToWorkflow(rule: Rule): void {
    this.ruleAdded.emit(rule);
  }

  /**
   * Remove a rule from the workflow
   * @param rule The rule to remove
   */
  removeRuleFromWorkflow(rule: Rule): void {
    this.ruleRemoved.emit(rule);
  }

  /**
   * Handle drag and drop reordering of rules
   * @param event The drag drop event
   */
  dropRule(event: CdkDragDrop<Rule[]>): void {
    if (event.previousIndex === event.currentIndex) return;

    moveItemInArray(this.workflowRules, event.previousIndex, event.currentIndex);
    this.rulesReordered.emit(this.workflowRules);
  }

  /**
   * Save the workflow rules order and dependencies
   */
  saveWorkflowRules(): void {
    this.saveRules.emit();
  }

  /**
   * Open the dependency editor for a rule
   * @param rule The rule to edit dependencies for
   */
  editDependencies(rule: Rule): void {
    this.selectedRule = rule;

    // Get current dependencies for this rule
    const dependencies = this.ruleDependencies.get(rule.ruleID) || [];

    // Store the selected dependencies in our array for the checkboxes
    this.selectedDependencies = [...dependencies];

    // Reset search filter
    this.dependencySearchText = '';

    // Initialize filtered rules
    this.filteredWorkflowRules = [...this.workflowRules];

    // Update the form
    this.dependencyForm.patchValue({
      ruleId: rule.ruleID,
      dependencyRules: dependencies
    });

    // Open dialog to edit dependencies
    this.dialog.open(this.dependencyDialog, {
      width: '450px',
      maxHeight: '70vh',
      data: { rule: rule }
    });
  }

  /**
   * Filter the workflow rules based on search text
   */
  filterDependencies(): void {
    if (!this.dependencySearchText) {
      this.filteredWorkflowRules = [...this.workflowRules];
      return;
    }

    const searchText = this.dependencySearchText.toLowerCase();
    this.filteredWorkflowRules = this.workflowRules.filter(rule =>
      rule.name.toLowerCase().includes(searchText) ||
      rule.description?.toLowerCase().includes(searchText)
    );
  }

  /**
   * Check if a dependency is selected
   * @param ruleId The rule ID to check
   * @returns True if the rule is selected as a dependency
   */
  isDependencySelected(ruleId: number): boolean {
    return this.selectedDependencies.includes(ruleId);
  }

  /**
   * Toggle a dependency selection
   * @param ruleId The rule ID to toggle
   * @param checked Whether the checkbox is checked
   */
  toggleDependency(ruleId: number, checked: boolean): void {
    if (checked) {
      // Add to selected dependencies if not already there
      if (!this.selectedDependencies.includes(ruleId)) {
        this.selectedDependencies.push(ruleId);
      }
    } else {
      // Remove from selected dependencies
      this.selectedDependencies = this.selectedDependencies.filter(id => id !== ruleId);
    }

    // Update the form value
    this.dependencyForm.patchValue({
      dependencyRules: this.selectedDependencies
    });
  }

  /**
   * Save rule dependencies
   */
  saveDependencies(): void {
    if (!this.workflow || !this.selectedRule) return;

    const ruleId = this.selectedRule.ruleID;

    this.layoutService.showSpinner();

    this.rulesService.updateRuleDependencies(
      this.workflow.workflowID,
      ruleId,
      this.selectedDependencies
    ).subscribe({
      next: () => {
        this.layoutService.hideSpinner();
        this.toastr.success('Rule dependencies updated', 'Success');
        this.ruleDependencies.set(ruleId, [...this.selectedDependencies]);
        this.dialog.closeAll();
      },
      error: (error) => {
        this.layoutService.hideSpinner();
        console.error('Error updating rule dependencies:', error);
        this.toastr.error('Failed to update rule dependencies', 'Error');
      }
    });
  }

  /**
   * Get dependencies for a rule
   * @param ruleId The rule ID
   * @returns Array of dependency rule IDs
   */
  getDependenciesForRule(ruleId: number): number[] {
    return this.ruleDependencies.get(ruleId) || [];
  }

  /**
   * Get rule name by ID
   * @param ruleId The rule ID
   * @returns The rule name or 'Unknown Rule'
   */
  getRuleNameById(ruleId: number): string {
    const rule = this.workflowRules.find(r => r.ruleID === ruleId);
    return rule ? rule.name : 'Unknown Rule';
  }

  /**
   * Check if a rule can be a dependency for another rule
   * @param targetRuleId The rule that would depend on others
   * @param potentialDependencyId The potential dependency rule
   * @returns True if the rule can be a dependency
   */
  canBeADependency(targetRuleId: number, potentialDependencyId: number): boolean {
    // A rule cannot depend on itself
    if (targetRuleId === potentialDependencyId) return false;

    // A rule cannot depend on a rule that depends on it (circular dependency)
    const potentialDependencies = this.ruleDependencies.get(potentialDependencyId) || [];
    if (potentialDependencies.includes(targetRuleId)) return false;

    return true;
  }
}
